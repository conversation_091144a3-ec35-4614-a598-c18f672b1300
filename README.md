# CV分析系统 (BXT Analysis)

一个基于Docker的计算机视觉分析系统，包含调度器(Scheduler)和推理服务(Inference)组件。

## 📁 项目结构

```
bxt-analysis-1/
├── README.md                           # 项目主README
├── docker-compose.yml                  # 主docker-compose文件
├── build.sh                           # 主构建脚本
├── start.sh                           # 主启动脚本
├── stop.sh                            # 主停止脚本
│
├── docs/                              # 📚 项目文档
│   ├── api/                           # API文档
│   ├── deployment/                    # 部署文档
│   ├── development/                   # 开发文档
│   ├── features/                      # 功能特性文档
│   └── upgrades/                      # 升级说明文档
│
├── examples/                          # 🧪 示例和测试用例
│   ├── requests/                      # API请求示例
│   │   ├── yolo/                      # YOLO相关请求
│   │   ├── ovit/                      # OVIT相关请求
│   │   └── scheduler/                 # Scheduler相关请求
│   ├── scripts/                       # 测试脚本
│   └── data/                          # 测试数据
│
├── services/                          # 🚀 微服务
│   ├── scheduler/                     # 调度服务
│   ├── inference/                     # 推理服务
│   ├── inference-mock/                # 模拟推理服务
│   └── rtsp-server/                   # RTSP服务器
│
├── infrastructure/                    # 🏗️ 基础设施
│   ├── kafka/                         # Kafka配置
│   ├── mongo-init/                    # MongoDB初始化
│   └── logs/                          # 日志目录
│
├── test/                              # 🧪 测试套件
│   ├── end-to-end/                    # 端到端测试
│   ├── inference/                     # 推理服务测试
│   ├── inference-mock/                # 模拟服务测试
│   └── scheduler/                     # 调度服务测试
│
└── tools/                            # 🔧 工具和分析
    ├── kafka_consumer.py              # Kafka消费者工具
    └── analysis/                      # 分析文档
```

## 🏗️ 系统架构

- **cv-scheduler**: CV推理服务调度器，负责任务分配和服务管理
- **cv-inference-mock**: 推理服务模拟器，支持多种算法编排
- **外部依赖**: MongoDB、Kafka、Redis(可选)

## 🚀 快速开始

### 1. 环境准备

确保已安装以下依赖：
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.6+

### 2. 启动外部服务

```bash
# 启动MongoDB
docker run -d --name mongodb \
  -p 27017:27017 \
  -e MONGO_INITDB_ROOT_USERNAME=admin \
  -e MONGO_INITDB_ROOT_PASSWORD=password123 \
  mongo:6.0

# 启动Kafka
docker run -d --name kafka \
  -p 9092:9092 \
  -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
  -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
  confluentinc/cp-kafka:latest
```

### 3. 配置环境变量

```bash
# 复制配置模板
cp .env.example .env

# 编辑配置文件
vim .env
```

### 4. 构建和启动

```bash
# 构建镜像
./build.sh

# 启动服务
./start.sh

# 查看状态
./start.sh status
```

## 📖 详细文档

- [Docker部署指南](DOCKER_DEPLOYMENT.md) - 完整的Docker部署说明
- [测试执行指南](TEST_EXECUTION_GUIDE.md) - 测试相关文档
- [实现总结](IMPLEMENTATION_SUMMARY.md) - 系统实现详情

## 🔗 服务地址

启动成功后，可以访问以下地址：

- **Scheduler**: http://localhost:8080
- **Scheduler健康检查**: http://localhost:8080/actuator/health
- **Inference-Mock**: http://localhost:8081
- **Inference-Mock健康检查**: http://localhost:8081/health

## 📋 API文档

- **Scheduler API**: http://localhost:8080/swagger-ui.html
- **健康检查**: 各服务的 `/health` 或 `/actuator/health` 端点

## 🛠️ 开发指南

### 项目结构

```
├── scheduler/          # 调度器服务
├── inference-mock/     # 推理模拟服务
├── docker-compose.yml  # Docker编排文件
├── build.sh           # 镜像构建脚本
├── start.sh           # 启动脚本
├── stop.sh            # 停止脚本
└── .env.example       # 环境变量模板
```

### 常用命令

```bash
# 构建特定服务镜像
./build.sh scheduler
./build.sh inference-mock

# 启动单实例
./start.sh start-single

# 启动多实例
./start.sh start-multi

# 查看日志
./start.sh logs

# 停止服务
./stop.sh

# 停止并清理
./stop.sh --cleanup
```

## 🔧 故障排除

1. **检查外部服务**: 确保MongoDB和Kafka正常运行
2. **查看日志**: 使用 `./start.sh logs` 查看详细日志
3. **检查端口**: 确保8080、8081端口未被占用
4. **重新构建**: 如有问题可尝试 `./build.sh` 重新构建镜像

## 📝 更新日志

- **v1.0.0**: 初始版本，支持Docker部署
- 统一的Docker部署方案
- 支持环境变量配置
- 支持单实例和多实例部署
