#!/usr/bin/env python3
"""
Kafka消费者脚本 - 用于查看vision-events topic的数据格式
"""

import json
import sys
from datetime import datetime
from kafka import KafkaConsumer
from kafka.errors import KafkaError

def create_consumer():
    """创建Kafka消费者"""
    try:
        consumer = KafkaConsumer(
            'vision-events',
            bootstrap_servers=['localhost:9092'],
            auto_offset_reset='earliest',  # 从最早的消息开始
            enable_auto_commit=True,
            group_id='data-format-viewer',
            value_deserializer=lambda m: m.decode('utf-8') if m else None,
            consumer_timeout_ms=30000  # 30秒超时
        )
        return consumer
    except Exception as e:
        print(f"❌ 创建消费者失败: {e}")
        return None

def format_message(message):
    """格式化消息显示"""
    print("=" * 80)
    print(f"📅 时间戳: {datetime.fromtimestamp(message.timestamp / 1000)}")
    print(f"📍 分区: {message.partition}, 偏移量: {message.offset}")
    print(f"🔑 Key: {message.key.decode('utf-8') if message.key else 'None'}")
    print("📄 消息内容:")
    
    try:
        # 尝试解析JSON
        data = json.loads(message.value)
        print(json.dumps(data, indent=2, ensure_ascii=False))
    except json.JSONDecodeError:
        # 如果不是JSON，直接显示原始内容
        print(message.value)
    
    print("=" * 80)
    print()

def main():
    """主函数"""
    print("🔍 Kafka消费者 - vision-events数据格式查看器")
    print("=" * 60)
    print("📡 连接到: localhost:9092")
    print("📋 Topic: vision-events")
    print("⏰ 超时时间: 30秒")
    print("💡 按 Ctrl+C 停止消费")
    print("=" * 60)
    print()
    
    # 创建消费者
    consumer = create_consumer()
    if not consumer:
        sys.exit(1)
    
    message_count = 0
    
    try:
        print("🎧 开始监听消息...")
        for message in consumer:
            message_count += 1
            print(f"📨 收到第 {message_count} 条消息:")
            format_message(message)
            
            # 每10条消息询问是否继续
            if message_count % 10 == 0:
                response = input(f"已显示 {message_count} 条消息，继续？(y/n): ")
                if response.lower() != 'y':
                    break
                    
    except KeyboardInterrupt:
        print("\n🛑 用户中断，停止消费")
    except KafkaError as e:
        print(f"❌ Kafka错误: {e}")
    except Exception as e:
        print(f"❌ 未知错误: {e}")
    finally:
        consumer.close()
        print(f"✅ 消费完成，共处理 {message_count} 条消息")

if __name__ == "__main__":
    main()
