# Kafka 配置问题分析和解决方案

## 🔍 问题诊断

### 当前状况
- ✅ **Bootstrap 连接成功**: 能连接到 `*************:9092`
- ❌ **集群元数据问题**: 集群中有 broker 配置为 `localhost:9092`，但本地无 Kafka 服务
- ❌ **消息发送失败**: 因为无法连接到所有 broker，导致元数据更新失败

### 错误日志分析
```
Node 1 connection failed -- refreshing metadata
KafkaTimeoutError: Failed to update metadata after 5.0 secs.
```

## 🎯 根本原因

**远端 Kafka 服务器配置问题**：
- Kafka 集群的 `advertised.listeners` 配置中包含了 `localhost:9092`
- 客户端获取元数据时，会尝试连接集群中的所有 broker
- 本地没有 Kafka 服务，导致连接 `localhost:9092` 失败

## 🛠️ 解决方案

### 方案1：修复远端 Kafka 服务器配置（推荐）

**需要在远端 Kafka 服务器上修改配置**：

```properties
# server.properties
advertised.listeners=PLAINTEXT://*************:9092
listeners=PLAINTEXT://0.0.0.0:9092
```

**重启 Kafka 服务**：
```bash
sudo systemctl restart kafka
```

### 方案2：客户端配置优化（临时解决）

**已应用的优化**：
- 添加连接超时配置
- 限制元数据刷新时间
- 设置请求超时

**配置参数**：
```python
producer = KafkaProducer(
    bootstrap_servers=['*************:9092'],
    request_timeout_ms=15000,
    max_block_ms=5000,
    metadata_max_age_ms=60000,
    connections_max_idle_ms=60000,
    security_protocol='PLAINTEXT'
)
```

### 方案3：使用单节点配置（备选）

如果远端 Kafka 是单节点部署，可以配置为：
```properties
# server.properties
broker.id=0
advertised.listeners=PLAINTEXT://*************:9092
listeners=PLAINTEXT://0.0.0.0:9092
num.network.threads=3
num.io.threads=8
socket.send.buffer.bytes=102400
socket.receive.buffer.bytes=102400
socket.request.max.bytes=104857600
```

## 📋 验证步骤

### 1. 检查远端 Kafka 配置
```bash
# 在远端服务器上
cat /opt/kafka/config/server.properties | grep advertised.listeners
```

### 2. 检查 Kafka 集群状态
```bash
# 在远端服务器上
/opt/kafka/bin/kafka-broker-api-versions.sh --bootstrap-server localhost:9092
```

### 3. 测试客户端连接
```bash
# 在本地
cd inference-mock
source venv/bin/activate
python ../test_kafka_simple.py
```

## 🎯 推荐行动

1. **立即行动**: 联系远端 Kafka 管理员，修复 `advertised.listeners` 配置
2. **临时措施**: 当前的客户端优化配置已应用，可以减少连接问题
3. **验证**: 配置修复后，重新运行测试脚本验证

## 📝 配置文件更新

### inference-mock/config.yaml
```yaml
kafka:
  bootstrap_servers: ["*************:9092"]  # ✅ 正确
  topic: "vision-events"                      # ✅ 正确
  client_id: "inference-mock-1"               # ✅ 正确
  # 其他配置保持不变
```

### test/end-to-end/test_complete_correct.py
```python
consumer = KafkaConsumer(
    'vision-events',                    # ✅ 已修复 topic 名称
    bootstrap_servers=['*************:9092'],  # ✅ 已修复服务器地址
    # 其他配置保持不变
)
```

## 🔄 后续监控

- 监控 inference-mock 日志中的 Kafka 连接状态
- 检查事件是否成功发送到 Kafka
- 验证测试脚本中的消费者能否接收到消息

## 💡 最终建议

**最有效的解决方案是修复远端 Kafka 服务器的 `advertised.listeners` 配置**。这是一个常见的 Kafka 部署问题，通常发生在：
- 开发环境配置迁移到生产环境时
- Docker 容器部署时网络配置不当
- 多网卡服务器配置错误

修复后，所有 Kafka 连接问题都会解决。
