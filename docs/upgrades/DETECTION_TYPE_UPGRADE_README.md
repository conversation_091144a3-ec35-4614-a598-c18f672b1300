# 检测类型配置升级说明 (v2.0)

## 🎯 升级概述

本次升级统一了检测和跟踪算法的配置方式，简化了API接口，提高了系统的可维护性和扩展性。

## 🔄 主要变更

### 1. **检测类型枚举化**
新增`DetectionType`枚举，支持三种基础检测类型：
- `PERSON`: 人员检测
- `VEHICLE`: 车辆检测  
- `NON_MOTOR_VEHICLE`: 非机动车检测

### 2. **算法ID设计原则**
- **YOLO检测**: 统一使用`algorithmId: "detection"`，具体检测类型由`detection_type`配置决定
- **OWL/OVIT检测**: 保持特定算法ID（如`"ovit_detection"`），用于万物检测
- **跟踪算法**: 统一使用`algorithmId: "tracking"`，不再区分具体目标类型

### 3. **配置方式优化**
YOLO检测模块支持两种配置方式：
- **推荐方式**: 使用`detection_type`枚举
- **兼容方式**: 直接指定`class_ids`数组

## 📋 配置对比

### 旧版本配置 (v1.x)
```json
{
  "algorithmId": "person_detection",
  "config": {
    "confidence": 0.7,
    "class_ids": [0]
  }
},
{
  "algorithmId": "person_tracking",
  "algorithmType": "TRACKING"
}
```

### 新版本配置 (v2.0)
```json
{
  "algorithmId": "detection",
  "algorithmName": "目标检测",
  "config": {
    "confidence": 0.7,
    "detection_type": "PERSON"
  }
},
{
  "algorithmId": "tracking",
  "algorithmName": "目标跟踪",
  "algorithmType": "TRACKING"
}
```

## 🚗 支持的检测类型

| 检测算法 | 算法ID | 检测类型 | 枚举值 | COCO Class IDs | 说明 |
|----------|--------|----------|--------|----------------|------|
| YOLO检测 | `detection` | 人员检测 | `PERSON` | [0] | 检测人员目标 |
| YOLO检测 | `detection` | 车辆检测 | `VEHICLE` | [2,3,5,7] | 汽车、摩托车、公交车、卡车 |
| YOLO检测 | `detection` | 非机动车检测 | `NON_MOTOR_VEHICLE` | [1] | 自行车等非机动车 |
| OWL检测 | `ovit_detection` | 万物检测 | - | 开放词汇 | 任意物体检测 |

## 📁 更新的文件

### 代码文件
- `inference/vas/entrypoints/task_interface.py` - 新增DetectionType枚举
- `inference/vas/modules/yolov8_detector_module.py` - 支持detection_type配置
- `inference/config/config.yaml` - 更新默认配置

### 请求示例文件
- `yolo_tracking_task_request.json` - 人员检测+跟踪+分类
- `yolo_tripwire_classification_task_request.json` - 人员绊线检测
- `yolo_zone_intrusion_classification_task_request.json` - 人员区域入侵
- `yolo_vehicle_tripwire_classification_task_request.json` - 车辆绊线检测
- `yolo_bicycle_zone_intrusion_classification_task_request.json` - 非机动车区域入侵

### 文档文件
- `doc/vision-flow-simplified-protocol.md` - 更新协议说明

## 🔧 代码实现细节

### YOLO检测模块改动
```python
class Yolov8DetectorModule(BaseModule):
    # COCO数据集类别ID映射
    DETECTION_TYPE_CLASS_IDS = {
        "PERSON": [0],
        "VEHICLE": [2, 3, 5, 7], 
        "NON_MOTOR_VEHICLE": [1]
    }
    
    def __init__(self, module_name, config, queue, logger, task_info=None):
        # 支持两种配置方式
        if "detection_type" in self.config[module_name]:
            detection_type = self.config[module_name]["detection_type"]
            self.class_ids = self.DETECTION_TYPE_CLASS_IDS.get(detection_type, [0])
        else:
            self.class_ids = self.config[module_name].get("class_ids", [0])
```

## ✅ 向后兼容性

- ✅ 保持对原有`class_ids`配置的支持
- ✅ 现有任务请求无需修改即可运行
- ✅ 新旧配置方式可以混合使用

## 🚀 使用建议

1. **新项目**: 推荐使用`detection_type`枚举配置
2. **现有项目**: 可以逐步迁移到新配置方式
3. **特殊需求**: 仍可使用`class_ids`直接指定类别

## 🔮 未来扩展

系统设计支持轻松扩展新的检测类型：
- 添加新的`DetectionType`枚举值
- 在`DETECTION_TYPE_CLASS_IDS`中配置对应的class_ids
- 无需修改核心算法逻辑

## 🎯 **统一化优势**

### **接口简化**
- 检测算法统一使用`"detection"`
- 跟踪算法统一使用`"tracking"`
- 减少API复杂度和学习成本

### **配置清晰**
- 算法功能由`algorithmType`决定
- 具体行为由配置参数决定
- 语义更加明确直观

### **易于维护**
- 减少算法ID的维护成本
- 统一的命名规范
- 降低配置错误率

### **扩展友好**
- 新增检测类型只需添加枚举值
- 所有算法ID保持不变
- 无需修改核心逻辑

## 📞 技术支持

如有问题，请参考：
- 协议文档: `doc/vision-flow-simplified-protocol.md`
- 示例请求: `*_task_request.json`文件
- 配置文件: `inference/config/config.yaml`
