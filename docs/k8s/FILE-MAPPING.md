# K8s YAML 文件序号重新整理

## 📋 文件重命名映射

为了确保部署顺序的逻辑性，我们重新整理了 Kubernetes YAML 文件的序号：

### 🔄 文件映射表

| 旧文件名 | 新文件名 | 说明 |
|----------|----------|------|
| `00-namespace.yaml` | `01-namespace.yaml` | 命名空间和 RBAC 权限 |
| `01-storage.yaml` | `02-storage.yaml` | 持久化存储 (PV/PVC) |
| `02-configmaps.yaml` | `03-configmaps.yaml` | 配置文件映射 |
| `03-secrets.yaml` | `04-secrets.yaml` | 密钥配置 |
| `10-zookeeper.yaml` | `05-zookeeper.yaml` | Zookeeper 服务 |
| `11-kafka.yaml` | `06-kafka.yaml` | Kafka 消息队列 |
| `12-mongodb.yaml` | `07-mongodb.yaml` | MongoDB 数据库 |
| `20-scheduler.yaml` | `08-scheduler.yaml` | Scheduler 调度器 |
| `21-inference.yaml` | `09-inference.yaml` | Inference 推理服务 |
| `22-rtsp-server.yaml` | `10-rtsp-server.yaml` | RTSP 视频服务器 |
| `23-inference-mock.yaml` | `11-inference-mock.yaml` | Inference Mock 模拟服务 |
| `30-ingress.yaml` | `12-ingress.yaml` | Ingress 网络配置 |
| `40-monitoring.yaml` | `13-monitoring.yaml` | 监控和告警配置 |

### 📊 新的部署顺序

```
01 → 02 → 03 → 04 → 05 → 06 → 07 → 08 → 09 → 10 → 11 → 12 → 13
 │    │    │    │    │    │    │    │    │    │    │    │    │
 │    │    │    │    │    │    │    │    │    │    │    │    └─ 监控配置
 │    │    │    │    │    │    │    │    │    │    │    └─ Ingress
 │    │    │    │    │    │    │    │    │    │    └─ Inference Mock
 │    │    │    │    │    │    │    │    │    └─ RTSP Server
 │    │    │    │    │    │    │    │    └─ Inference
 │    │    │    │    │    │    │    └─ Scheduler
 │    │    │    │    │    │    └─ MongoDB
 │    │    │    │    │    └─ Kafka
 │    │    │    │    └─ Zookeeper
 │    │    │    └─ 密钥配置
 │    │    └─ 配置文件
 │    └─ 存储配置
 └─ 命名空间
```

### 🎯 部署逻辑

#### 第一阶段：基础设施准备 (01-04)
1. **命名空间**: 创建隔离环境和权限
2. **存储**: 准备持久化存储卷
3. **配置**: 部署应用配置文件
4. **密钥**: 部署敏感信息配置

#### 第二阶段：数据和消息服务 (05-07)
5. **Zookeeper**: Kafka 的协调服务
6. **Kafka**: 消息队列服务
7. **MongoDB**: 数据库服务

#### 第三阶段：应用服务 (08-11)
8. **Scheduler**: 核心调度器服务
9. **Inference**: GPU 推理服务 (可选)
10. **RTSP Server**: 视频流服务
11. **Inference Mock**: 模拟推理服务

#### 第四阶段：网络和监控 (12-13)
12. **Ingress**: 外部访问配置
13. **Monitoring**: 监控和告警配置

### 🔧 更新的脚本

以下脚本已更新以使用新的文件序号：

- ✅ `scripts/deploy.sh` - 部署脚本
- ✅ `scripts/cleanup.sh` - 清理脚本
- ✅ `README.md` - 文档中的手动部署步骤

### 📝 使用说明

#### 一键部署 (推荐)
```bash
./scripts/deploy.sh
```

#### 手动部署
```bash
# 按顺序部署
for i in {01..13}; do
    kubectl apply -f ${i}-*.yaml
    echo "已部署: ${i}-*.yaml"
done
```

#### 分阶段部署
```bash
# 第一阶段：基础设施
kubectl apply -f 01-namespace.yaml
kubectl apply -f 02-storage.yaml
kubectl apply -f 03-configmaps.yaml
kubectl apply -f 04-secrets.yaml

# 第二阶段：数据服务
kubectl apply -f 05-zookeeper.yaml
kubectl apply -f 06-kafka.yaml
kubectl apply -f 07-mongodb.yaml

# 第三阶段：应用服务
kubectl apply -f 08-scheduler.yaml
kubectl apply -f 09-inference.yaml      # 可选
kubectl apply -f 10-rtsp-server.yaml
kubectl apply -f 11-inference-mock.yaml

# 第四阶段：网络监控
kubectl apply -f 12-ingress.yaml        # 可选
kubectl apply -f 13-monitoring.yaml     # 可选
```

### ⚠️ 注意事项

1. **依赖关系**: 严格按照序号顺序部署，确保依赖服务先启动
2. **等待就绪**: 在部署应用服务前，确保基础设施服务已就绪
3. **可选组件**: Inference (09) 需要 GPU 节点，Ingress (12) 和 Monitoring (13) 为可选组件
4. **脚本兼容**: 所有自动化脚本已更新，可直接使用

### 🔍 验证部署

```bash
# 检查部署顺序
ls -1 [0-9][0-9]-*.yaml

# 验证所有服务
kubectl get all -n cv-system

# 健康检查
./scripts/health-check.sh
```

---

这个重新整理确保了部署的逻辑性和可维护性，使得文件序号与实际部署顺序完全一致。
