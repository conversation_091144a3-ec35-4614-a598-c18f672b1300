# Inference Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference
  namespace: cv-system
  labels:
    app: inference
    tier: application
spec:
  replicas: 1
  selector:
    matchLabels:
      app: inference
  template:
    metadata:
      labels:
        app: inference
        tier: application
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9001"
        prometheus.io/path: "/metrics"
    spec:
      serviceAccountName: cv-system-sa
      nodeSelector:
        accelerator: nvidia-tesla-a10  # 选择有GPU的节点
      containers:
      - name: inference
        image: cv/inference:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9001
          name: http
          protocol: TCP
        env:
        - name: SERVICE_NAME
          value: "inference-service"
        - name: SERVICE_PORT
          value: "9001"
        - name: SCHEDULER_URL
          value: "http://scheduler:8080"
        - name: KAFKA_BOOTSTRAP_SERVERS
          value: "kafka:9092"
        - name: MAX_QUOTA
          value: "10"
        - name: REGION
          value: "default"
        - name: GPU_TYPE
          value: "A10"
        - name: TRITON_SERVER_IP
          valueFrom:
            secretKeyRef:
              name: triton-secret
              key: server-ip
        - name: TRITON_SERVER_PORT
          valueFrom:
            secretKeyRef:
              name: triton-secret
              key: server-port
        - name: S3_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: s3-secret
              key: access-key
        - name: S3_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: s3-secret
              key: secret-key
        - name: S3_ENDPOINT
          valueFrom:
            secretKeyRef:
              name: s3-secret
              key: endpoint
        - name: S3_BUCKET_NAME
          valueFrom:
            secretKeyRef:
              name: s3-secret
              key: bucket-name
        - name: PYTHONPATH
          value: "/app"
        - name: PYTHONUNBUFFERED
          value: "1"
        - name: TZ
          value: "Asia/Shanghai"
        - name: CUDA_VISIBLE_DEVICES
          value: "0"
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
            nvidia.com/gpu: 1
          limits:
            memory: "8Gi"
            cpu: "4000m"
            nvidia.com/gpu: 1
        volumeMounts:
        - name: inference-config
          mountPath: /app/config
          readOnly: true
        - name: inference-models
          mountPath: /app/models
        - name: inference-logs
          mountPath: /app/logs
        - name: inference-data
          mountPath: /app/data
        - name: shm
          mountPath: /dev/shm
        livenessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 180
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 120
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 10
          failureThreshold: 20
      volumes:
      - name: inference-config
        configMap:
          name: inference-config
      - name: inference-models
        persistentVolumeClaim:
          claimName: inference-models-pvc
      - name: inference-logs
        emptyDir: {}
      - name: inference-data
        emptyDir: {}
      - name: shm
        emptyDir:
          medium: Memory
          sizeLimit: 2Gi
      restartPolicy: Always
      terminationGracePeriodSeconds: 60
---
# Inference Service
apiVersion: v1
kind: Service
metadata:
  name: inference
  namespace: cv-system
  labels:
    app: inference
    tier: application
    monitoring: "true"
spec:
  type: ClusterIP
  ports:
  - port: 9001
    targetPort: 9001
    protocol: TCP
    name: http
  selector:
    app: inference
---
# Inference Headless Service
apiVersion: v1
kind: Service
metadata:
  name: inference-headless
  namespace: cv-system
  labels:
    app: inference
    tier: application
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 9001
    targetPort: 9001
    protocol: TCP
    name: http
  selector:
    app: inference
---
# Inference HorizontalPodAutoscaler
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: inference-hpa
  namespace: cv-system
  labels:
    app: inference
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: inference
  minReplicas: 1
  maxReplicas: 3
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleDown:
      stabilizationWindowSeconds: 600  # GPU服务扩缩容要更谨慎
      policies:
      - type: Percent
        value: 25
        periodSeconds: 120
    scaleUp:
      stabilizationWindowSeconds: 120
      policies:
      - type: Percent
        value: 50
        periodSeconds: 120
---
# Inference PodDisruptionBudget
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: inference-pdb
  namespace: cv-system
  labels:
    app: inference
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: inference
