# ServiceMonitor 配置 (Prometheus Operator)
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: cv-system-monitor
  namespace: cv-system
  labels:
    app: cv-system
    monitoring: prometheus
spec:
  selector:
    matchLabels:
      monitoring: "true"
  endpoints:
  - port: http
    path: /actuator/prometheus
    interval: 30s
    scrapeTimeout: 10s
    targetPort: 8080
    scheme: http
    honorLabels: true
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    targetPort: 9001
    scheme: http
    honorLabels: true
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
    targetPort: 8081
    scheme: http
    honorLabels: true
  namespaceSelector:
    matchNames:
    - cv-system
---
# PrometheusRule 告警规则
apiVersion: monitoring.coreos.com/v1
kind: PrometheusRule
metadata:
  name: cv-system-alerts
  namespace: cv-system
  labels:
    app: cv-system
    monitoring: prometheus
spec:
  groups:
  - name: cv-system.rules
    interval: 30s
    rules:
    # 服务可用性告警
    - alert: CVSystemServiceDown
      expr: up{job=~"scheduler|inference|inference-mock"} == 0
      for: 1m
      labels:
        severity: critical
        service: "{{ $labels.job }}"
      annotations:
        summary: "CV系统服务不可用"
        description: "服务 {{ $labels.job }} 在 {{ $labels.instance }} 上已经停止响应超过1分钟"
    
    # 高内存使用率告警
    - alert: CVSystemHighMemoryUsage
      expr: (container_memory_working_set_bytes{pod=~"scheduler-.*|inference-.*|inference-mock-.*"} / container_spec_memory_limit_bytes) > 0.85
      for: 5m
      labels:
        severity: warning
        service: "{{ $labels.pod }}"
      annotations:
        summary: "CV系统内存使用率过高"
        description: "Pod {{ $labels.pod }} 内存使用率为 {{ $value | humanizePercentage }}"
    
    # 高CPU使用率告警
    - alert: CVSystemHighCPUUsage
      expr: rate(container_cpu_usage_seconds_total{pod=~"scheduler-.*|inference-.*|inference-mock-.*"}[5m]) > 0.8
      for: 5m
      labels:
        severity: warning
        service: "{{ $labels.pod }}"
      annotations:
        summary: "CV系统CPU使用率过高"
        description: "Pod {{ $labels.pod }} CPU使用率为 {{ $value | humanizePercentage }}"
    
    # 任务失败率告警
    - alert: CVSystemHighTaskFailureRate
      expr: (rate(scheduler_tasks_failed_total[5m]) / rate(scheduler_tasks_total[5m])) > 0.1
      for: 2m
      labels:
        severity: warning
        service: scheduler
      annotations:
        summary: "任务失败率过高"
        description: "过去5分钟任务失败率为 {{ $value | humanizePercentage }}"
    
    # 配额使用率告警
    - alert: CVSystemHighQuotaUsage
      expr: (scheduler_quota_used / scheduler_quota_total) > 0.8
      for: 5m
      labels:
        severity: warning
        service: scheduler
      annotations:
        summary: "配额使用率过高"
        description: "当前配额使用率为 {{ $value | humanizePercentage }}"
    
    # Pod 重启频繁告警
    - alert: CVSystemPodRestartingTooOften
      expr: rate(kube_pod_container_status_restarts_total{pod=~"scheduler-.*|inference-.*|inference-mock-.*"}[15m]) > 0
      for: 5m
      labels:
        severity: warning
        service: "{{ $labels.pod }}"
      annotations:
        summary: "Pod重启过于频繁"
        description: "Pod {{ $labels.pod }} 在过去15分钟内重启了 {{ $value }} 次"
    
    # 磁盘空间不足告警
    - alert: CVSystemDiskSpaceLow
      expr: (1 - (node_filesystem_avail_bytes{mountpoint="/"} / node_filesystem_size_bytes{mountpoint="/"})) > 0.85
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "磁盘空间不足"
        description: "节点 {{ $labels.instance }} 磁盘使用率为 {{ $value | humanizePercentage }}"
    
    # GPU 使用率告警 (如果有GPU监控)
    - alert: CVSystemGPUHighUsage
      expr: nvidia_gpu_utilization > 90
      for: 5m
      labels:
        severity: warning
      annotations:
        summary: "GPU使用率过高"
        description: "GPU {{ $labels.gpu }} 使用率为 {{ $value }}%"
---
# Grafana Dashboard ConfigMap
apiVersion: v1
kind: ConfigMap
metadata:
  name: cv-system-dashboard
  namespace: cv-system
  labels:
    app: cv-system
    grafana_dashboard: "1"
data:
  cv-system-overview.json: |
    {
      "dashboard": {
        "id": null,
        "title": "CV System Overview",
        "tags": ["cv-system"],
        "timezone": "browser",
        "panels": [
          {
            "id": 1,
            "title": "Service Status",
            "type": "stat",
            "targets": [
              {
                "expr": "up{job=~\"scheduler|inference|inference-mock\"}",
                "legendFormat": "{{ job }}"
              }
            ],
            "fieldConfig": {
              "defaults": {
                "mappings": [
                  {
                    "options": {
                      "0": {
                        "text": "DOWN",
                        "color": "red"
                      },
                      "1": {
                        "text": "UP",
                        "color": "green"
                      }
                    },
                    "type": "value"
                  }
                ]
              }
            }
          },
          {
            "id": 2,
            "title": "Memory Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "container_memory_working_set_bytes{pod=~\"scheduler-.*|inference-.*|inference-mock-.*\"} / 1024 / 1024",
                "legendFormat": "{{ pod }}"
              }
            ],
            "yAxes": [
              {
                "label": "Memory (MB)"
              }
            ]
          },
          {
            "id": 3,
            "title": "CPU Usage",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(container_cpu_usage_seconds_total{pod=~\"scheduler-.*|inference-.*|inference-mock-.*\"}[5m]) * 100",
                "legendFormat": "{{ pod }}"
              }
            ],
            "yAxes": [
              {
                "label": "CPU (%)",
                "max": 100
              }
            ]
          },
          {
            "id": 4,
            "title": "Task Statistics",
            "type": "graph",
            "targets": [
              {
                "expr": "rate(scheduler_tasks_total[5m])",
                "legendFormat": "Total Tasks"
              },
              {
                "expr": "rate(scheduler_tasks_success_total[5m])",
                "legendFormat": "Success Tasks"
              },
              {
                "expr": "rate(scheduler_tasks_failed_total[5m])",
                "legendFormat": "Failed Tasks"
              }
            ]
          }
        ],
        "time": {
          "from": "now-1h",
          "to": "now"
        },
        "refresh": "30s"
      }
    }
---
# 日志收集 - Fluent Bit DaemonSet
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: fluent-bit
  namespace: cv-system
  labels:
    app: fluent-bit
    tier: logging
spec:
  selector:
    matchLabels:
      app: fluent-bit
  template:
    metadata:
      labels:
        app: fluent-bit
        tier: logging
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: fluent-bit
        image: fluent/fluent-bit:2.1.10
        ports:
        - containerPort: 2020
          name: http
        env:
        - name: FLUENT_CONF
          value: fluent-bit.conf
        - name: FLUENT_OPT
          value: ""
        volumeMounts:
        - name: fluent-bit-config
          mountPath: /fluent-bit/etc
        - name: varlog
          mountPath: /var/log
          readOnly: true
        - name: varlibdockercontainers
          mountPath: /var/lib/docker/containers
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "500m"
      volumes:
      - name: fluent-bit-config
        configMap:
          name: fluent-bit-config
      - name: varlog
        hostPath:
          path: /var/log
      - name: varlibdockercontainers
        hostPath:
          path: /var/lib/docker/containers
      tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
        effect: NoSchedule
---
# Fluent Bit 配置
apiVersion: v1
kind: ConfigMap
metadata:
  name: fluent-bit-config
  namespace: cv-system
  labels:
    app: fluent-bit
data:
  fluent-bit.conf: |
    [SERVICE]
        Flush         1
        Log_Level     info
        Daemon        off
        Parsers_File  parsers.conf
        HTTP_Server   On
        HTTP_Listen   0.0.0.0
        HTTP_Port     2020
    
    [INPUT]
        Name              tail
        Path              /var/log/containers/*cv-system*.log
        Parser            docker
        Tag               kube.*
        Refresh_Interval  5
        Mem_Buf_Limit     50MB
        Skip_Long_Lines   On
    
    [FILTER]
        Name                kubernetes
        Match               kube.*
        Kube_URL            https://kubernetes.default.svc:443
        Kube_CA_File        /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        Kube_Token_File     /var/run/secrets/kubernetes.io/serviceaccount/token
        Kube_Tag_Prefix     kube.var.log.containers.
        Merge_Log           On
        Keep_Log            Off
        K8S-Logging.Parser  On
        K8S-Logging.Exclude Off
    
    [OUTPUT]
        Name  stdout
        Match *
  
  parsers.conf: |
    [PARSER]
        Name   docker
        Format json
        Time_Key time
        Time_Format %Y-%m-%dT%H:%M:%S.%L
        Time_Keep   On
