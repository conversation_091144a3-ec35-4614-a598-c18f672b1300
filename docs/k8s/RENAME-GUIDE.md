# K8s YAML 文件重命名指南

## 📋 概述

本指南说明如何使用重命名脚本将 K8s YAML 文件从旧的序号重命名为新的正确序号。

## 🎯 重命名映射

| 旧文件名 | 新文件名 | 说明 |
|----------|----------|------|
| `00-namespace.yaml` | `01-namespace.yaml` | 命名空间和权限 |
| `01-storage.yaml` | `02-storage.yaml` | 持久化存储 |
| `02-configmaps.yaml` | `03-configmaps.yaml` | 配置映射 |
| `03-secrets.yaml` | `04-secrets.yaml` | 密钥配置 |
| `10-zookeeper.yaml` | `05-zookeeper.yaml` | Zookeeper 服务 |
| `11-kafka.yaml` | `06-kafka.yaml` | Kafka 消息队列 |
| `12-mongodb.yaml` | `07-mongodb.yaml` | MongoDB 数据库 |
| `20-scheduler.yaml` | `08-scheduler.yaml` | Scheduler 调度器 |
| `21-inference.yaml` | `09-inference.yaml` | Inference 推理服务 |
| `22-rtsp-server.yaml` | `10-rtsp-server.yaml` | RTSP 视频服务器 |
| `23-inference-mock.yaml` | `11-inference-mock.yaml` | Inference Mock 模拟服务 |
| `30-ingress.yaml` | `12-ingress.yaml` | Ingress 网络配置 |
| `40-monitoring.yaml` | `13-monitoring.yaml` | 监控和告警配置 |

## 🛠️ 重命名脚本

### 1. 一键修复脚本 (推荐)

**文件**: `scripts/fix-and-rename.sh`

**特点**:
- 🚀 自动检测和修复
- 🔐 自动设置脚本权限
- 🛡️ 安全的重命名过程
- 📋 完整的状态检查和验证

**使用方法**:
```bash
# 进入 k8s 目录
cd docs/k8s

# 一键修复和重命名
./scripts/fix-and-rename.sh
```

### 2. 简单重命名脚本

**文件**: `scripts/simple-rename.sh`

**特点**:
- 🔧 最大兼容性 (适用于所有 shell)
- 🔄 逐个文件处理
- 💾 自动备份冲突文件
- 📝 清晰的执行日志

**使用方法**:
```bash
# 进入 k8s 目录
cd docs/k8s

# 给脚本添加执行权限
chmod +x scripts/simple-rename.sh

# 执行简单重命名
./scripts/simple-rename.sh
```

### 3. 完整版重命名脚本

**文件**: `scripts/rename-files.sh`

**特点**:
- 📊 详细的日志输出
- 🔒 交互式确认
- 🗂️ 使用临时目录避免冲突
- ✅ 完整的验证和提示

**使用方法**:
```bash
# 进入 k8s 目录
cd docs/k8s

# 给脚本添加执行权限
chmod +x scripts/rename-files.sh

# 执行重命名
./scripts/rename-files.sh
```

### 4. 快速重命名脚本

**文件**: `scripts/quick-rename.sh`

**特点**:
- ⚡ 无需确认，直接执行
- 📄 简洁的输出
- 🚀 快速重命名过程
- 🤖 适合自动化脚本

**使用方法**:
```bash
# 进入 k8s 目录
cd docs/k8s

# 给脚本添加执行权限
chmod +x scripts/quick-rename.sh

# 执行快速重命名
./scripts/quick-rename.sh
```

## 📝 使用步骤

### 步骤 1: 检查当前文件

```bash
# 查看当前文件列表
ls -1 [0-9][0-9]-*.yaml

# 检查是否有旧序号的文件
ls -1 {00,20,21,22,23,30,40}-*.yaml 2>/dev/null || echo "无旧文件"
```

### 步骤 2: 选择重命名方式

#### 方式 A: 一键修复 (强烈推荐)
```bash
./scripts/fix-and-rename.sh
```

#### 方式 B: 简单重命名 (兼容性最好)
```bash
chmod +x scripts/simple-rename.sh
./scripts/simple-rename.sh
```

#### 方式 C: 完整版重命名
```bash
chmod +x scripts/rename-files.sh
./scripts/rename-files.sh
```

#### 方式 D: 快速重命名
```bash
chmod +x scripts/quick-rename.sh
./scripts/quick-rename.sh
```

#### 方式 E: 手动重命名
```bash
# 如果只有少量文件需要重命名
mv 00-namespace.yaml 01-namespace.yaml
mv 20-scheduler.yaml 08-scheduler.yaml
mv 21-inference.yaml 09-inference.yaml
# ... 其他文件
```

### 步骤 3: 验证重命名结果

```bash
# 验证文件序号和内容
./scripts/verify-files.sh

# 查看新的文件列表
ls -1 [0-9][0-9]-*.yaml | sort
```

### 步骤 4: 更新脚本引用 (如果需要)

重命名脚本会自动检查并提示是否需要更新脚本中的文件引用。

## 🔍 故障排查

### 问题 1: 权限不足

**错误**: `Permission denied`

**解决**:
```bash
# 添加执行权限
chmod +x scripts/rename-files.sh
chmod +x scripts/quick-rename.sh
```

### 问题 2: 文件冲突

**错误**: `File exists`

**原因**: 目标文件已存在

**解决**:
```bash
# 检查冲突的文件
ls -la [0-9][0-9]-*.yaml

# 手动处理冲突文件
# 选项 1: 备份现有文件
mv 01-namespace.yaml 01-namespace.yaml.backup

# 选项 2: 删除现有文件 (谨慎操作)
rm 01-namespace.yaml

# 然后重新运行重命名脚本
```

### 问题 3: 部分文件缺失

**现象**: 某些文件不存在，无法重命名

**解决**:
```bash
# 检查哪些文件缺失
./scripts/verify-files.sh

# 从其他环境复制缺失的文件，或重新创建
```

## ✅ 验证清单

重命名完成后，请检查以下项目：

- [ ] 所有文件都按 01-13 的序号排列
- [ ] 没有旧序号的文件残留
- [ ] 文件内容没有损坏
- [ ] 脚本中的文件引用已更新
- [ ] 可以正常执行部署脚本

## 🚀 重命名后的操作

### 1. 验证文件
```bash
./scripts/verify-files.sh
```

### 2. 构建镜像
```bash
./scripts/build-images.sh
```

### 3. 部署系统
```bash
./scripts/deploy.sh
```

### 4. 健康检查
```bash
./scripts/health-check.sh
```

## 📞 获取帮助

如果在重命名过程中遇到问题：

1. **查看脚本帮助**:
   ```bash
   ./scripts/rename-files.sh --help
   ```

2. **检查文件状态**:
   ```bash
   ./scripts/verify-files.sh
   ```

3. **查看详细文档**:
   - [FILE-MAPPING.md](./FILE-MAPPING.md) - 文件映射详情
   - [README.md](./README.md) - 完整部署指南
   - [QUICKSTART.md](./QUICKSTART.md) - 快速开始指南

4. **联系技术支持**:
   - GitHub Issues
   - 技术支持邮箱

---

**注意**: 重命名操作会修改文件名，建议在执行前备份重要文件。
