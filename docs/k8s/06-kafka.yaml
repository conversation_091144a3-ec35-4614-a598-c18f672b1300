# Kafka Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kafka
  namespace: cv-system
  labels:
    app: kafka
    tier: infrastructure
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kafka
  template:
    metadata:
      labels:
        app: kafka
        tier: infrastructure
    spec:
      serviceAccountName: cv-system-sa
      containers:
      - name: kafka
        image: confluentinc/cp-kafka:7.4.0
        ports:
        - containerPort: 9092
          name: kafka
        - containerPort: 9093
          name: kafka-internal
        env:
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: <PERSON><PERSON><PERSON>_ZOOKEEPER_CONNECT
          value: "zookeeper:2181"
        - name: <PERSON><PERSON><PERSON>_LISTENER_SECURITY_PROTOCOL_MAP
          value: "PLAINTEXT:PLAINTEXT,PLAINTEXT_INTERNAL:PLAINTEXT"
        - name: K<PERSON>KA_ADVERTISED_LISTENERS
          value: "PLAINTEXT://kafka:9092,PLAINTEXT_INTERNAL://kafka:9093"
        - name: <PERSON>AFKA_LISTENERS
          value: "PLAINTEXT://0.0.0.0:9092,PLAINTEXT_INTERNAL://0.0.0.0:9093"
        - name: KAFKA_INTER_BROKER_LISTENER_NAME
          value: "PLAINTEXT_INTERNAL"
        - name: KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_TRANSACTION_STATE_LOG_MIN_ISR
          value: "1"
        - name: KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_GROUP_INITIAL_REBALANCE_DELAY_MS
          value: "0"
        - name: KAFKA_AUTO_CREATE_TOPICS_ENABLE
          value: "true"
        - name: KAFKA_DELETE_TOPIC_ENABLE
          value: "true"
        - name: KAFKA_LOG_RETENTION_HOURS
          value: "168"
        - name: KAFKA_LOG_RETENTION_BYTES
          value: "1073741824"
        - name: KAFKA_LOG_SEGMENT_BYTES
          value: "1073741824"
        - name: KAFKA_NUM_PARTITIONS
          value: "3"
        - name: KAFKA_DEFAULT_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_MIN_INSYNC_REPLICAS
          value: "1"
        - name: KAFKA_HEAP_OPTS
          value: "-Xmx1G -Xms1G"
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
        volumeMounts:
        - name: kafka-data
          mountPath: /var/lib/kafka/data
        - name: kafka-logs
          mountPath: /kafka/logs
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 60
          periodSeconds: 30
          timeoutSeconds: 10
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "kafka-broker-api-versions --bootstrap-server localhost:9092"
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
      volumes:
      - name: kafka-data
        persistentVolumeClaim:
          claimName: kafka-pvc
      - name: kafka-logs
        emptyDir: {}
      restartPolicy: Always
---
# Kafka Service
apiVersion: v1
kind: Service
metadata:
  name: kafka
  namespace: cv-system
  labels:
    app: kafka
    tier: infrastructure
spec:
  type: ClusterIP
  ports:
  - port: 9092
    targetPort: 9092
    protocol: TCP
    name: kafka
  - port: 9093
    targetPort: 9093
    protocol: TCP
    name: kafka-internal
  selector:
    app: kafka
---
# Kafka Headless Service
apiVersion: v1
kind: Service
metadata:
  name: kafka-headless
  namespace: cv-system
  labels:
    app: kafka
    tier: infrastructure
spec:
  type: ClusterIP
  clusterIP: None
  ports:
  - port: 9092
    targetPort: 9092
    protocol: TCP
    name: kafka
  selector:
    app: kafka
---
# Kafka Topic 初始化 Job
apiVersion: batch/v1
kind: Job
metadata:
  name: kafka-topic-init
  namespace: cv-system
  labels:
    app: kafka
    type: init
spec:
  template:
    metadata:
      labels:
        app: kafka
        type: init
    spec:
      serviceAccountName: cv-system-sa
      restartPolicy: OnFailure
      containers:
      - name: kafka-topic-init
        image: confluentinc/cp-kafka:7.4.0
        command:
        - sh
        - -c
        - |
          echo "等待 Kafka 启动..."
          until kafka-broker-api-versions --bootstrap-server kafka:9092; do
            echo "Kafka 未就绪，等待 5 秒..."
            sleep 5
          done
          
          echo "创建主题..."
          
          # 创建视频分析事件主题
          kafka-topics --bootstrap-server kafka:9092 --create --if-not-exists \
            --topic video_analysis_events --partitions 3 --replication-factor 1
          
          # 创建视觉事件主题
          kafka-topics --bootstrap-server kafka:9092 --create --if-not-exists \
            --topic vision-events --partitions 3 --replication-factor 1
          
          # 创建系统监控主题
          kafka-topics --bootstrap-server kafka:9092 --create --if-not-exists \
            --topic system-monitoring --partitions 1 --replication-factor 1
          
          echo "主题创建完成，列出所有主题:"
          kafka-topics --bootstrap-server kafka:9092 --list
      backoffLimit: 3
