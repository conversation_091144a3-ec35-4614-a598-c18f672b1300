#!/bin/bash

# CV 推理系统 Docker 镜像构建脚本
# 作者: CV System Team
# 版本: v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 配置变量
REGISTRY=""
TAG="latest"
PUSH=false
SERVICES=()
PROJECT_ROOT=""

# 检查 Docker
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker 服务未运行，请启动 Docker"
        exit 1
    fi
    
    log_success "Docker 检查通过"
}

# 查找项目根目录
find_project_root() {
    local current_dir=$(pwd)
    
    # 尝试从当前目录向上查找
    while [ "$current_dir" != "/" ]; do
        if [ -f "$current_dir/services/scheduler/pom.xml" ] && [ -f "$current_dir/services/inference/Dockerfile" ]; then
            PROJECT_ROOT="$current_dir"
            log_success "找到项目根目录: $PROJECT_ROOT"
            return 0
        fi
        current_dir=$(dirname "$current_dir")
    done
    
    log_error "未找到项目根目录，请在项目根目录或子目录中运行此脚本"
    exit 1
}

# 构建 Scheduler 镜像
build_scheduler() {
    log_info "构建 Scheduler 镜像..."
    
    local service_dir="$PROJECT_ROOT/services/scheduler"
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        log_error "Scheduler Dockerfile 不存在: $service_dir/Dockerfile"
        return 1
    fi
    
    # 检查是否需要构建 JAR
    if [ ! -f "$service_dir/target/cv-scheduler-1.0.0.jar" ]; then
        log_info "构建 Scheduler JAR..."
        cd "$service_dir"
        
        if command -v mvn &> /dev/null; then
            mvn clean package -DskipTests
        else
            log_error "Maven 未安装，无法构建 JAR"
            return 1
        fi
        
        cd - > /dev/null
    fi
    
    # 构建 Docker 镜像
    local image_name="${REGISTRY}cv/scheduler:${TAG}"
    docker build -t "$image_name" "$service_dir"
    
    log_success "Scheduler 镜像构建完成: $image_name"
    
    if [ "$PUSH" = true ]; then
        docker push "$image_name"
        log_success "Scheduler 镜像推送完成"
    fi
}

# 构建 Inference 镜像
build_inference() {
    log_info "构建 Inference 镜像..."
    
    local service_dir="$PROJECT_ROOT/services/inference"
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        log_error "Inference Dockerfile 不存在: $service_dir/Dockerfile"
        return 1
    fi
    
    # 检查基础镜像
    if ! docker image inspect deepstream:7.0-triton-multiarch &> /dev/null; then
        log_warning "基础镜像 deepstream:7.0-triton-multiarch 不存在"
        log_info "请先准备 DeepStream 基础镜像或修改 Dockerfile"
        
        # 提供替代方案
        read -p "是否使用 Python 基础镜像替代? (y/n): " -r
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            # 创建临时 Dockerfile
            cat > "$service_dir/Dockerfile.temp" << 'EOF'
FROM python:3.9-slim

ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgtk-3-0 \
    libgl1-mesa-glx \
    libgthread-2.0-0 \
    curl \
    wget \
    && rm -rf /var/lib/apt/lists/*

WORKDIR /app

COPY requirements.txt .
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

COPY . .

RUN mkdir -p logs data models

EXPOSE 9001

ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

CMD ["python", "launch_server.py", "--config-path", "config/config.yaml", "--port", "9001"]
EOF
            
            local image_name="${REGISTRY}cv/inference:${TAG}"
            docker build -f "$service_dir/Dockerfile.temp" -t "$image_name" "$service_dir"
            rm "$service_dir/Dockerfile.temp"
        else
            return 1
        fi
    else
        local image_name="${REGISTRY}cv/inference:${TAG}"
        docker build -t "$image_name" "$service_dir"
    fi
    
    log_success "Inference 镜像构建完成: $image_name"
    
    if [ "$PUSH" = true ]; then
        docker push "$image_name"
        log_success "Inference 镜像推送完成"
    fi
}

# 构建 Inference Mock 镜像
build_inference_mock() {
    log_info "构建 Inference Mock 镜像..."
    
    local service_dir="$PROJECT_ROOT/services/inference-mock"
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        log_error "Inference Mock Dockerfile 不存在: $service_dir/Dockerfile"
        return 1
    fi
    
    local image_name="${REGISTRY}cv/inference-mock:${TAG}"
    docker build -t "$image_name" "$service_dir"
    
    log_success "Inference Mock 镜像构建完成: $image_name"
    
    if [ "$PUSH" = true ]; then
        docker push "$image_name"
        log_success "Inference Mock 镜像推送完成"
    fi
}

# 构建 RTSP Server 镜像
build_rtsp_server() {
    log_info "构建 RTSP Server 镜像..."
    
    local service_dir="$PROJECT_ROOT/services/rtsp-server"
    
    if [ ! -f "$service_dir/Dockerfile" ]; then
        log_error "RTSP Server Dockerfile 不存在: $service_dir/Dockerfile"
        return 1
    fi
    
    local image_name="${REGISTRY}cv/rtsp-server:${TAG}"
    docker build -t "$image_name" "$service_dir"
    
    log_success "RTSP Server 镜像构建完成: $image_name"
    
    if [ "$PUSH" = true ]; then
        docker push "$image_name"
        log_success "RTSP Server 镜像推送完成"
    fi
}

# 显示镜像信息
show_images() {
    log_info "构建的镜像列表:"
    echo
    
    local images=("cv/scheduler" "cv/inference" "cv/inference-mock" "cv/rtsp-server")
    
    for image in "${images[@]}"; do
        local full_name="${REGISTRY}${image}:${TAG}"
        if docker image inspect "$full_name" &> /dev/null; then
            local size=$(docker image inspect "$full_name" --format='{{.Size}}' | numfmt --to=iec)
            local created=$(docker image inspect "$full_name" --format='{{.Created}}' | cut -d'T' -f1)
            echo "  ✅ $full_name ($size, $created)"
        else
            echo "  ❌ $full_name (未构建)"
        fi
    done
    
    echo
    log_info "Docker 镜像总大小:"
    docker images --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}" | grep "cv/"
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理旧镜像..."
    
    # 清理 dangling 镜像
    local dangling=$(docker images -f "dangling=true" -q)
    if [ -n "$dangling" ]; then
        docker rmi $dangling
        log_success "清理了 $(echo $dangling | wc -w) 个悬空镜像"
    fi
    
    # 清理旧版本镜像 (保留最新的3个版本)
    for image in "cv/scheduler" "cv/inference" "cv/inference-mock" "cv/rtsp-server"; do
        local old_images=$(docker images "$image" --format "{{.ID}}" | tail -n +4)
        if [ -n "$old_images" ]; then
            docker rmi $old_images 2>/dev/null || true
        fi
    done
    
    log_success "旧镜像清理完成"
}

# 主函数
main() {
    echo "🐳 CV 推理系统 Docker 镜像构建"
    echo "================================"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            --registry)
                REGISTRY="$2/"
                shift 2
                ;;
            --tag)
                TAG="$2"
                shift 2
                ;;
            --push)
                PUSH=true
                shift
                ;;
            --service)
                SERVICES+=("$2")
                shift 2
                ;;
            --cleanup)
                CLEANUP=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --registry REGISTRY  镜像仓库地址 (如: registry.example.com)"
                echo "  --tag TAG           镜像标签 (默认: latest)"
                echo "  --push              构建后推送到仓库"
                echo "  --service SERVICE   只构建指定服务 (scheduler|inference|inference-mock|rtsp-server)"
                echo "  --cleanup           构建后清理旧镜像"
                echo "  -h, --help          显示帮助信息"
                echo
                echo "示例:"
                echo "  $0                                    # 构建所有镜像"
                echo "  $0 --service scheduler                # 只构建 scheduler"
                echo "  $0 --registry registry.example.com --push  # 构建并推送到仓库"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查环境
    check_docker
    find_project_root
    
    # 如果指定了仓库，检查登录状态
    if [ -n "$REGISTRY" ] && [ "$PUSH" = true ]; then
        log_info "检查镜像仓库登录状态..."
        if ! docker info | grep -q "Registry:"; then
            log_warning "请先登录到镜像仓库: docker login ${REGISTRY%/}"
        fi
    fi
    
    # 确定要构建的服务
    if [ ${#SERVICES[@]} -eq 0 ]; then
        SERVICES=("scheduler" "inference" "inference-mock" "rtsp-server")
    fi
    
    # 构建镜像
    for service in "${SERVICES[@]}"; do
        case $service in
            scheduler)
                build_scheduler
                ;;
            inference)
                build_inference
                ;;
            inference-mock)
                build_inference_mock
                ;;
            rtsp-server)
                build_rtsp_server
                ;;
            *)
                log_error "未知服务: $service"
                ;;
        esac
    done
    
    # 清理旧镜像
    if [ "$CLEANUP" = true ]; then
        cleanup_old_images
    fi
    
    # 显示结果
    show_images
    
    echo
    log_success "🎉 镜像构建完成！"
    
    if [ "$PUSH" = true ]; then
        echo
        log_info "📤 镜像已推送到仓库，可以在 Kubernetes 中使用"
    else
        echo
        log_info "💡 提示:"
        echo "  - 推送镜像: $0 --push"
        echo "  - 部署到 K8s: cd ../.. && ./scripts/deploy.sh"
    fi
}

# 错误处理
trap 'log_error "构建过程中发生错误"; exit 1' ERR

# 执行主函数
main "$@"
