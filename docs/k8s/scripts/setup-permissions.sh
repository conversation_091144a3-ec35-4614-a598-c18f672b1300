#!/bin/bash

# 设置所有脚本的执行权限
# 作者: CV System Team

echo "🔧 设置脚本执行权限"
echo "=================="

# 进入脚本目录
cd "$(dirname "$0")"

echo "当前目录: $(pwd)"

# 获取所有 .sh 文件
scripts=(*.sh)

if [ ${#scripts[@]} -eq 0 ]; then
    echo "❌ 未找到任何 .sh 文件"
    exit 1
fi

echo "发现 ${#scripts[@]} 个脚本文件:"

# 为所有脚本添加执行权限
for script in "${scripts[@]}"; do
    if [ -f "$script" ]; then
        chmod +x "$script"
        echo "  ✅ $script"
    else
        echo "  ❌ $script (不存在)"
    fi
done

echo
echo "🎉 权限设置完成！"

echo
echo "可用的脚本:"
echo "  ./rename-files.sh      # 完整版重命名脚本"
echo "  ./quick-rename.sh      # 快速重命名脚本"
echo "  ./verify-files.sh      # 验证文件脚本"
echo "  ./build-images.sh      # 构建镜像脚本"
echo "  ./deploy.sh            # 部署脚本"
echo "  ./cleanup.sh           # 清理脚本"
echo "  ./health-check.sh      # 健康检查脚本"

echo
echo "下一步:"
echo "  1. 重命名文件: ./rename-files.sh"
echo "  2. 验证文件:   ./verify-files.sh"
echo "  3. 部署系统:   ./deploy.sh"
