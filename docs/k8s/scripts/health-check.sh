#!/bin/bash

# CV 推理系统 Kubernetes 健康检查脚本
# 作者: CV System Team
# 版本: v1.0.0

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 全局变量
NAMESPACE="cv-system"
FAILED_CHECKS=0
TOTAL_CHECKS=0

# 增加检查计数
increment_check() {
    TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
    if [ "$1" != "success" ]; then
        FAILED_CHECKS=$((FAILED_CHECKS + 1))
    fi
}

# 检查命名空间
check_namespace() {
    log_info "检查命名空间..."
    
    if kubectl get namespace $NAMESPACE &> /dev/null; then
        log_success "命名空间 $NAMESPACE 存在"
        increment_check "success"
    else
        log_error "命名空间 $NAMESPACE 不存在"
        increment_check "failed"
        return 1
    fi
}

# 检查 Pod 状态
check_pods() {
    log_info "检查 Pod 状态..."
    
    local pods=$(kubectl get pods -n $NAMESPACE -o json)
    local pod_count=$(echo "$pods" | jq -r '.items | length')
    
    if [ "$pod_count" -eq 0 ]; then
        log_warning "没有找到 Pod"
        increment_check "warning"
        return 0
    fi
    
    echo "$pods" | jq -r '.items[] | "\(.metadata.name) \(.status.phase) \(.status.containerStatuses[0].ready // false)"' | while read -r name phase ready; do
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        if [ "$phase" = "Running" ] && [ "$ready" = "true" ]; then
            log_success "Pod $name: $phase (Ready)"
        elif [ "$phase" = "Running" ] && [ "$ready" = "false" ]; then
            log_warning "Pod $name: $phase (Not Ready)"
            increment_check "warning"
        else
            log_error "Pod $name: $phase"
            increment_check "failed"
        fi
    done
}

# 检查服务状态
check_services() {
    log_info "检查 Service 状态..."
    
    local services=("scheduler" "inference" "inference-mock" "rtsp-server" "mongodb" "kafka" "zookeeper")
    
    for service in "${services[@]}"; do
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        if kubectl get service "$service" -n $NAMESPACE &> /dev/null; then
            local endpoints=$(kubectl get endpoints "$service" -n $NAMESPACE -o jsonpath='{.subsets[*].addresses[*].ip}' 2>/dev/null)
            if [ -n "$endpoints" ]; then
                log_success "Service $service: 可用 (端点: $(echo $endpoints | wc -w))"
            else
                log_warning "Service $service: 无端点"
                increment_check "warning"
            fi
        else
            log_warning "Service $service: 不存在"
            increment_check "warning"
        fi
    done
}

# 检查 PVC 状态
check_pvcs() {
    log_info "检查 PVC 状态..."
    
    local pvcs=$(kubectl get pvc -n $NAMESPACE -o json)
    local pvc_count=$(echo "$pvcs" | jq -r '.items | length')
    
    if [ "$pvc_count" -eq 0 ]; then
        log_warning "没有找到 PVC"
        increment_check "warning"
        return 0
    fi
    
    echo "$pvcs" | jq -r '.items[] | "\(.metadata.name) \(.status.phase)"' | while read -r name phase; do
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        if [ "$phase" = "Bound" ]; then
            log_success "PVC $name: $phase"
        else
            log_error "PVC $name: $phase"
            increment_check "failed"
        fi
    done
}

# 检查应用健康状态
check_application_health() {
    log_info "检查应用健康状态..."
    
    # 检查 Scheduler
    if kubectl get pod -l app=scheduler -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if kubectl exec -n $NAMESPACE deployment/scheduler -- curl -f http://localhost:8080/actuator/health &> /dev/null; then
            log_success "Scheduler 健康检查通过"
        else
            log_error "Scheduler 健康检查失败"
            increment_check "failed"
        fi
    else
        log_warning "Scheduler Pod 未运行"
        increment_check "warning"
    fi
    
    # 检查 Inference
    if kubectl get pod -l app=inference -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if kubectl exec -n $NAMESPACE deployment/inference -- curl -f http://localhost:9001/health &> /dev/null; then
            log_success "Inference 健康检查通过"
        else
            log_error "Inference 健康检查失败"
            increment_check "failed"
        fi
    else
        log_warning "Inference Pod 未运行"
        increment_check "warning"
    fi
    
    # 检查 Inference Mock
    if kubectl get pod -l app=inference-mock -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if kubectl exec -n $NAMESPACE deployment/inference-mock -- curl -f http://localhost:8081/health &> /dev/null; then
            log_success "Inference Mock 健康检查通过"
        else
            log_error "Inference Mock 健康检查失败"
            increment_check "failed"
        fi
    else
        log_warning "Inference Mock Pod 未运行"
        increment_check "warning"
    fi
    
    # 检查 RTSP Server
    if kubectl get pod -l app=rtsp-server -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        if kubectl exec -n $NAMESPACE deployment/rtsp-server -- curl -f http://localhost:8889/v3/config/global/get &> /dev/null; then
            log_success "RTSP Server 健康检查通过"
        else
            log_error "RTSP Server 健康检查失败"
            increment_check "failed"
        fi
    else
        log_warning "RTSP Server Pod 未运行"
        increment_check "warning"
    fi
}

# 检查资源使用情况
check_resource_usage() {
    log_info "检查资源使用情况..."
    
    # 检查节点资源
    log_info "节点资源使用情况:"
    kubectl top nodes 2>/dev/null || log_warning "无法获取节点资源使用情况 (需要 metrics-server)"
    
    echo
    
    # 检查 Pod 资源
    log_info "Pod 资源使用情况:"
    kubectl top pods -n $NAMESPACE 2>/dev/null || log_warning "无法获取 Pod 资源使用情况 (需要 metrics-server)"
}

# 检查网络连通性
check_network_connectivity() {
    log_info "检查网络连通性..."
    
    # 检查服务间连通性
    if kubectl get pod -l app=scheduler -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        # 检查 Scheduler 到 MongoDB 的连接
        if kubectl exec -n $NAMESPACE deployment/scheduler -- nc -z mongodb 27017 &> /dev/null; then
            log_success "Scheduler -> MongoDB: 连通"
        else
            log_error "Scheduler -> MongoDB: 不通"
            increment_check "failed"
        fi
        
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        # 检查 Scheduler 到 Kafka 的连接
        if kubectl exec -n $NAMESPACE deployment/scheduler -- nc -z kafka 9092 &> /dev/null; then
            log_success "Scheduler -> Kafka: 连通"
        else
            log_error "Scheduler -> Kafka: 不通"
            increment_check "failed"
        fi
    fi
    
    if kubectl get pod -l app=inference-mock -n $NAMESPACE | grep -q Running; then
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        # 检查 Inference Mock 到 Scheduler 的连接
        if kubectl exec -n $NAMESPACE deployment/inference-mock -- nc -z scheduler 8080 &> /dev/null; then
            log_success "Inference Mock -> Scheduler: 连通"
        else
            log_error "Inference Mock -> Scheduler: 不通"
            increment_check "failed"
        fi
        
        TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
        
        # 检查 Inference Mock 到 RTSP Server 的连接
        if kubectl exec -n $NAMESPACE deployment/inference-mock -- nc -z rtsp-server 9554 &> /dev/null; then
            log_success "Inference Mock -> RTSP Server: 连通"
        else
            log_error "Inference Mock -> RTSP Server: 不通"
            increment_check "failed"
        fi
    fi
}

# 检查日志错误
check_logs_for_errors() {
    log_info "检查最近的错误日志..."
    
    local services=("scheduler" "inference" "inference-mock" "rtsp-server")
    
    for service in "${services[@]}"; do
        if kubectl get deployment "$service" -n $NAMESPACE &> /dev/null; then
            local errors=$(kubectl logs deployment/"$service" -n $NAMESPACE --tail=100 2>/dev/null | grep -i -E "error|exception|failed" | wc -l)
            
            TOTAL_CHECKS=$((TOTAL_CHECKS + 1))
            
            if [ "$errors" -eq 0 ]; then
                log_success "$service: 无错误日志"
            elif [ "$errors" -lt 5 ]; then
                log_warning "$service: 发现 $errors 条错误日志"
                increment_check "warning"
            else
                log_error "$service: 发现 $errors 条错误日志"
                increment_check "failed"
            fi
        fi
    done
}

# 检查存储空间
check_storage_space() {
    log_info "检查存储空间..."
    
    # 检查 PV 使用情况
    kubectl get pv -o custom-columns=NAME:.metadata.name,CAPACITY:.spec.capacity.storage,STATUS:.status.phase,CLAIM:.spec.claimRef.name 2>/dev/null | grep cv-system || log_warning "未找到相关 PV"
    
    # 检查节点磁盘空间
    log_info "节点磁盘使用情况:"
    kubectl get nodes -o json | jq -r '.items[] | "\(.metadata.name) \(.status.allocatable.storage // "N/A")"' | while read -r node storage; do
        echo "  $node: $storage"
    done
}

# 生成健康报告
generate_health_report() {
    echo
    log_info "=== 健康检查报告 ==="
    
    local success_rate=0
    if [ "$TOTAL_CHECKS" -gt 0 ]; then
        success_rate=$(( (TOTAL_CHECKS - FAILED_CHECKS) * 100 / TOTAL_CHECKS ))
    fi
    
    echo "📊 检查统计:"
    echo "  总检查项: $TOTAL_CHECKS"
    echo "  成功: $((TOTAL_CHECKS - FAILED_CHECKS))"
    echo "  失败: $FAILED_CHECKS"
    echo "  成功率: ${success_rate}%"
    echo
    
    if [ "$FAILED_CHECKS" -eq 0 ]; then
        log_success "🎉 系统健康状态良好！"
        return 0
    elif [ "$FAILED_CHECKS" -lt 3 ]; then
        log_warning "⚠️  系统存在一些问题，但基本可用"
        return 1
    else
        log_error "❌ 系统存在严重问题，需要立即处理"
        return 2
    fi
}

# 显示修复建议
show_repair_suggestions() {
    if [ "$FAILED_CHECKS" -gt 0 ]; then
        echo
        log_info "🔧 修复建议:"
        echo "  1. 检查失败的 Pod 日志: kubectl logs <pod-name> -n $NAMESPACE"
        echo "  2. 重启有问题的服务: kubectl rollout restart deployment/<service> -n $NAMESPACE"
        echo "  3. 检查资源配额: kubectl describe nodes"
        echo "  4. 检查存储状态: kubectl get pv,pvc -n $NAMESPACE"
        echo "  5. 重新部署: ./deploy.sh"
        echo
    fi
}

# 主函数
main() {
    echo "🏥 CV 推理系统健康检查"
    echo "======================"
    
    # 解析参数
    DETAILED=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --detailed)
                DETAILED=true
                shift
                ;;
            -h|--help)
                echo "用法: $0 [选项]"
                echo "选项:"
                echo "  --detailed    详细检查模式"
                echo "  -h, --help    显示帮助信息"
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                exit 1
                ;;
        esac
    done
    
    # 检查 kubectl
    if ! command -v kubectl &> /dev/null; then
        log_error "kubectl 未安装"
        exit 1
    fi
    
    if ! kubectl cluster-info &> /dev/null; then
        log_error "无法连接到 Kubernetes 集群"
        exit 1
    fi
    
    # 执行检查
    check_namespace || exit 1
    check_pods
    check_services
    check_pvcs
    check_application_health
    check_network_connectivity
    check_logs_for_errors
    
    if [ "$DETAILED" = true ]; then
        check_resource_usage
        check_storage_space
    fi
    
    # 生成报告
    generate_health_report
    local exit_code=$?
    
    show_repair_suggestions
    
    echo
    log_info "健康检查完成"
    
    exit $exit_code
}

# 执行主函数
main "$@"
