# CV 推理系统 Kubernetes 快速开始指南

## 🚀 5分钟快速部署

本指南帮助你在 5 分钟内快速部署 CV 推理系统到 Kubernetes 集群。

### 📋 前置条件检查

```bash
# 1. 检查 Kubernetes 集群
kubectl cluster-info

# 2. 检查节点资源
kubectl get nodes -o wide

# 3. 检查存储类 (可选)
kubectl get storageclass

# 4. 检查 Docker (用于构建镜像)
docker --version
```

### 🛠️ 第一步：准备镜像

```bash
# 克隆项目 (如果还没有)
git clone <repository-url>
cd bxt-analysis

# 进入 K8s 部署目录
cd docs/k8s

# 构建所有镜像
./scripts/build-images.sh

# 查看构建的镜像
docker images | grep cv/
```

### 🚀 第二步：一键部署

```bash
# 执行一键部署脚本
./scripts/deploy.sh

# 等待部署完成 (大约 3-5 分钟)
# 脚本会自动：
# - 创建命名空间
# - 部署存储和配置
# - 部署基础设施 (<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Zookeeper)
# - 部署应用服务 (Scheduler, Inference Mock, RTSP Server)
# - 验证部署状态
```

### ✅ 第三步：验证部署

```bash
# 检查所有 Pod 状态
kubectl get pods -n cv-system

# 应该看到类似输出：
# NAME                              READY   STATUS    RESTARTS   AGE
# scheduler-xxx                     1/1     Running   0          2m
# inference-mock-xxx                1/1     Running   0          2m
# rtsp-server-xxx                   1/1     Running   0          2m
# mongodb-xxx                       1/1     Running   0          3m
# kafka-xxx                         1/1     Running   0          3m
# zookeeper-xxx                     1/1     Running   0          3m

# 运行健康检查
./scripts/health-check.sh
```

### 🌐 第四步：访问服务

```bash
# 获取服务访问地址
kubectl get services -n cv-system

# 端口转发到本地 (在新终端中运行)
kubectl port-forward svc/scheduler 8080:8080 -n cv-system &
kubectl port-forward svc/inference-mock 8081:8081 -n cv-system &
kubectl port-forward svc/rtsp-server 9554:9554 -n cv-system &

# 测试服务
curl http://localhost:8080/actuator/health  # Scheduler 健康检查
curl http://localhost:8081/health           # Inference Mock 健康检查
```

### 🎯 第五步：创建测试任务

```bash
# 创建测试任务配置
cat > test-task.json << 'EOF'
{
  "taskRequest": {
    "taskId": "test-task-001",
    "taskName": "测试任务",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "zone_intrusion"
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch-001",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "yolo-v8",
          "algorithmName": "YOLO目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true
        }
      ]
    },
    "device": {
      "deviceId": "camera-001",
      "streamConfig": {
        "url": "rtsp://rtsp-server:9554/knight",
        "resolution": "1920x1080",
        "frameRate": 25
      }
    }
  },
  "region": "default",
  "priority": 1
}
EOF

# 提交任务到调度器
curl -X POST http://localhost:8080/api/v1/scheduler/schedule \
  -H "Content-Type: application/json" \
  -d @test-task.json

# 查看任务状态
curl http://localhost:8080/api/v1/scheduler/tasks

# 查看 Inference Mock 任务
curl http://localhost:8081/api/v1/tasks
```

## 🔧 常用操作

### 查看日志

```bash
# 查看 Scheduler 日志
kubectl logs -f deployment/scheduler -n cv-system

# 查看 Inference Mock 日志
kubectl logs -f deployment/inference-mock -n cv-system

# 查看所有服务日志
kubectl logs -f -l tier=application -n cv-system
```

### 扩缩容

```bash
# 扩展 Inference Mock 服务
kubectl scale deployment inference-mock --replicas=2 -n cv-system

# 查看扩缩容状态
kubectl get deployment -n cv-system
```

### 重启服务

```bash
# 重启 Scheduler
kubectl rollout restart deployment/scheduler -n cv-system

# 重启 Inference Mock
kubectl rollout restart deployment/inference-mock -n cv-system
```

## 🚨 故障排查

### 常见问题

#### 1. Pod 一直处于 Pending 状态

```bash
# 检查节点资源
kubectl describe nodes

# 检查 Pod 事件
kubectl describe pod <pod-name> -n cv-system

# 可能原因：
# - 资源不足 (CPU/内存)
# - 存储卷无法挂载
# - 节点选择器不匹配
```

#### 2. 镜像拉取失败

```bash
# 检查镜像是否存在
docker images | grep cv/

# 重新构建镜像
./scripts/build-images.sh --service <service-name>

# 检查 Pod 事件
kubectl describe pod <pod-name> -n cv-system
```

#### 3. 服务无法访问

```bash
# 检查服务状态
kubectl get services -n cv-system

# 检查端点
kubectl get endpoints -n cv-system

# 检查网络连通性
kubectl exec -n cv-system deployment/scheduler -- nslookup mongodb
```

#### 4. 数据库连接失败

```bash
# 检查 MongoDB 状态
kubectl logs deployment/mongodb -n cv-system

# 检查密钥配置
kubectl get secret mongodb-secret -n cv-system -o yaml

# 测试数据库连接
kubectl exec -n cv-system deployment/mongodb -- mongosh --eval "db.adminCommand('ping')"
```

### 获取帮助

```bash
# 运行健康检查
./scripts/health-check.sh --detailed

# 查看部署脚本帮助
./scripts/deploy.sh --help

# 查看清理脚本帮助
./scripts/cleanup.sh --help
```

## 🧹 清理环境

### 保留数据的清理

```bash
# 清理应用但保留数据
./scripts/cleanup.sh
```

### 完全清理

```bash
# 清理所有资源和数据
./scripts/cleanup.sh --delete-data --force
```

## 📚 下一步

- 📖 阅读完整的 [部署文档](./README.md)
- 🔧 查看 [API 接口文档](../api/inference-scheduler-api.md)
- 🏗️ 了解 [系统架构](../inference-scheduler-interaction.md)
- 🔍 学习 [故障排查](../troubleshooting/inference-scheduler-troubleshooting.md)

## 💡 提示

1. **生产环境部署**：
   - 使用外部数据库和消息队列
   - 配置持久化存储
   - 设置资源限制和请求
   - 启用监控和告警

2. **性能优化**：
   - 根据负载调整副本数
   - 配置 HPA 自动扩缩容
   - 优化资源配置

3. **安全加固**：
   - 使用 TLS 证书
   - 配置网络策略
   - 启用 RBAC
   - 定期更新镜像

---

🎉 恭喜！你已经成功部署了 CV 推理系统。如有问题，请查看故障排查部分或联系技术支持。
