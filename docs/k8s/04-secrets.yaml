# MongoDB 密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: mongodb-secret
  namespace: cv-system
  labels:
    app: mongodb
type: Opaque
data:
  # 用户名: admin (base64编码)
  username: YWRtaW4=
  # 密码: password123 (base64编码)
  password: cGFzc3dvcmQxMjM=
  # 数据库名: cv_scheduler (base64编码)
  database: Y3Zfc2NoZWR1bGVy
---
# Kafka 密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: kafka-secret
  namespace: cv-system
  labels:
    app: kafka
type: Opaque
data:
  # Kafka 用户名和密码 (如果启用认证)
  # username: a2Fma2E=  # kafka
  # password: a2Fma2EtcGFzc3dvcmQ=  # kafka-password
  
  # Zookeeper 连接字符串 (base64编码)
  zookeeper-connect: em9va2VlcGVyOjIxODE=  # zookeeper:2181
---
# S3 存储密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: s3-secret
  namespace: cv-system
  labels:
    app: inference
    type: storage
type: Opaque
data:
  # S3 访问密钥 (base64编码)
  access-key: WUVPVTNFNFHUSTIZNDRNNTI1UTRVUg==  # YEOU3E6XTI234M25Q4UR
  secret-key: NERCUFA1TUJleWpjeTBMbjFENk9ZVDlPYzNGc3lZdmYweDFXN3pYbQ==  # 4DBRP1MBeyjcy0Ln1D6OYT9Oc3FsyYvf0x1W7zXm
  endpoint: aHR0cDovLzE3NS4xNjguMTIuMTU6Mzg4ODg=  # http://*************:38888
  bucket-name: bXlidWNrZXQ=  # mybucket
  region: dXMtZWFzdC0x  # us-east-1
---
# Triton Server 密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: triton-secret
  namespace: cv-system
  labels:
    app: inference
    type: triton
type: Opaque
data:
  # Triton 服务器地址 (base64编码)
  server-ip: MTc1LjE2OC4xMC41Mg==  # *************
  server-port: OTk5MQ==  # 9991
---
# TLS 证书配置 (可选)
apiVersion: v1
kind: Secret
metadata:
  name: cv-system-tls
  namespace: cv-system
  labels:
    app: cv-system
    type: tls
type: kubernetes.io/tls
data:
  # TLS 证书和私钥 (base64编码)
  # 生产环境中应该使用真实的证书
  tls.crt: LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0t...
  tls.key: LS0tLS1CRUdJTiBQUklWQVRFIEtFWS0tLS0t...
---
# 镜像拉取密钥 (如果使用私有镜像仓库)
apiVersion: v1
kind: Secret
metadata:
  name: cv-system-registry
  namespace: cv-system
  labels:
    app: cv-system
    type: registry
type: kubernetes.io/dockerconfigjson
data:
  # Docker 配置 (base64编码)
  # 格式: {"auths":{"registry.example.com":{"username":"user","password":"pass","auth":"base64(user:pass)"}}}
  .dockerconfigjson: eyJhdXRocyI6e319
---
# 应用密钥配置
apiVersion: v1
kind: Secret
metadata:
  name: app-secret
  namespace: cv-system
  labels:
    app: cv-system
    type: application
type: Opaque
data:
  # JWT 密钥 (base64编码)
  jwt-secret: Y3Ytc3lzdGVtLWp3dC1zZWNyZXQtMjAyNA==  # cv-system-jwt-secret-2024
  
  # API 密钥 (base64编码)
  api-key: Y3Ytc3lzdGVtLWFwaS1rZXktMjAyNA==  # cv-system-api-key-2024
  
  # 加密密钥 (base64编码)
  encryption-key: Y3Ytc3lzdGVtLWVuY3J5cHRpb24ta2V5LTIwMjQ=  # cv-system-encryption-key-2024
