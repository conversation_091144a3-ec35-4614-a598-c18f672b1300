# CV 推理系统 Kubernetes 部署 Makefile
# 作者: CV System Team
# 版本: v1.0.0

.PHONY: help build deploy clean health check logs scale restart backup

# 默认目标
.DEFAULT_GOAL := help

# 变量定义
NAMESPACE := cv-system
REGISTRY := 
TAG := latest
SERVICES := scheduler inference inference-mock rtsp-server

# 颜色定义
BLUE := \033[0;34m
GREEN := \033[0;32m
YELLOW := \033[1;33m
RED := \033[0;31m
NC := \033[0m # No Color

# 帮助信息
help: ## 显示帮助信息
	@echo "$(BLUE)CV 推理系统 Kubernetes 部署工具$(NC)"
	@echo "=================================="
	@echo
	@echo "$(GREEN)可用命令:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-15s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo
	@echo "$(GREEN)示例:$(NC)"
	@echo "  make build              # 构建所有镜像"
	@echo "  make deploy             # 部署系统"
	@echo "  make health             # 健康检查"
	@echo "  make clean              # 清理环境"
	@echo

# 检查依赖
check-deps: ## 检查依赖工具
	@echo "$(BLUE)检查依赖工具...$(NC)"
	@command -v kubectl >/dev/null 2>&1 || { echo "$(RED)kubectl 未安装$(NC)"; exit 1; }
	@command -v docker >/dev/null 2>&1 || { echo "$(RED)docker 未安装$(NC)"; exit 1; }
	@kubectl cluster-info >/dev/null 2>&1 || { echo "$(RED)无法连接到 Kubernetes 集群$(NC)"; exit 1; }
	@echo "$(GREEN)依赖检查通过$(NC)"

# 设置脚本权限
setup: ## 设置脚本执行权限
	@echo "$(BLUE)设置脚本执行权限...$(NC)"
	@chmod +x scripts/*.sh
	@echo "$(GREEN)权限设置完成$(NC)"

# 构建镜像
build: check-deps setup ## 构建 Docker 镜像
	@echo "$(BLUE)构建 Docker 镜像...$(NC)"
	@./scripts/build-images.sh $(if $(REGISTRY),--registry $(REGISTRY)) $(if $(TAG),--tag $(TAG))

# 构建特定服务镜像
build-%: check-deps setup ## 构建特定服务镜像 (如: make build-scheduler)
	@echo "$(BLUE)构建 $* 镜像...$(NC)"
	@./scripts/build-images.sh --service $* $(if $(REGISTRY),--registry $(REGISTRY)) $(if $(TAG),--tag $(TAG))

# 推送镜像
push: check-deps ## 推送镜像到仓库
	@echo "$(BLUE)推送镜像到仓库...$(NC)"
	@./scripts/build-images.sh --push $(if $(REGISTRY),--registry $(REGISTRY)) $(if $(TAG),--tag $(TAG))

# 部署系统
deploy: check-deps setup ## 部署 CV 推理系统
	@echo "$(BLUE)部署 CV 推理系统...$(NC)"
	@./scripts/deploy.sh

# 快速部署 (跳过镜像检查)
deploy-fast: check-deps setup ## 快速部署 (跳过镜像检查)
	@echo "$(BLUE)快速部署 CV 推理系统...$(NC)"
	@./scripts/deploy.sh --skip-images

# 健康检查
health: check-deps ## 执行健康检查
	@echo "$(BLUE)执行健康检查...$(NC)"
	@./scripts/health-check.sh

# 详细健康检查
health-detailed: check-deps ## 执行详细健康检查
	@echo "$(BLUE)执行详细健康检查...$(NC)"
	@./scripts/health-check.sh --detailed

# 查看状态
status: check-deps ## 查看系统状态
	@echo "$(BLUE)查看系统状态...$(NC)"
	@echo "$(GREEN)Pod 状态:$(NC)"
	@kubectl get pods -n $(NAMESPACE) -o wide
	@echo
	@echo "$(GREEN)Service 状态:$(NC)"
	@kubectl get services -n $(NAMESPACE)
	@echo
	@echo "$(GREEN)PVC 状态:$(NC)"
	@kubectl get pvc -n $(NAMESPACE)

# 查看日志
logs: check-deps ## 查看所有服务日志
	@echo "$(BLUE)查看服务日志...$(NC)"
	@kubectl logs -f -l tier=application -n $(NAMESPACE)

# 查看特定服务日志
logs-%: check-deps ## 查看特定服务日志 (如: make logs-scheduler)
	@echo "$(BLUE)查看 $* 日志...$(NC)"
	@kubectl logs -f deployment/$* -n $(NAMESPACE)

# 扩缩容
scale: check-deps ## 扩缩容服务 (需要指定 SERVICE 和 REPLICAS)
	@if [ -z "$(SERVICE)" ] || [ -z "$(REPLICAS)" ]; then \
		echo "$(RED)请指定 SERVICE 和 REPLICAS$(NC)"; \
		echo "示例: make scale SERVICE=inference-mock REPLICAS=3"; \
		exit 1; \
	fi
	@echo "$(BLUE)扩缩容 $(SERVICE) 到 $(REPLICAS) 个副本...$(NC)"
	@kubectl scale deployment $(SERVICE) --replicas=$(REPLICAS) -n $(NAMESPACE)
	@kubectl get deployment $(SERVICE) -n $(NAMESPACE)

# 重启服务
restart: check-deps ## 重启所有服务
	@echo "$(BLUE)重启所有服务...$(NC)"
	@for service in $(SERVICES); do \
		echo "重启 $$service..."; \
		kubectl rollout restart deployment/$$service -n $(NAMESPACE) 2>/dev/null || true; \
	done

# 重启特定服务
restart-%: check-deps ## 重启特定服务 (如: make restart-scheduler)
	@echo "$(BLUE)重启 $* 服务...$(NC)"
	@kubectl rollout restart deployment/$* -n $(NAMESPACE)
	@kubectl rollout status deployment/$* -n $(NAMESPACE)

# 更新配置
update-config: check-deps ## 更新配置文件
	@echo "$(BLUE)更新配置文件...$(NC)"
	@kubectl apply -f 03-configmaps.yaml
	@kubectl apply -f 04-secrets.yaml
	@echo "$(GREEN)配置更新完成，请重启相关服务$(NC)"

# 端口转发
port-forward: check-deps ## 启动端口转发
	@echo "$(BLUE)启动端口转发...$(NC)"
	@echo "$(GREEN)Scheduler:$(NC) http://localhost:8080"
	@echo "$(GREEN)Inference Mock:$(NC) http://localhost:8081"
	@echo "$(GREEN)RTSP Server:$(NC) rtsp://localhost:9554"
	@echo "$(YELLOW)按 Ctrl+C 停止端口转发$(NC)"
	@kubectl port-forward svc/scheduler 8080:8080 -n $(NAMESPACE) &
	@kubectl port-forward svc/inference-mock 8081:8081 -n $(NAMESPACE) &
	@kubectl port-forward svc/rtsp-server 9554:9554 -n $(NAMESPACE) &
	@wait

# 进入容器
shell-%: check-deps ## 进入特定服务容器 (如: make shell-scheduler)
	@echo "$(BLUE)进入 $* 容器...$(NC)"
	@kubectl exec -it deployment/$* -n $(NAMESPACE) -- bash

# 备份
backup: check-deps ## 备份系统配置和数据
	@echo "$(BLUE)备份系统配置和数据...$(NC)"
	@mkdir -p backup
	@kubectl get all,configmap,secret,pvc -n $(NAMESPACE) -o yaml > backup/cv-system-backup-$(shell date +%Y%m%d_%H%M%S).yaml
	@kubectl create job mongodb-backup-manual --from=cronjob/mongodb-backup -n $(NAMESPACE) 2>/dev/null || true
	@echo "$(GREEN)备份完成$(NC)"

# 恢复
restore: check-deps ## 恢复系统配置 (需要指定 BACKUP_FILE)
	@if [ -z "$(BACKUP_FILE)" ]; then \
		echo "$(RED)请指定备份文件$(NC)"; \
		echo "示例: make restore BACKUP_FILE=backup/cv-system-backup-20241208_120000.yaml"; \
		exit 1; \
	fi
	@echo "$(BLUE)恢复系统配置...$(NC)"
	@kubectl apply -f $(BACKUP_FILE)
	@echo "$(GREEN)恢复完成$(NC)"

# 清理环境
clean: check-deps setup ## 清理环境 (保留数据)
	@echo "$(BLUE)清理环境...$(NC)"
	@./scripts/cleanup.sh

# 完全清理
clean-all: check-deps setup ## 完全清理 (包括数据)
	@echo "$(BLUE)完全清理环境...$(NC)"
	@./scripts/cleanup.sh --delete-data --force

# 强制清理
clean-force: check-deps setup ## 强制清理 (不询问确认)
	@echo "$(BLUE)强制清理环境...$(NC)"
	@./scripts/cleanup.sh --force

# 测试部署
test: check-deps ## 测试部署
	@echo "$(BLUE)测试部署...$(NC)"
	@echo "创建测试任务..."
	@curl -X POST http://localhost:8080/api/v1/scheduler/schedule \
		-H "Content-Type: application/json" \
		-d '{"taskRequest":{"taskId":"test-$(shell date +%s)","taskName":"测试任务","device":{"deviceId":"camera-001","streamConfig":{"url":"rtsp://rtsp-server:9554/knight"}}},"region":"default","priority":1}' \
		2>/dev/null || echo "$(YELLOW)请先启动端口转发: make port-forward$(NC)"

# 监控
monitor: check-deps ## 监控系统状态
	@echo "$(BLUE)监控系统状态...$(NC)"
	@watch -n 5 'kubectl get pods -n $(NAMESPACE) && echo && kubectl top pods -n $(NAMESPACE) 2>/dev/null || echo "需要 metrics-server"'

# 查看资源使用
resources: check-deps ## 查看资源使用情况
	@echo "$(BLUE)查看资源使用情况...$(NC)"
	@echo "$(GREEN)节点资源:$(NC)"
	@kubectl top nodes 2>/dev/null || echo "需要 metrics-server"
	@echo
	@echo "$(GREEN)Pod 资源:$(NC)"
	@kubectl top pods -n $(NAMESPACE) 2>/dev/null || echo "需要 metrics-server"

# 网络测试
network-test: check-deps ## 测试网络连通性
	@echo "$(BLUE)测试网络连通性...$(NC)"
	@kubectl exec -n $(NAMESPACE) deployment/scheduler -- nc -z mongodb 27017 && echo "$(GREEN)Scheduler -> MongoDB: OK$(NC)" || echo "$(RED)Scheduler -> MongoDB: FAIL$(NC)"
	@kubectl exec -n $(NAMESPACE) deployment/scheduler -- nc -z kafka 9092 && echo "$(GREEN)Scheduler -> Kafka: OK$(NC)" || echo "$(RED)Scheduler -> Kafka: FAIL$(NC)"
	@kubectl exec -n $(NAMESPACE) deployment/inference-mock -- nc -z scheduler 8080 && echo "$(GREEN)Inference Mock -> Scheduler: OK$(NC)" || echo "$(RED)Inference Mock -> Scheduler: FAIL$(NC)"

# 版本信息
version: ## 显示版本信息
	@echo "$(BLUE)CV 推理系统版本信息$(NC)"
	@echo "=========================="
	@echo "Makefile 版本: v1.0.0"
	@echo "Kubernetes 版本: $(shell kubectl version --short --client 2>/dev/null | grep Client || echo 'N/A')"
	@echo "Docker 版本: $(shell docker --version 2>/dev/null || echo 'N/A')"
	@echo "集群信息: $(shell kubectl config current-context 2>/dev/null || echo 'N/A')"

# 开发模式
dev: build deploy port-forward ## 开发模式 (构建、部署、端口转发)

# 生产部署
prod: build push deploy ## 生产部署 (构建、推送、部署)

# 快速重建
rebuild: clean build deploy ## 快速重建 (清理、构建、部署)
