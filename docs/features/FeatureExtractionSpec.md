# 特征抽取底层gRPC接口规范文档

## 概述

本文档定义了系统中特征抽取相关的底层gRPC接口规范，主要包含三个层次的接口：
- **上层业务接口**：业务服务服务提供的业务级特征抽取接口
- **底层算法接口**：FeatureExtract系列接口，直接调用算法服务
- **统一请求接口**：FoundationModelRequest统一请求格式

这些接口通过Protocol Buffers定义，支持图片特征抽取、文本特征抽取、特征匹配等核心功能。

## 一、上层业务接口（业务服务）

### 服务定义

```protobuf
service BusiService {
  // 图片特征抽取
  rpc AnalysisImageFeature (AnalysisImageFeatureRequest) returns (AnalysisImageFeatureResponse) {}
  
  // 文本特征抽取  
  rpc AnalysisTextFeature (AnalysisTextFeatureRequest) returns (AnalysisTextFeatureResponse) {}
  
  // 特征模型匹配
  rpc FeatureModelMatching (FeatureModelMatchingRequest) returns (FeatureModelMatchingResponse) {}
  
  // 特征文本匹配
  rpc FeatureTextMatching (FeatureTextMatchingRequest) returns (FeatureTextMatchingResponse) {}
  
  // 图文检测
  rpc ImageTextDetect (ImageTextDetectRequest) returns (ImageTextDetectResponse) {}
}
```

### 1.1 图片特征抽取接口

#### 接口描述
用于抽取图片特征，支持多个裁剪区域的批量处理。

#### 请求消息

```protobuf
message AnalysisImageFeatureRequest {
  int32 imageType = 1;                    // 图片类型
  string sceneImageBase64 = 2;            // Base64编码的图片数据
  string sceneImageUrl = 3;               // 图片URL地址
  repeated Crop crops = 4;                // 裁剪区域列表

  message Crop {
    string rectId = 1;                    // 矩形区域ID，唯一标识
    string featureVersion = 2;            // 特征版本号
    Rect rect = 3;                        // 裁剪矩形坐标
    google.protobuf.Struct extraMeta = 4; // 扩展元数据

    message Rect {
      int32 x = 1;  // 左上角X坐标
      int32 y = 2;  // 左上角Y坐标
      int32 w = 3;  // 矩形宽度
      int32 h = 4;  // 矩形高度
    }
  }
}
```

#### 响应消息

```protobuf
message AnalysisImageFeatureResponse {
  repeated FeatureResult featureResults = 1;

  message FeatureResult {
    string rectId = 1;                    // 对应的矩形区域ID
    string featureVersion = 2;            // 特征版本号
    string featureBase64 = 3;             // Base64编码的特征数据
    google.protobuf.Struct extraMeta = 4; // 扩展元数据
  }
}
```

### 1.2 文本特征抽取接口

#### 接口描述
用于抽取文本特征，将输入文本转换为向量特征。

#### 请求消息

```protobuf
message AnalysisTextFeatureRequest {
  string text = 1;                       // 待抽取特征的文本
  string textFeatureVersion = 2;         // 文本特征版本号
  google.protobuf.Struct extraMeta = 3;  // 扩展元数据
}
```

#### 响应消息

```protobuf
message AnalysisTextFeatureResponse {
  string textFeatureVersion = 1;         // 文本特征版本号
  string textFeatureBase64 = 2;          // Base64编码的特征数据
  google.protobuf.Struct extraMeta = 3;  // 扩展元数据
}
```

## 二、底层算法接口

### 核心请求/响应结构

```protobuf
message FeatureExtractRequest {
  oneof request_multiplexing {
    ImageFeatureExtractRequest image_feature_extract_request = 1;     // 图片特征抽取
    TextFeatureExtractRequest text_feature_extract_request = 2;       // 文本特征抽取
    FeatureScoreCalcRequest feature_score_calc_request = 3;           // 特征相似度计算
    ImageFeatureMapExtractRequest image_feature_map_extract_request = 4; // 图片特征图抽取
    ImageDetectRequest image_detect_request = 5;                      // 图片检测
    FeatureAverageRequest feature_average_request = 6;                // 特征平均
    ToInt8FeatureRequest to_int8_feature_request = 7;                 // 转int8特征
    ToFloatFeatureRequest to_float_feature_request = 8;               // 转float特征
  }
}

message FeatureExtractResponse {
  int32 rtn = 1;                         // 返回码（0表示成功）
  string message = 2;                    // 返回消息

  oneof response_multiplexing {
    ImageFeatureExtractResponse image_feature_extract_response = 3;
    TextFeatureExtractResponse text_feature_extract_response = 4;
    FeatureScoreCalcResponse feature_score_calc_response = 5;
    ImageFeatureMapExtractResponse image_feature_map_extract_response = 6;
    ImageDetectResponse image_detect_response = 7;
    FeatureAverageResponse feature_average_response = 8;
    ToInt8FeatureResponse to_int8_feature_response = 9;
    ToFloatFeatureResponse to_float_feature_response = 10;
  }
}
```

### 2.1 图片特征抽取（底层）

#### 请求消息

```protobuf
message ImageFeatureExtractRequest {
  string feature_version = 1;            // 特征版本号
  Image image = 2;                       // 图片数据
  repeated Rect2D rect_list = 3;         // 裁剪区域列表

  bool need_norm = 4;                    // 是否需要归一化
  bool need_encrypt = 5;                 // 是否需要加密
  bool need_base64 = 6;                  // 是否需要Base64编码
  bool to_int8_feature = 7;              // 是否转换为int8特征
}

// 图片数据定义
message Image {
  oneof data {
    bytes binary_data = 1;               // 二进制图片数据
    string base64_data = 2;              // Base64编码的图片数据
    string url = 3;                      // 图片URL地址
  }
}

// 矩形区域定义
message Rect2D {
  int32 x = 1;  // 左上角X坐标
  int32 y = 2;  // 左上角Y坐标
  int32 w = 3;  // 矩形宽度
  int32 h = 4;  // 矩形高度
}
```

#### 响应消息

```protobuf
message ImageFeatureExtractResponse {
  repeated bytes feature_list = 1;       // 特征数据列表（对应每个裁剪区域）
}
```

### 2.2 文本特征抽取（底层）

#### 请求消息

```protobuf
message TextFeatureExtractRequest {
  string feature_version = 1;            // 特征版本号
  string text = 2;                       // 待抽取特征的文本

  bool need_norm = 3;                    // 是否需要归一化
  bool need_encrypt = 4;                 // 是否需要加密
  bool need_base64 = 5;                  // 是否需要Base64编码
  bool to_int8_feature = 6;              // 是否转换为int8特征
}
```

#### 响应消息

```protobuf
message TextFeatureExtractResponse {
  bytes feature = 1;                     // 特征数据
}
```

### 2.3 特征平均计算

#### 请求消息

```protobuf
message FeatureAverageRequest {
  repeated bytes feature_list = 1;       // 待平均的特征列表

  bool base64_feature = 3;               // 输入特征是否为Base64格式
  bool need_norm_input = 4;              // 输入是否需要归一化

  bool need_norm = 5;                    // 输出是否需要归一化
  bool need_encrypt = 6;                 // 输出是否需要加密
  bool need_base64 = 7;                  // 输出是否需要Base64编码
  bool to_int8_feature = 8;              // 是否转换为int8特征
}
```

#### 响应消息

```protobuf
message FeatureAverageResponse {
  bytes avg_feature = 1;                 // 平均后的特征数据
}
```

### 2.4 特征相似度计算

#### 请求消息

```protobuf
message FeatureScoreCalcRequest {
  bytes query = 1;                       // 查询特征
  repeated bytes db_list = 2;            // 数据库特征列表

  bool base64_feature = 3;               // 特征是否为Base64格式
  bool need_norm = 4;                    // 是否需要归一化
}
```

#### 响应消息

```protobuf
message FeatureScoreCalcResponse {
  repeated float score_list = 1;         // 相似度分数列表
}
```

## 三、统一请求格式

### FoundationModelRequest

```protobuf
message FoundationModelRequest {
  enum RequestType {
    IMAGE_TEXT_MATCH = 0;               // 图文匹配
    IMAGE_TEXT_FEATURE_MATCH = 1;       // 图文特征匹配
    REFINE_MODEL_MATCH = 2;             // 精化模型匹配
    TEXT_FEATURE_EXTRACT = 3;           // 文本特征抽取
    IMAGE_FEATURE_EXTRACT = 4;          // 图片特征抽取
    AVG_IMAGE_FEATURE = 5;              // 图片特征平均
  }
  RequestType type = 1;                 // 请求类型
  bytes requestBinary = 2;              // 序列化的请求数据
}
```

## 四、数据类型定义

### 张量类型

```protobuf
enum TypeCode {
  Int = 0;    // 整型
  UInt = 1;   // 无符号整型
  Float = 2;  // 浮点型
  Bool = 3;   // 布尔型
}

message Tensor {
  TypeCode type_code = 1;               // 数据类型
  int32 bits = 2;                       // 位数
  repeated int32 shape = 3;             // 张量形状
  bytes data = 4;                       // 张量数据
}
```

### 检测结果

```protobuf
message DetectBbox {
  Rect2D bbox = 1;                      // 检测框
  int32 class_id = 2;                   // 类别ID
  float score = 3;                      // 置信度分数
}

message DetectBboxList {
  repeated DetectBbox bbox_list = 1;    // 检测框列表
}
```

## 五、接口调用流程

### 5.1 图片特征抽取流程

1. **构建请求**：根据图片数据和裁剪区域构建 `ImageFeatureExtractRequest`
2. **封装请求**：将请求封装到 `FeatureExtractRequest` 中
3. **发送请求**：通过gRPC调用算法服务
4. **解析响应**：从 `FeatureExtractResponse` 中提取特征数据
5. **数据处理**：根据需要进行Base64解码、反归一化等处理

### 5.2 文本特征抽取流程

1. **构建请求**：根据文本内容构建 `TextFeatureExtractRequest`
2. **封装请求**：将请求封装到 `FeatureExtractRequest` 中
3. **发送请求**：通过gRPC调用算法服务
4. **解析响应**：从 `FeatureExtractResponse` 中提取特征数据
5. **数据处理**：根据需要进行格式转换

## 六、错误处理

### 返回码定义

| 返回码 | 说明 |
|--------|------|
| 0 | 成功 |
| -1 | 通用错误 |
| -2 | 参数错误 |
| -3 | 模型加载失败 |
| -4 | 特征抽取失败 |
| -5 | 内存不足 |

### 异常处理机制

1. **网络异常**：gRPC连接失败，自动重试机制
2. **算法异常**：返回码非0时，记录详细错误信息
3. **数据异常**：输入数据格式错误，返回参数错误
4. **资源异常**：GPU内存不足等，返回资源错误

## 七、性能优化

### 7.1 批量处理

- 图片特征抽取支持多个裁剪区域的批量处理
- 特征平均计算支持多个特征的批量平均
- 特征相似度计算支持一对多的批量计算

### 7.2 数据格式优化

- 支持int8量化特征，减少存储和传输开销
- 支持Base64编码，便于JSON传输
- 支持二进制格式，提高传输效率

### 7.3 内存管理

- 大图片自动分块处理
- 特征数据流式处理
- 及时释放中间结果

## 八、使用示例

### 8.1 图片特征抽取示例

```protobuf
# 请求示例
ImageFeatureExtractRequest {
  feature_version: "CLS-M2-KETHER-A"
  image: {
    url: "http://example.com/image.jpg"
  }
  rect_list: [
    {x: 100, y: 100, w: 200, h: 200}
  ]
  need_base64: true
  to_int8_feature: false
}

# 响应示例
ImageFeatureExtractResponse {
  feature_list: ["base64_encoded_feature_data"]
}
```

### 8.2 文本特征抽取示例

```protobuf
# 请求示例
TextFeatureExtractRequest {
  feature_version: "CLS-M2-KETHER-A"
  text: "这是一段测试文本"
  need_base64: true
  need_norm: true
}

# 响应示例
TextFeatureExtractResponse {
  feature: "base64_encoded_feature_data"
}
```

## 九、注意事项

1. **特征版本兼容性**：确保使用的特征版本与部署的模型版本一致
2. **图片格式支持**：支持JPEG、PNG、BMP等常见格式
3. **文本长度限制**：建议单次文本长度控制在1000字符以内
4. **并发限制**：单个客户端建议并发请求数不超过100个
5. **超时设置**：建议设置30秒的请求超时时间
6. **资源清理**：及时释放大型特征数据，避免内存泄漏
