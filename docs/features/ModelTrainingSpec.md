# Fusion-Moss 模型训练接口规范

## 1. 概述

本文档定义了 Fusion-Moss 平台的模型训练接口规范，用于支持第三方算法的训练适配。通过遵循此规范，第三方算法可以无缝集成到 Fusion-Moss 训练流程中。

### 1.1 适用范围
- 深度学习模型训练
- 分类和检索任务
- GPU/CPU 训练环境
- Docker 容器化部署

### 1.2 设计原则
- **标准化**：统一的接口和数据格式
- **灵活性**：支持多种模型架构和训练策略
- **可扩展性**：便于集成新的算法和功能
- **容器化**：完全基于Docker容器运行

---

## 2. 容器化训练规范

### 2.1 Docker 容器接口

#### 基本要求
- 支持 Linux 容器运行环境
- 支持 GPU 加速（NVIDIA GPU + CUDA）
- 容器需要无交互式运行
- 支持资源限制和监控

#### 容器启动命令格式
```bash
docker run \
  --shm-size=10g \
  -e NVIDIA_VISIBLE_DEVICES=0 \
  -v /host/config:/opt/fusion-moss/config \
  -v /host/tmp:/tmp \
  -v /host/data:/data \
  --rm -t \
  <training_image>
```

#### 参数说明
| 参数 | 描述 | 是否必需 |
|------|------|----------|
| `--shm-size` | 共享内存大小，建议10g以上 | 建议 |
| `-e NVIDIA_VISIBLE_DEVICES` | GPU设备ID，支持多GPU | 可选 |
| `-v config` | 配置文件挂载点 | 必需 |
| `-v tmp` | 临时输出目录挂载点 | 必需 |
| `-v data` | 训练数据目录挂载点 | 必需 |

---

## 3. 目录结构规范

### 3.1 配置目录 (`/opt/fusion-moss/config`)

容器内配置文件路径，包含训练所需的所有配置信息。

```
/opt/fusion-moss/config/
└── config.yaml          # 主配置文件
```

### 3.2 数据目录 (`/data`)

训练数据集目录，包含预处理后的特征数据和标注信息。

```
/data/
├── dataset_<id>_<tag>/
│   ├── files/
│   │   ├── <resource_id>.feature    # 特征文件（二进制）
│   │   ├── <resource_id>.feature
│   │   └── ...
│   └── meta.json                    # 数据集元信息
├── dataset_<id2>_<tag2>/
└── ...
```

#### 用途说明
- **特征文件**：存储从原始数据中提取的特征向量
- **元数据文件**：记录特征文件与标签的对应关系
- **多数据集**：支持同时使用多个数据集进行训练

### 3.3 输出目录 (`/tmp`)

训练过程的临时文件和最终输出。

```
/tmp/
├── checkpoints/                     # 训练检查点（可选）
├── <model_id>_<version>_<timestamp>.tar  # 最终模型输出
└── target.onnx                      # ONNX格式模型（可选）
```

#### 用途说明
- **checkpoints**：保存训练过程中的模型检查点，用于恢复训练
- **最终模型**：训练完成后的模型文件，必须按指定格式命名
- **ONNX模型**：可选的ONNX格式转换输出

---

## 4. 配置文件接口

### 4.1 配置文件说明

第三方训练算法将通过 `/opt/fusion-moss/config/config.yaml` 文件接收训练配置信息。配置文件内容由平台根据训练任务动态生成，第三方算法只需要按照以下接口规范读取所需信息。

### 4.2 必需配置项

训练算法必须能够处理以下配置项：

```yaml
# 数据配置 - 训练数据路径
data:
  train_data:                        # 训练数据路径列表
    - "file:///data/dataset_xxx_tag1"
    - "file:///data/dataset_xxx_tag2"

# 模型基本信息
model:
  name: "CustomModel"                # 模型名称
  task: "CLASSIFY"                   # 任务类型
  
  # 模型保存配置
  model_saver:
    upload_uri: "file:///tmp/model_output.tar"  # 最终模型输出路径
```

### 4.3 任务类型说明

| 任务类型 | 描述 | 输出要求 |
|----------|------|----------|
| `CLASSIFY` | 分类任务 | 输出分类模型 |
| `RETRIEVAL` | 检索任务 | 输出检索模型 |

### 4.4 配置读取要求

1. **配置解析**：支持YAML格式配置文件解析
2. **路径处理**：正确处理 `file://` 前缀的文件路径
3. **容错处理**：对于可选配置项提供默认值
4. **参数验证**：验证必需配置项的存在性

---

## 5. 数据集格式规范

### 5.1 数据集目录结构

每个数据集目录格式固定：

```
dataset_<dataset_id>_<tag_name>/
├── files/
│   ├── <resource_id1>.feature
│   ├── <resource_id2>.feature
│   └── ...
└── meta.json
```

### 5.2 特征文件格式

- **文件名格式**：`<resource_id>.feature`
- **文件类型**：二进制文件
- **内容格式**：浮点数数组的二进制表示
- **读取方式**：使用相应的二进制读取接口

### 5.3 元数据文件 (meta.json)

```json
{
  "id": "dataset_12345",
  "create_time": "2023-03-06 12:00:00.000",
  "annotation": [
    {
      "feature": "files/resource_001.feature",
      "label": "positive"
    },
    {
      "feature": "files/resource_002.feature", 
      "label": "negative"
    }
  ]
}
```

#### 字段说明
| 字段 | 类型 | 描述 | 是否必需 |
|------|------|------|----------|
| `id` | string | 数据集ID | 必需 |
| `create_time` | string | 创建时间戳 | 必需 |
| `annotation` | array | 标注信息数组 | 必需 |
| `annotation[].feature` | string | 特征文件相对路径 | 必需 |
| `annotation[].label` | string | 标签 | 必需 |

---

## 6. 模型输出规范

### 6.1 输出文件要求

1. **最终模型文件**：必须输出到 `/tmp/<model_id>_<version>_<timestamp>.tar`
2. **文件格式**：TAR 压缩包格式
3. **文件命名**：严格按照 `模型ID_版本_时间戳.tar` 格式
4. **文件权限**：确保平台可读取

### 6.2 模型包内容结构

```
model_output.tar
├── model.pth                # 模型权重文件
├── model.onnx               # ONNX格式模型（可选）
├── model_info.json          # 模型基本信息
└── readme.txt               # 模型说明文件（可选）
```

### 6.3 模型信息文件 (model_info.json)

```json
{
  "model_type": "CLASSIFY",
  "model_name": "CustomClassifier",
  "version": "1.0.0",
  "input_format": "binary_feature",
  "output_format": "classification_result",
  "created_time": "2023-03-06T12:00:00Z",
  "description": "Custom classification model for Fusion-Moss platform"
}
```

---

## 7. 环境变量规范

### 7.1 GPU 相关环境变量

| 变量名 | 描述 | 示例值 | 默认值 |
|--------|------|--------|--------|
| `NVIDIA_VISIBLE_DEVICES` | 可见的GPU设备ID | `0` 或 `0,1,2,3` | `0` |
| `CUDA_VISIBLE_DEVICES` | CUDA可见设备ID | `0,1` | - |

### 7.2 训练相关环境变量

| 变量名 | 描述 | 示例值 | 默认值 |
|--------|------|--------|--------|
| `OMP_NUM_THREADS` | OpenMP线程数 | `8` | `1` |
| `PYTHONUNBUFFERED` | Python输出不缓冲 | `1` | `0` |
| `TORCH_HOME` | PyTorch缓存目录 | `/tmp/torch` | - |

---

## 8. 训练流程时序图

```mermaid
sequenceDiagram
    participant Platform as Fusion-Moss平台
    participant Container as 训练容器
    participant Storage as 存储系统
    
    Platform->>Platform: 1. 准备训练数据
    Platform->>Platform: 2. 生成配置文件
    Platform->>Container: 3. 启动训练容器
    
    Container->>Container: 4. 验证环境配置
    Container->>Container: 5. 读取配置文件
    Container->>Container: 6. 加载训练数据
    Container->>Container: 7. 初始化模型
    Container->>Container: 8. 执行训练循环
    
    Container->>Container: 9. 模型评估
    Container->>Container: 10. 保存最终模型
    Container->>Platform: 11. 返回训练结果
    
    Platform->>Storage: 12. 上传模型文件
    Platform->>Platform: 13. 更新模型状态
```

---

## 9. 错误处理规范

### 9.1 退出码规范

| 退出码 | 含义 | 描述 | 处理建议 |
|--------|------|------|----------|
| 0 | 成功 | 训练成功完成 | 正常流程继续 |
| 1 | 配置错误 | 配置文件格式错误或缺失 | 检查配置文件 |
| 2 | 数据错误 | 训练数据格式错误或缺失 | 检查数据格式 |
| 3 | 内存不足 | 训练过程中内存不足 | 减少batch_size |
| 4 | GPU错误 | GPU相关错误 | 检查GPU状态 |
| 5 | 模型保存失败 | 无法保存最终模型 | 检查输出目录权限 |
| 99 | 未知错误 | 其他未分类错误 | 查看详细日志 |

### 9.2 日志输出规范

#### 日志格式
```
[TIMESTAMP] [LEVEL] [COMPONENT] MESSAGE
```

#### 日志示例
```
[2023-03-06 12:00:00.123] [INFO] [DataLoader] 开始加载训练数据
[2023-03-06 12:00:10.456] [INFO] [DataLoader] 加载数据集: dataset_12345_positive, 样本数: 1000
[2023-03-06 12:00:20.789] [INFO] [Trainer] 开始训练, Epoch: 1/200
[2023-03-06 12:05:30.012] [INFO] [Trainer] Epoch: 1, Loss: 0.4523, Accuracy: 0.7834
[2023-03-06 12:10:40.345] [ERROR] [GPU] GPU内存不足，尝试减少batch_size
```

---

## 10. 实现指南

### 10.1 基本实现流程

第三方训练算法需要实现以下基本流程：

1. **配置读取**：从 `/opt/fusion-moss/config/config.yaml` 读取配置
2. **数据加载**：从 `/data` 目录加载训练数据
3. **训练执行**：执行模型训练过程
4. **模型保存**：将训练好的模型保存到 `/tmp` 目录

### 10.2 关键接口实现

```python
import os
import yaml
import logging

# 设置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] [%(levelname)s] [%(name)s] %(message)s'
)
logger = logging.getLogger(__name__)

class TrainingContainer:
    def __init__(self):
        self.config_path = "/opt/fusion-moss/config/config.yaml"
        self.data_path = "/data"
        self.output_path = "/tmp"
        
    def load_config(self):
        """加载配置文件"""
        try:
            with open(self.config_path, 'r') as f:
                config = yaml.safe_load(f)
            logger.info("配置文件加载成功")
            return config
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            exit(1)
    
    def load_datasets(self, config):
        """加载数据集"""
        try:
            for data_path in config['data']['train_data']:
                dataset_path = data_path.replace('file://', '')
                logger.info(f"加载数据集: {dataset_path}")
                # 实现具体的数据集加载逻辑
                
        except Exception as e:
            logger.error(f"数据集加载失败: {e}")
            exit(2)
    
    def save_model(self, config):
        """保存训练好的模型"""
        try:
            output_path = config['model']['model_saver']['upload_uri'].replace('file://', '')
            logger.info(f"保存模型到: {output_path}")
            # 实现具体的模型保存逻辑
            
        except Exception as e:
            logger.error(f"模型保存失败: {e}")
            exit(5)
    
    def run(self):
        """主训练函数"""
        try:
            config = self.load_config()
            self.load_datasets(config)
            # 执行训练逻辑
            self.save_model(config)
            logger.info("训练完成")
            
        except Exception as e:
            logger.error(f"训练失败: {e}")
            exit(99)

if __name__ == "__main__":
    trainer = TrainingContainer()
    trainer.run()
```

### 10.3 Docker镜像要求

第三方训练镜像需要满足以下要求：

1. **基础镜像**：建议使用官方的语言运行时基础镜像
2. **入口点**：容器启动时自动执行训练脚本
3. **环境变量**：正确设置必要的环境变量
4. **工作目录**：合理设置工作目录结构

#### Dockerfile 参考结构
```dockerfile
FROM <base_image>

# 安装必要的系统依赖
RUN apt-get update && apt-get install -y \
    <required_packages> \
    && rm -rf /var/lib/apt/lists/*

# 设置工作目录
WORKDIR /app

# 安装应用依赖
COPY requirements.txt .
RUN <install_dependencies>

# 复制应用代码
COPY . .

# 设置环境变量
ENV PYTHONUNBUFFERED=1

# 定义入口点
CMD ["<training_command>"]
```

---

## 11. 总结

本规范定义了 Fusion-Moss 平台模型训练的接口标准，为第三方算法提供统一的集成规范。

### 11.1 核心要求

1. **容器化部署**：所有训练算法必须以Docker容器形式运行
2. **标准接口**：严格遵循目录结构和文件格式要求
3. **数据处理**：正确处理平台提供的特征数据和标注信息
4. **模型输出**：按照规定格式输出训练好的模型
5. **错误处理**：使用标准化的退出码和日志格式

### 11.2 集成流程

1. **开发阶段**：基于此规范开发训练算法
2. **测试阶段**：验证容器接口和数据处理
3. **部署阶段**：提交训练镜像到平台
4. **运行阶段**：平台自动调度和执行训练任务

### 11.3 技术支持

如需技术支持或规范澄清，请联系 Fusion-Moss 平台技术团队。

遵循此规范，第三方算法可以无缝集成到 Fusion-Moss 训练流程中，实现高效的模型训练和管理。 