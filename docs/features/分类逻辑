类似owl检测，我需要实现一个api接口，来支持分类：
分类的输入包括图片、图片中的框、以及需要分类的文本和阈值，其中文本中需要区分正例文本和负例文本，阈值只有一个，表示任意一个正例文本的分数超过阈值则预警，同时超过阈值的整理文本分数也要大于负例文本分数，比如要分类红色衣服：

输入：
图片：省略
对一个图片的分类框：A、B、C、D
文本1: 红色 - 正例
文本2: 赤色 - 正例
文本3: 蓝色 - 负例
阈值：0.15

分类结果
A {红色 - 0.5, 赤色 - 0.4, 蓝色 - 0.1} - 告警
B {红色 - 0.3, 赤色 - 0.2, 蓝色 - 0.6} - 正例标签超过阈值但是没有超过负例标签的分数，不预警
C {红色 - 0.14, 赤色 - 0.13, 蓝色 - 0.11} - 正例标签没有超过阈值，不报警
D{红色 - 0.14, 赤色 - 0.18, 蓝色 - 0.11}  - 有一个正理标签超过阈值，报警
