# Inference-Scheduler 部署运维指南

## 📋 概述

本文档提供 Inference 推理服务与 Scheduler 调度器的完整部署、配置和运维指南，包括环境准备、服务部署、监控告警等。

## 🏗️ 系统架构

```
┌─────────────────────────────────────────────────────────────────┐
│                        负载均衡层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   Nginx     │  │   HAProxy   │  │   Trae<PERSON><PERSON>   │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                        应用服务层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │ Scheduler   │  │ Inference-1 │  │ Inference-2 │              │
│  │   :8080     │  │   :9001     │  │   :9002     │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────────┐
│                        基础设施层                                │
│  ┌─────────────┐  ┌─────────────┐  ┌─────────────┐              │
│  │   MySQL     │  │    Kafka    │  │   Triton    │              │
│  │   :3306     │  │   :9092     │  │   :8000     │              │
│  └─────────────┘  └─────────────┘  └─────────────┘              │
└─────────────────────────────────────────────────────────────────┘
```

## 🔧 环境准备

### 1. 硬件要求

#### 1.1 最小配置

| 组件 | CPU | 内存 | 存储 | GPU |
|------|-----|------|------|-----|
| **Scheduler** | 4核 | 8GB | 100GB | 无 |
| **Inference** | 8核 | 16GB | 200GB | RTX 3080/A10 |
| **MySQL** | 2核 | 4GB | 100GB | 无 |
| **Kafka** | 4核 | 8GB | 200GB | 无 |

#### 1.2 推荐配置

| 组件 | CPU | 内存 | 存储 | GPU |
|------|-----|------|------|-----|
| **Scheduler** | 8核 | 16GB | 500GB | 无 |
| **Inference** | 16核 | 32GB | 1TB | RTX 4090/A100 |
| **MySQL** | 4核 | 8GB | 500GB SSD | 无 |
| **Kafka** | 8核 | 16GB | 1TB SSD | 无 |

### 2. 软件依赖

#### 2.1 基础环境

```bash
# 操作系统
Ubuntu 20.04 LTS / CentOS 8

# 容器运行时
Docker 20.10+
Docker Compose 2.0+

# 编程语言
Java 11+ (Scheduler)
Python 3.9+ (Inference)

# 数据库
MySQL 8.0+

# 消息队列
Apache Kafka 2.8+

# GPU驱动 (Inference节点)
NVIDIA Driver 470+
CUDA 11.8+
cuDNN 8.6+
```

#### 2.2 Python依赖 (Inference)

```bash
# requirements.txt
torch>=1.13.0
torchvision>=0.14.0
transformers>=4.21.0
opencv-python>=4.6.0
numpy>=1.21.0
pillow>=9.0.0
flask>=2.2.0
kafka-python>=2.0.2
mysql-connector-python>=8.0.29
pyyaml>=6.0
requests>=2.28.0
```

#### 2.3 Java依赖 (Scheduler)

```xml
<!-- pom.xml 主要依赖 -->
<dependencies>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>2.7.0</version>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
        <version>2.7.0</version>
    </dependency>
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.29</version>
    </dependency>
</dependencies>
```

## 🚀 部署方案

### 1. Docker Compose 部署 (推荐)

#### 1.1 目录结构

```
deployment/
├── docker-compose.yml
├── .env
├── configs/
│   ├── scheduler/
│   │   └── application.yml
│   ├── inference/
│   │   └── config.yaml
│   ├── mysql/
│   │   └── init.sql
│   └── kafka/
│       └── server.properties
├── data/
│   ├── mysql/
│   ├── kafka/
│   └── models/
└── logs/
    ├── scheduler/
    ├── inference/
    └── kafka/
```

#### 1.2 Docker Compose 配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  # MySQL 数据库
  mysql:
    image: mysql:8.0
    container_name: cv-mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: cv_scheduler
      MYSQL_USER: scheduler
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    ports:
      - "3306:3306"
    volumes:
      - ./data/mysql:/var/lib/mysql
      - ./configs/mysql/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - cv-network
    restart: unless-stopped

  # Kafka 消息队列
  zookeeper:
    image: confluentinc/cp-zookeeper:7.0.1
    container_name: cv-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cv-network
    restart: unless-stopped

  kafka:
    image: confluentinc/cp-kafka:7.0.1
    container_name: cv-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
    volumes:
      - ./data/kafka:/var/lib/kafka/data
    networks:
      - cv-network
    restart: unless-stopped

  # Scheduler 调度器
  scheduler:
    build:
      context: ../services/scheduler
      dockerfile: Dockerfile
    container_name: cv-scheduler
    depends_on:
      - mysql
      - kafka
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: production
      SPRING_DATASOURCE_URL: ************************************
      SPRING_DATASOURCE_USERNAME: scheduler
      SPRING_DATASOURCE_PASSWORD: ${MYSQL_PASSWORD}
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
    volumes:
      - ./configs/scheduler/application.yml:/app/config/application.yml
      - ./logs/scheduler:/app/logs
    networks:
      - cv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Inference 推理服务
  inference-1:
    build:
      context: ../services/inference
      dockerfile: Dockerfile
    container_name: cv-inference-1
    depends_on:
      - scheduler
      - kafka
    ports:
      - "9001:9001"
    environment:
      SERVICE_NAME: inference-service-1
      SERVICE_PORT: 9001
      SCHEDULER_URL: http://scheduler:8080
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      MAX_QUOTA: 10
      REGION: default
      GPU_TYPE: A10
    volumes:
      - ./configs/inference/config.yaml:/app/config/config.yaml
      - ./data/models:/app/models
      - ./logs/inference:/app/logs
      - /tmp/.X11-unix:/tmp/.X11-unix:rw
    networks:
      - cv-network
    restart: unless-stopped
    deploy:
      resources:
        reservations:
          devices:
            - driver: nvidia
              count: 1
              capabilities: [gpu]
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9001/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  cv-network:
    driver: bridge

volumes:
  mysql-data:
  kafka-data:
```

#### 1.3 环境变量配置

```bash
# .env
MYSQL_ROOT_PASSWORD=root_password_123
MYSQL_PASSWORD=scheduler_password_123

# 服务配置
SCHEDULER_PORT=8080
INFERENCE_PORT=9001

# 资源配置
INFERENCE_MAX_QUOTA=10
INFERENCE_REGION=default
INFERENCE_GPU_TYPE=A10

# 日志级别
LOG_LEVEL=INFO
```

### 2. Kubernetes 部署

#### 2.1 命名空间

```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: cv-system
  labels:
    name: cv-system
```

#### 2.2 ConfigMap 配置

```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: scheduler-config
  namespace: cv-system
data:
  application.yml: |
    spring:
      datasource:
        url: ********************************************
        username: scheduler
        password: ${MYSQL_PASSWORD}
      jpa:
        hibernate:
          ddl-auto: update
        show-sql: false
    
    scheduler:
      strategy:
        default-mode: FILL_FIRST
        enable-dynamic-schedule: true
    
    logging:
      level:
        com.bohua.scheduler: INFO
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: inference-config
  namespace: cv-system
data:
  config.yaml: |
    service_registry:
      enabled: true
      scheduler:
        url: "http://scheduler-service:8080"
        registration_retry_interval: 30
      service:
        name: "${SERVICE_NAME}"
        max_quota: "${MAX_QUOTA}"
        region: "${REGION}"
        gpu_type: "${GPU_TYPE}"
    
    logging:
      level: INFO
```

#### 2.3 Deployment 配置

```yaml
# scheduler-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: scheduler
  namespace: cv-system
spec:
  replicas: 2
  selector:
    matchLabels:
      app: scheduler
  template:
    metadata:
      labels:
        app: scheduler
    spec:
      containers:
      - name: scheduler
        image: cv/scheduler:latest
        ports:
        - containerPort: 8080
        env:
        - name: MYSQL_PASSWORD
          valueFrom:
            secretKeyRef:
              name: mysql-secret
              key: password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: scheduler-config
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: inference
  namespace: cv-system
spec:
  replicas: 3
  selector:
    matchLabels:
      app: inference
  template:
    metadata:
      labels:
        app: inference
    spec:
      containers:
      - name: inference
        image: cv/inference:latest
        ports:
        - containerPort: 9001
        env:
        - name: SERVICE_NAME
          value: "inference-service"
        - name: MAX_QUOTA
          value: "10"
        - name: REGION
          value: "default"
        - name: GPU_TYPE
          value: "A10"
        resources:
          limits:
            nvidia.com/gpu: 1
          requests:
            nvidia.com/gpu: 1
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        - name: model-volume
          mountPath: /app/models
        livenessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 120
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /health
            port: 9001
          initialDelaySeconds: 60
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: inference-config
      - name: model-volume
        persistentVolumeClaim:
          claimName: model-pvc
      nodeSelector:
        accelerator: nvidia-tesla-a10
```

#### 2.4 Service 配置

```yaml
# services.yaml
apiVersion: v1
kind: Service
metadata:
  name: scheduler-service
  namespace: cv-system
spec:
  selector:
    app: scheduler
  ports:
  - port: 8080
    targetPort: 8080
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  name: inference-service
  namespace: cv-system
spec:
  selector:
    app: inference
  ports:
  - port: 9001
    targetPort: 9001
  type: ClusterIP
```

## ⚙️ 配置管理

### 1. Scheduler 配置

#### 1.1 应用配置

```yaml
# application.yml
spring:
  application:
    name: cv-scheduler
  
  datasource:
    url: ****************************************
    username: scheduler
    password: ${MYSQL_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
  
  kafka:
    bootstrap-servers: localhost:9092
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer

server:
  port: 8080
  servlet:
    context-path: /

scheduler:
  strategy:
    default-mode: FILL_FIRST  # FILL_FIRST | SPREAD_FIRST
    enable-dynamic-schedule: true
  
  health-check:
    interval: 30  # 秒
    timeout: 10   # 秒
  
  task-maintenance:
    interval: 30  # 秒
    retry-failed-tasks: true
    cleanup-orphan-tasks: true

logging:
  level:
    com.bohua.scheduler: INFO
    org.springframework: WARN
    org.hibernate: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/scheduler.log
    max-size: 100MB
    max-history: 30

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

### 2. Inference 配置

#### 2.1 服务配置

```yaml
# config.yaml
service_registry:
  enabled: true
  scheduler:
    url: "http://localhost:8080"
    registration_retry_interval: 30  # 秒
    health_check_interval: 60       # 秒
  service:
    name: "inference-service-1"
    max_quota: 10
    region: "default"
    gpu_type: "A10"
    tags: ["production", "gpu-enabled"]

http_server:
  host: "0.0.0.0"
  port: 9001
  debug: false

task_manager:
  max_concurrent_tasks: 10
  task_timeout: 3600  # 秒
  cleanup_interval: 300  # 秒

# 算法模块配置
modules:
  dsl_pipeline_module:
    work_mode: "trigger"
    video_decode:
      max_width: 1920
      max_height: 1080
      target_fps: 25

  yolov8_detector_module:
    work_mode: "trigger"
    model:
      model_path: "/app/models/yolov8n.pt"
      device: "cuda:0"
      confidence: 0.5
      iou_threshold: 0.45

  byte_tracker_module:
    work_mode: "trigger"
    tracker:
      track_thresh: 0.45
      track_buffer: 25
      match_thresh: 0.8
      frame_rate: 25

  classification_module:
    work_mode: "trigger"
    clip_model:
      model_path: "/app/models/clip-vit-base-patch32"
      device: "cuda:0"

  zone_intrusion_module:
    work_mode: "trigger"
    detection:
      alarm_thresh: 3.0
      re_alarm_thresh: 10.0

# Kafka配置
kafka:
  bootstrap_servers: "localhost:9092"
  topic: "cv_events"
  producer:
    acks: 1
    retries: 3
    batch_size: 16384
    linger_ms: 10

# 日志配置
logging:
  level: INFO
  format: "%(asctime)s [%(levelname)s] %(name)s: %(message)s"
  file:
    path: "logs/inference.log"
    max_size: "100MB"
    backup_count: 10
```

## 🔍 监控告警

### 1. 健康检查

#### 1.1 服务健康检查

```bash
#!/bin/bash
# health_check.sh

# Scheduler健康检查
check_scheduler() {
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:8080/actuator/health)
    if [ "$response" = "200" ]; then
        echo "✅ Scheduler服务正常"
        return 0
    else
        echo "❌ Scheduler服务异常 (HTTP: $response)"
        return 1
    fi
}

# Inference健康检查
check_inference() {
    local port=$1
    response=$(curl -s -o /dev/null -w "%{http_code}" http://localhost:$port/health)
    if [ "$response" = "200" ]; then
        echo "✅ Inference服务 :$port 正常"
        return 0
    else
        echo "❌ Inference服务 :$port 异常 (HTTP: $response)"
        return 1
    fi
}

# 数据库连接检查
check_database() {
    mysql -h localhost -u scheduler -p$MYSQL_PASSWORD -e "SELECT 1" cv_scheduler > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ 数据库连接正常"
        return 0
    else
        echo "❌ 数据库连接异常"
        return 1
    fi
}

# Kafka连接检查
check_kafka() {
    kafka-topics.sh --bootstrap-server localhost:9092 --list > /dev/null 2>&1
    if [ $? -eq 0 ]; then
        echo "✅ Kafka连接正常"
        return 0
    else
        echo "❌ Kafka连接异常"
        return 1
    fi
}

# 执行所有检查
echo "开始系统健康检查..."
check_scheduler
check_inference 9001
check_inference 9002
check_database
check_kafka
echo "健康检查完成"
```

#### 1.2 Prometheus 监控

```yaml
# prometheus.yml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'scheduler'
    static_configs:
      - targets: ['localhost:8080']
    metrics_path: '/actuator/prometheus'
    scrape_interval: 30s

  - job_name: 'inference'
    static_configs:
      - targets: ['localhost:9001', 'localhost:9002']
    metrics_path: '/metrics'
    scrape_interval: 30s

  - job_name: 'mysql'
    static_configs:
      - targets: ['localhost:9104']

  - job_name: 'kafka'
    static_configs:
      - targets: ['localhost:9308']
```

### 2. 告警规则

#### 2.1 Prometheus 告警规则

```yaml
# alert_rules.yml
groups:
  - name: cv_system_alerts
    rules:
      # 服务可用性告警
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "服务 {{ $labels.job }} 不可用"
          description: "服务 {{ $labels.job }} 在 {{ $labels.instance }} 上已经停止响应超过1分钟"

      # 任务失败率告警
      - alert: HighTaskFailureRate
        expr: (rate(scheduler_tasks_failed_total[5m]) / rate(scheduler_tasks_total[5m])) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "任务失败率过高"
          description: "过去5分钟任务失败率为 {{ $value | humanizePercentage }}"

      # 配额使用率告警
      - alert: HighQuotaUsage
        expr: (scheduler_quota_used / scheduler_quota_total) > 0.8
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "配额使用率过高"
          description: "当前配额使用率为 {{ $value | humanizePercentage }}"

      # 内存使用率告警
      - alert: HighMemoryUsage
        expr: (1 - (node_memory_MemAvailable_bytes / node_memory_MemTotal_bytes)) > 0.85
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "内存使用率过高"
          description: "节点 {{ $labels.instance }} 内存使用率为 {{ $value | humanizePercentage }}"

      # GPU使用率告警
      - alert: HighGPUUsage
        expr: nvidia_gpu_utilization > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "GPU使用率过高"
          description: "GPU {{ $labels.gpu }} 使用率为 {{ $value }}%"
```

### 3. 日志监控

#### 3.1 ELK Stack 配置

```yaml
# logstash.conf
input {
  file {
    path => "/app/logs/scheduler/*.log"
    type => "scheduler"
    codec => "json"
  }
  file {
    path => "/app/logs/inference/*.log"
    type => "inference"
    codec => "json"
  }
}

filter {
  if [type] == "scheduler" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{DATA:thread}\] %{LOGLEVEL:level} %{DATA:logger} - %{GREEDYDATA:message}" }
    }
  }
  
  if [type] == "inference" {
    grok {
      match => { "message" => "%{TIMESTAMP_ISO8601:timestamp} \[%{LOGLEVEL:level}\] %{DATA:logger}: %{GREEDYDATA:message}" }
    }
  }
  
  date {
    match => [ "timestamp", "yyyy-MM-dd HH:mm:ss,SSS" ]
  }
}

output {
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "cv-logs-%{+YYYY.MM.dd}"
  }
}
```

## 🔧 运维操作

### 1. 启动停止

#### 1.1 Docker Compose 方式

```bash
# 启动所有服务
docker-compose up -d

# 启动特定服务
docker-compose up -d scheduler
docker-compose up -d inference-1

# 停止所有服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f scheduler
docker-compose logs -f inference-1
```

#### 1.2 Kubernetes 方式

```bash
# 部署所有资源
kubectl apply -f k8s/

# 查看部署状态
kubectl get pods -n cv-system
kubectl get services -n cv-system

# 查看日志
kubectl logs -f deployment/scheduler -n cv-system
kubectl logs -f deployment/inference -n cv-system

# 扩缩容
kubectl scale deployment inference --replicas=5 -n cv-system

# 删除部署
kubectl delete -f k8s/
```

### 2. 配置更新

#### 2.1 热更新配置

```bash
# 更新Scheduler配置
kubectl create configmap scheduler-config --from-file=application.yml --dry-run=client -o yaml | kubectl apply -f -
kubectl rollout restart deployment/scheduler -n cv-system

# 更新Inference配置
kubectl create configmap inference-config --from-file=config.yaml --dry-run=client -o yaml | kubectl apply -f -
kubectl rollout restart deployment/inference -n cv-system
```

#### 2.2 数据库迁移

```bash
# 备份数据库
mysqldump -h localhost -u root -p cv_scheduler > backup_$(date +%Y%m%d_%H%M%S).sql

# 执行迁移脚本
mysql -h localhost -u root -p cv_scheduler < migration_v1.1.sql

# 验证迁移结果
mysql -h localhost -u root -p cv_scheduler -e "SHOW TABLES; SELECT COUNT(*) FROM task_allocations;"
```

### 3. 故障排查

#### 3.1 常见问题诊断

```bash
#!/bin/bash
# troubleshoot.sh

echo "=== 系统诊断脚本 ==="

# 检查端口占用
echo "1. 检查端口占用情况:"
netstat -tlnp | grep -E ":(8080|9001|9002|3306|9092)"

# 检查容器状态
echo "2. 检查容器状态:"
docker ps -a | grep -E "(scheduler|inference|mysql|kafka)"

# 检查磁盘空间
echo "3. 检查磁盘空间:"
df -h

# 检查内存使用
echo "4. 检查内存使用:"
free -h

# 检查GPU状态
echo "5. 检查GPU状态:"
nvidia-smi

# 检查网络连通性
echo "6. 检查网络连通性:"
ping -c 3 localhost
curl -s http://localhost:8080/actuator/health | jq .
curl -s http://localhost:9001/health | jq .

# 检查日志错误
echo "7. 检查最近的错误日志:"
tail -n 50 logs/scheduler/scheduler.log | grep -i error
tail -n 50 logs/inference/inference.log | grep -i error
```

#### 3.2 性能调优

```bash
# JVM调优 (Scheduler)
export JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"

# Python调优 (Inference)
export PYTHONOPTIMIZE=1
export OMP_NUM_THREADS=4
export CUDA_VISIBLE_DEVICES=0

# MySQL调优
# my.cnf
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
max_connections = 200
query_cache_size = 128M
```

## 📊 性能基准

### 1. 基准测试

| 指标 | 目标值 | 实际值 | 说明 |
|------|-------|--------|------|
| **任务调度延迟** | < 1s | 0.3s | 从请求到分配完成 |
| **服务注册延迟** | < 500ms | 200ms | 服务注册响应时间 |
| **并发任务数** | 100+ | 150 | 单个Scheduler支持的并发任务 |
| **吞吐量** | 1000 TPS | 1200 TPS | 每秒处理的任务数 |
| **可用性** | 99.9% | 99.95% | 系统可用性 |

### 2. 压力测试

```bash
# 使用Apache Bench进行压力测试
ab -n 1000 -c 10 -H "Content-Type: application/json" -p task_request.json http://localhost:8080/api/v1/scheduler/schedule

# 使用JMeter进行复杂场景测试
jmeter -n -t cv_system_test.jmx -l results.jtl -e -o report/
```

---

## 📞 技术支持

### 1. 故障联系方式

- **紧急故障**: 7x24小时技术支持热线
- **一般问题**: 工作日技术支持邮箱
- **文档更新**: GitHub Issues

### 2. 维护窗口

- **定期维护**: 每周日凌晨2:00-4:00
- **紧急维护**: 提前24小时通知
- **版本升级**: 提前1周通知

---

本文档将根据系统演进持续更新，请关注版本变更通知。
