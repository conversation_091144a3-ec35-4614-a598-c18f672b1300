# CV分析系统 Docker部署指南

本文档介绍如何使用Docker和Docker Compose部署CV分析系统，包括scheduler调度器和inference-mock推理服务。

## 🏗️ 架构概述

本部署方案包含以下组件：
- **cv-scheduler**: CV推理服务调度器
- **cv-inference-mock**: 推理服务模拟器（支持多实例）

**外部依赖**（需要单独部署）：
- **MongoDB**: 数据存储
- **Kafka**: 消息队列
- **Redis**: 缓存（可选，用于分布式锁）

## 📋 前置要求

### 系统要求
- Docker 20.10+
- Docker Compose 2.0+
- Maven 3.6+ (用于构建scheduler)
- 至少4GB可用内存

### 外部服务
在启动CV分析系统之前，请确保以下外部服务已经运行：

1. **MongoDB** (必需)
   ```bash
   # 使用Docker快速启动MongoDB
   docker run -d --name mongodb \
     -p 27017:27017 \
     -e MONGO_INITDB_ROOT_USERNAME=admin \
     -e MONGO_INITDB_ROOT_PASSWORD=password123 \
     mongo:6.0
   ```

2. **Kafka** (必需)
   ```bash
   # 使用Docker快速启动Kafka
   docker run -d --name kafka \
     -p 9092:9092 \
     -e KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181 \
     -e KAFKA_ADVERTISED_LISTENERS=PLAINTEXT://localhost:9092 \
     confluentinc/cp-kafka:latest
   ```

3. **Redis** (可选)
   ```bash
   # 使用Docker快速启动Redis
   docker run -d --name redis \
     -p 6379:6379 \
     redis:7-alpine
   ```

## 🚀 快速开始

### 1. 配置环境变量

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件
vim .env
```

主要配置项：
```bash
# MongoDB配置
MONGODB_URI=*************************************************************************

# Kafka配置
KAFKA_BOOTSTRAP_SERVERS=localhost:9092

# 服务端口
SCHEDULER_PORT=8080
INFERENCE_MOCK_1_PORT=8081
```

### 2. 构建镜像

```bash
# 构建所有镜像
./build.sh

# 或分别构建
./build.sh scheduler
./build.sh inference-mock
```

### 3. 启动服务

```bash
# 启动单实例模式（推荐）
./start.sh

# 或启动多实例模式
./start.sh start-multi

# 查看服务状态
./start.sh status
```

### 4. 验证部署

访问以下地址验证服务：
- Scheduler: http://localhost:8080/actuator/health
- Inference-Mock: http://localhost:8081/health

## 📖 详细使用说明

### 构建脚本 (build.sh)

```bash
# 构建所有镜像
./build.sh

# 只构建scheduler
./build.sh scheduler

# 只构建inference-mock
./build.sh inference-mock
```

### 启动脚本 (start.sh)

```bash
# 启动服务（单实例）
./start.sh start

# 启动多实例
./start.sh start-multi

# 停止服务
./start.sh stop

# 重启服务
./start.sh restart

# 查看状态
./start.sh status

# 查看日志
./start.sh logs

# 构建镜像
./start.sh build
```

### 停止脚本 (stop.sh)

```bash
# 停止服务
./stop.sh

# 停止服务并清理资源
./stop.sh --cleanup
```

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 默认值 | 说明 |
|--------|--------|------|
| `SCHEDULER_PORT` | 8080 | 调度器服务端口 |
| `INFERENCE_MOCK_1_PORT` | 8081 | 推理服务1端口 |
| `INFERENCE_MOCK_2_PORT` | 8082 | 推理服务2端口 |
| `MONGODB_URI` | - | MongoDB连接字符串 |
| `KAFKA_BOOTSTRAP_SERVERS` | localhost:9092 | Kafka服务器地址 |
| `SCHEDULER_LOCK_TYPE` | local | 锁类型(local/redis) |

### 服务配置

#### Scheduler配置
- 端口: 8080
- 健康检查: `/actuator/health`
- API文档: `/swagger-ui.html`

#### Inference-Mock配置
- 端口: 8081, 8082
- 健康检查: `/health`
- 支持两种编排模式: YOLO_TRACKING_CLIP, OVIT_CLIP

## 🔧 故障排除

### 常见问题

1. **镜像构建失败**
   ```bash
   # 检查Maven是否安装
   mvn --version
   
   # 检查Docker是否运行
   docker ps
   ```

2. **服务启动失败**
   ```bash
   # 查看详细日志
   docker-compose logs cv-scheduler
   docker-compose logs cv-inference-mock-1
   ```

3. **外部服务连接失败**
   ```bash
   # 检查MongoDB连接
   docker exec -it mongodb mongo --eval "db.adminCommand('ismaster')"
   
   # 检查Kafka连接
   docker exec -it kafka kafka-topics --bootstrap-server localhost:9092 --list
   ```

### 日志查看

```bash
# 查看所有服务日志
./start.sh logs

# 查看特定服务日志
docker-compose logs cv-scheduler
docker-compose logs cv-inference-mock-1

# 实时跟踪日志
docker-compose logs -f cv-scheduler
```

## 🔄 升级和维护

### 更新镜像

```bash
# 停止服务
./stop.sh

# 重新构建镜像
./build.sh

# 启动服务
./start.sh
```

### 数据备份

```bash
# 备份MongoDB数据
docker exec mongodb mongodump --out /backup

# 备份日志
docker cp cv-scheduler:/app/logs ./backup/scheduler-logs
```

## 📚 API文档

### Scheduler API
- 基础URL: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html
- 健康检查: http://localhost:8080/actuator/health

### Inference-Mock API
- 基础URL: http://localhost:8081
- 健康检查: http://localhost:8081/health

## 🤝 支持

如有问题，请查看：
1. 日志文件: `./start.sh logs`
2. 服务状态: `./start.sh status`
3. 健康检查接口

## 📝 更新日志

- v1.0.0: 初始版本，支持Docker部署
- 支持单实例和多实例部署模式
- 支持外部MongoDB和Kafka配置
