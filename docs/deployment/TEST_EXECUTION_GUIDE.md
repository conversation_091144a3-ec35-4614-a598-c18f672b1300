# 🧪 完整测试执行指南

## 📋 快速验证清单

### **1. 启动所有服务**
```bash
# 启动调度器 (终端1)
cd scheduler
./start.sh --docker-compose

# 启动inference-mock (终端2) 
cd inference-mock
./start.sh --docker-compose

# 等待30秒让服务完全启动
sleep 30
```

### **2. 基础健康检查** ⭐
```bash
# 检查调度器
curl http://localhost:8080/health
# 期望: {"status": "UP", ...}

# 检查inference-mock
curl http://localhost:8081/health  
# 期望: {"status": "UP", "details": {"kafka_connected": true, ...}}
```

### **3. 核心功能验证** ⭐⭐⭐

#### 测试YOLO模式(30秒告警间隔)
```bash
cd test/end-to-end
python test_alert_intervals.py
```
**期望结果**:
- YOLO任务每30秒产生1条告警
- 10分钟内应产生约20条告警

#### 测试OVIT模式(5分钟告警间隔)  
```bash
cd test/end-to-end
python test_direct_orchestration.py
```
**期望结果**:
- OVIT任务每5分钟产生1条告警
- 需要等待5分钟观察第一条告警

### **4. 图片存储验证** ⭐⭐
```bash
# 创建任务后等待1分钟
sleep 60

# 查看截图目录
ls -la inference-mock/screenshots/
# 期望: 看到以task-id_timestamp命名的jpg文件

# 通过HTTP访问截图
curl -I http://localhost:8081/screenshots/direct-yolo-task-001_20241205_143022.jpg
# 期望: HTTP 200 OK
```

### **5. Kafka事件验证** ⭐⭐⭐
```bash
# 启动Kafka消费者 (终端3)
cd test/inference-mock
python test_kafka_consumer.py

# 在消费者终端应该看到:
# ✅ 连接成功到 localhost:9092
# 📨 收到事件: xxx
# 📄 Event Type: HELMET_MISSING (YOLO模式) 或 OBJECT_ABANDONED (OVIT模式)
# 🖼️ Image URI: http://localhost:8081/screenshots/xxx.jpg
```

## 🎯 分步测试流程

### **Step 1: 环境验证** (5分钟)

#### 1.1 Docker环境检查
```bash
docker --version
docker-compose --version
```

#### 1.2 端口占用检查  
```bash
# 检查关键端口是否被占用
netstat -tlnp | grep -E ':(8080|8081|9092|2181)'
# 期望: 无输出或显示docker进程占用
```

#### 1.3 服务启动验证
```bash
# 启动所有服务
cd scheduler && ./start.sh --docker-compose &
cd inference-mock && ./start.sh --docker-compose &

# 等待启动完成
sleep 45

# 检查容器状态
docker ps
# 期望: 看到scheduler、inference-mock、kafka、zookeeper容器都在运行
```

### **Step 2: 服务注册验证** (2分钟)

```bash
# 检查inference-mock是否成功注册到scheduler
curl http://localhost:8080/api/v1/scheduler/services

# 期望响应包含:
# {
#   "services": [
#     {
#       "serviceName": "inference-mock-1",
#       "baseUrl": "http://inference-mock:8081",
#       "status": "HEALTHY"
#     }
#   ]
# }
```

### **Step 3: 任务创建和分配** (3分钟)

#### 3.1 通过scheduler创建YOLO任务
```bash
cd test/end-to-end
python -c "
import requests
task_data = {
    'taskRequest': {
        'taskId': 'yolo-step3-test',
        'taskMeta': {'eventTypeId': 'HELMET_MISSING', 'taskLevel': 'HIGH'},
        'device': {'deviceId': 'camera-step3', 'rtspUrl': 'rtsp://test/stream'},
        'algorithmOrchestration': {
            'orchestrationId': 'orch-step3',
            'orchestrationType': 'YOLO_TRACKING_CLIP',
            'algorithmChain': [{
                'algorithmId': 'yolo-v8',
                'algorithmName': 'YOLO目标检测',
                'algorithmType': 'DETECTION',
                'order': 1
            }]
        }
    }
}
response = requests.post('http://localhost:8080/api/v1/scheduler/schedule', json=task_data)
print(f'Status: {response.status_code}')
print(f'Response: {response.text}')
"
```

#### 3.2 验证任务分配成功
```bash
# 检查scheduler中的任务
curl http://localhost:8080/api/v1/scheduler/tasks

# 检查inference-mock中的任务  
curl http://localhost:8081/api/v1/tasks

# 期望: 两边都能看到 yolo-step3-test 任务
```

### **Step 4: 告警生成验证** (10分钟)

#### 4.1 监控YOLO告警(30秒间隔)
```bash
# 启动监控脚本
cd test/end-to-end
python -c "
import requests
import time
import json

task_id = 'yolo-step3-test'
start_time = time.time()

print('🔍 开始监控YOLO告警生成 (30秒间隔)...')
last_count = 0

for i in range(12):  # 监控6分钟
    try:
        response = requests.get(f'http://localhost:8081/api/v1/tasks/{task_id}')
        if response.status_code == 200:
            data = response.json()
            current_count = data.get('event_count', 0)
            elapsed = int(time.time() - start_time)
            
            if current_count > last_count:
                print(f'✅ [{elapsed}s] 新告警! 总计: {current_count}')
                last_count = current_count
            else:
                print(f'⏳ [{elapsed}s] 等待告警... 当前: {current_count}')
        
        time.sleep(30)  # 每30秒检查一次
    except Exception as e:
        print(f'❌ 检查失败: {e}')

print(f'📊 监控完成，最终告警数: {last_count}')
print(f'📈 预期告警数: {int((time.time() - start_time) / 30)} (每30秒1个)')
"
```

#### 4.2 Kafka消息验证
```bash
# 开新终端启动消费者
cd test/inference-mock  
python test_kafka_consumer.py

# 期望看到:
# - 每30秒收到一条HELMET_MISSING事件
# - 事件包含完整的实体信息和图片链接
# - 图片URI指向实际可访问的截图
```

### **Step 5: OVIT长间隔测试** (可选，15分钟)

#### 5.1 创建OVIT任务
```bash
cd test/end-to-end
python test_direct_orchestration.py
```

#### 5.2 验证5分钟告警间隔
```bash
# 等待并检查告警时间
python -c "
import requests
import time
from datetime import datetime

task_id = 'direct-ovit-task-001'
print(f'⏰ {datetime.now().strftime(\"%H:%M:%S\")} - 开始监控OVIT告警 (5分钟间隔)')

# 检查10次，每分钟一次
for i in range(10):
    try:
        response = requests.get(f'http://localhost:8081/api/v1/tasks/{task_id}')
        if response.status_code == 200:
            data = response.json()
            count = data.get('event_count', 0)
            last_time = data.get('last_event_time', 'None')
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f'⏰ {current_time} - OVIT告警数: {count}, 最后告警: {last_time}')
        
        time.sleep(60)  # 每分钟检查
    except Exception as e:
        print(f'❌ 检查失败: {e}')
"
```

### **Step 6: 图片和HTTP访问验证** (3分钟)

#### 6.1 检查截图文件
```bash
# 查看截图目录内容
ls -la inference-mock/screenshots/
# 期望: 看到多个jpg文件，以taskId_timestamp格式命名

# 检查文件大小和时间戳
ls -lht inference-mock/screenshots/ | head -5
# 期望: 文件大小合理(通常50KB-500KB)，时间戳符合告警间隔
```

#### 6.2 HTTP访问测试
```bash
# 获取最新截图文件名
LATEST_SCREENSHOT=$(ls -t inference-mock/screenshots/*.jpg | head -1 | xargs basename)
echo "最新截图: $LATEST_SCREENSHOT"

# 测试HTTP访问
curl -I "http://localhost:8081/screenshots/$LATEST_SCREENSHOT"
# 期望: HTTP/1.1 200 OK, Content-Type: image/jpeg

# 下载并验证文件
curl -o /tmp/test_screenshot.jpg "http://localhost:8081/screenshots/$LATEST_SCREENSHOT"
file /tmp/test_screenshot.jpg
# 期望: JPEG image data
```

## 🚨 常见问题排查

### **问题1: Kafka连接失败**
```bash
# 检查Kafka容器状态
docker ps | grep kafka

# 查看Kafka日志
docker logs kafka

# 测试Kafka连接
echo "test" | docker exec -i kafka kafka-console-producer.sh --broker-list localhost:9092 --topic test-topic
```

### **问题2: 服务注册失败**
```bash
# 检查inference-mock日志
tail -f inference-mock/logs/inference-mock.log

# 手动测试服务注册
curl -X POST http://localhost:8080/api/v1/scheduler/register \
  -H "Content-Type: application/json" \
  -d '{"serviceName": "test", "baseUrl": "http://localhost:8081"}'
```

### **问题3: 告警间隔不正确**
```bash
# 检查配置文件
cat inference-mock/config.yaml | grep -A 10 orchestration_configs

# 查看任务详细信息
curl http://localhost:8081/api/v1/tasks/your-task-id | jq
```

### **问题4: 图片生成失败**
```bash
# 检查RTSP连接状态
curl http://localhost:8081/api/v1/stats | jq '.task_manager'

# 手动测试视频文件
python -c "
import cv2
cap = cv2.VideoCapture('/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov')
print(f'Video opened: {cap.isOpened()}')
ret, frame = cap.read()
print(f'Frame read: {ret}, Shape: {frame.shape if ret else None}')
cap.release()
"
```

## ✅ 验收标准

### **基础功能** (必须通过)
- [ ] 服务健康检查返回UP状态
- [ ] 服务成功注册到scheduler  
- [ ] 任务创建和查询正常
- [ ] Kafka连接和消息发送成功

### **核心功能** (必须通过)
- [ ] YOLO模式每30秒生成一次告警
- [ ] OVIT模式每5分钟生成一次告警  
- [ ] 每次告警都生成对应的截图文件
- [ ] 截图通过HTTP可以正常访问
- [ ] Kafka事件格式符合AtomicEventInstance协议

### **稳定性** (推荐验证)
- [ ] 连续运行10分钟无崩溃
- [ ] 内存和CPU使用稳定
- [ ] 告警时间精度误差小于5秒
- [ ] 图片文件自动清理功能正常

## 🎯 测试结果评估

### **A级: 优秀** (所有测试通过)
- 告警间隔精确(误差<5秒)
- 图片生成和HTTP访问完美
- Kafka事件格式完全正确
- 服务稳定运行无异常

### **B级: 良好** (核心功能通过)
- 告警间隔基本正确(误差<30秒)
- 图片生成正常，HTTP访问偶尔失败
- Kafka事件基本正确，少量字段问题
- 服务运行基本稳定

### **C级: 可接受** (基础功能通过)
- 告警能够生成，间隔有较大误差
- 图片生成不稳定或格式问题
- Kafka连接正常，事件格式需要调整
- 服务可运行但偶有异常

### **F级: 不合格** (基础功能失败)
- 无法生成告警或告警间隔完全错误
- 图片无法生成或HTTP访问失败
- Kafka连接失败或事件格式错误
- 服务频繁崩溃或无法启动

---

**🎉 完成所有测试后，你就对整个inference-mock服务有了全面的了解！**