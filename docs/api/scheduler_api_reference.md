# CV推理服务调度器 API 接口文档

## 概述

CV推理服务调度器提供 RESTful API 接口，用于管理推理服务和任务调度。

**基础信息：**
- 基础URL: `http://localhost:8080`
- API版本: `v1`
- 内容类型: `application/json`

---

## 1. 任务调度接口

### 1.1 调度任务

**接口描述：** 将任务分配到合适的推理服务

```http
POST /api/v1/scheduler/schedule
Content-Type: application/json
```

**请求参数：**
```json
{
  "taskRequest": {
    "taskId": "yolo-tracking-test-001",
    "taskName": "YOLO跟踪分类测试",
    "taskDescription": "完整的YOLO检测+跟踪+分类任务链",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "HIGH",
      "protocol": "VIDEO",
      "eventTypeId": "helmet_detection_event",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch-yolo-tracking-001",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "person_detection",
          "algorithmName": "人员检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "dependsOn": [],
          "config": {
            "confidence": 0.7,
            "nms_threshold": 0.5
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera-yolo-test-001",
      "deviceName": "YOLO测试摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://localhost:8554/knight",
        "decoderConf": {
          "keyFrameOnly": false,
          "decodeStep": 4
        }
      }
    }
  },
  "config": {},
  "region": "default",
  "priority": 1
}
```

**响应示例：**
```json
{
  "success": true,
  "taskId": "yolo-tracking-test-001",
  "serviceId": "inference-mock-1",
  "serviceUrl": "http://localhost:8081",
  "errorMessage": null,
  "errorCode": null
}
```

**状态码：**
- `200 OK` - 调度成功
- `503 Service Unavailable` - 无可用服务
- `500 Internal Server Error` - 服务器内部错误

### 1.2 释放任务

**接口描述：** 释放已分配的任务资源

```http
POST /api/v1/scheduler/release/{taskId}
```

**路径参数：**
- `taskId` - 任务ID

**响应：**
- `200 OK` - 释放成功
- `404 Not Found` - 任务不存在
- `500 Internal Server Error` - 服务器内部错误

### 1.3 查询服务状态

**接口描述：** 获取所有推理服务的状态信息

```http
GET /api/v1/scheduler/services
```

**查询参数：**
- `region` (可选) - 按区域过滤
- `status` (可选) - 按状态过滤

**响应示例：**
```json
[
  {
    "serviceId": "inference-mock-1",
    "serviceName": "inference-mock-1",
    "baseUrl": "http://localhost:8081",
    "maxQuota": 10,
    "currentQuota": 3,
    "status": "ACTIVE",
    "gpuType": "A10",
    "region": "default",
    "createTime": "2025-07-27T10:00:00",
    "updateTime": "2025-07-27T18:00:00",
    "loadRate": 0.3,
    "availableQuota": 7
  }
]
```

### 1.4 查询单个服务信息

**接口描述：** 获取指定服务的详细信息

```http
GET /api/v1/scheduler/services/{serviceId}
```

**路径参数：**
- `serviceId` - 服务ID

**响应：** 同上面的服务信息格式

### 1.5 查询任务列表

**接口描述：** 批量查询任务信息

```http
POST /api/v1/scheduler/tasks
Content-Type: application/json
```

**请求参数：**
```json
{
  "taskIds": ["yolo-tracking-test-001", "person-detection-002"]
}
```

**请求参数说明：**
- `taskIds` (可选) - 任务ID列表，如果不提供则查询所有任务

**响应示例：**
```json
[
  {
    "taskId": "yolo-tracking-test-001",
    "taskName": "YOLO跟踪分类测试",
    "status": "RUNNING",
    "serviceId": "inference-mock-1",
    "serviceUrl": "http://localhost:8081",
    "region": "default",
    "createTime": "2025-07-27T10:00:00",
    "updateTime": "2025-07-27T18:00:00",
    "deviceId": "camera-yolo-test-001"
  }
]
```

### 1.6 查询特定任务

**接口描述：** 获取指定任务的详细信息

```http
GET /api/v1/scheduler/tasks/{taskId}
```

**路径参数：**
- `taskId` - 任务ID

**响应示例：**
```json
{
  "taskId": "yolo-tracking-test-001",
  "taskName": "YOLO跟踪分类测试",
  "taskDescription": "完整的YOLO检测+跟踪+分类任务链",
  "status": "RUNNING",
  "serviceId": "inference-mock-1",
  "serviceUrl": "http://localhost:8081",
  "region": "default",
  "createTime": "2025-07-27T10:00:00",
  "updateTime": "2025-07-27T18:00:00",
  "taskMeta": {
    "enabled": true,
    "taskLevel": "HIGH",
    "protocol": "VIDEO",
    "eventTypeId": "helmet_detection_event",
    "eventAction": ["ALERT"]
  },
  "algorithmOrchestration": {
    "orchestrationId": "orch-yolo-tracking-001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "algorithmChain": [
      {
        "algorithmId": "person_detection",
        "algorithmName": "人员检测",
        "algorithmType": "DETECTION",
        "order": 1,
        "required": true,
        "dependsOn": [],
        "config": {
          "confidence": 0.7,
          "nms_threshold": 0.5
        }
      }
    ]
  },
  "device": {
    "deviceId": "camera-yolo-test-001",
    "deviceName": "YOLO测试摄像头",
    "streamConfig": {
      "resolution": "1920x1080",
      "frameRate": 25,
      "protocol": "RTSP",
      "url": "rtsp://localhost:8554/knight",
      "decoderConf": {
        "keyFrameOnly": false,
        "decodeStep": 4
      }
    }
  }
}
```

**状态码：**
- `200 OK` - 查询成功
- `404 Not Found` - 任务不存在
- `500 Internal Server Error` - 服务器内部错误

---

## 2. 服务管理接口

### 2.1 注册推理服务

**接口描述：** 注册新的推理服务实例

```http
POST /api/v1/services/register
Content-Type: application/json
```

**请求参数：**
```json
{
  "serviceName": "inference-service-1",
  "baseUrl": "http://*************:8081",
  "gpuType": "A10",
  "region": "beijing",
  "customQuota": 20,
  "tags": {
    "environment": "production",
    "version": "1.0.0"
  },
  "metadata": {
    "description": "生产环境推理服务"
  }
}
```

**响应示例：**
```json
{
  "serviceId": "service-12345",
  "serviceName": "inference-service-1",
  "baseUrl": "http://*************:8081",
  "status": "ACTIVE",
  "gpuType": "A10",
  "region": "beijing",
  "maxQuota": 20,
  "currentQuota": 0,
  "createTime": "2025-07-27T18:00:00",
  "updateTime": "2025-07-27T18:00:00"
}
```

### 2.2 注销推理服务

**接口描述：** 注销指定的推理服务

```http
DELETE /api/v1/services/{serviceId}
```

### 2.3 更新服务状态

**接口描述：** 更新推理服务的状态

```http
PUT /api/v1/services/{serviceId}/status
```

**查询参数：**
- `status` - 新状态 (ACTIVE/INACTIVE/MAINTENANCE)

### 2.4 更新服务配额

**接口描述：** 更新推理服务的最大配额

```http
PUT /api/v1/services/{serviceId}/quota
```

**查询参数：**
- `maxQuota` - 新的最大配额值

### 2.5 服务心跳

**接口描述：** 推理服务发送心跳保持活跃状态

```http
POST /api/v1/services/{serviceId}/heartbeat
```

---

## 3. 监控接口

### 3.1 健康检查

**接口描述：** 检查调度器服务健康状态

```http
GET /api/v1/scheduler/health
```

**响应：**
```json
{
  "status": "OK"
}
```

### 3.2 Spring Boot Actuator 端点

**健康检查：**
```http
GET /actuator/health
```

**应用指标：**
```http
GET /actuator/metrics
```

**Prometheus 指标：**
```http
GET /actuator/prometheus
```

---

## 4. 数据模型

### 4.1 任务相关模型

**SimplifiedAtomicTask:**
```json
{
  "taskId": "string",
  "taskName": "string", 
  "taskDescription": "string",
  "taskMeta": "TaskMeta",
  "algorithmOrchestration": "AlgorithmOrchestration",
  "device": "Device"
}
```

**TaskMeta:**
```json
{
  "enabled": "boolean",
  "taskLevel": "string",
  "protocol": "string", 
  "eventTypeId": "string",
  "eventAction": ["string"]
}
```

**AlgorithmOrchestration:**
```json
{
  "orchestrationId": "string",
  "orchestrationType": "string",
  "algorithmChain": ["Algorithm"]
}
```

**Algorithm:**
```json
{
  "algorithmId": "string",
  "algorithmName": "string",
  "algorithmType": "string",
  "order": "integer",
  "required": "boolean",
  "dependsOn": ["string"],
  "config": "object",
  "alertConfig": "object",
  "trainingConfig": "object"
}
```

**Device:**
```json
{
  "deviceId": "string",
  "deviceName": "string",
  "streamConfig": {
    "resolution": "string",
    "frameRate": "integer",
    "protocol": "string",
    "url": "string",
    "decoderConf": "object"
  }
}
```

### 4.2 服务相关模型

**ServiceInfo:**
```json
{
  "serviceId": "string",
  "serviceName": "string",
  "baseUrl": "string",
  "maxQuota": "integer",
  "currentQuota": "integer",
  "status": "ServiceStatus",
  "gpuType": "GpuType",
  "region": "string",
  "createTime": "datetime",
  "updateTime": "datetime",
  "loadRate": "double",
  "availableQuota": "integer"
}
```

### 4.3 任务状态相关模型

**TaskInfo:**
```json
{
  "taskId": "string",
  "taskName": "string",
  "taskDescription": "string",
  "status": "TaskStatus",
  "serviceId": "string",
  "serviceUrl": "string",
  "region": "string",
  "createTime": "datetime",
  "updateTime": "datetime",
  "taskMeta": "TaskMeta",
  "algorithmOrchestration": "AlgorithmOrchestration",
  "device": "Device"
}
```

**枚举类型：**
- `ServiceStatus`: ACTIVE, INACTIVE, MAINTENANCE
- `GpuType`: A10, A100, V100, T4
- `ScheduleMode`: FILL_FIRST, SPREAD_FIRST
- `TaskStatus`: RUNNING, STOPPED, ERROR

---

## 5. 错误码说明

| 错误码 | 描述 | HTTP状态码 |
|--------|------|-----------|
| `NO_AVAILABLE_SERVICE` | 无可用服务 | 503 |
| `INVALID_REQUEST` | 请求参数无效 | 400 |
| `SERVICE_NOT_FOUND` | 服务不存在 | 404 |
| `TASK_NOT_FOUND` | 任务不存在 | 404 |
| `INTERNAL_ERROR` | 内部服务器错误 | 500 |

---

## 6. 使用示例

### 6.1 完整的任务调度流程

```bash
# 1. 查询可用服务
curl -X GET http://localhost:8080/api/v1/scheduler/services

# 2. 调度任务
curl -X POST http://localhost:8080/api/v1/scheduler/schedule \
  -H "Content-Type: application/json" \
  -d @task_request.json

# 3. 查询任务状态
curl -X GET http://localhost:8080/api/v1/scheduler/tasks/yolo-tracking-test-001

# 4. 释放任务 (使用taskId)
curl -X POST http://localhost:8080/api/v1/scheduler/release/yolo-tracking-test-001
```

### 6.2 任务查询流程

```bash
# 1. 查询所有任务
curl -X POST http://localhost:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d '{}'

# 2. 查询指定任务列表
curl -X POST http://localhost:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskIds": ["yolo-tracking-test-001", "person-detection-002"],
    "status": "RUNNING"
  }'

# 3. 查询特定任务详情
curl -X GET http://localhost:8080/api/v1/scheduler/tasks/yolo-tracking-test-001
```

### 6.3 服务管理流程

```bash
# 1. 注册服务
curl -X POST http://localhost:8080/api/v1/services/register \
  -H "Content-Type: application/json" \
  -d '{
    "serviceName": "inference-service-1",
    "baseUrl": "http://*************:8081", 
    "gpuType": "A10",
    "region": "beijing"
  }'

# 2. 发送心跳
curl -X POST http://localhost:8080/api/v1/services/{serviceId}/heartbeat

# 3. 更新状态
curl -X PUT "http://localhost:8080/api/v1/services/{serviceId}/status?status=MAINTENANCE"
```
