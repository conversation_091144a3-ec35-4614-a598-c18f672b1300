# 分类API接口规范

## 概述

特征提取API提供对图像区域提特征功能。

## 接口设计

### 1. 特征提取接口

#### 接口描述
对图像中指定的边界框区域进行特征提取。

#### 请求方式
```
POST /api/v1/get_feature
```

#### 请求头
```
Content-Type: multipart/form-data
```

#### 请求参数(2种)

<!-- 传输图片文件 -->
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | File | 是 | 输入图像文件 (支持jpg, jpeg, png, bmp, tiff) |
| bboxes | String | 是 | JSON格式的边界框列表，格式: `[{"id": "A", "x": 100, "y": 100, "w": 200, "h": 200}]` |

<!-- 传输图片url -->
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | String | 是 | 输入图像url |
| bboxes | String | 是 | JSON格式的边界框列表，格式: `[{"id": "A", "x": 100, "y": 100, "w": 200, "h": 200}]` |


#### 边界框格式说明
```json
[
  {
    "id": "A",        // 边界框唯一标识
    "x": 100,         // 左上角X坐标
    "y": 100,         // 左上角Y坐标  
    "w": 200,         // 宽度
    "h": 200          // 高度
  },
  {
    "id": "B",
    "x": 300,
    "y": 150,
    "w": 180,
    "h": 220
  }
]
```

#### 响应格式

**成功响应 (200)**
```json
{
  "code": 200,
  "message": "特征提取成功",
  "data": {
    "results": [
      {
        "bbox_id": "A",
        "bbox": {"x": 100, "y": 100, "w": 200, "h": 200},
        "feature": ""
      },
      {
        "bbox_id": "B",
        "bbox": {"x": 300, "y": 150, "w": 180, "h": 220},
        "feature": "",
      }
    ],
  }
}
```

**错误响应**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "边界框格式不正确"
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 413 | 文件大小超过限制 |
| 415 | 不支持的文件格式 |
| 422 | 图像处理失败 |
| 500 | 服务器内部错误 |

## 限制说明

- 图像文件大小限制: 10MB
- 支持的图像格式: jpg, jpeg, png, bmp, tiff
- 边界框数量限制: 最多50个

## 使用示例

### cURL示例
```bash
curl -X POST "http://localhost:8080/api/v1/get_feature" \
  -F "image=@test_image.jpg" \
  -F "bboxes=[{\"id\":\"A\",\"x\":100,\"y\":100,\"w\":200,\"h\":200}]"
```

### Python示例
```python
import requests

url = "http://localhost:8080/api/v1/get_feature"
files = {"image": open("test_image.jpg", "rb")}
data = {
    "bboxes": '[{"id":"A","x":100,"y":100,"w":200,"h":200}]',
}

response = requests.post(url, files=files, data=data)
result = response.json()
```
