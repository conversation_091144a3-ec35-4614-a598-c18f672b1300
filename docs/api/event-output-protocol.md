# Vision Flow 原子事件输出协议

## 协议概述

本协议基于现有的`AtomicEventInstance`数据结构定义算法预警事件的标准化输出格式。该协议旨在：

- 🎯 **保持简洁**：直接基于现有AtomicEventInstance结构
- 🔄 **零破坏性**：完全兼容现有数据结构
- 📊 **标准化**：统一算法事件输出格式
- 🚀 **易于使用**：简化集成和开发工作

## 核心数据结构

### 1. 原子事件实例 (AtomicEventInstance)

```json
{
  "atomicEventInstanceId": "string",
  "eventTypeId": "string",
  "taskId": "string",
  "deviceId": "string",
  "timestamp": 1701425400000,
  "imageUri": "string",
  "entities": [
    {
      "entityInstanceId": "string",
      "entityType": "string",
      "algorithmId": "string",
      "boundingBox": {
        "x": 100,
        "y": 200,
        "width": 150,
        "height": 200
      },
      "partOf": {
        "head": {
          "algorithmId": "person_detection",
          "confidence": 0.95,
          "entities": ["person_001_head"]
        }
      },
      "entityAttributes": {
        "helmet": {
          "attributeName": "helmet",
          "attributeValue": false,
          "confidence": 0.93,
          "algorithmId": "helmet_detection"
        }
      },
      "entityRelationship": {
        "inArea": {
          "confidence": 0.88,
          "entities": ["area_entity_001"],
          "algorithmId": "area_intrusion_detection",
          "attributes": { // 关系扩展字段
          }
        }
      },
      "externalInfo": {
        "trackId": "track_001",
        "quality": 0.92
      }
    }
  ],
  "relationEntities": [
    {
      "entityInstanceId": "area_entity_001",
      "entityType": "Area",
      "algorithmId": "area_intrusion_detection",
      "polygon": {
        "points": [
          {"x": 100, "y": 100},
          {"x": 300, "y": 100},
          {"x": 300, "y": 400},
          {"x": 100, "y": 400}
        ]
      },
      "entityAttributes": {
        "areaType": {
          "attributeName": "areaType",
          "attributeValue": "restricted",
          "confidence": 1.0,
          "algorithmId": "area_intrusion_detection"
        }
      },
      "externalInfo": {}
    }
  ],
  "taskInfo": {
    "taskId": "string",
    "eventTypeId": "string",
    "orchestrationId": "string",
    "orchestrationType": "string",
    "taskLevel": "string",
    "deviceName": "string",
    "frameId": "string",
    "externalInfo": {}
  }
}
```

## 字段说明

### 2.1 AtomicEventInstance 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `atomicEventInstanceId` | string | ✅ | 原子事件实例ID |
| `eventTypeId` | string | ✅ | 事件类型ID（与任务中的定义保持一致） |
| `taskId` | string | ✅ | 产生告警的任务ID |
| `deviceId` | string | ✅ | 设备ID（统一使用deviceId） |
| `timestamp` | long | ✅ | 发生时间戳 |
| `imageUri` | string | ✅ | 图片URI |
| `entities` | array | ✅ | 事件实体列表 |
| `relationEntities` | array | ❌ | 关联实体列表 |
| `taskInfo` | object | ✅ | 任务信息 |

### 2.2 EntityInstance 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `entityInstanceId` | string | ✅ | 实体ID（等价于TrackID） |
| `entityType` | string | ✅ | 实体类型（Person、MotorVehicle、Area、Tripwire等） |
| `algorithmId` | string | ✅ | 产生该实体的算法ID |
| `boundingBox` | object | ❌ | 实体检测框（Area、Tripwire类型不使用此字段） |
| `polygon` | object | ❌ | 多边形区域（仅Area类型使用） |
| `line` | object | ❌ | 线段信息（仅Tripwire类型使用） |
| `partOf` | object | ❌ | 从属关系（如人的头部） |
| `entityAttributes` | object | ❌ | 实体属性 |
| `entityRelationship` | object | ❌ | 实体间关系（如人骑车、人绊线） |
| `externalInfo` | object | ❌ | 扩展信息 |

### 2.3 EntityAttribute 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `attributeName` | string | ✅ | 属性名称 |
| `attributeValue` | any | ✅ | 属性值（可以是boolean、string、number等基础类型） |
| `confidence` | double | ✅ | 属性置信度（统一改为confidence） |
| `algorithmId` | string | ✅ | 生成该属性的算法ID |

**attributeValue 类型说明：**
- `boolean` 类型：如 `helmet: false`、`mask: true`
- `string` 类型：如 `gender: "MALE"`、`color: "red"`、`identity: "张三"`
- `number` 类型：如 `age: 35`、`speed: 60.5`
- `array` 类型：如 `colors: ["red", "blue"]`（多值情况）

### 2.4 PartOf 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `algorithmId` | string | ✅ | 产生该关系信息的算法ID |
| `confidence` | float | ✅ | 关系置信度 |
| `entities` | array | ✅ | 对应实体的ID字符串数组，ID在relationEntities中查找 |

### 2.5 RelationNameValue 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `confidence` | float | ✅ | 关系置信度 |
| `entities` | array | ❌ | 关系中涉及的其他实体ID列表 |
| `algorithmId` | string | ✅ | 生成该关系的算法ID（分类或规则算法） |
| `attributes` | object | ❌ | 关系扩展属性 |

### 2.6 TaskInfo 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `taskId` | string | ✅ | 任务ID |
| `eventTypeId` | string | ✅ | 事件类型ID |
| `orchestrationId` | string | ✅ | 算法编排ID |
| `orchestrationType` | string | ✅ | 算法编排类型 |
| `taskLevel` | string | ✅ | 任务级别 |
| `deviceName` | string | ✅ | 设备名称 |
| `frameId` | string | ✅ | 帧ID |
| `externalInfo` | object | ❌ | 扩展信息字段 |

### 2.7 BoundingBox 字段

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `x` | int | ✅ | 左上角X坐标 |
| `y` | int | ✅ | 左上角Y坐标 |
| `width` | int | ✅ | 宽度 |
| `height` | int | ✅ | 高度 |

### 2.8 Polygon 字段（Area类型专用）

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `points` | array | ✅ | 多边形顶点坐标列表 |

**Point 结构：**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `x` | int | ✅ | X坐标 |
| `y` | int | ✅ | Y坐标 |

### 2.9 Line 字段（Tripwire类型专用）

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `startPoint` | object | ✅ | 起始点坐标 |
| `endPoint` | object | ✅ | 结束点坐标 |
| `direction` | object | ❌ | 方向信息 |

**Direction 结构：**
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `directionType` | string | ✅ | 方向类型（CLOCKWISE、COUNTERCLOCKWISE、BIDIRECTIONAL） |

### 2.10 关系类型说明

**partOf（从属关系）：** 表示实体的组成部分
- 示例：人 partOf 头部、车辆 partOf 车牌
- 用于表示检测目标的细分部位

**entityRelationship（实体关系）：** 表示多个实体之间的交互关系
- **分类算法输出**：人 "骑" 车、人 "穿" 制服等属性关系
- **规则算法输出**：人 "绊" 线、人 "在" 区域内等空间关系
- `entities` 字段包含关系中另一方实体的ID
- `algorithmId` 字段标识生成该关系的算法（CLASSIFICATION或RULE类型）

## 常用实体类型

### 3.1 基础实体类型

| 实体类型 | 说明 | 几何信息 | 常用属性 |
|----------|------|----------|----------|
| `Person` | 人员 | boundingBox | helmet, mask, age, gender, clothing |
| `MotorVehicle` | 机动车 | boundingBox | vehicleType, color, licensePlate, brand |
| `NonMotorVehicle` | 非机动车 | boundingBox | vehicleType, color, riderCount |
| `Face` | 人脸 | boundingBox | age, gender, emotion, identity |
| `Head` | 人头 | boundingBox | helmet, hair, hat |
| `Cyclist` | 骑行者 | boundingBox | helmet, clothing, direction |
| `Rider` | 骑手 | boundingBox | helmet, clothing, vehicle |
| `Area` | 区域 | polygon | areaType, areaName, restricted |
| `Tripwire` | 绊线 | line | tripwireName, direction, sensitivity |

### 3.2 任务级别说明

| 任务级别 | 说明 | 适用场景 |
|----------|------|----------|
| `HIGH` | 高优先级 | 安全帽检测、危险区域入侵 |
| `MEDIUM` | 中优先级 | 口罩检测、一般违规行为 |
| `LOW` | 低优先级 | 人脸识别、统计类任务 |

### 3.3 常用事件类型

| 事件类型 | 说明 | 适用场景 |
|----------|------|----------|
| `HELMET_MISSING` | 未戴安全帽 | 工地、工厂安全检查 |
| `MASK_MISSING` | 未戴口罩 | 疫情防控、医院环境 |
| `AREA_INTRUSION` | 区域入侵 | 禁止区域、敏感区域 |
| `SMOKING` | 吸烟行为 | 禁烟区域、加油站 |
| `WANDERING` | 徘徊行为 | 安防监控、异常行为 |
| `LINE_CROSSING` | 越线行为 | 交通管理、区域管控 |
| `VEHICLE_VIOLATION` | 车辆违规 | 违规停车、逆行 |
| `CROWD_GATHERING` | 人群聚集 | 公共安全、疫情防控 |
| `FIRE_DETECTION` | 火灾检测 | 消防安全、工业监控 |
| `OBJECT_ABANDONED` | 物品遗留 | 安防监控、公共场所 |

## 使用示例

### 4.1 安全帽检测事件

```json
{
  "atomicEventInstanceId": "aei_20241201_103000_001",
  "eventTypeId": "event_type_uuid_001",
  "taskId": "task_001",
  "deviceId": "camera_001",
  "timestamp": 1701425400000,
  "imageUri": "https://storage.example.com/images/scene_001.jpg",
  "entities": [
    {
      "entityInstanceId": "track_001",
      "entityType": "Person",
      "algorithmId": "person_detection",
      "boundingBox": {
        "x": 100,
        "y": 200,
        "width": 150,
        "height": 200
      },
      "partOf": {
        "head": {
          "algorithmId": "person_detection",
          "confidence": 0.95,
          "entities": ["track_001_head"]
        }
      },
      "entityAttributes": {
        "helmet": {
          "attributeName": "helmet",
          "attributeValue": false,
          "confidence": 0.93,
          "algorithmId": "helmet_detection"
        },
        "age": {
          "attributeName": "age",
          "attributeValue": 35,
          "confidence": 0.78,
          "algorithmId": "age_estimation"
        },
        "gender": {
          "attributeName": "gender",
          "attributeValue": "MALE",
          "confidence": 0.89,
          "algorithmId": "gender_classification"
        },
        "isWorker": {
          "attributeName": "isWorker",
          "attributeValue": true,
          "confidence": 0.96,
          "algorithmId": "worker_classification"
        }
      },
      "entityRelationship": {},
      "externalInfo": {
        "trackId": "track_001",
        "quality": 0.92,
        "workerId": "worker_001",
        "department": "construction"
      }
    }
  ],
  "relationEntities": [],
  "taskInfo": {
    "taskId": "task_001",
    "eventTypeId": "event_type_uuid_001",
    "orchestrationId": "orch_001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "taskLevel": "HIGH",
    "deviceName": "工地入口摄像头",
    "frameId": "frame_20241201_103000_001",
    "externalInfo": {
      "taskName": "人员安全帽检测任务",
      "algorithmChain": ["person_detection", "person_tracking", "helmet_detection"],
      "customFields": {
        "workShift": "morning",
        "weatherCondition": "sunny",
        "actionRequired": true,
        "recommendations": ["立即提醒作业人员佩戴安全帽", "暂停相关作业"]
      }
    }
  }
}
```

### 4.2 区域入侵检测事件

```json
{
  "atomicEventInstanceId": "aei_20241201_140000_002",
  "eventTypeId": "event_type_uuid_002",
  "taskId": "task_002",
  "deviceId": "camera_002",
  "timestamp": 1701438000000,
  "imageUri": "https://storage.example.com/images/scene_002.jpg",
  "entities": [
    {
      "entityInstanceId": "track_002",
      "entityType": "Person",
      "algorithmId": "person_detection",
      "boundingBox": {
        "x": 300,
        "y": 150,
        "width": 80,
        "height": 180
      },
      "partOf": {},
      "entityAttributes": {
        "age": {
          "attributeName": "age",
          "attributeValue": 28,
          "confidence": 0.82,
          "algorithmId": "age_estimation"
        },
        "gender": {
          "attributeName": "gender",
          "attributeValue": "FEMALE",
          "confidence": 0.91,
          "algorithmId": "gender_classification"
        },
        "clothing": {
          "attributeName": "clothing",
          "attributeValue": "casual",
          "confidence": 0.85,
          "algorithmId": "clothing_classification"
        },
        "isAuthorized": {
          "attributeName": "isAuthorized",
          "attributeValue": false,
          "confidence": 0.99,
          "algorithmId": "authorization_classification"
        }
      },
      "entityRelationship": {
        "inArea": {
          "confidence": 0.96,
          "entities": ["area_001"],
          "algorithmId": "area_intrusion_detection"
        }
      },
      "externalInfo": {
        "trackId": "track_002",
        "entryTime": 1701438000000,
        "entryPoint": "north_entrance"
      }
    }
  ],
  "relationEntities": [
    {
      "entityInstanceId": "area_001",
      "entityType": "Area",
      "algorithmId": "area_intrusion_detection",
      "polygon": {
        "points": [
          {"x": 250, "y": 100},
          {"x": 450, "y": 100},
          {"x": 450, "y": 400},
          {"x": 250, "y": 400}
        ]
      },
      "entityAttributes": {
        "areaType": {
          "attributeName": "areaType",
          "attributeValue": "restricted",
          "confidence": 1.0,
          "algorithmId": "area_intrusion_detection"
        }
      },
      "externalInfo": {}
    }
  ],
  "taskInfo": {
    "taskId": "task_002",
    "eventTypeId": "event_type_uuid_002",
    "orchestrationId": "orch_002",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "taskLevel": "MEDIUM",
    "deviceName": "厂区围墙摄像头",
    "frameId": "frame_20241201_140000_002",
    "externalInfo": {
      "ruleId": "rule_area_001",
      "ruleName": "禁止区域入侵检测",
      "violationType": "AREA_INTRUSION",
      "algorithmId": "area_intrusion_v1",
      "algorithmName": "区域入侵检测算法",
      "customFields": {
        "regionId": "restricted_area_001",
        "regionName": "禁止入内区域",
        "regionType": "AREA",
        "securityLevel": "high",
        "alertSent": true,
        "actionRequired": true,
        "recommendations": ["发送警告通知", "派遣安保人员"]
      }
    }
  }
}
```

### 4.3 车辆违规检测事件

```json
{
  "atomicEventInstanceId": "aei_20241201_160000_003",
  "atomicEventId": "vehicle_violation_rule_001",
  "deviceId": "camera_003",
  "timestamp": 1701445200000,
  "imageUri": "https://storage.example.com/images/scene_003.jpg",
  "entities": [
    {
      "entityInstanceId": "track_003",
      "entityType": "MotorVehicle",
      "boundingBox": {
        "x": 200,
        "y": 300,
        "width": 180,
        "height": 120
      },
      "partOf": {
        "licensePlate": {
          "name": "licensePlate",
          "value": "track_003_plate",
          "confidence": 0.98,
          "entities": []
        }
      },
      "entityAttributes": {
        "vehicleType": {
          "attributeName": "vehicleType",
          "attributeValue": "car",
          "confidence": 0.96,
          "algorithmId": "vehicle_type_classification"
        },
        "color": {
          "attributeName": "color",
          "attributeValue": "blue",
          "confidence": 0.89,
          "algorithmId": "vehicle_color_classification"
        },
        "licensePlate": {
          "attributeName": "licensePlate",
          "attributeValue": "京A12345",
          "confidence": 0.95,
          "algorithmId": "license_plate_recognition"
        },
        "brand": {
          "attributeName": "brand",
          "attributeValue": "Honda",
          "confidence": 0.72,
          "algorithmId": "vehicle_brand_classification"
        },
        "model": {
          "attributeName": "model",
          "attributeValue": "Civic",
          "confidence": 0.68,
          "algorithmId": "vehicle_model_classification"
        }
      },
      "entityRelationship": {
        "inZone": {
          "name": "inZone",
          "value": "no_parking_zone_001",
          "confidence": 0.94,
          "entities": ["zone_001"]
        }
      },
      "externalInfo": {
        "trackId": "track_003",
        "parkingDuration": 600,
        "entryTime": 1701444600000
      }
    }
  ],
  "relationEntities": [
    {
      "entityInstanceId": "zone_001",
      "entityType": "Zone",
      "boundingBox": {
        "x": 150,
        "y": 250,
        "width": 300,
        "height": 200
      },
      "entityAttributes": {
        "zoneType": {
          "attributeName": "zoneType",
          "attributeValue": "no_parking",
          "confidence": 1.0,
          "algorithmId": "parking_zone_detection"
        }
      },
      "externalInfo": {
        "zoneId": "no_parking_zone_001",
        "zoneName": "禁止停车区域"
      }
    }
  ],
  "externalInfo": {
    "taskId": "task_003",
    "taskName": "违规停车检测任务",
    "eventTypeId": "event_type_uuid_003",
    "orchestrationId": "orch_003",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "taskLevel": "LOW",
    "deviceName": "停车场摄像头",
    "algorithmChain": ["vehicle_detection", "vehicle_tracking", "parking_rule"],
    "frameId": "frame_20241201_160000_003",
    "customFields": {
      "zoneId": "no_parking_zone_001",
      "zoneName": "禁止停车区域",
      "zoneType": "AREA",
      "warningIssued": false,
      "actionRequired": false,
      "recommendations": ["发送提醒通知", "记录违规信息"]
    }
  }
}
```

### 4.4 人脸识别事件

```json
{
  "atomicEventInstanceId": "aei_20241201_180000_004",
  "atomicEventId": "face_recognition_rule_001",
  "deviceId": "camera_004",
  "timestamp": 1701452400000,
  "imageUri": "https://storage.example.com/images/scene_004.jpg",
  "entities": [
    {
      "entityInstanceId": "track_004",
      "entityType": "Person",
      "boundingBox": {
        "x": 150,
        "y": 100,
        "width": 120,
        "height": 200
      },
      "partOf": {
        "face": {
          "name": "face",
          "value": "track_004_face",
          "confidence": 0.97,
          "entities": []
        }
      },
      "entityAttributes": {
        "age": {
          "attributeName": "age",
          "attributeValue": 32,
          "confidence": 0.85,
          "algorithmId": "age_estimation"
        },
        "gender": {
          "attributeName": "gender",
          "attributeValue": "MALE",
          "confidence": 0.92,
          "algorithmId": "gender_classification"
        },
        "identity": {
          "attributeName": "identity",
          "attributeValue": "张三",
          "confidence": 0.88,
          "algorithmId": "face_recognition"
        },
        "employeeId": {
          "attributeName": "employeeId",
          "attributeValue": "EMP_001",
          "confidence": 0.95,
          "algorithmId": "employee_identification"
        }
      },
      "entityRelationship": {
        "belongsTo": {
          "confidence": 0.99,
          "entities": ["dept_001"],
          "algorithmId": "department_classification"
        }
      },
      "externalInfo": {
        "trackId": "track_004",
        "recognitionFeature": "feature_vector_base64",
        "similarity": 0.88
      }
    }
  ],
  "relationEntities": [
    {
      "entityInstanceId": "face_004",
      "entityType": "Face",
      "boundingBox": {
        "x": 180,
        "y": 120,
        "width": 60,
        "height": 80
      },
      "entityAttributes": {
        "quality": {
          "attributeName": "quality",
          "attributeValue": 0.92,
          "confidence": 0.92,
          "algorithmId": "face_quality_assessment"
        },
        "pose": {
          "attributeName": "pose",
          "attributeValue": "frontal",
          "confidence": 0.87,
          "algorithmId": "face_pose_estimation"
        },
        "expression": {
          "attributeName": "expression",
          "attributeValue": "neutral",
          "confidence": 0.91,
          "algorithmId": "expression_recognition"
        }
      },
      "entityRelationship": {
        "belongsTo": {
          "confidence": 0.97,
          "entities": ["track_004"],
          "algorithmId": "face_person_association"
        }
      },
      "externalInfo": {
        "faceFeature": "face_feature_vector_base64"
      }
    }
  ],
  "externalInfo": {
    "taskId": "task_004",
    "taskName": "人脸识别验证任务",
    "eventTypeId": "event_type_uuid_004",
    "orchestrationId": "orch_004",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "taskLevel": "LOW",
    "deviceName": "大厦入口摄像头",
    "algorithmChain": ["face_detection", "face_recognition"],
    "frameId": "frame_20241201_180000_004",
    "customFields": {
      "accessGranted": true,
      "accessLevel": "L2",
      "department": "IT",
      "actionRequired": false,
      "recommendations": ["记录访问日志", "更新访问统计"]
    }
  }
}
```

## 扩展信息规范

### 5.1 externalInfo 标准字段

**TaskInfo.externalInfo 推荐字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `taskName` | string | 任务名称 |
| `algorithmChain` | array | 算法链ID列表 |
| `customFields` | object | 业务自定义字段 |

**EntityInstance.externalInfo 推荐字段：**

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `trackId` | string | 跟踪ID（等价于entityInstanceId） |
| `quality` | double | 目标质量分数 |
| `processingTime` | long | 处理耗时（毫秒） |
| `modelVersion` | string | 模型版本 |
| `customFields` | object | 业务自定义字段 |

**注意：** 去掉了事件级别的 `confidence` 字段，所有属性级别的 `confidence` 都超过阈值后，inference才会输出事件。

### 5.2 常用属性名称

**Person 实体常用属性：**
- `helmet` (boolean) - 是否佩戴安全帽
- `mask` (boolean) - 是否佩戴口罩
- `age` (number) - 年龄
- `gender` (string) - 性别，如 "MALE"、"FEMALE"
- `clothing` (string) - 服装类型，如 "uniform"、"casual"
- `isWorker` (boolean) - 是否为工人
- `isSecurity` (boolean) - 是否为安保人员
- `isAuthorized` (boolean) - 是否被授权
- `identity` (string) - 身份信息，如人名
- `employeeId` (string) - 员工ID

**MotorVehicle 实体常用属性：**
- `vehicleType` (string) - 车辆类型，如 "car"、"truck"、"bus"
- `color` (string) - 车辆颜色，如 "red"、"blue"、"white"
- `licensePlate` (string) - 车牌号，如 "京A12345"
- `brand` (string) - 品牌，如 "Honda"、"Toyota"
- `model` (string) - 型号，如 "Civic"、"Camry"
- `direction` (string) - 行驶方向，如 "north"、"south"
- `speed` (number) - 行驶速度，单位 km/h

**Face 实体常用属性：**
- `quality` (number) - 人脸质量分数，0.0-1.0
- `pose` (string) - 人脸姿态，如 "frontal"、"left"、"right"
- `expression` (string) - 表情，如 "neutral"、"smile"、"frown"
- `emotion` (string) - 情绪，如 "happy"、"sad"、"angry"
- `identity` (string) - 身份，如人名
- `similarity` (number) - 相似度分数，0.0-1.0

### 5.3 常用关系名称

**部分关系 (partOf)：**
- `head` - 头部
- `face` - 人脸
- `licensePlate` - 车牌
- `body` - 身体

**实体关系 (entityRelationship)：**
- `belongsTo` - 属于
- `inArea` - 在区域内
- `inZone` - 在区域内
- `crossLine` - 越过线段
- `near` - 靠近
- `interactWith` - 交互

## 验证规范

### 6.1 必填字段验证

```json
{
  "atomicEventInstanceId": "必填",
  "eventTypeId": "必填（与任务中的定义保持一致）",
  "taskId": "必填（产生告警的任务ID）",
  "deviceId": "必填（统一使用deviceId）",
  "timestamp": "必填",
  "entities": [
    {
      "entityInstanceId": "必填（等价于trackId）",
      "entityType": "必填",
      "algorithmId": "必填（产生该实体的算法ID）",
      "boundingBox": {
        "x": "必填",
        "y": "必填",
        "width": "必填",
        "height": "必填"
      }
    }
  ],
  "taskInfo": {
    "taskId": "必填",
    "eventTypeId": "必填",
    "orchestrationId": "必填",
    "orchestrationType": "必填",
    "taskLevel": "必填",
    "deviceName": "必填",
    "frameId": "必填"
  }
}
```

### 6.2 JSON 解析建议

由于 `attributeValue` 字段可以是不同的基础类型，建议在 JSON 解析时采用以下策略：

**Java 示例：**
```java
// 使用 Object 类型接收，然后根据业务逻辑转换
@JsonProperty("attributeValue")
private Object attributeValue;

// 获取具体类型的方法
public Boolean getBooleanValue() {
    return attributeValue instanceof Boolean ? (Boolean) attributeValue : null;
}

public String getStringValue() {
    return attributeValue instanceof String ? (String) attributeValue : null;
}

public Number getNumberValue() {
    return attributeValue instanceof Number ? (Number) attributeValue : null;
}
```

**JavaScript/TypeScript 示例：**
```typescript
interface EntityAttribute {
  attributeName: string;
  attributeValue: boolean | string | number | any[];
  confidence: number;
  algorithmId: string;
}

// 类型检查
function getTypedValue(attr: EntityAttribute) {
  if (typeof attr.attributeValue === 'boolean') {
    return attr.attributeValue as boolean;
  } else if (typeof attr.attributeValue === 'string') {
    return attr.attributeValue as string;
  } else if (typeof attr.attributeValue === 'number') {
    return attr.attributeValue as number;
  }
  return attr.attributeValue;
}
```

### 6.3 字段一致性要求

- `algorithmId` 必须与任务下发时的 `algorithmId` 保持一致
- `deviceId` 统一使用，不再使用 `cameraId`
- `entityInstanceId` 等价于 `trackId`，用于目标跟踪
- 去掉 `atomicEventId`，改为 `eventTypeId`，与任务中的定义保持一致
- 增加 `taskId` 字段，表示产生告警的任务
- `partOf` 字段不再需要 `name`、`value` 冗余字段，增加 `algorithmId` 字段
- `entities` 字段改为字符串数组，每个字符串表示对应实体的ID
- `entityAttributes` 字段分数统一改为 `confidence`
- `entities` 和 `relationEntities` 均增加 `algorithmId` 字段
- `taskInfo` 增加 `externalInfo` 字段作为扩展

## 7. 快速参考

### 7.1 常用实体类型和属性

| 实体类型 | 常用属性 | 类型 | 示例值 |
|----------|----------|------|--------|
| `Person` | `helmet`, `mask`, `age`, `gender`, `isWorker` | boolean, boolean, number, string, boolean | `false`, `true`, `35`, `"MALE"`, `true` |
| `MotorVehicle` | `vehicleType`, `color`, `licensePlate` | string, string, string | `"car"`, `"red"`, `"京A12345"` |
| `NonMotorVehicle` | `vehicleType`, `riderCount` | string, number | `"bicycle"`, `1` |
| `Face` | `age`, `gender`, `emotion`, `identity` | number, string, string, string | `25`, `"FEMALE"`, `"happy"`, `"person_001"` |

### 7.2 常用关系类型

| 关系名称 | 说明 | 适用场景 |
|----------|------|----------|
| `inArea` | 在区域内 | 区域入侵检测 |
| `crossLine` | 穿越线段 | 绊线检测 |
| `riding` | 骑行关系 | 人骑车检测 |
| `carrying` | 携带关系 | 物品携带检测 |

### 7.3 事件输出时机

- **检测算法**：每帧都可能输出事件
- **分类算法**：当属性值满足告警条件时输出事件
- **规则算法**：当规则被触发时输出事件
- **跟踪算法**：通常不直接输出事件，为其他算法提供trackId

### 7.4 协议变更要点

1. **任务级配置**：`eventTypeId` 和 `taskLevel` 从 `taskMeta` 透传到事件
2. **单设备模式**：任务只绑定单个设备，不再支持多设备
3. **简化告警配置**：去掉 `alertConfig.violationType` 和 `alertConfig.enabled`
4. **规则算法简化**：`area_intrusion_detection` 和 `tripwire` 去掉 `config` 字段
5. **算法编排**：任务创建时直接包含完整的算法编排信息
- 去掉实体级别的 `timestamp`，只保留事件级别时间戳