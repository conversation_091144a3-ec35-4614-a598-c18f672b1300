# Inference-Scheduler API cURL 调用示例

## 📋 概述

本文档提供了 Inference-Scheduler API 的完整 cURL 调用示例，包括所有主要接口的使用方法和实用脚本。

## 🌐 基础信息

- **Scheduler 基础URL**: `http://*************:8080`
- **Inference 基础URL**: `http://*************:9001`
- **内容类型**: `application/json`

---

## 🚀 cURL 调用示例

### 1. 服务管理接口示例

#### 1.1 注册推理服务
```bash
curl -X POST http://*************:8080/api/v1/services/register \
  -H "Content-Type: application/json" \
  -d '{
    "serviceName": "inference-service-1",
    "baseUrl": "http://*************:9001",
    "gpuType": "A10",
    "region": "default",
    "customQuota": 10,
    "tags": {
      "environment": "production",
      "gpu-enabled": "true"
    },
    "metadata": {
      "version": "1.0.0",
      "build": "20241201"
    }
  }'
```

#### 1.2 注销推理服务
```bash
curl -X DELETE http://*************:8080/api/v1/services/svc-123456789
```

#### 1.3 查询服务列表
```bash
# 查询所有服务
curl -X GET http://*************:8080/api/v1/scheduler/services

# 按区域过滤
curl -X GET "http://*************:8080/api/v1/scheduler/services?region=default"

# 按状态过滤
curl -X GET "http://*************:8080/api/v1/scheduler/services?status=ACTIVE"

# 组合过滤
curl -X GET "http://*************:8080/api/v1/scheduler/services?region=default&status=ACTIVE"
```

#### 1.4 查询单个服务信息
```bash
curl -X GET http://*************:8080/api/v1/scheduler/services/svc-123456789
```

#### 1.5 服务心跳
```bash
curl -X POST http://*************:8080/api/v1/services/svc-123456789/heartbeat
```

#### 1.6 手动健康检查
```bash
curl -X POST http://*************:8080/api/v1/services/svc-123456789/health-check
```

### 2. 任务调度接口示例

#### 2.1 调度任务
```bash
curl -X POST http://*************:8080/api/v1/scheduler/schedule \
  -H "Content-Type: application/json" \
  -d '{
    "taskRequest": {
      "taskId": "task-001",
      "taskName": "摄像头监控任务",
      "taskDescription": "商场入口监控区域入侵检测",
      "taskMeta": {
        "enabled": true,
        "taskLevel": "MEDIUM",
        "protocol": "VIDEO",
        "eventTypeId": "zone_intrusion",
        "eventAction": ["ALERT", "RECORD"]
      },
      "algorithmOrchestration": {
        "orchestrationId": "orch-001",
        "orchestrationName": "人员检测跟踪分析",
        "orchestrationType": "YOLO_TRACKING_CLIP",
        "algorithmChain": [
          {
            "algorithmId": "yolov8_detection",
            "algorithmName": "YOLOv8目标检测",
            "algorithmType": "DETECTION",
            "order": 1,
            "required": true,
            "config": {
              "detection_type": "PERSON",
              "threshold": 0.5
            }
          },
          {
            "algorithmId": "byte_tracking",
            "algorithmName": "ByteTracker目标跟踪",
            "algorithmType": "TRACKING",
            "order": 2,
            "required": true
          },
          {
            "algorithmId": "zone_intrusion",
            "algorithmName": "区域入侵检测",
            "algorithmType": "RULE",
            "order": 3,
            "required": true,
            "ruleConfig": {
              "ruleType": "zone_intrusion",
              "polygons": [
                {
                  "polygonId": "zone-001",
                  "polygonName": "禁入区域",
                  "points": [
                    {"x": 100, "y": 100},
                    {"x": 200, "y": 100},
                    {"x": 200, "y": 200},
                    {"x": 100, "y": 200}
                  ],
                  "alarmThresh": 10.0,
                  "reAlarmThresh": 30.0,
                  "enabled": true
                }
              ]
            }
          }
        ]
      },
      "device": {
        "deviceId": "camera-001",
        "deviceName": "商场入口摄像头",
        "streamConfig": {
          "resolution": "1920x1080",
          "frameRate": 25,
          "protocol": "RTSP",
          "url": "rtsp://*************:554/stream1"
        }
      }
    },
    "region": "default",
    "priority": 1,
    "config": {
      "timeout": 300,
      "retryCount": 3
    }
  }'
```

#### 2.2 释放任务
```bash
curl -X POST http://*************:8080/api/v1/scheduler/release/task-001
```

#### 2.3 查询任务信息
```bash
curl -X GET http://*************:8080/api/v1/scheduler/tasks/task-001
```

#### 2.4 批量查询任务
```bash
# 查询所有活跃任务
curl -X POST http://*************:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d '{}'

# 查询指定任务列表
curl -X POST http://*************:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskIds": ["task-001", "task-002"]
  }'
```

### 3. 监控接口示例

#### 3.1 调度器健康检查
```bash
curl -X GET http://*************:8080/api/v1/scheduler/health
```

#### 3.2 系统统计信息
```bash
curl -X GET http://*************:8080/api/v1/scheduler/stats
```

### 4. 推理服务接口示例

#### 4.1 创建任务
```bash
curl -X POST http://*************:9001/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskRequest": {
      "taskId": "task-001",
      "taskName": "摄像头监控任务",
      "taskDescription": "商场入口监控区域入侵检测",
      "taskMeta": {
        "enabled": true,
        "taskLevel": "MEDIUM",
        "protocol": "VIDEO",
        "eventTypeId": "zone_intrusion",
        "eventAction": ["ALERT", "RECORD"]
      },
      "algorithmOrchestration": {
        "orchestrationId": "orch-001",
        "orchestrationName": "人员检测跟踪分析",
        "orchestrationType": "YOLO_TRACKING_CLIP",
        "algorithmChain": [...]
      },
      "device": {
        "deviceId": "camera-001",
        "deviceName": "商场入口摄像头",
        "streamConfig": {
          "resolution": "1920x1080",
          "frameRate": 25,
          "protocol": "RTSP",
          "url": "rtsp://*************:554/stream1",
          "decoderConf": {
            "keyFrameOnly": false,
            "decodeStep": 4
          }
        }
      }
    },
    "region": "default",
    "priority": 1,
    "config": {
      "timeout": 300,
      "retryCount": 3
    }
  }'
```

#### 4.2 查询任务状态
```bash
curl -X GET http://*************:9001/api/v1/tasks/task-001
```

#### 4.3 查询任务列表
```bash
curl -X GET http://*************:9001/api/v1/tasks
```

#### 4.4 删除任务
```bash
curl -X DELETE http://*************:9001/api/v1/tasks/task-001
```

#### 4.5 查询任务事件
```bash
# 基本查询
curl -X GET http://*************:9001/api/v1/tasks/task-001/events

# 带参数查询
curl -X GET "http://*************:9001/api/v1/tasks/task-001/events?limit=20&offset=0&startTime=2024-12-01T10:00:00&endTime=2024-12-01T11:00:00"
```

#### 4.6 健康检查
```bash
curl -X GET http://*************:9001/health
```

#### 4.7 服务统计信息
```bash
curl -X GET http://*************:9001/api/v1/stats
```

#### 4.8 服务信息
```bash
curl -X GET http://*************:9001/
```

### 5. 实用脚本示例

#### 5.1 完整的任务调度流程
```bash
#!/bin/bash

# 1. 检查调度器健康状态
echo "检查调度器健康状态..."
curl -s http://*************:8080/api/v1/scheduler/health | jq .

# 2. 查询可用服务
echo "查询可用服务..."
curl -s http://*************:8080/api/v1/scheduler/services | jq .

# 3. 调度任务
echo "调度任务..."
TASK_RESPONSE=$(curl -s -X POST http://*************:8080/api/v1/scheduler/schedule \
  -H "Content-Type: application/json" \
  -d '{
    "taskRequest": {
      "taskId": "test-task-001",
      "taskName": "测试任务",
      "taskDescription": "API测试任务",
      "taskMeta": {
        "enabled": true,
        "taskLevel": "MEDIUM",
        "protocol": "VIDEO",
        "eventTypeId": "test_event",
        "eventAction": ["ALERT"]
      },
      "algorithmOrchestration": {
        "orchestrationId": "test-orch-001",
        "orchestrationName": "测试编排",
        "orchestrationType": "YOLO_TRACKING_CLIP",
        "algorithmChain": []
      },
      "device": {
        "deviceId": "test-camera-001",
        "deviceName": "测试摄像头",
        "streamConfig": {
          "resolution": "1920x1080",
          "frameRate": 25,
          "protocol": "RTSP",
          "url": "rtsp://*************:554/stream1",
          "decoderConf": {
            "keyFrameOnly": false,
            "decodeStep": 4
          }
        }
      }
    },
    "region": "default",
    "priority": 1
  }')

echo "任务调度结果: $TASK_RESPONSE"

# 4. 查询任务状态
echo "查询任务状态..."
sleep 2
curl -s http://*************:8080/api/v1/scheduler/tasks/test-task-001 | jq .

# 5. 释放任务
echo "释放任务..."
curl -s -X POST http://*************:8080/api/v1/scheduler/release/test-task-001
```

#### 5.2 批量任务管理
```bash
#!/bin/bash

# 批量查询任务状态
TASK_IDS=("task-001" "task-002" "task-003")

echo "批量查询任务状态..."
curl -s -X POST http://*************:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d "{\"taskIds\": [$(printf '"%s",' "${TASK_IDS[@]}" | sed 's/,$//')]}" | jq .
```

#### 5.3 服务监控脚本
```bash
#!/bin/bash

# 服务监控脚本
echo "=== 服务监控报告 ==="

# 1. 调度器健康状态
echo "1. 调度器健康状态:"
SCHEDULER_HEALTH=$(curl -s http://*************:8080/api/v1/scheduler/health)
echo "$SCHEDULER_HEALTH" | jq .

# 2. 系统统计信息
echo "2. 系统统计信息:"
STATS=$(curl -s http://*************:8080/api/v1/scheduler/stats)
echo "$STATS" | jq .

# 3. 活跃服务列表
echo "3. 活跃服务列表:"
SERVICES=$(curl -s "http://*************:8080/api/v1/scheduler/services?status=ACTIVE")
echo "$SERVICES" | jq .

# 4. 活跃任务列表
echo "4. 活跃任务列表:"
TASKS=$(curl -s -X POST http://*************:8080/api/v1/scheduler/tasks \
  -H "Content-Type: application/json" \
  -d '{}')
echo "$TASKS" | jq .

echo "=== 监控报告完成 ==="
```

#### 5.4 快速测试脚本
```bash
#!/bin/bash

# 快速API测试脚本
BASE_URL="http://*************:8080"

echo "=== API 快速测试 ==="

# 测试健康检查
echo "测试健康检查..."
if curl -s -f "$BASE_URL/api/v1/scheduler/health" > /dev/null; then
    echo "✅ 健康检查通过"
else
    echo "❌ 健康检查失败"
    exit 1
fi

# 测试服务列表查询
echo "测试服务列表查询..."
if curl -s -f "$BASE_URL/api/v1/scheduler/services" > /dev/null; then
    echo "✅ 服务列表查询成功"
else
    echo "❌ 服务列表查询失败"
fi

# 测试任务列表查询
echo "测试任务列表查询..."
if curl -s -f -X POST "$BASE_URL/api/v1/scheduler/tasks" \
    -H "Content-Type: application/json" \
    -d '{}' > /dev/null; then
    echo "✅ 任务列表查询成功"
else
    echo "❌ 任务列表查询失败"
fi

echo "=== 测试完成 ==="
```

### 6. 常用命令组合

#### 6.1 查看系统整体状态
```bash
# 一键查看系统状态
curl -s http://*************:8080/api/v1/scheduler/health && \
curl -s http://*************:8080/api/v1/scheduler/stats && \
curl -s http://*************:8080/api/v1/scheduler/services
```

#### 6.2 格式化输出
```bash
# 使用 jq 美化 JSON 输出
curl -s http://*************:8080/api/v1/scheduler/services | jq '.[0]'

# 提取特定字段
curl -s http://*************:8080/api/v1/scheduler/services | jq '.[].serviceName'

# 过滤活跃服务
curl -s http://*************:8080/api/v1/scheduler/services | jq '.[] | select(.status == "ACTIVE")'
```

#### 6.3 错误处理
```bash
# 带错误处理的请求
response=$(curl -s -w "%{http_code}" http://*************:8080/api/v1/scheduler/health)
http_code="${response: -3}"
body="${response%???}"

if [ "$http_code" -eq 200 ]; then
    echo "请求成功: $body"
else
    echo "请求失败，HTTP状态码: $http_code"
fi
```

---

## 📝 使用说明

### 前置要求
- 安装 `curl` 命令行工具
- 安装 `jq` 用于 JSON 格式化（可选）
- 确保服务正在运行

### 环境变量配置
```bash
# 设置环境变量
export SCHEDULER_URL="http://*************:8080"
export INFERENCE_URL="http://*************:9001"

# 使用环境变量
curl -X GET "$SCHEDULER_URL/api/v1/scheduler/health"
```

### 调试技巧
```bash
# 显示详细请求信息
curl -v http://*************:8080/api/v1/scheduler/health

# 只显示 HTTP 状态码
curl -s -o /dev/null -w "%{http_code}" http://*************:8080/api/v1/scheduler/health

# 显示响应时间
curl -s -o /dev/null -w "响应时间: %{time_total}s\n" http://*************:8080/api/v1/scheduler/health
```

---

## 📞 技术支持

如有使用问题，请参考：
- **API 文档**: `docs/api/inference-scheduler-api.md`
- **问题反馈**: 项目 Issues
- **技术交流**: 开发团队
