# Vision Flow 简化版协议设计

## 概述

**当前版本**: v2.0
**更新日期**: 2025-08-03

### v2.0 主要更新
- ✅ **检测类型枚举化**: 支持`PERSON`、`VEHICLE`、`NON_MOTOR_VEHICLE`三种基础检测类型
- ✅ **跟踪算法统一化**: 所有跟踪算法统一使用`algorithmId: "tracking"`
- ✅ **配置方式优化**: 推荐使用`detection_type`替代直接指定`class_ids`
- ✅ **向后兼容**: 保持对原有`class_ids`配置的支持
- ✅ **完整示例**: 新增OVIT万物检测+分类任务完整示例



## 协议结构

### 统一协议 (简化版)

#### 1. 任务创建请求

```json
{
  "taskId": "task_001",
  "taskName": "人员检测任务",
  "taskDescription": "检测未戴安全帽的人员",
  "taskMeta": {
    "enabled": true,
    "taskLevel": "HIGH",
    "protocol": "VIDEO",
    "eventTypeId": "event_type_uuid_001",
    "eventAction": ["ALERT"]
  },
  "algorithmOrchestration": {
    "orchestrationId": "orch_001", // 组合算法ID
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "algorithmChain": [
      {
        "algorithmId": "detection",
        "algorithmName": "目标检测",
        "algorithmType": "DETECTION",
        "order": 1,
        "required": true,
        "config": {
          "confidence": 0.7,
          "nms_threshold": 0.5,
          "detection_type": "PERSON"
        }
      },
      {
        "algorithmId": "tracking",
        "algorithmName": "目标跟踪",
        "algorithmType": "TRACKING",
        "order": 2,
        "required": true,
        "dependsOn": ["detection"],
        "config": {
          "track_thresh": 0.45,
          "track_buffer": 25,
          "match_thresh": 0.8,
          "frame_rate": 30
        }
      },
      {
        "algorithmId": "helmet_detection",
        "algorithmName": "安全帽检测",
        "algorithmType": "CLASSIFICATION_ZERO_SHOT",
        "order": 3,
        "required": true,
        "dependsOn": ["tracking"],
        "alertConfig": {
          "positiveLabels": ["helmet"], // 多个标签的情况下，只要有一个满足便预警
          "negativeLabels": ["no_helmet"], // 多个标签的情况下，只要有一个满足便预警，非必填
          // "labels": ["no_helmet"], // 多个标签的情况下，只要有一个满足便预警
          "confidence": 0.8
        },
        "trainingConfig": {
          "labels": ["no_helmet"],
          "dataCollection": {
            "enabled": true,
            "thresholds": {
              "minConfidence": 0.1,
              "maxConfidence": 0.9
            },
            "samplingRate": 0.1,
            "maxSamplesPerDay": 1000
          },
          "modelVersion": "v1.0"
        }
      },
      {
        "algorithmId": "worker_classification",
        "algorithmName": "工人分类",
        "algorithmType": "CLASSIFICATION",
        "order": 4,
        "required": true,
        "dependsOn": ["tracking"],
        "alertConfig": {
          "labels": ["worker"], // 多个标签的情况下，只要有一个满足便预警
          "confidence": 0.7
        },
        "trainingConfig": {
          "labels": ["worker", "visitor"], // 多个标签的情况下，只要有一个满足便预警
          "dataCollection": {
            "enabled": true,
            "thresholds": {
              "minConfidence": 0.2,
              "maxConfidence": 0.8
            },
            "samplingRate": 0.05,
            "maxSamplesPerDay": 500
          },
          "modelVersion": "v1.0"
        }
      }
    ]
  },
  "device": {
    "deviceId": "camera_001",
    "deviceName": "工地入口摄像头",
    "streamConfig": {
      "resolution": "1920x1080",
      "frameRate": 25,
      "protocol": "RTSP",
      "url": "rtsp://*************:554/stream",
      "decoderConf": {
        "keyFrameOnly": false,
        "decodeStep": 4
      }
    }
  }
}
```

#### 2. 任务创建响应

```json
{
  "code": 200,
  "message": "任务创建成功",
  "data": {
    "taskId": "task_001",
    "status": "CREATED"
  }
}
```

#### 3. 任务停止请求

```json
{
  "taskId": "task_001",
  "action": "STOP"
}
```

#### 4. 任务停止响应

```json
{
  "code": 200,
  "message": "任务停止成功",
  "data": {
    "taskId": "task_001",
    "status": "STOPPED",
    "stopTime": "2024-12-19T11:00:00Z"
  }
}
```

## 核心数据结构

### 1. SimplifiedAtomicTask（简化原子任务）

```java
@Data
public class SimplifiedAtomicTask {
    /** 任务ID */
    private String taskId;
    /** 任务名称 */
    private String taskName;
    /** 任务描述 */
    private String taskDescription;
    /** 任务元信息 */
    private TaskMeta taskMeta;
    /** 算法编排 */
    private AlgorithmOrchestration algorithmOrchestration;
    /** 设备信息 */
    private Device device;
}
```

### 2. AlgorithmOrchestration（算法编排）

```java
@Data
public class AlgorithmOrchestration {
    /** 编排ID */
    private String orchestrationId;
    /** 编排名称 */
    private String orchestrationName;
    /** 编排类型 */
    private OrchestrationType orchestrationType;
    /** 算法链 */
    private List<Algorithm> algorithmChain;
    /** 解码器配置 */
    private DecoderConfig decoderConfig;

    /** 编排状态 */
    private OrchestrationStatus status;
}
```

### 3. Algorithm（算法）

```java
@Data
public class Algorithm {
    /** 算法ID */
    private String algorithmId;
    /** 算法名称 */
    private String algorithmName;
    /** 算法类型 */
    private AlgorithmType algorithmType;
    /** 执行顺序 */
    private Integer order;
    /** 是否必需 */
    private Boolean required;
    /** 依赖的算法ID列表 */
    private List<String> dependsOn;
    /** 算法配置 */
    private Map<String, Object> config;
    /** 告警配置（仅分类算法需要） */
    private AlertConfig alertConfig;
    /** 训练配置（仅分类算法需要） */
    private TrainingConfig trainingConfig;
    /** 规则配置（仅规则类算法需要） */
    private RuleConfig ruleConfig;
}
```

### 4. AlertConfig（告警配置）

```java
@Data
public class AlertConfig {
    /** 告警标签列表 */
    private List<String> labels;
    /** 置信度阈值 */
    private Double confidence;
}
```

### 5. TrainingConfig（训练配置）

```java
@Data
public class TrainingConfig {
    /** 标签列表 */
    private List<String> labels;
    /** 数据收集配置 */
    private DataCollectionConfig dataCollection;
    /** 模型版本 */
    private String modelVersion;
}



@Data
public class DataCollectionConfig {
    /** 是否启用数据收集 */
    private Boolean enabled;
    /** 置信度阈值配置 */
    private ConfidenceThreshold thresholds;
    /** 采样率 */
    private Double samplingRate;
    /** 每日最大样本数 */
    private Integer maxSamplesPerDay;
}

@Data
public class ConfidenceThreshold {
    /** 最小置信度 */
    private Double minConfidence;
    /** 最大置信度 */
    private Double maxConfidence;
}





### 6. TaskMeta（任务元信息）

```java
@Data
public class TaskMeta {
    /** 是否启用 */
    private Boolean enabled;
    /** 任务级别 */
    private String taskLevel;
    /** 协议类型 */
    private String protocol;
    /** 事件类型ID */
    private String eventTypeId;
    /** 事件动作 */
    private List<String> eventAction;
}
```

@Data
public class RuleConfig {
    /** 规则类型 */
    private String ruleType;
    /** 多边形区域配置 */
    private List<Polygon> polygons;
    /** 线段配置 */
    private List<Line> lines;
}

@Data
public class Polygon {
    /** 多边形ID */
    private String polygonId;
    /** 多边形名称 */
    private String polygonName;
    /** 顶点坐标列表 */
    private List<Point> points;
}

@Data
public class Line {
    /** 线段ID */
    private String lineId;
    /** 线段名称 */
    private String lineName;
    /** 起点坐标 */
    private Point startPoint;
    /** 终点坐标 */
    private Point endPoint;
    /** 检测方向 */
    private Direction direction;
}

@Data
public class Point {
    /** X坐标 */
    private Integer x;
    /** Y坐标 */
    private Integer y;
}

@Data
public class Direction {
    /** 方向类型：CLOCKWISE（顺时针）/COUNTERCLOCKWISE（逆时针）/BOTH（双向） */
    private DirectionType directionType;
}

public enum DirectionType {
    CLOCKWISE,        // 顺时针方向（正向）
    COUNTERCLOCKWISE, // 逆时针方向（逆向）
    BOTH             // 双向
}


```

### 6. Device（设备信息）

```java
@Data
public class Device {
    /** 设备ID */
    private String deviceId;
    /** 设备名称 */
    private String deviceName;
    /** 流配置 */
    private StreamConfig streamConfig;
}
```

### 7. StreamConfig（流配置）

```java
@Data
public class StreamConfig {
    /** 分辨率 */
    private String resolution;
    /** 帧率 */
    private Integer frameRate;
    /** 协议类型 */
    private String protocol;
    /** 流地址 */
    private String url;
    /** 解码器配置 */
    private DecoderConf decoderConf;
}

@Data
public class DecoderConf {
    /** 是否只解码关键帧 */
    private Boolean keyFrameOnly;
    /** 解码步长 */
    private Integer decodeStep;
}
```

### 8. DecoderConfig（解码器配置）

```java
@Data
public class DecoderConfig {
    /** 是否只解码关键帧 */
    private Boolean keyFrameOnly;
    /** 解码步长（每隔几帧解码一次） */
    private Integer decodeStep;
}
```



## 算法类型详细说明

### 检测算法 (DETECTION)

#### 算法ID设计原则
- **YOLO检测**: 统一使用`algorithmId: "detection"`，通过`detection_type`配置区分人车非类型
- **OWL/OVIT检测**: 使用特定算法ID（如`"ovit_detection"`），用于万物检测

#### YOLO检测类型
支持三种基础检测类型，通过`detection_type`配置：

#### 人员检测 (PERSON)
- **COCO Class IDs**: [0]
- **适用场景**: 人员监控、安全帽检测、行为分析
- **配置示例**:
```json
{
  "algorithmId": "detection",
  "algorithmName": "目标检测",
  "algorithmType": "DETECTION",
  "config": {
    "detection_type": "PERSON",
    "confidence": 0.7,
    "nms_threshold": 0.5
  }
}
```

#### 车辆检测 (VEHICLE)
- **COCO Class IDs**: [2, 3, 5, 7] (汽车、摩托车、公交车、卡车)
- **适用场景**: 交通监控、违停检测、车辆计数
- **配置示例**:
```json
{
  "algorithmId": "detection",
  "algorithmName": "目标检测",
  "algorithmType": "DETECTION",
  "config": {
    "detection_type": "VEHICLE",
    "confidence": 0.7,
    "nms_threshold": 0.5
  }
}
```

#### 非机动车检测 (NON_MOTOR_VEHICLE)
- **COCO Class IDs**: [1] (自行车)
- **适用场景**: 非机动车道监控、违规检测
- **配置示例**:
```json
{
  "algorithmId": "detection",
  "algorithmName": "目标检测",
  "algorithmType": "DETECTION",
  "config": {
    "detection_type": "NON_MOTOR_VEHICLE",
    "confidence": 0.6,
    "nms_threshold": 0.5
  }
}
```

#### 万物检测 (OWL/OVIT)
- **算法类型**: 开放词汇检测
- **适用场景**: 任意物体检测、长周期监控
- **配置示例**:
```json
{
  "algorithmId": "ovit_detection",
  "algorithmName": "OVIT万物检测",
  "algorithmType": "DETECTION",
  "config": {
    "query_texts": ["person", "bag", "tool"],
    "confidence": 0.15,
    "nms_threshold": 0.3
  }
}
```

### 跟踪算法 (TRACKING)

统一的多目标跟踪算法，支持所有检测类型：

- **算法ID**: 统一使用`"tracking"`
- **支持目标**: 人员、车辆、非机动车等所有检测类型
- **配置示例**:
```json
{
  "algorithmId": "tracking",
  "algorithmType": "TRACKING",
  "config": {
    "track_thresh": 0.45,
    "track_buffer": 25,
    "match_thresh": 0.8,
    "frame_rate": 30
  }
}
```

### 分类算法 (CLASSIFICATION)

基于CLIP的零样本分类或有监督分类：

- **零样本分类**: 无需训练，直接使用文本描述
- **有监督分类**: 基于训练数据的分类模型

### 规则算法 (RULE)

基于几何规则的检测算法：

#### 绊线检测 (TRIPWIRE)
- **功能**: 检测目标穿越指定线段
- **适用场景**: 边界监控、通道计数、方向检测
- **配置示例**:
```json
{
  "algorithmId": "tripwire_detection",
  "algorithmType": "RULE",
  "config": {
    "direction": "both",
    "max_age": 3
  },
  "ruleConfig": {
    "ruleType": "tripwire",
    "lines": [
      {
        "lineId": "tripwire_001",
        "lineName": "主入口绊线",
        "startPoint": {"x": 100, "y": 200},
        "endPoint": {"x": 500, "y": 200},
        "direction": {
          "directionType": "BOTH"
        },       
        "enabled": true
      }
    ]
  }
}
```

#### 区域入侵 (ZONE_INTRUSION)
- **功能**: 检测目标进入指定区域并停留超过阈值时间
- **适用场景**: 禁区监控、安全防护、滞留检测
- **配置示例**:
```json
{
  "algorithmId": "zone_intrusion_detection",
  "algorithmType": "RULE",
  "config": {
    "alarm_thresh": 10,
    "alarm_frames": 200,
    "fps": 25,
    "re_alarm_thresh": 10,
    "re_alarm_frames": 200
  },
  "ruleConfig": {
    "ruleType": "zone_intrusion",
    "polygons": [
      {
        "polygonId": "zone_001",
        "polygonName": "禁入区域",
        "points": [
          {"x": 100, "y": 100},
          {"x": 400, "y": 100},
          {"x": 400, "y": 300},
          {"x": 100, "y": 300}
        ],
        "alarmThresh": 10.0,
        "reAlarmThresh": 30.0,
        "enabled": true
      }
    ]
  }
}
```

**配置参数说明**:
- `alarm_thresh`: 告警时间阈值（秒）
- `alarm_frames`: 告警帧数阈值
- `fps`: 视频帧率
- `re_alarm_thresh`: 重复告警间隔（秒）
- `alarmThresh`: 区域级别的告警阈值
- `reAlarmThresh`: 区域级别的重复告警间隔

## 枚举定义

### 算法类型定义

```java
public enum AlgorithmType {
    DETECTION,      // 检测
    CLASSIFICATION, // 分类
    TRACKING,       // 跟踪
    RULE,           // 规则类算法（绊线、区域入侵等）
    RECOGNITION,    // 识别
    SEGMENTATION,   // 分割
    PREPROCESSING,  // 预处理
    POSTPROCESSING  // 后处理
}
```

### 检测类型定义

```java
public enum DetectionType {
    PERSON,             // 人员检测
    VEHICLE,            // 车辆检测
    NON_MOTOR_VEHICLE   // 非机动车检测
}
```

### 编排类型定义

```java
public enum OrchestrationType {
    YOLO_TRACKING_CLIP,  // YOLO检测 + 跟踪 + CLIP分类 (支持人员、车辆、非机动车)
    OVIT_CLIP           // O-VIT万物检测 + CLIP分类 (开放词汇检测)
}
```

### 任务状态定义

```java
public enum TaskStatus {
    CREATED,    // 已创建
    RUNNING,    // 运行中
    ERROR,      // 错误
    STOPPED     // 已停止
}
```

### 状态转换规则

- `CREATED -> RUNNING`：下发任务
- `RUNNING -> ERROR`：任务执行出错
- `RUNNING -> STOPPED`：任务正常停止
- `ERROR -> STOPPED`：任务出错后手动停止
- `STOPPED -> RUNNING`：任务恢复

## 算法编排模式

### 模式一：YOLO_TRACKING_CLIP

**适用场景：** 需要实时跟踪的检测任务（如安全帽检测、人员行为分析）

**处理流程：**
1. **视频解码**：每隔4帧解码一次（支持跟踪算法）
2. **YOLO检测**：检测人体、车辆、非机动车等目标
3. **ByteTrack跟踪**：对检测目标进行跟踪，生成trackId
4. **CLIP分类**：对跟踪目标进行属性分类（如是否戴安全帽）

**配置示例：**
```json
{
  "orchestrationType": "YOLO_TRACKING_CLIP",
  "decoderConfig": {
    "keyFrameOnly": false,
    "decodeStep": 4
  },
  "algorithmChain": [
    {"algorithmId": "detection", "algorithmType": "DETECTION", "config": {"detection_type": "PERSON"}},
    {"algorithmId": "tracking", "algorithmType": "TRACKING"},
    {"algorithmId": "clip_classification", "algorithmType": "CLASSIFICATION"}
  ]
}
```

### 模式二：OVIT_CLIP

**适用场景：** 长周期监控任务（如物品遗留检测、异常行为检测）

**处理流程：**
1. **视频解码**：每30秒解码一次（降低计算成本）
2. **O-VIT检测**：万物检测算法，识别各种目标
3. **CLIP分类**：对检测目标进行分类和属性判断

**配置示例：**
```json
{
  "orchestrationType": "OVIT_CLIP",
  "decoderConfig": {
    "keyFrameOnly": true,
    "decodeStep": 750
  },
  "algorithmChain": [
    {
      "algorithmId": "ovit_detection",
      "algorithmType": "DETECTION",
      "config": {
        "query_texts": ["person", "bag", "suitcase"],
        "confidence": 0.15,
        "nms_threshold": 0.3
      }
    },
    {
      "algorithmId": "object_classification",
      "algorithmType": "CLASSIFICATION",
      "alertConfig": {
        "positiveLabels": ["遗留物品", "可疑物体"],
        "confidence": 0.6
      }
    }
  ]
}
```

### 规则类算法说明

#### 绊线检测方向定义
- **起点到终点**：定义绊线的基准方向向量
- **CLOCKWISE（顺时针）**：目标从绊线右侧穿越到左侧时触发
- **COUNTERCLOCKWISE（逆时针）**：目标从绊线左侧穿越到右侧时触发
- **BOTH（双向）**：目标从任意方向穿越绊线都会触发

**示例：** 水平绊线（从左到右）
- 起点：(200, 300)，终点：(600, 300)
- CLOCKWISE：目标从上方穿越到下方
- COUNTERCLOCKWISE：目标从下方穿越到上方

## 优势

1. **简化架构**：去掉调度层，统一协议接口
2. **模式化编排**：预定义两种主要算法编排模式
3. **隐含模型类型**：算法类型自动对应模型类型（DETECTION→OVIT，CLASSIFICATION→CLIP）
4. **简化告警逻辑**：每个算法独立配置告警标签和阈值，满足条件自动触发
5. **易于理解**：结构化的算法编排更直观
6. **降低门槛**：减少复杂的依赖关系配置和实现细节暴露
7. **保持功能**：核心检测、分类、识别功能完整保留
8. **算法仓库**：支持远程算法下载和版本管理
9. **训练集成**：内置数据回流和持续训练机制
10. **规则支持**：支持绊线、区域入侵等规则类算法

## 接口示例

### 创建工人安全帽检测任务（多算法编排）
```bash
curl -X POST "http://app-master:9803/api/v1/simplified-tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "task_001",
    "taskName": "工人安全帽检测任务",
    "taskDescription": "检测未戴安全帽的工人",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "HIGH",
      "protocol": "VIDEO",
      "eventTypeId": "event_type_uuid_001",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch_001",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "detection",
          "algorithmName": "目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "config": {
            "confidence": 0.7,
            "nms_threshold": 0.5,
            "detection_type": "PERSON"
          }
        },
        {
          "algorithmId": "tracking",
          "algorithmName": "目标跟踪",
          "algorithmType": "TRACKING",
          "order": 2,
          "required": true,
          "dependsOn": ["detection"],
          "config": {
            "track_thresh": 0.45,
            "track_buffer": 25,
            "match_thresh": 0.8,
            "frame_rate": 30
          }
        },
        {
          "algorithmId": "helmet_detection",
          "algorithmName": "安全帽检测",
          "algorithmType": "CLASSIFICATION",
          "order": 3,
          "required": true,
          "dependsOn": ["tracking"],
          "alertConfig": {
            "labels": ["no_helmet"],
            "confidence": 0.8
          },
          "trainingConfig": {
            "labels": ["no_helmet"],
            "dataCollection": {
              "enabled": true,
              "thresholds": {
                "minConfidence": 0.1,
                "maxConfidence": 0.9
              },
              "samplingRate": 0.1,
              "maxSamplesPerDay": 1000
            },
            "modelVersion": "v1.0"
          }
        },
        {
          "algorithmId": "worker_classification",
          "algorithmName": "工人分类",
          "algorithmType": "CLASSIFICATION",
          "order": 4,
          "required": true,
          "dependsOn": ["tracking"],
          "alertConfig": {
            "labels": ["worker"],
            "confidence": 0.7
          },
          "trainingConfig": {
            "labels": ["worker", "visitor"],
            "dataCollection": {
              "enabled": true,
              "thresholds": {
                "minConfidence": 0.2,
                "maxConfidence": 0.8
              },
              "samplingRate": 0.05,
              "maxSamplesPerDay": 500
            },
            "modelVersion": "v1.0"
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera_001",
      "deviceName": "工地入口摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://*************:554/stream",
        "decoderConf": {
          "keyFrameOnly": false,
          "decodeStep": 4
        }
      }
    }
  }'
```

### 创建绊线检测任务
```bash
curl -X POST "http://app-master:9803/api/v1/simplified-tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "task_002",
    "taskName": "人员绊线检测任务",
    "taskDescription": "检测人员穿越绊线",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "event_type_uuid_002",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch_002",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "detection",
          "algorithmName": "目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "config": {
            "confidence": 0.7,
            "nms_threshold": 0.5,
            "detection_type": "PERSON"
          }
        },
        {
          "algorithmId": "tracking",
          "algorithmName": "目标跟踪",
          "algorithmType": "TRACKING",
          "order": 2,
          "required": true,
          "dependsOn": ["detection"],
          "config": {
            "track_thresh": 0.45,
            "track_buffer": 25,
            "match_thresh": 0.8,
            "frame_rate": 30
          }
        },
        {
          "algorithmId": "tripwire_detection",
          "algorithmName": "绊线检测",
          "algorithmType": "RULE",
          "order": 3,
          "required": true,
          "dependsOn": ["tracking"],
          "ruleConfig": {
            "ruleType": "TRIPWIRE",
            "lines": [
              {
                "lineId": "tripwire_001",
                "lineName": "入口绊线",
                "startPoint": {"x": 200, "y": 300},
                "endPoint": {"x": 600, "y": 300},
                "direction": {
                  "directionType": "CLOCKWISE"
                }
              }
            ]
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera_002",
      "deviceName": "入口监控摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://*************:554/stream",
        "decoderConf": {
          "keyFrameOnly": false,
          "decodeStep": 4
        }
      }
    }
  }'
```

### 示例三：区域入侵检测任务

```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "zone_intrusion_task_001",
    "taskName": "区域入侵检测任务",
    "taskDescription": "检测人员进入禁区并停留超过阈值时间",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "HIGH",
      "protocol": "VIDEO",
      "eventTypeId": "zone_intrusion_event",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch_003",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "detection",
          "algorithmName": "目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "config": {
            "confidence": 0.7,
            "nms_threshold": 0.5,
            "detection_type": "PERSON"
          }
        },
        {
          "algorithmId": "tracking",
          "algorithmName": "目标跟踪",
          "algorithmType": "TRACKING",
          "order": 2,
          "required": true,
          "dependsOn": ["detection"],
          "config": {
            "track_thresh": 0.45,
            "track_buffer": 25,
            "match_thresh": 0.8,
            "frame_rate": 30
          }
        },
        {
          "algorithmId": "zone_intrusion_detection",
          "algorithmName": "区域入侵检测",
          "algorithmType": "RULE",
          "order": 3,
          "required": true,
          "dependsOn": ["tracking"],
          "config": {
            "alarm_thresh": 10,
            "alarm_frames": 200,
            "fps": 25,
            "re_alarm_thresh": 30,
            "re_alarm_frames": 600
          },
          "ruleConfig": {
            "ruleType": "zone_intrusion",
            "polygons": [
              {
                "polygonId": "forbidden_zone_001",
                "polygonName": "设备禁入区域",
                "points": [
                  {"x": 200, "y": 150},
                  {"x": 600, "y": 150},
                  {"x": 600, "y": 400},
                  {"x": 200, "y": 400}
                ],
                "alarmThresh": 10.0,
                "reAlarmThresh": 30.0,
                "enabled": true
              },
              {
                "polygonId": "forbidden_zone_002",
                "polygonName": "危险品存放区",
                "points": [
                  {"x": 700, "y": 200},
                  {"x": 900, "y": 200},
                  {"x": 900, "y": 350},
                  {"x": 700, "y": 350}
                ],
                "alarmThresh": 5.0,
                "reAlarmThresh": 60.0,
                "enabled": true
              }
            ]
          }
        },
        {
          "algorithmId": "intrusion_classification",
          "algorithmName": "入侵行为分类",
          "algorithmType": "CLASSIFICATION",
          "order": 4,
          "required": false,
          "dependsOn": ["zone_intrusion_detection"],
          "alertConfig": {
            "labels": ["unauthorized_intrusion", "security_breach"],
            "positiveLabels": ["非法入侵", "安全违规", "未授权进入"],
            "negativeLabels": ["正常通行", "授权人员"],
            "confidence": 0.6
          },
          "trainingConfig": {
            "labels": ["非法入侵", "安全违规", "正常通行", "授权人员"],
            "dataCollection": {
              "enabled": true,
              "thresholds": {
                "minConfidence": 0.2,
                "maxConfidence": 0.8
              },
              "samplingRate": 0.1,
              "maxSamplesPerDay": 200
            },
            "modelVersion": "v1.0"
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera_zone_003",
      "deviceName": "区域监控摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://*************:554/stream",
        "decoderConf": {
          "keyFrameOnly": false,
          "decodeStep": 4
        }
      }
    }
  }'
```

### 示例四：车辆绊线检测任务

```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "vehicle_tripwire_task_001",
    "taskName": "车辆绊线检测任务",
    "taskDescription": "检测车辆穿越指定绊线",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "vehicle_tripwire_event",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch_004",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "detection",
          "algorithmName": "目标检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "config": {
            "confidence": 0.7,
            "nms_threshold": 0.5,
            "detection_type": "VEHICLE"
          }
        },
        {
          "algorithmId": "tracking",
          "algorithmName": "目标跟踪",
          "algorithmType": "TRACKING",
          "order": 2,
          "required": true,
          "dependsOn": ["detection"],
          "config": {
            "track_thresh": 0.45,
            "track_buffer": 25,
            "match_thresh": 0.8,
            "frame_rate": 30
          }
        },
        {
          "algorithmId": "tripwire_detection",
          "algorithmName": "绊线检测",
          "algorithmType": "RULE",
          "order": 3,
          "required": true,
          "dependsOn": ["tracking"],
          "config": {
            "direction": "both",
            "max_age": 5
          },
          "ruleConfig": {
            "ruleType": "tripwire",
            "lines": [
              {
                "lineId": "vehicle_line_001",
                "lineName": "车辆通行绊线",
                "startPoint": {"x": 200, "y": 300},
                "endPoint": {"x": 600, "y": 300},
                "direction": {
                  "directionType": "BOTH"
                }
                "enabled": true
              }
            ]
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera_vehicle_004",
      "deviceName": "车辆监控摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://*************:554/stream",
        "decoderConf": {
          "keyFrameOnly": false,
          "decodeStep": 4
        }
      }
    }
  }'
```

### 示例五：OVIT万物检测+分类任务

```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskId": "ovit-classification-test-001",
    "taskName": "OVIT万物检测+分类测试",
    "taskDescription": "基于OVIT的万物检测和CLIP分类任务链，适用于长周期监控场景",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "object_abandoned_event",
      "eventAction": ["ALERT"]
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch-ovit-001",
      "orchestrationType": "OVIT_CLIP",
      "algorithmChain": [
        {
          "algorithmId": "ovit_detection",
          "algorithmName": "OVIT万物检测",
          "algorithmType": "DETECTION",
          "order": 1,
          "required": true,
          "dependsOn": [],
          "config": { 	// 应用只关注前面两个配置
            "query_texts": ["person"],
            "confidence": 0.15,
            "nms_threshold": 0.3
          }
        },
        {
          "algorithmId": "object_classification",
          "algorithmName": "物体分类识别",
          "algorithmType": "CLASSIFICATION",
          "order": 2,
          "required": true,
          "dependsOn": ["ovit_detection"],
          "config": {
            "confidence": 0.6
          },
          "alertConfig": {
            "labels": ["abandoned_object", "suspicious_item"],
            "positiveLabels": ["遗留物品", "可疑物体", "无人看管的包"],
            "negativeLabels": ["正常携带", "有人看管"],
            "confidence": 0.6
          },
          "trainingConfig": {
            "labels": ["遗留物品", "可疑物体", "正常携带", "有人看管"],
            "dataCollection": {
              "enabled": true,
              "thresholds": {
                "minConfidence": 0.2,
                "maxConfidence": 0.8
              },
              "samplingRate": 0.05,
              "maxSamplesPerDay": 500
            },
            "modelVersion": "v1.0"
          }
        },
        {
          "algorithmId": "object_classification",
          "algorithmName": "物体分类识别",
          "algorithmType": "CLASSIFICATION_TINY_MODEL",
          "order": 4,
          "required": true,
          "dependsOn": ["object_classification"],
          "config": {
          	"modeId": "用来下载模型",
            "confidence": 0.6
          },
          "alertConfig": {
            "labels": ["abandoned_object", "suspicious_item"],
            "positiveLabels": ["遗留物品", "可疑物体", "无人看管的包"],
            "negativeLabels": ["正常携带", "有人看管"],
            "confidence": 0.6
          },
          "trainingConfig": {
            "labels": ["遗留物品", "可疑物体", "正常携带", "有人看管"],
            "dataCollection": {
              "enabled": true,
              "thresholds": {
                "minConfidence": 0.2,
                "maxConfidence": 0.8
              },
              "samplingRate": 0.05,
              "maxSamplesPerDay": 500
            },
            "modelVersion": "v1.0"
          }
        }
      ]
    },
    "device": {
      "deviceId": "camera-ovit-test-001",
      "deviceName": "OVIT长周期监控摄像头",
      "streamConfig": {
        "resolution": "1920x1080",
        "frameRate": 25,
        "protocol": "RTSP",
        "url": "rtsp://175.168.10.52:9554/knight",
        "decoderConf": {
          "keyFrameOnly": true,
          "decodeStep": 750
        }
      }
    }
  }'
```

**OVIT检测特点**:
- **开放词汇检测**: 可以检测任意文本描述的物体
- **长周期监控**: 适合每隔750帧检测一次的场景
- **灵活配置**: 通过`query_texts`指定检测目标
- **低频检测**: 使用`keyFrameOnly: true`和大的`decodeStep`

## 检测类型配置说明 (v2.0)

### 支持的检测类型

从v2.0版本开始，YOLO检测算法支持通过`detection_type`配置检测目标类型：

#### 配置示例
```json
{
  "algorithmId": "detection",
  "algorithmName": "目标检测",
  "algorithmType": "DETECTION",
  "config": {
    "confidence": 0.7,
    "nms_threshold": 0.5,
    "detection_type": "PERSON"
  }
}
```

#### 支持的检测类型
- **PERSON**: 人员检测 (COCO class_ids: [0])
- **VEHICLE**: 车辆检测 (COCO class_ids: [2,3,5,7] - 汽车、摩托车、公交车、卡车)
- **NON_MOTOR_VEHICLE**: 非机动车检测 (COCO class_ids: [1] - 自行车)

#### 跟踪算法统一化
从v2.0版本开始，所有跟踪算法统一使用`algorithmId: "tracking"`，不再区分具体目标类型：

```json
{
  "algorithmId": "tracking",
  "algorithmName": "目标跟踪",
  "algorithmType": "TRACKING",
  "config": {
    "track_thresh": 0.45,
    "track_buffer": 25,
    "match_thresh": 0.8,
    "frame_rate": 30
  }
}
```

## 总结

本协议设计旨在提供一个简化、统一的视觉分析任务接口，支持多种算法组合和灵活的配置方式。

### v2.0 版本优势

1. **简化配置**: 通过检测类型枚举，简化了YOLO检测的配置方式
2. **统一接口**: 跟踪算法统一化，降低了API复杂度
3. **易于扩展**: 新的检测类型可以轻松添加到枚举中
4. **向后兼容**: 保持对现有配置方式的完全支持
5. **类型安全**: 枚举化配置减少了配置错误的可能性

### 支持的应用场景

#### YOLO_TRACKING_CLIP 模式
- **安防监控**: 人员检测 + 跟踪 + 行为分析
- **交通监控**: 车辆检测 + 跟踪 + 违规检测
- **区域管控**: 绊线检测 + 区域入侵 + 告警分类
- **工业安全**: 安全帽检测 + 人员分类 + 违规告警

#### OVIT_CLIP 模式
- **物品遗留检测**: 万物检测 + 遗留物品分类
- **异常物体监控**: 开放词汇检测 + 可疑物体识别
- **长周期监控**: 低频检测 + 智能分析
- **自定义目标检测**: 任意物体检测 + 零样本分类

通过标准化的数据结构和清晰的接口定义，可以有效支持各种视觉分析场景的需求。