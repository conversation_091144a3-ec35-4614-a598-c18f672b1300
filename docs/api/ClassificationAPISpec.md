# 分类API接口规范

## 概述

分类API提供基于文本描述的图像区域分类功能，支持正例和负例文本标签，并根据阈值和标签优先级进行预警判断。

## 接口设计

### 1. 分类检测接口

#### 接口描述
对图像中指定的边界框区域进行分类，支持多个正例和负例文本标签，根据分类结果和阈值规则进行预警判断。

#### 请求方式
```
POST /api/v1/classify
```

#### 请求头
```
Content-Type: multipart/form-data
```

#### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| image | File | 是 | 输入图像文件 (支持jpg, jpeg, png, bmp, tiff) |
| bboxes | String | 是 | JSON格式的边界框列表，格式: `[{"id": "A", "x": 100, "y": 100, "w": 200, "h": 200}]` |
| positive_texts | String | 是 | JSON格式的正例文本列表，格式: `["红色", "赤色"]` |
| negative_texts | String | 否 | JSON格式的负例文本列表，格式: `["蓝色", "绿色"]`，默认为空 |
| threshold | Float | 否 | 预警阈值，默认0.15，范围[0.0, 1.0] |

#### 边界框格式说明
```json
[
  {
    "id": "A",        // 边界框唯一标识
    "x": 100,         // 左上角X坐标
    "y": 100,         // 左上角Y坐标  
    "w": 200,         // 宽度
    "h": 200          // 高度
  },
  {
    "id": "B",
    "x": 300,
    "y": 150,
    "w": 180,
    "h": 220
  }
]
```

#### 响应格式

**成功响应 (200)**
```json
{
  "code": 200,
  "message": "分类成功",
  "data": {
    "results": [
      {
        "bbox_id": "A",
        "bbox": {"x": 100, "y": 100, "w": 200, "h": 200},
        "classifications": [
          {
            "text": "红色",
            "type": "positive",
            "score": 0.5
          },
          {
            "text": "赤色", 
            "type": "positive",
            "score": 0.4
          },
          {
            "text": "蓝色",
            "type": "negative", 
            "score": 0.1
          }
        ],
        "alert": true,
        "alert_reason": "正例标签'红色'分数0.5超过阈值0.15，且大于最高负例分数0.1"
      },
      {
        "bbox_id": "B",
        "bbox": {"x": 300, "y": 150, "w": 180, "h": 220},
        "classifications": [
          {
            "text": "红色",
            "type": "positive",
            "score": 0.3
          },
          {
            "text": "赤色",
            "type": "positive", 
            "score": 0.2
          },
          {
            "text": "蓝色",
            "type": "negative",
            "score": 0.6
          }
        ],
        "alert": false,
        "alert_reason": "正例标签最高分数0.3未超过负例标签最高分数0.6"
      }
    ],
    "summary": {
      "total_bboxes": 2,
      "alert_count": 1,
      "processing_time": 0.856
    },
    "parameters": {
      "positive_texts": ["红色", "赤色"],
      "negative_texts": ["蓝色"],
      "threshold": 0.15
    }
  }
}
```

**错误响应**
```json
{
  "code": 400,
  "message": "请求参数错误",
  "error": "边界框格式不正确"
}
```

## 预警逻辑规则

### 预警判断条件
一个边界框区域触发预警需要同时满足以下条件：

1. **阈值条件**: 至少有一个正例文本的分类分数 ≥ 设定阈值
2. **优先级条件**: 满足阈值的正例文本分数 > 所有负例文本的最高分数

### 预警逻辑示例

#### 示例1: 触发预警
```
正例: 红色(0.5), 赤色(0.4)
负例: 蓝色(0.1)  
阈值: 0.15
结果: 预警 (红色0.5 ≥ 0.15 且 0.5 > 0.1)
```

#### 示例2: 不触发预警 - 负例分数更高
```
正例: 红色(0.3), 赤色(0.2)
负例: 蓝色(0.6)
阈值: 0.15  
结果: 不预警 (红色0.3 ≥ 0.15 但 0.3 < 0.6)
```

#### 示例3: 不触发预警 - 未达到阈值
```
正例: 红色(0.14), 赤色(0.13)
负例: 蓝色(0.11)
阈值: 0.15
结果: 不预警 (所有正例分数 < 0.15)
```

#### 示例4: 触发预警 - 部分正例达标
```
正例: 红色(0.14), 赤色(0.18)  
负例: 蓝色(0.11)
阈值: 0.15
结果: 预警 (赤色0.18 ≥ 0.15 且 0.18 > 0.11)
```

#### 示例5: 无负例情况
```
正例: 红色(0.25), 赤色(0.18)
负例: 无
阈值: 0.15
结果: 预警 (红色0.25 ≥ 0.15，无负例竞争)
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 413 | 文件大小超过限制 |
| 415 | 不支持的文件格式 |
| 422 | 图像处理失败 |
| 500 | 服务器内部错误 |

## 限制说明

- 图像文件大小限制: 10MB
- 支持的图像格式: jpg, jpeg, png, bmp, tiff
- 边界框数量限制: 最多50个
- 正例文本数量限制: 最多20个
- 负例文本数量限制: 最多20个
- 阈值范围: [0.0, 1.0]

## 使用示例

### cURL示例
```bash
curl -X POST "http://localhost:8080/api/v1/classify" \
  -F "image=@test_image.jpg" \
  -F "bboxes=[{\"id\":\"A\",\"x\":100,\"y\":100,\"w\":200,\"h\":200}]" \
  -F "positive_texts=[\"红色\",\"赤色\"]" \
  -F "negative_texts=[\"蓝色\"]" \
  -F "threshold=0.15"
```

### Python示例
```python
import requests

url = "http://localhost:8080/api/v1/classify"
files = {"image": open("test_image.jpg", "rb")}
data = {
    "bboxes": '[{"id":"A","x":100,"y":100,"w":200,"h":200}]',
    "positive_texts": '["红色","赤色"]',
    "negative_texts": '["蓝色"]', 
    "threshold": 0.15
}

response = requests.post(url, files=files, data=data)
result = response.json()
```
