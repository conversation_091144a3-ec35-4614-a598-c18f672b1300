# 项目文件重组计划

## 🎯 目标
将当前平铺在根目录的文件按功能和类型进行分类整理，提高项目的可维护性和可读性。

## 📁 新的目录结构

```
bxt-analysis-1/
├── README.md                           # 项目主README
├── docker-compose.yml                  # 主docker-compose文件
├── build.sh                           # 主构建脚本
├── start.sh                           # 主启动脚本
├── stop.sh                            # 主停止脚本
│
├── docs/                              # 📚 项目文档
│   ├── api/                           # API文档
│   │   ├── scheduler_api_reference.md
│   │   ├── vision-flow-simplified-protocol.md
│   │   ├── event-output-protocol.md
│   │   └── ClassificationAPISpec.md
│   ├── deployment/                    # 部署文档
│   │   ├── DOCKER_DEPLOYMENT.md
│   │   └── TEST_EXECUTION_GUIDE.md
│   ├── development/                   # 开发文档
│   │   ├── interface-review-issues.md
│   │   ├── scheduler_tech.md
│   │   └── training-inference-integration.md
│   ├── features/                      # 功能特性文档
│   │   ├── FeatureExtractionSpec.md
│   │   ├── ModelTrainingSpec.md
│   │   └── 分类逻辑/
│   └── upgrades/                      # 升级说明文档
│       ├── DETECTION_TYPE_UPGRADE_README.md
│       ├── ALGORITHM_ID_UNIFICATION_STATUS.md
│       ├── FINAL_SUMMARY.md
│       └── IMPLEMENTATION_SUMMARY.md
│
├── examples/                          # 🧪 示例和测试用例
│   ├── requests/                      # API请求示例
│   │   ├── yolo/                      # YOLO相关请求
│   │   │   ├── yolo_tracking_task_request.json
│   │   │   ├── yolo_tripwire_classification_task_request.json
│   │   │   ├── yolo_zone_intrusion_classification_task_request.json
│   │   │   ├── yolo_vehicle_tripwire_classification_task_request.json
│   │   │   └── yolo_bicycle_zone_intrusion_classification_task_request.json
│   │   ├── ovit/                      # OVIT相关请求
│   │   │   └── ovit_classification_task_request.json
│   │   └── scheduler/                 # Scheduler相关请求
│   │       ├── test-schedule-request.json
│   │       ├── test-service-registration.json
│   │       └── test-simplified-task-request.json
│   ├── scripts/                       # 测试脚本
│   │   ├── yolo_tracking_curl_request.sh
│   │   ├── integration_test.py
│   │   ├── test_kafka_connection.py
│   │   └── test_kafka_simple.py
│   └── data/                          # 测试数据
│       ├── video/
│       │   └── knight.mov
│       └── extra-task.json
│
├── services/                          # 🚀 微服务
│   ├── scheduler/                     # 调度服务
│   ├── inference/                     # 推理服务
│   ├── inference-mock/                # 模拟推理服务
│   └── rtsp-server/                   # RTSP服务器
│
├── infrastructure/                    # 🏗️ 基础设施
│   ├── kafka/                         # Kafka配置
│   ├── mongo-init/                    # MongoDB初始化
│   └── logs/                          # 日志目录
│
└── tools/                            # 🔧 工具和分析
    ├── kafka_consumer.py
    └── analysis/
        └── KAFKA_CONFIGURATION_ANALYSIS.md
```

## 🔄 文件移动计划

### 1. 文档整理
- 将 `doc/` 重命名为 `docs/`
- 按功能分类移动文档到子目录

### 2. 示例和测试用例整理
- 创建 `examples/` 目录
- 按类型分类移动请求文件和测试脚本

### 3. 服务目录整理
- 将现有服务移动到 `services/` 下
- 保持各服务内部结构不变

### 4. 基础设施整理
- 将基础设施相关文件移动到 `infrastructure/`

### 5. 工具整理
- 将独立的工具脚本移动到 `tools/`

## ✅ 优势

1. **清晰的分类**：按功能和用途分类，易于查找
2. **标准化结构**：符合现代项目组织规范
3. **易于维护**：相关文件集中管理
4. **新人友好**：目录结构一目了然
5. **CI/CD友好**：便于自动化脚本处理

## 📋 执行步骤

1. 创建新的目录结构
2. 移动文件到对应目录
3. 更新相关引用路径
4. 更新README和文档
5. 验证所有服务正常运行
