# CV推理服务调度器详细设计

## 1. 概述

### 1.1 项目背景
CV推理服务调度器负责管理多个推理服务实例，根据设备资源和调度策略，将视频流任务合理分配到不同的推理服务上，确保系统资源的高效利用。

### 1.2 核心功能
- **服务管理**：管理多个推理服务实例的配置和状态
- **资源调度**：根据quota限制和调度策略分配任务
- **负载均衡**：支持优先沾满和优先平铺两种调度模式
- **健康检查**：监控推理服务的健康状态和任务执行状态
- **任务管理**：跟踪任务分配、下发和执行状态
- **异常恢复**：检测任务异常并自动重新下发

### 1.3 技术栈
- **Java 11**：稳定的LTS版本
- **Spring Boot 2.7**：微服务框架，提供依赖注入和自动配置
- **Maven 3.9**：项目构建和依赖管理
- **本地锁**：单例部署模式下的内存锁（可选Redis分布式锁）
- **MongoDB 8.0**：持久化存储

---

## 2. 系统架构

### 2.1 整体架构图

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   App Master    │    │   Scheduler     │    │ Inference Service│
│                 │    │                 │    │                 │
│ ┌─────────────┐ │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ │Task Manager │ │───▶│ │Schedule Core│ │───▶│ │ GPU Service │ │
│ └─────────────┘ │    │ └─────────────┘ │    │ └─────────────┘ │
│                 │    │ ┌─────────────┐ │    │ ┌─────────────┐ │
│ ┌─────────────┐ │    │ │Service Mgmt │ │    │ │Model Engine │ │
│ │Device Mgmt  │ │    │ └─────────────┘ │    │ └─────────────┘ │
│ └─────────────┘ │    │ ┌─────────────┐ │    │                 │
└─────────────────┘    │ │Health Check │ │    └─────────────────┘
                       │ └─────────────┘ │
                       └─────────────────┘
```

### 2.2 核心组件

#### 2.2.1 调度核心 (ScheduleCore)
- 任务调度算法实现
- 资源分配策略
- 负载均衡逻辑

#### 2.2.2 服务管理 (ServiceManager)
- 推理服务注册和发现
- 服务配置管理
- 服务状态维护

#### 2.2.3 健康检查 (HealthChecker)
- 服务健康状态监控
- 故障检测和恢复
- 服务可用性评估

---

## 3. 数据模型设计

### 3.1 推理服务配置

```java
@Document(collection = "inference_service")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InferenceService {
    @Id
    private String serviceId;

    @Field("service_name")
    @Indexed
    private String serviceName;

    @Field("base_url")
    private String baseUrl;

    @Field("max_quota")
    private Integer maxQuota;

    @Field("current_quota")
    private Integer currentQuota;

    @Field("status")
    @Indexed
    private ServiceStatus status;

    @Field("gpu_type")
    private GpuType gpuType;

    @Field("region")
    @Indexed
    private String region;

    @Field("create_time")
    @CreatedDate
    private LocalDateTime createTime;

    @Field("update_time")
    @LastModifiedDate
    private LocalDateTime updateTime;

    @Field("tags")
    private Map<String, String> tags;

    @Field("metadata")
    private Map<String, Object> metadata;
}

public enum ServiceStatus {
    ACTIVE,     // 服务正常
    INACTIVE,   // 服务不可用
    MAINTENANCE // 维护中
}

public enum GpuType {
    A10,        // 30路
    A100,       // 50路
    V100,       // 20路
    T4          // 10路
}
```

### 3.2 任务分配记录

```java
@Document(collection = "task_allocation")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAllocation {
    @Id
    private String allocationId;

    @Field("task_id")
    @Indexed
    private String taskId;

    @Field("device_id")
    @Indexed
    private String deviceId;

    @Field("service_id")
    @Indexed
    private String serviceId;

    @Field("task_status")
    @Indexed
    private TaskStatus taskStatus;

    @Field("allocate_time")
    @CreatedDate
    private LocalDateTime allocateTime;

    @Field("start_time")
    private LocalDateTime startTime;

    @Field("end_time")
    private LocalDateTime endTime;

    @Field("task_request")
    private SimplifiedAtomicTask taskRequest;

    @Field("execution_metrics")
    private ExecutionMetrics executionMetrics;
}

public enum TaskStatus {
    ALLOCATED,  // 已分配
    RUNNING,    // 运行中
    COMPLETED,  // 已完成
    FAILED,     // 失败
    CANCELLED   // 已取消
}
```

### 3.3 调度策略配置

```java
@Document(collection = "schedule_strategy")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleStrategy {
    @Id
    private String strategyId;

    @Field("schedule_mode")
    private ScheduleMode scheduleMode;

    @Field("enable_dynamic_schedule")
    private Boolean enableDynamicSchedule;

    @Field("rebalance_threshold")
    private Integer rebalanceThreshold;

    @Field("health_check_interval")
    private Integer healthCheckInterval;

    @Field("create_time")
    @CreatedDate
    private LocalDateTime createTime;

    @Field("config")
    private Map<String, Object> config;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionMetrics {
    private Long processingTime;
    private Integer frameCount;
    private Double avgFps;
    private Map<String, Object> algorithmMetrics;
}

public enum ScheduleMode {
    FILL_FIRST,     // 优先沾满
    SPREAD_FIRST    // 优先平铺
}

### 3.4 简化原子任务模型

```java
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedAtomicTask {
    /** 任务ID */
    private String taskId;
    /** 任务名称 */
    private String taskName;
    /** 任务描述 */
    private String taskDescription;
    /** 任务元信息 */
    private TaskMeta taskMeta;
    /** 算法编排 */
    private AlgorithmOrchestration algorithmOrchestration;
    /** 设备信息 */
    private Device device;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskMeta {
    /** 是否启用 */
    private Boolean enabled;
    /** 任务级别 */
    private String taskLevel;
    /** 协议类型 */
    private String protocol;
    /** 事件类型ID */
    private String eventTypeId;
    /** 事件动作 */
    private List<String> eventAction;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmOrchestration {
    /** 编排ID */
    private String orchestrationId;
    /** 编排类型 */
    private String orchestrationType;
    /** 算法链 */
    private List<Algorithm> algorithmChain;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Algorithm {
    /** 算法ID */
    private String algorithmId;
    /** 算法名称 */
    private String algorithmName;
    /** 算法类型 */
    private String algorithmType;
    /** 执行顺序 */
    private Integer order;
    /** 是否必需 */
    private Boolean required;
    /** 依赖的算法ID列表 */
    private List<String> dependsOn;
    /** 算法配置 */
    private Map<String, Object> config;
    /** 告警配置（仅分类算法需要） */
    private AlertConfig alertConfig;
    /** 训练配置（仅分类算法需要） */
    private TrainingConfig trainingConfig;
    /** 规则配置（仅规则类算法需要） */
    private RuleConfig ruleConfig;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertConfig {
    /** 告警标签列表（兼容旧版本） */
    private List<String> labels;
    /** 置信度阈值 */
    private Double confidence;
    /** 零样本分类正例标签 */
    private List<String> positiveLabels;
    /** 零样本分类负例标签 */
    private List<String> negativeLabels;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrainingConfig {
    /** 标签列表 */
    private List<String> labels;
    /** 数据收集配置 */
    private DataCollectionConfig dataCollection;
    /** 模型版本 */
    private String modelVersion;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCollectionConfig {
    /** 是否启用数据收集 */
    private Boolean enabled;
    /** 置信度阈值配置 */
    private ConfidenceThreshold thresholds;
    /** 采样率 */
    private Double samplingRate;
    /** 每日最大样本数 */
    private Integer maxSamplesPerDay;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ConfidenceThreshold {
    /** 最小置信度 */
    private Double minConfidence;
    /** 最大置信度 */
    private Double maxConfidence;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleConfig {
    /** 规则类型 */
    private String ruleType;
    /** 多边形区域配置 */
    private List<Polygon> polygons;
    /** 线段配置 */
    private List<Line> lines;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Device {
    /** 设备ID */
    private String deviceId;
    /** 设备名称 */
    private String deviceName;
    /** 流配置 */
    private StreamConfig streamConfig;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamConfig {
    /** 分辨率 */
    private String resolution;
    /** 帧率 */
    private Integer frameRate;
    /** 协议类型 */
    private String protocol;
    /** 流地址 */
    private String url;
    /** 解码器配置 */
    private DecoderConf decoderConf;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DecoderConf {
    /** 是否只解码关键帧 */
    private Boolean keyFrameOnly;
    /** 解码步长 */
    private Integer decodeStep;
}
```
```

---

## 4. 核心业务逻辑

### 4.1 调度服务接口

```java
### 3.4 Repository接口

```java
@Repository
public interface InferenceServiceRepository extends MongoRepository<InferenceService, String> {

    List<InferenceService> findByStatus(ServiceStatus status);

    List<InferenceService> findByRegion(String region);

    List<InferenceService> findByGpuType(GpuType gpuType);

    @Query("{'status': ?0, 'currentQuota': {$lt: '$maxQuota'}}")
    List<InferenceService> findAvailableServices(ServiceStatus status);

    @Query("{'region': ?0, 'status': ?1}")
    List<InferenceService> findByRegionAndStatus(String region, ServiceStatus status);
}

@Repository
public interface TaskAllocationRepository extends MongoRepository<TaskAllocation, String> {

    List<TaskAllocation> findByServiceId(String serviceId);

    List<TaskAllocation> findByTaskStatus(TaskStatus taskStatus);

    List<TaskAllocation> findByDeviceId(String deviceId);

    @Query("{'serviceId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
    List<TaskAllocation> findActiveTasksByServiceId(String serviceId);

    @Query("{'allocateTime': {$gte: ?0, $lte: ?1}}")
    List<TaskAllocation> findByAllocateTimeBetween(LocalDateTime start, LocalDateTime end);
}

@Repository
public interface ScheduleStrategyRepository extends MongoRepository<ScheduleStrategy, String> {

    Optional<ScheduleStrategy> findByScheduleMode(ScheduleMode scheduleMode);

    @Query("{'enableDynamicSchedule': true}")
    List<ScheduleStrategy> findDynamicStrategies();
}
```

---

## 4. 核心业务逻辑

### 4.1 任务调度和下发流程

#### 4.1.1 任务调度核心逻辑

**调度流程：**
1. **任务接收**：接收来自App Master的任务调度请求
2. **服务选择**：根据调度策略选择合适的推理服务
3. **资源分配**：使用分布式锁确保资源分配的原子性
4. **任务下发**：将任务下发到选中的推理服务
5. **状态跟踪**：记录任务分配状态并持久化

**关键实现要点：**
- 使用本地锁（单例模式）或Redis锁（分布式模式）保证并发安全
- 任务分配成功后立即下发到推理服务
- 记录任务分配关系用于后续监控和恢复

#### 4.1.2 任务下发机制

**下发策略：**
- 任务调度成功后，立即调用推理服务的任务创建接口
- 下发失败时，回滚资源分配并抛出异常
- 支持同步下发和异步重试机制

**下发内容：**
- 完整的任务配置（包含算法编排、设备信息、任务元数据）
- 推理服务需要的所有参数
- 任务优先级和超时配置

### 4.2 任务监控和异常恢复

#### 4.2.1 任务状态监控机制

**监控策略：**
- 推理服务不做任务持久化，所有任务状态由调度器维护
- 调度器定时查询推理服务的任务状态接口
- 通过任务状态对比检测异常情况

**监控频率：**
- 正常任务：每60秒检查一次
- 异常任务：每30秒检查一次
- 新下发任务：首次检查延迟10秒

**监控内容：**
- 任务是否存在于推理服务中
- 任务运行状态是否正常
- 推理服务健康状态
- 任务执行时长和性能指标

#### 4.2.2 异常检测和恢复

**异常情况识别：**
1. **任务丢失**：调度器记录中存在，但推理服务中不存在
2. **任务状态异常**：推理服务中任务状态为ERROR或STOPPED
3. **服务不可达**：推理服务健康检查失败
4. **任务超时**：任务执行时间超过预期阈值

**恢复策略：**
1. **删除异常任务**：先调用推理服务删除异常任务（如果存在）
2. **重新调度**：将任务重新加入调度队列
3. **服务降级**：标记异常服务为不可用，避免继续分配
4. **重试限制**：单个任务最多重试3次，超过后标记为失败

**恢复流程：**
```
异常检测 → 任务清理 → 资源回收 → 重新调度 → 状态更新
```

    /**
     * 根据策略选择服务
     */
    private InferenceService selectService(List<InferenceService> services,
                                         ScheduleRequest request,
                                         ScheduleStrategy strategy) {
        return switch (strategy.getScheduleMode()) {
            case FILL_FIRST -> selectByFillFirst(services);
            case SPREAD_FIRST -> selectBySpreadFirst(services);
        };
    }

    /**
     * 优先沾满策略：优先选择已有任务较多的服务
     */
    private InferenceService selectByFillFirst(List<InferenceService> services) {
        return services.stream()
            .filter(service -> service.getCurrentQuota() < service.getMaxQuota())
            .max(Comparator.comparing(InferenceService::getCurrentQuota))
            .orElseThrow(() -> new NoAvailableServiceException("No available service"));
    }

    /**
     * 优先平铺策略：优先选择负载最轻的服务
     */
    private InferenceService selectBySpreadFirst(List<InferenceService> services) {
        return services.stream()
            .filter(service -> service.getCurrentQuota() < service.getMaxQuota())
            .min(Comparator.comparing(InferenceService::getCurrentQuota))
            .orElseThrow(() -> new NoAvailableServiceException("No available service"));
    }
}
```

### 4.2 服务管理

```java
@Service
@Slf4j
public class ServiceManager {

    @Autowired
    private InferenceServiceRepository serviceRepository;

    @Autowired
    private RestTemplate restTemplate;

    /**
     * 注册推理服务
     */
    public void registerService(ServiceRegistration registration) {
        InferenceService service = InferenceService.builder()
            .serviceId(generateServiceId())
            .serviceName(registration.getServiceName())
            .baseUrl(registration.getBaseUrl())
            .maxQuota(getQuotaByGpuType(registration.getGpuType()))
            .currentQuota(0)
            .status(ServiceStatus.ACTIVE)
            .gpuType(registration.getGpuType())
            .region(registration.getRegion())
            .build();

        serviceRepository.save(service);
        log.info("Service registered: {}", service.getServiceId());
    }

    /**
     * 根据GPU类型获取默认quota
     */
    private Integer getQuotaByGpuType(GpuType gpuType) {
        return switch (gpuType) {
            case A10 -> 30;
            case A100 -> 50;
            case V100 -> 20;
            case T4 -> 10;
        };
    }

    /**
     * 更新服务状态
     */
    public void updateServiceStatus(String serviceId, ServiceStatus status) {
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new NotFoundException("Service not found"));

        service.setStatus(status);
        serviceRepository.save(service);

        log.info("Service {} status updated to {}", serviceId, status);
    }
}
```

### 4.3 健康检查和任务监控服务

#### 4.3.1 双重检查机制

**服务健康检查：**
- 检查推理服务的 `/health` 接口
- 验证服务基本可用性
- 更新服务状态（ACTIVE/INACTIVE）

**任务状态监控：**
- 查询推理服务的 `/api/v1/tasks/{taskId}` 接口
- 验证任务是否正常运行
- 检测任务异常并触发恢复

#### 4.3.2 监控实现逻辑

**健康检查流程：**
1. 定时扫描所有ACTIVE状态的推理服务
2. 调用服务健康检查接口
3. 根据响应更新服务状态
4. 记录检查结果和异常信息

**任务监控流程：**
1. 定时扫描所有RUNNING状态的任务分配记录
2. 调用推理服务查询对应任务状态
3. 对比调度器记录与推理服务实际状态
4. 检测到异常时触发恢复流程

**监控频率配置：**
- 服务健康检查：每30秒一次
- 任务状态监控：每60秒一次
- 异常任务重试：每30秒一次

#### 4.3.3 异常恢复实现

**任务异常检测：**
1. 调用推理服务 `GET /api/v1/tasks/{taskId}` 接口
2. 如果返回404，说明任务在推理服务中不存在
3. 如果返回任务状态为ERROR或STOPPED，说明任务异常
4. 如果调用失败，说明推理服务不可达

**恢复操作步骤：**
1. **清理异常任务**：调用 `DELETE /api/v1/tasks/{taskId}` 删除推理服务中的异常任务
2. **回收资源**：释放该任务占用的quota资源
3. **更新状态**：将任务分配记录标记为FAILED
4. **重新调度**：如果重试次数未超限，重新进行任务调度
5. **记录日志**：记录异常原因和恢复操作

**重试策略：**
- 最大重试次数：3次
- 重试间隔：指数退避（30s, 60s, 120s）
- 重试条件：推理服务恢复健康状态
- 失败处理：超过重试次数后标记任务为永久失败

### 4.4 接口设计

#### 4.4.1 与推理服务的交互接口

**任务下发接口：**
```
POST /api/v1/tasks
Content-Type: application/json

请求体：完整的任务配置（包含算法编排、设备信息、任务元数据）
响应：任务创建结果
```

**任务状态查询接口：**
```
GET /api/v1/tasks/{taskId}

响应：任务详细状态信息
```

**任务删除接口：**
```
DELETE /api/v1/tasks/{taskId}

响应：删除操作结果
```

**健康检查接口：**
```
GET /health

响应：服务健康状态和统计信息
```

---

## 5. 关键技术实现

### 5.1 并发控制

**锁机制选择：**
- **单例模式**：使用本地内存锁（ReentrantLock）
- **分布式模式**：使用Redis分布式锁
- **配置切换**：通过 `scheduler.lock.type` 配置项控制

**锁的使用场景：**
- 任务调度时的资源分配
- 服务quota更新操作
- 任务状态变更操作

### 5.2 异步处理

**任务下发异步化：**
- 任务调度成功后立即返回结果
- 异步下发任务到推理服务
- 下发失败时通过监控机制重试

**监控任务异步化：**
- 使用Spring的@Scheduled注解实现定时任务
- 健康检查和任务监控并行执行
- 异常恢复操作异步处理

### 5.3 容错设计

**服务降级：**
- 推理服务异常时自动标记为不可用
- 避免将新任务分配到异常服务
- 支持服务自动恢复机制

**数据一致性：**
- 任务分配记录持久化到MongoDB
- 操作失败时自动回滚
- 定期数据一致性检查
    public ResponseEntity<Void> releaseTask(@PathVariable String allocationId) {
        try {
            schedulerService.releaseTask(allocationId);
            return ResponseEntity.ok().build();
        } catch (NotFoundException e) {
            return ResponseEntity.notFound().build();
        } catch (Exception e) {
            log.error("Release task failed", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 查询服务状态
     */
    @GetMapping("/services")
    public ResponseEntity<List<ServiceInfo>> getServices() {
        List<ServiceInfo> services = schedulerService.getAllServices();
        return ResponseEntity.ok(services);
    }
}
```

### 5.2 请求响应模型

```java
@Data
@Builder
public class ScheduleRequest {
    private SimplifiedAtomicTask taskRequest;
    private Map<String, Object> config;
}

@Data
@Builder
public class ScheduleResult {
    private boolean success;
    private String serviceId;
    private String serviceUrl;
    private String allocationId;
    private String errorMessage;
}

@Data
@Builder
public class ServiceInfo {
    private String serviceId;
    private String serviceName;
    private String baseUrl;
    private Integer maxQuota;
    private Integer currentQuota;
    private ServiceStatus status;
    private GpuType gpuType;
    private String region;
}
```

---

## 6. 配置管理

### 6.1 应用配置 (application.yml)

```yaml
server:
  port: 8080

spring:
  application:
    name: cv-scheduler

  data:
    mongodb:
      uri: ${MONGODB_URI:mongodb://localhost:27017/cv_scheduler}
      database: ${MONGODB_DATABASE:cv_scheduler}
      username: ${MONGODB_USERNAME:}
      password: ${MONGODB_PASSWORD:}
      auto-index-creation: true

  mongodb:
    embedded:
      version: 6.0.0

  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0

scheduler:
  health-check:
    interval: 30000  # 30秒
    timeout: 5000    # 5秒

  strategy:
    default-mode: FILL_FIRST
    enable-dynamic-schedule: true
    rebalance-threshold: 80  # 负载超过80%时触发重平衡

  service:
    discovery:
      enabled: true
      interval: 60000  # 60秒

logging:
  level:
    com.bohua.scheduler: DEBUG
    org.springframework.web: INFO
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

### 6.2 Maven配置 (pom.xml)

```xml
<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0
         http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.bohua</groupId>
    <artifactId>cv-scheduler</artifactId>
    <version>1.0.0</version>
    <packaging>jar</packaging>

    <name>CV Inference Scheduler</name>
    <description>CV推理服务调度器</description>

    <properties>
        <java.version>17</java.version>
        <spring-boot.version>3.2.0</spring-boot.version>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>

        <!-- Database -->
        <dependency>
            <groupId>de.flapdoodle.embed</groupId>
            <artifactId>de.flapdoodle.embed.mongo</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- HTTP Client -->
        <dependency>
            <groupId>org.apache.httpcomponents.client5</groupId>
            <artifactId>httpclient5</artifactId>
        </dependency>

        <!-- Utilities -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>junit-jupiter</artifactId>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>org.testcontainers</groupId>
            <artifactId>mongodb</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot.version}</version>
                <configuration>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.11.0</version>
                <configuration>
                    <source>17</source>
                    <target>17</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
```

---

## 7. 部署配置

### 7.1 Docker配置

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

LABEL maintainer="bohua-team"
LABEL description="CV Inference Scheduler"

WORKDIR /app

# 复制jar文件
COPY target/cv-scheduler-1.0.0.jar app.jar

# 创建非root用户
RUN groupadd -r scheduler && useradd -r -g scheduler scheduler
RUN chown -R scheduler:scheduler /app
USER scheduler

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["java", "-jar", "/app/app.jar"]
```

### 7.2 Docker Compose配置

```yaml
# docker-compose.yml
version: '3.8'

services:
  cv-scheduler:
    build: .
    container_name: cv-scheduler
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MONGODB_URI=mongodb://mongodb:27017/cv_scheduler
      - MONGODB_USERNAME=scheduler
      - MONGODB_PASSWORD=scheduler123
      - REDIS_HOST=redis
    depends_on:
      - mongodb
      - redis
    networks:
      - cv-network
    restart: unless-stopped

  mongodb:
    image: mongo:6.0
    container_name: cv-mongodb
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
      - MONGO_INITDB_DATABASE=cv_scheduler
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo/init.js:/docker-entrypoint-initdb.d/init.js
    networks:
      - cv-network
    restart: unless-stopped
    command: mongod --auth

  redis:
    image: redis:7-alpine
    container_name: cv-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - cv-network
    restart: unless-stopped

volumes:
  mongodb_data:
  redis_data:

networks:
  cv-network:
    driver: bridge
```

---

## 8. 监控和运维

### 8.1 监控指标

```java
@Component
public class SchedulerMetrics {

    private final MeterRegistry meterRegistry;
    private final Counter taskScheduledCounter;
    private final Counter taskFailedCounter;
    private final Gauge activeServicesGauge;
    private final Timer scheduleLatencyTimer;

    public SchedulerMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.taskScheduledCounter = Counter.builder("scheduler.tasks.scheduled")
            .description("Total number of scheduled tasks")
            .register(meterRegistry);

        this.taskFailedCounter = Counter.builder("scheduler.tasks.failed")
            .description("Total number of failed tasks")
            .register(meterRegistry);

        this.activeServicesGauge = Gauge.builder("scheduler.services.active")
            .description("Number of active inference services")
            .register(meterRegistry, this, SchedulerMetrics::getActiveServiceCount);

        this.scheduleLatencyTimer = Timer.builder("scheduler.schedule.latency")
            .description("Task scheduling latency")
            .register(meterRegistry);
    }

    public void incrementTaskScheduled() {
        taskScheduledCounter.increment();
    }

    public void incrementTaskFailed() {
        taskFailedCounter.increment();
    }

    public Timer.Sample startScheduleTimer() {
        return Timer.start(meterRegistry);
    }

    private double getActiveServiceCount() {
        // 实现获取活跃服务数量的逻辑
        return 0.0;
    }
}
```

### 8.2 日志配置

```xml
<!-- logback-spring.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <springProfile name="!prod">
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
    </springProfile>

    <springProfile name="prod">
        <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
            <file>logs/scheduler.log</file>
            <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                <fileNamePattern>logs/scheduler.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                    <maxFileSize>100MB</maxFileSize>
                </timeBasedFileNamingAndTriggeringPolicy>
                <maxHistory>30</maxHistory>
            </rollingPolicy>
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <root level="INFO">
            <appender-ref ref="FILE"/>
        </root>
    </springProfile>

    <logger name="com.bohua.scheduler" level="DEBUG"/>
    <logger name="org.springframework.web" level="INFO"/>
    <logger name="org.springframework.data.mongodb" level="DEBUG"/>
</configuration>
```

### 8.3 MongoDB配置类

```java
@Configuration
@EnableMongoRepositories(basePackages = "com.bohua.scheduler.repository")
@EnableMongoAuditing
public class MongoConfig {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), "cv_scheduler");
    }

    @Bean
    public MongoClient mongoClient() {
        return MongoClients.create(mongoUri);
    }

    @Bean
    public MongoTransactionManager transactionManager(MongoDatabaseFactory dbFactory) {
        return new MongoTransactionManager(dbFactory);
    }

    /**
     * 自定义转换器配置
     */
    @Bean
    public MongoCustomConversions customConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new LocalDateTimeToDateConverter());
        converters.add(new DateToLocalDateTimeConverter());
        return new MongoCustomConversions(converters);
    }

    /**
     * LocalDateTime转Date转换器
     */
    @WritingConverter
    public static class LocalDateTimeToDateConverter implements Converter<LocalDateTime, Date> {
        @Override
        public Date convert(LocalDateTime source) {
            return Date.from(source.atZone(ZoneId.systemDefault()).toInstant());
        }
    }

    /**
     * Date转LocalDateTime转换器
     */
    @ReadingConverter
    public static class DateToLocalDateTimeConverter implements Converter<Date, LocalDateTime> {
        @Override
        public LocalDateTime convert(Date source) {
            return source.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        }
    }
}
```

### 8.4 MongoDB初始化脚本

```javascript
// mongo/init.js
db = db.getSiblingDB('cv_scheduler');

// 创建用户
db.createUser({
  user: 'scheduler',
  pwd: 'scheduler123',
  roles: [
    {
      role: 'readWrite',
      db: 'cv_scheduler'
    }
  ]
});

// 创建索引
db.inference_service.createIndex({ "status": 1 });
db.inference_service.createIndex({ "region": 1 });
db.inference_service.createIndex({ "gpu_type": 1 });
db.inference_service.createIndex({ "service_name": 1 });

db.task_allocation.createIndex({ "service_id": 1 });
db.task_allocation.createIndex({ "task_status": 1 });
db.task_allocation.createIndex({ "device_id": 1 });
db.task_allocation.createIndex({ "allocate_time": 1 });
db.task_allocation.createIndex({ "task_id": 1 });

db.schedule_strategy.createIndex({ "schedule_mode": 1 });

// 插入默认调度策略
db.schedule_strategy.insertOne({
  _id: "default_strategy",
  schedule_mode: "FILL_FIRST",
  enable_dynamic_schedule: true,
  rebalance_threshold: 80,
  health_check_interval: 30000,
  create_time: new Date(),
  config: {
    "max_retry_count": 3,
    "retry_delay_ms": 1000
  }
});

print("MongoDB initialization completed successfully!");
```
```

---

## 6. 配置管理

### 6.1 核心配置项

**调度器配置：**
```yaml
scheduler:
  # 锁类型：local(本地锁) 或 redis(分布式锁)
  lock:
    type: local

  # 健康检查配置
  health-check:
    interval: 30000      # 检查间隔（毫秒）
    timeout: 5000        # 超时时间（毫秒）
    enabled: true

  # 任务监控配置
  task-monitor:
    interval: 60000      # 监控间隔（毫秒）
    retry-interval: 30000 # 重试间隔（毫秒）
    max-retries: 3       # 最大重试次数

  # 调度策略配置
  strategy:
    default-mode: FILL_FIRST
    enable-dynamic-schedule: true
    rebalance-threshold: 80
```

**数据库配置：**
```yaml
spring:
  data:
    mongodb:
      uri: mongodb://localhost:27017/scheduler
      auto-index-creation: true
```

### 6.2 环境配置

**开发环境：**
- 使用本地锁，简化部署
- MongoDB单实例
- 详细日志输出

**生产环境：**
- 使用Redis分布式锁
- MongoDB集群部署
- 优化日志级别

---

## 7. 总结

### 10.1 核心特性
- **任务下发**：调度成功后立即下发任务到推理服务
- **状态监控**：定时监控任务执行状态，检测异常
- **异常恢复**：自动检测任务异常并重新下发
- **并发控制**：支持本地锁和分布式锁两种模式
- **服务管理**：完整的推理服务生命周期管理

### 10.2 技术亮点
- **双重监控**：服务健康检查 + 任务状态监控
- **智能恢复**：异常检测、任务清理、重新调度的完整流程
- **灵活部署**：支持单例模式（本地锁）和分布式模式（Redis锁）
- **MongoDB存储**：灵活的文档存储，支持复杂的算法配置
- **异步处理**：任务下发和监控异步化，提高系统响应性

### 10.3 关键改进
- **任务下发机制**：从仅入库改为实际下发到推理服务
- **监控策略**：从被动等待改为主动监控任务状态
- **异常处理**：完整的异常检测和自动恢复机制
- **资源管理**：精确的quota管理和资源回收
- **状态一致性**：调度器与推理服务状态保持同步

### 10.4 扩展方向
- 支持更复杂的调度策略（基于GPU利用率、任务优先级等）
- 集成更多监控指标（任务执行时间、成功率等）
- 支持任务优先级和抢占式调度
- 集成告警系统，及时通知异常情况