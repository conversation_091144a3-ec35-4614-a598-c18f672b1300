# 任务分配幂等性设计

## 概述

任务分配接口实现了幂等性，确保相同的任务ID多次调用时不会重复分配，避免资源浪费和状态不一致。

## 实现原理

### 1. 幂等性检查

在分配任务时，系统会首先检查是否已存在相同taskId的活跃任务：

```java
// 检查任务是否已经存在（幂等性检查）
List<TaskAllocation> existingTasks = allocationRepository.findActiveTasksByTaskId(taskId);

if (!existingTasks.isEmpty()) {
    // 任务已存在，返回现有的任务分配
    TaskAllocation existingTask = existingTasks.get(0);
    return new TaskAllocationResult(existingTask, false);
}
```

### 2. 活跃任务定义

活跃任务是指状态为`ALLOCATED`或`RUNNING`的任务：

```java
@Query("{'taskId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
List<TaskAllocation> findActiveTasksByTaskId(String taskId);
```

### 3. 条件执行

只有新创建的任务才会执行以下操作：
- 更新服务quota
- 向推理服务发送任务

```java
if (allocationResult.isNewlyCreated()) {
    // 更新服务quota
    updateServiceQuota(service.getServiceId(), 1);
    
    // 向推理服务发送任务
    boolean taskSent = sendTaskToInferenceService(request, service);
}
```

## 数据库设计

### TaskAllocation表结构

| 字段 | 类型 | 说明 |
|------|------|------|
| allocationId | String | 分配ID（主键） |
| taskId | String | 任务ID（业务主键） |
| deviceId | String | 设备ID |
| serviceId | String | 服务ID |
| taskStatus | TaskStatus | 任务状态 |
| allocateTime | LocalDateTime | 分配时间 |

### 索引设计

为了支持高效的幂等性查询，需要在以下字段上创建索引：

```javascript
// MongoDB索引
db.task_allocation.createIndex({ "taskId": 1, "taskStatus": 1 })
db.task_allocation.createIndex({ "taskId": 1 })
```

## 幂等性场景

### 场景1：完全相同的请求

**请求1**：
```json
{
  "taskRequest": {
    "taskId": "task-001",
    "device": {"deviceId": "camera-001"},
    "algorithmOrchestration": {...}
  }
}
```

**请求2**（相同taskId）：
```json
{
  "taskRequest": {
    "taskId": "task-001",
    "device": {"deviceId": "camera-002"},  // 设备不同
    "algorithmOrchestration": {...}
  }
}
```

**结果**：返回第一次分配的结果，不会创建新的分配。

### 场景2：任务状态变化

如果任务状态变为`COMPLETED`、`FAILED`或`CANCELLED`，则不再被视为活跃任务，可以重新分配。

## 日志记录

系统会记录详细的幂等性操作日志：

```java
// 任务已存在
log.info("任务已存在，返回现有分配: taskId={}, allocationId={}, serviceId={}", 
    taskId, existingTask.getAllocationId(), existingTask.getServiceId());

// 服务不匹配警告
log.warn("任务已分配给其他服务: taskId={}, currentService={}, requestedService={}", 
    taskId, existingTask.getServiceId(), service.getServiceId());

// 新任务创建
log.info("创建新任务分配: taskId={}, allocationId={}, serviceId={}", 
    taskId, savedAllocation.getAllocationId(), savedAllocation.getServiceId());
```

## 测试验证

### 幂等性测试

运行测试脚本验证幂等性：

```bash
python test/scheduler/test_idempotent_allocation.py
```

### 测试用例

1. **相同taskId多次调用**：验证返回相同的allocationId
2. **不同taskId调用**：验证返回不同的allocationId
3. **任务状态变化后重新分配**：验证已完成任务可以重新分配

## 性能考虑

### 查询优化

- 使用复合索引`(taskId, taskStatus)`提高查询效率
- 限制查询结果数量，只取第一个匹配的任务

### 并发控制

- 使用数据库的唯一约束防止并发创建重复任务
- 考虑使用分布式锁处理高并发场景

## 错误处理

### 服务不匹配

当现有任务分配给了不同的服务时，系统会：
1. 记录警告日志
2. 返回现有的分配结果
3. 不更新quota或发送任务

### 数据库异常

- 查询失败时抛出异常
- 保存失败时回滚操作

## 监控指标

建议监控以下指标：

1. **幂等性命中率**：`existing_tasks / total_requests`
2. **任务分配延迟**：从请求到分配完成的时间
3. **重复分配错误**：因幂等性检查失败的请求数

## 最佳实践

1. **taskId设计**：使用有意义且唯一的taskId
2. **状态管理**：及时更新任务状态，避免僵尸任务
3. **清理策略**：定期清理已完成的历史任务
4. **监控告警**：监控幂等性相关的异常情况
