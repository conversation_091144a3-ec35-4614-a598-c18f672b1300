# 训练-推理-预警数据集成规范

## 概述

本文档定义了模型训练、推理和预警系统之间的数据关联规范，建立训练标签与预警属性的映射关系，以及数据回流机制。

## 核心设计原则

1. **标签属性一致性**：训练标签与预警属性保持一致的命名和类型
2. **数据回流机制**：支持推理结果回流用于持续训练
3. **阈值驱动收集**：基于置信度阈值收集训练数据
4. **版本化管理**：支持模型版本与标签schema的对应关系

---

## 1. 算法-标签一对一映射规范

### 1.1 设计原则

- **一个算法对应一个模型**：每个算法ID对应一个独立的模型文件
- **一个算法对应一个标签**：每个分类算法只输出一个标签属性
- **多属性聚合通过编排实现**：复杂判断逻辑通过算法编排链实现

### 1.2 算法标签映射示例

```json
{
  "算法映射关系": {
    "helmet_detection": {
      "label": "helmet",
      "type": "boolean",
      "description": "是否佩戴安全帽",
      "values": [true, false]
    },
    "worker_classification": {
      "label": "isWorker",
      "type": "boolean",
      "description": "是否为工人",
      "values": [true, false]
    },
    "age_estimation": {
      "label": "age",
      "type": "integer",
      "description": "年龄估计",
      "range": [0, 100]
    },
    "gender_classification": {
      "label": "gender",
      "type": "string",
      "description": "性别识别",
      "values": ["MALE", "FEMALE", "UNKNOWN"]
    },
    "vehicle_type_classification": {
      "label": "vehicleType",
      "type": "string",
      "description": "车辆类型",
      "values": ["car", "truck", "bus", "motorcycle"]
    }
  },
  "规则类算法（无需训练）": {
    "area_intrusion_detection": {
      "ruleType": "AREA_INTRUSION",
      "description": "区域入侵检测",
      "outputAttribute": "inRestrictedArea"
    },
    "tripwire_detection": {
      "ruleType": "TRIPWIRE",
      "description": "绊线检测",
      "outputAttribute": "crossedTripwire",
      "directionTypes": ["CLOCKWISE", "COUNTERCLOCKWISE", "BOTH"]
    }
  }
}
```

### 1.3 编排实现复杂判断

**场景：未戴安全帽的工人告警**

通过算法编排实现：
```json
{
  "algorithmChain": [
    {
      "algorithmId": "person_detection",
      "algorithmType": "DETECTION",
      "order": 1
    },
    {
      "algorithmId": "worker_classification",
      "algorithmType": "CLASSIFICATION",
      "order": 2,
      "dependsOn": ["person_detection"],
      "outputLabel": "isWorker",
      "alertConfig": {
        "enabled": true,
        "alertLabel": true,
        "alertThreshold": 0.7
      }
    },
    {
      "algorithmId": "helmet_detection",
      "algorithmType": "CLASSIFICATION",
      "order": 3,
      "dependsOn": ["person_detection"],
      "outputLabel": "helmet",
      "alertConfig": {
        "enabled": true,
        "alertLabel": false,
        "alertThreshold": 0.8,
        "violationType": "WORKER_NO_HELMET",
        "severity": "HIGH"
      }
    }
  ]
}
```

**告警逻辑：** 当 `isWorker >= 0.7` 且 `helmet < 0.8` 时，自动触发告警

---

## 2. 数据回流机制

### 2.1 单算法回流配置

每个分类算法独立配置数据回流：

```json
{
  "algorithmId": "helmet_detection",
  "dataCollectionConfig": {
    "enabled": true,
    "labelName": "helmet",
    "thresholds": {
      "minConfidence": 0.1,
      "maxConfidence": 0.9
    },
    "samplingRate": 0.1,
    "maxSamplesPerDay": 1000
  }
}
```

```json
{
  "algorithmId": "worker_classification",
  "dataCollectionConfig": {
    "enabled": true,
    "labelName": "isWorker",
    "thresholds": {
      "minConfidence": 0.2,
      "maxConfidence": 0.8
    },
    "samplingRate": 0.05,
    "maxSamplesPerDay": 500
  }
}
```

### 2.2 回流数据格式

每个算法独立生成回流数据：

**安全帽检测算法回流数据：**
```json
{
  "collectionId": "collection_helmet_20241219_001",
  "algorithmId": "helmet_detection",
  "timestamp": 1701425400000,
  "deviceId": "camera_001",
  "samples": [
    {
      "resourceId": "resource_001",
      "feature": "base64_encoded_feature_vector",
      "label": "helmet",
      "prediction": {
        "value": false,
        "confidence": 0.65
      },
      "metadata": {
        "imageUri": "https://storage.example.com/images/scene_001.jpg",
        "boundingBox": {"x": 100, "y": 200, "width": 150, "height": 200},
        "trackId": "track_001"
      }
    }
  ]
}
```

**工人分类算法回流数据：**
```json
{
  "collectionId": "collection_worker_20241219_001",
  "algorithmId": "worker_classification",
  "timestamp": 1701425400000,
  "deviceId": "camera_001",
  "samples": [
    {
      "resourceId": "resource_001",
      "feature": "base64_encoded_feature_vector",
      "label": "isWorker",
      "prediction": {
        "value": true,
        "confidence": 0.75
      },
      "metadata": {
        "imageUri": "https://storage.example.com/images/scene_001.jpg",
        "boundingBox": {"x": 100, "y": 200, "width": 150, "height": 200},
        "trackId": "track_001"
      }
    }
  ]
}
```

---

## 3. 任务下发协议扩展

### 3.1 单算法配置扩展

每个分类算法独立配置训练相关参数：

**安全帽检测算法配置：**
```json
{
  "algorithmId": "helmet_detection",
  "algorithmName": "安全帽检测",
  "algorithmType": "CLASSIFICATION",
  "order": 3,
  "required": true,
  "config": {
    "confidence": 0.8,
    "model_type": "clip"
  },
  "trainingConfig": {
    "labelName": "helmet",
    "labelSchema": {
      "type": "boolean",
      "description": "是否佩戴安全帽",
      "values": [true, false]
    },
    "dataCollection": {
      "enabled": true,
      "thresholds": {
        "minConfidence": 0.1,
        "maxConfidence": 0.9
      },
      "samplingRate": 0.1,
      "maxSamplesPerDay": 1000
    },
    "modelVersion": "v1.0",
    "retrainTrigger": {
      "minSamples": 5000,
      "maxDays": 30
    }
  }
}
```

**工人分类算法配置：**
```json
{
  "algorithmId": "worker_classification",
  "algorithmName": "工人分类",
  "algorithmType": "CLASSIFICATION",
  "order": 4,
  "required": true,
  "config": {
    "confidence": 0.7,
    "model_type": "clip"
  },
  "trainingConfig": {
    "labelName": "isWorker",
    "labelSchema": {
      "type": "boolean",
      "description": "是否为工人",
      "values": [true, false]
    },
    "dataCollection": {
      "enabled": true,
      "thresholds": {
        "minConfidence": 0.2,
        "maxConfidence": 0.8
      },
      "samplingRate": 0.05,
      "maxSamplesPerDay": 500
    },
    "modelVersion": "v1.0",
    "retrainTrigger": {
      "minSamples": 3000,
      "maxDays": 45
    }
  }
}
```

### 3.2 数据收集配置

```json
{
  "dataCollectionConfig": {
    "storageConfig": {
      "featureStorage": "s3://training-data/features/",
      "metadataStorage": "s3://training-data/metadata/",
      "retentionDays": 90
    },
    "qualityControl": {
      "minImageQuality": 0.7,
      "minBoundingBoxArea": 1000,
      "duplicateDetection": true
    }
  }
}
```

---

## 4. 预警输出协议扩展

### 4.1 多算法预警输出

当多个算法参与判断时，预警输出包含所有相关算法的信息：

```json
{
  "atomicEventInstanceId": "aei_worker_no_helmet_001",
  "atomicEventId": "worker_no_helmet_rule",
  "deviceId": "camera_001",
  "timestamp": 1701425400000,
  "entities": [
    {
      "entityInstanceId": "track_001",
      "entityType": "Person",
      "boundingBox": {"x": 100, "y": 200, "width": 150, "height": 200},
      "entityAttributes": {
        "helmet": {
          "attributeName": "helmet",
          "attributeValue": false,
          "score": 0.65,
          "algorithmId": "helmet_detection",
          "modelVersion": "v1.0",
          "inCollectionRange": true
        },
        "isWorker": {
          "attributeName": "isWorker",
          "attributeValue": true,
          "score": 0.75,
          "algorithmId": "worker_classification",
          "modelVersion": "v1.0",
          "inCollectionRange": true
        }
      },
      "externalInfo": {
        "trackId": "track_001"
      }
    }
  ],
  "externalInfo": {
    "ruleId": "rule_worker_helmet_001",
    "ruleName": "工人安全帽检测",
    "violationType": "WORKER_NO_HELMET",
    "severity": "HIGH",
    "algorithmChain": ["helmet_detection", "worker_classification"],
    "trainingInfo": {
      "algorithmsWithDataCollection": [
        {
          "algorithmId": "helmet_detection",
          "dataCollected": true,
          "collectionReason": "confidence_in_range"
        },
        {
          "algorithmId": "worker_classification",
          "dataCollected": true,
          "collectionReason": "confidence_in_range"
        }
      ]
    }
  }
}
```

---

## 5. 训练数据生成流程

### 5.1 数据收集流程

```mermaid
sequenceDiagram
    participant Inference as 推理引擎
    participant Collector as 数据收集器
    participant Storage as 存储系统
    participant Trainer as 训练系统
    
    Inference->>Inference: 执行推理
    Inference->>Inference: 检查置信度阈值
    
    alt 置信度在收集范围内
        Inference->>Collector: 发送数据样本
        Collector->>Collector: 质量检查
        Collector->>Storage: 存储特征和标签
        
        Storage->>Trainer: 触发重训练检查
        alt 满足重训练条件
            Trainer->>Storage: 获取新数据
            Trainer->>Trainer: 执行模型训练
            Trainer->>Inference: 部署新模型
        end
    end
```

### 5.2 数据格式转换

从预警输出到训练数据的转换：

```python
def convert_inference_to_training_data(inference_result):
    """将推理结果转换为训练数据格式"""
    training_sample = {
        "feature": extract_feature_vector(inference_result),
        "labels": {}
    }
    
    # 转换属性为标签
    for attr_name, attr_data in inference_result["entities"][0]["entityAttributes"].items():
        if attr_data["inCollectionRange"]:
            training_sample["labels"][attr_name] = attr_data["attributeValue"]
    
    return training_sample
```

---

## 6. 配置示例

### 6.1 完整的多算法编排配置示例

**场景：未戴安全帽的工人告警**

```json
{
  "taskName": "工人安全帽检测任务",
  "algorithmOrchestration": {
    "orchestrationId": "orch_worker_helmet_001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "algorithmChain": [
      {
        "algorithmId": "person_detection",
        "algorithmType": "DETECTION",
        "order": 1,
        "config": {"confidence": 0.7}
      },
      {
        "algorithmId": "person_tracking",
        "algorithmType": "TRACKING",
        "order": 2,
        "config": {"max_age": 30}
      },
      {
        "algorithmId": "helmet_detection",
        "algorithmType": "CLASSIFICATION",
        "order": 3,
        "config": {"confidence": 0.8},
        "trainingConfig": {
          "labelName": "helmet",
          "labelSchema": {
            "type": "boolean",
            "description": "是否佩戴安全帽",
            "values": [true, false]
          },
          "dataCollection": {
            "enabled": true,
            "thresholds": {"minConfidence": 0.1, "maxConfidence": 0.9},
            "samplingRate": 0.1,
            "maxSamplesPerDay": 1000
          },
          "modelVersion": "v1.0"
        },
        "alertConfig": {
          "enabled": true,
          "alertLabel": false,
          "alertThreshold": 0.8,
          "violationType": "HELMET_MISSING",
          "severity": "HIGH"
        }
      },
      {
        "algorithmId": "worker_classification",
        "algorithmType": "CLASSIFICATION",
        "order": 4,
        "config": {"confidence": 0.7},
        "trainingConfig": {
          "labelName": "isWorker",
          "labelSchema": {
            "type": "boolean",
            "description": "是否为工人",
            "values": [true, false]
          },
          "dataCollection": {
            "enabled": true,
            "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8},
            "samplingRate": 0.05,
            "maxSamplesPerDay": 500
          },
          "modelVersion": "v1.0"
        },
        "alertConfig": {
          "enabled": true,
          "alertLabel": true,
          "alertThreshold": 0.7,
          "violationType": "WORKER_DETECTED",
          "severity": "INFO"
        }
      }
    ],
    "alertRule": {
      "condition": "isWorker == true AND helmet == false",
      "violationType": "WORKER_NO_HELMET",
      "severity": "HIGH"
    }
  },
  "devices": [{"deviceId": "camera_001"}]
}
```

### 6.2 数据收集配置示例

```json
{
  "globalDataCollectionConfig": {
    "enabled": true,
    "storage": {
      "type": "s3",
      "bucket": "training-data-collection",
      "prefix": "inference-samples/"
    },
    "retention": {
      "days": 90,
      "maxSamples": 1000000
    },
    "quality": {
      "minImageQuality": 0.7,
      "minBoundingBoxArea": 1000
    }
  }
}
```

---

## 7. 实施建议

### 7.1 分阶段实施

1. **第一阶段**：建立标签-属性映射关系
2. **第二阶段**：实现数据回流机制
3. **第三阶段**：集成自动重训练流程

### 7.2 监控指标

- 数据收集率
- 模型性能变化
- 重训练频率
- 存储使用量

通过这个集成规范，可以实现训练、推理和预警系统的无缝数据流转，支持持续学习和模型优化。
