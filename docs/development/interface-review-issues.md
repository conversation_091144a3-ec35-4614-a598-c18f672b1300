# CV接口设计文档问题清单

## 文档概述

本文档整理了对以下三个CV接口设计文档的审查问题：
- `vision-flow-simplified-protocol.md` - 任务下发功能
- `event-output-protocol.md` - 告警输出接口
- `event-quick-reference.md` - 告警输出快速参考

## 问题分类

### 🔴 高优先级问题（影响核心功能）
### 🟡 中优先级问题（影响易用性）
### 🟢 低优先级问题（优化建议）

---

## 1. 任务下发功能问题

### 1.1 算法编排配置不一致 🔴

**问题描述：**
- 应用层协议中的 `Algorithm` 对象缺少 `functionId` 字段
- 调度层协议中又出现了 `functionId` 字段
- 两层协议的算法配置结构不一致

**具体位置：**
- 应用层：第32-55行，算法链配置
- 调度层：第109-132行，算法配置

**问题影响：**
- 应用层到调度层的数据转换逻辑不明确
- 可能导致配置丢失或转换错误

**需要答复：**
1. `functionId` 字段应该在哪一层出现？
    - funcitonId原本是用来表示一个算法里面有哪些更细粒度的算子，比如人体检测，默认包含了yolo的检测算法+bytetrack的追踪算法
    - 当前实现方案上，直接去掉吧，这层关系在inference里直接实现
2. 应用层和调度层的算法配置转换规则是什么？
    - 我希望去掉调度层，直接使用应用层
3. 是否需要统一两层的数据结构？
    - 是的，如2所说，统一数据接口，以应用层为主

---

### 1.2 算法依赖关系验证逻辑不明确 🔴

**问题描述：**
- `dependsOn` 字段定义了算法依赖关系，但缺少验证逻辑
- 如何处理循环依赖？
- `order` 字段和 `dependsOn` 字段可能冲突时的优先级不明确

**具体位置：**
- 第50行：`"dependsOn": ["person_detection"]`
- 第126行：调度层的依赖关系

**需要答复：**
1. 如何检测和处理循环依赖？
2. `order` 和 `dependsOn` 冲突时，以哪个为准？
3. 依赖的算法失败时，后续算法如何处理？
    - 本质是DAG结构，但实际上会简单很多，一共两种pattern：
        - 第一种：yolo的人体、车辆、非机动车检测 + tracking + clip分类算法
        - 第二种：o-vit的万物检测 + clip分类算法
        - 首先这两种模式前置都有视频流的解码环节，但是频率有区别，yolo检测需要tracking，所以一秒需要每隔4帧解析一次；ovit 30s解析一次；
        - 其次，clip算法是可以训练的，输出预警是需要有相关的id字段来表示

---

### 1.3 设备配置字段冗余和不一致 🟡

**问题描述：**
- `streamConfig` 在应用层和调度层都存在，但字段不完全一致
- 应用层有 `resolution`、`frameRate`、`protocol`
- 调度层有 `decoder_conf`，但缺少分辨率等信息

**具体位置：**
- 应用层：第61-67行
- 调度层：第134-140行

**需要答复：**
1. 两层的流配置是否应该保持一致？
2. `decoder_conf` 的详细配置结构是什么？
3. 分辨率和帧率信息在调度层是否需要？
    - 保持一致，以应用层为准，decoder_conf有两个字段：是否为关键帧和解码step字段，表示每隔几帧解码一次

---

### 1.4 任务状态定义不完整 🟡

**问题描述：**
- 只定义了 `CREATED`、`RUNNING`、`INITIALIZED`、`ACTIVE` 状态
- 缺少失败、暂停、完成、错误等状态定义
- 状态转换规则不明确

**需要答复：**
1. 完整的任务状态有哪些？
2. 各状态之间的转换条件是什么？
3. 异常状态的处理机制是什么？
    - 简化成三个状态，CREATED、RUNNING、ERROR、STOPED
    - CREATED -> RUNNING：下发任务
    - RUNNING -> ERROR：任务执行出错
    - RUNNING -> STOPED：任务正常停止
    - ERROR -> STOPED：任务出错后手动停止
    - STOPED -> RUNNING：任务恢复

---

### 1.5 DAG生成逻辑不明确 🟡

**问题描述：**
- 从算法链到DAG的转换规则不明确
- 解码节点的自动插入逻辑没有说明
- DAG节点的命名规则不清晰

**具体位置：**
- 第161-189行：DAG结构定义

**需要答复：**
1. 算法链如何转换为DAG？
2. 解码节点何时自动插入？
3. DAG节点ID的生成规则是什么？
    - 参考1.2章节

---

## 2. 告警输出接口问题

### 2.1 实体关系结构复杂且重叠 🔴

**问题描述：**
- `partOf` 和 `entityRelationship` 两个字段功能重叠
- 使用场景和区别不明确
- `RelationNameValue` 结构中的 `entities` 字段用途不清晰

**具体位置：**
- 第37-44行：`partOf` 定义
- 第52-59行：`entityRelationship` 定义

**需要答复：**
1. `partOf` 和 `entityRelationship` 的具体区别是什么？
2. 什么情况下使用 `partOf`，什么情况下使用 `entityRelationship`？
3. `RelationNameValue.entities` 字段的作用是什么？
    - partOf：指从属关系，比如人 partOf 手
    - entityRelationship：更多指多个实体之间的关系，比如 人 “骑” 车；人 “绊” 线
    - RelationNameValue.entities 表示这个关系下的另外一个实体，比如人骑车中的车
---

### 2.2 属性值类型不统一 🔴

**问题描述：**
- `attributeValue` 可以是 boolean、number、string，但没有明确的类型约束
- 不同算法输出的同一属性可能类型不一致
- 缺少属性值的验证规则

**具体位置：**
- 第47-50行：helmet 属性为 boolean
- 第214-216行：age 属性为 number

**需要答复：**
1. 每种属性的标准数据类型是什么？
2. 如何保证不同算法输出的同一属性类型一致？
3. 是否需要属性值的验证规则？

---

### 2.3 ID字段命名不一致 🟡

**问题描述：**
- `entityInstanceId` 有时表示 TrackID，有时表示其他含义
- `atomicEventId` 和 `compositeEventId` 的关系不明确
- ID生成规则不统一

**需要答复：**
1. `entityInstanceId` 的标准含义是什么？
2. `atomicEventId` 和 `compositeEventId` 的关系和生成规则？
3. 各种ID的命名规范是什么？
    - entityInstanceId 是指一个对象，实现方式是用tracking来实现的，所以一定程度上等价trackId
    - 删除原子事件和组合事件概念，暂时只保留原子事件
---

### 2.4 时间戳一致性逻辑不明确 🟡

**问题描述：**
- 根级 `timestamp` 和实体级 `timestamp` 的关系不明确
- 什么情况下允许时间戳不一致？
- 时间戳精度要求不明确

**需要答复：**
1. 事件时间戳和实体时间戳应该相同吗？
2. 允许的时间戳差异范围是多少？
3. 时间戳的精度要求（毫秒/微秒）？
    - 暂时先只保留事件时间戳

---

### 2.5 置信度计算逻辑不明确 🟡

**问题描述：**
- 事件级 `confidence` 和属性级 `score` 的计算关系不明确
- 多个实体的置信度如何聚合为事件置信度？
- 置信度的计算公式不明确

**需要答复：**
1. 事件置信度如何从属性置信度计算得出？
2. 多实体事件的置信度聚合规则？
3. 置信度的取值范围和精度要求？
     - 事件级confidence可以先去掉
     - 所有属性级都超过阈值后才任务事件应该告警，inference才会输出

---

## 3. 跨文档一致性问题

### 3.1 算法ID映射关系不明确 🔴

**问题描述：**
- 任务下发中的 `algorithmId` 与告警输出中的 `algorithmId` 是否为同一概念？
- 缺少算法ID到违规类型的映射关系
- 算法版本管理机制不明确

**需要答复：**
1. 两个文档中的 `algorithmId` 是否应该保持一致？
2. 算法ID与违规类型的映射关系如何定义？
3. 算法版本升级时ID如何处理？
    - algorithmId是完全一样的，下发任务时是什么，预警时应该透传
    - algorithmId对应的算法类型如果是ovit的检测算法或者clip算法，需要从远端的算法仓库根据algorithmId来下载

---

### 3.2 设备ID一致性问题 🟡

**问题描述：**
- 任务下发中的 `deviceId` 与告警输出中的 `cameraId` 关系不明确
- 一个设备可能有多个摄像头的情况如何处理？
- 设备和摄像头的层级关系不清晰

**需要答复：**
1. `deviceId` 和 `cameraId` 的关系是什么？
2. 一对多的设备-摄像头关系如何表示？
3. 是否需要统一使用一种ID？
    - 统一使用deviceId，没有一对多关系

---

## 4. 建议改进方案

### 4.1 统一字段定义 🟢
- 建立统一的数据字典
- 明确每个字段的类型、取值范围、必填性
- 统一ID字段的命名规范和生成规则

### 4.2 完善状态机制 🟢
- 补充完整的任务状态定义和状态转换规则
- 明确异常情况的处理逻辑

### 4.3 简化关系结构 🟢
- 合并或明确区分 `partOf` 和 `entityRelationship`
- 简化 `RelationNameValue` 结构

### 4.4 增加验证规则 🟢
- 添加字段级别的验证规则
- 增加业务逻辑验证规则

---


**审查完成时间：** 2024-12-19  
**审查人：** Augment Agent  
**文档版本：** v1.0
