# Inference 与 Scheduler 交互架构文档

## 📋 概述

本文档详细描述了 CV 推理服务（Inference）与任务调度器（Scheduler）之间的交互逻辑、通信协议和数据流转机制。

## 🏗️ 整体架构

### 系统组件

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   外部客户端     │    │  Scheduler调度器  │    │ Inference推理服务 │
│                │    │     :8080       │    │     :9001      │
│ - Web应用       │───▶│ - 任务调度       │───▶│ - 视频分析      │
│ - API客户端     │    │ - 服务管理       │    │ - 算法流水线     │
│ - 第三方系统     │    │ - 负载均衡       │    │ - 事件生成      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                              │                        │
                              ▼                        ▼
                       ┌─────────────────┐    ┌─────────────────┐
                       │   MySQL数据库    │    │   Kafka消息队列  │
                       │ - 服务注册信息   │    │ - 事件流        │
                       │ - 任务分配记录   │    │ - 告警推送      │
                       │ - 调度策略      │    │ - 数据分析      │
                       └─────────────────┘    └─────────────────┘
```

### 核心交互模式

1. **服务注册**: Inference 主动向 Scheduler 注册服务能力
2. **任务调度**: Scheduler 根据策略将任务分发给 Inference
3. **状态同步**: 双向任务状态检查和一致性维护
4. **故障恢复**: 自动重试和孤儿任务清理机制

## 🔄 核心交互流程

### 1. 服务注册流程

#### 1.1 注册时序图

```mermaid
sequenceDiagram
    participant I as Inference服务
    participant R as Service Registry
    participant S as Scheduler调度器
    participant D as MySQL数据库
    
    Note over I,D: 服务启动和注册
    I->>R: 启动服务注册模块
    R->>R: 构建注册数据
    
    loop 注册循环 (每30秒)
        R->>S: POST /api/v1/services/register
        S->>D: 检查服务是否已存在
        alt 服务已存在
            S->>D: 更新服务状态为ACTIVE
        else 新服务
            S->>D: 创建新服务记录
        end
        S->>R: 返回注册结果
        
        alt 注册成功
            R->>R: 标记已注册，等待60秒
        else 注册失败
            R->>R: 等待30秒重试
        end
    end
```

#### 1.2 注册数据结构

```json
{
  "serviceName": "inference-service-1",
  "baseUrl": "http://*************:9001",
  "algorithmOrchestration": {
    "algorithm_chain": [
      {
        "algorithm_id": "yolov8_detection",
        "algorithm_name": "YOLOv8目标检测",
        "algorithm_type": "DETECTION",
        "order": 1
      },
      {
        "algorithm_id": "byte_tracking",
        "algorithm_name": "ByteTracker目标跟踪",
        "algorithm_type": "TRACKING",
        "order": 2
      }
    ]
  },
  "maxQuota": 10,
  "region": "default",
  "gpuType": "A10"
}
```

#### 1.3 配置示例

```yaml
# Inference服务配置
service_registry:
  enabled: true
  scheduler:
    url: "http://*************:8080"
    registration_retry_interval: 30
  service:
    name: "inference-service-1"
    max_quota: 10
    region: "default"
    gpu_type: "A10"
```

### 2. 任务调度流程

#### 2.1 调度时序图

```mermaid
sequenceDiagram
    participant C as 客户端
    participant S as Scheduler调度器
    participant D as MySQL数据库
    participant I as Inference服务
    participant P as 算法流水线
    participant K as Kafka消息队列
    
    C->>S: POST /api/v1/scheduler/schedule
    S->>D: 查询可用服务列表
    S->>S: 根据策略选择服务
    S->>D: 分配任务(幂等操作)
    S->>I: POST /api/v1/tasks
    I->>I: 解析任务配置
    I->>P: 启动算法流水线
    
    loop 视频处理循环
        P->>P: 视频解码→检测→跟踪→分类→规则
        alt 触发告警
            P->>K: 推送事件
        end
    end
    
    S->>C: 返回调度结果
```

#### 2.2 调度策略

| 策略类型 | 说明 | 适用场景 |
|---------|------|----------|
| **FILL_FIRST** | 优先填满策略，选择已有任务较多的服务 | 资源集中利用，提高单机效率 |
| **SPREAD_FIRST** | 优先平铺策略，选择负载最轻的服务 | 负载均衡，提高系统稳定性 |

#### 2.3 任务分配逻辑

```java
// 幂等性检查
List<TaskAllocation> existingTasks = allocationRepository.findActiveTasksByTaskId(taskId);
if (!existingTasks.isEmpty()) {
    return new TaskAllocationResult(existingTasks.get(0), false);
}

// 创建新任务分配
TaskAllocation allocation = TaskAllocation.builder()
    .allocationId(UUID.randomUUID().toString())
    .taskId(taskId)
    .serviceId(service.getServiceId())
    .taskStatus(TaskStatus.ALLOCATED)
    .build();
```

### 3. 配额管理机制

#### 3.1 配额更新流程

```java
// 分配任务时增加配额
updateServiceQuota(service.getServiceId(), 1);

// 任务完成时释放配额
updateServiceQuota(allocation.getServiceId(), -1);

// 配额边界检查
private void updateServiceQuota(String serviceId, int delta) {
    int newQuota = service.getCurrentQuota() + delta;
    if (newQuota < 0) newQuota = 0;
    if (newQuota > service.getMaxQuota()) newQuota = service.getMaxQuota();
    service.setCurrentQuota(newQuota);
}
```

#### 3.2 配额状态监控

| 状态 | 条件 | 说明 |
|------|------|------|
| **可用** | `currentQuota < maxQuota` | 可以接收新任务 |
| **满载** | `currentQuota >= maxQuota` | 暂时无法接收新任务 |
| **异常** | `currentQuota < 0` | 配额计算异常，自动修正为0 |

## 🔧 健康检查和故障恢复

### 1. 定时维护任务

```java
@Scheduled(fixedRate = 30000) // 每30秒执行
public void scheduledTaskMaintenance() {
    // 重试失败的任务
    retryFailedTasks();
    
    // 清理孤儿任务
    cleanupOrphanTasks();
}
```

### 2. 孤儿任务清理

#### 2.1 双向一致性检查

```mermaid
graph TB
    subgraph "Scheduler孤儿任务清理"
        A[查询Scheduler活跃任务] --> B[检查Inference中任务存在性]
        B --> C{任务是否存在?}
        C -->|不存在| D[重新分发孤儿任务]
        C -->|存在| E[任务状态正常]
    end
    
    subgraph "Inference孤儿任务清理"
        F[获取Inference任务列表] --> G[对比Scheduler记录]
        G --> H{Scheduler中是否有记录?}
        H -->|无记录| I[删除多余任务]
        H -->|有记录| J[任务状态正常]
    end
```

#### 2.2 孤儿任务处理逻辑

```java
private void handleOrphanTask(TaskAllocation orphanTask, InferenceService originalService, String reason) {
    // 1. 回滚原服务的quota
    updateServiceQuota(originalService.getServiceId(), -1);
    
    // 2. 删除原有的任务分配记录
    allocationRepository.delete(orphanTask);
    
    // 3. 重新调度任务
    ScheduleResult newSchedule = scheduleTask(retryRequest);
    
    if (newSchedule.isSuccess()) {
        log.info("孤儿任务重新分发成功: taskId={}", orphanTask.getTaskId());
    } else {
        // 创建失败记录
        createFailedTaskRecord(orphanTask, newSchedule.getErrorMessage());
    }
}
```

### 3. 失败任务重试

```java
public void retryFailedTasks() {
    List<TaskAllocation> failedTasks = allocationRepository.findByTaskStatus(TaskStatus.ERROR);
    
    for (TaskAllocation failedTask : failedTasks) {
        // 检查服务可用性
        if (service.isAvailable()) {
            // 重新发送任务
            boolean taskSent = sendTaskToInferenceService(retryRequest, service);
            if (taskSent) {
                // 更新状态为运行中
                failedTask.setTaskStatus(TaskStatus.RUNNING);
                updateServiceQuota(service.getServiceId(), 1);
            }
        }
    }
}
```

## 📡 通信协议和接口

### 1. 核心接口对照表

| 功能分类 | Scheduler接口 | Inference接口 | 通信方向 | 说明 |
|---------|--------------|---------------|----------|------|
| **服务管理** | `POST /api/v1/services/register` | 服务注册客户端 | I→S | Inference主动注册 |
| **服务管理** | `GET /actuator/health` | `GET /health` | S→I | 健康检查 |
| **任务调度** | `POST /api/v1/scheduler/schedule` | `POST /api/v1/tasks` | S→I | 任务分发 |
| **任务管理** | `GET /api/v1/scheduler/tasks/{taskId}` | `GET /api/v1/tasks/{taskId}` | S↔I | 状态查询 |
| **任务管理** | `POST /api/v1/scheduler/release/{taskId}` | `DELETE /api/v1/tasks/{taskId}` | S→I | 任务释放 |
| **监控统计** | `GET /api/v1/scheduler/services` | `GET /api/v1/stats` | S↔I | 服务状态 |

### 2. 数据模型

#### 2.1 任务请求模型

```json
{
  "taskRequest": {
    "taskId": "task-001",
    "taskName": "摄像头监控任务",
    "taskMeta": {
      "enabled": true,
      "taskLevel": "MEDIUM",
      "protocol": "VIDEO",
      "eventTypeId": "intrusion_detection"
    },
    "algorithmOrchestration": {
      "orchestrationId": "orch-001",
      "orchestrationType": "YOLO_TRACKING_CLIP",
      "algorithmChain": [...]
    },
    "device": {
      "deviceId": "camera-001",
      "streamConfig": {
        "url": "rtsp://*************:554/stream",
        "resolution": "1920x1080",
        "frameRate": 25
      }
    }
  },
  "region": "default",
  "priority": 1
}
```

#### 2.2 调度结果模型

```json
{
  "success": true,
  "taskId": "task-001",
  "serviceId": "service-123",
  "serviceUrl": "http://*************:9001",
  "message": "任务调度成功",
  "errorCode": null,
  "errorMessage": null
}
```

### 3. 错误处理

#### 3.1 常见错误码

| 错误码 | 说明 | 处理方式 |
|-------|------|----------|
| `NO_AVAILABLE_SERVICE` | 没有可用的推理服务 | 等待服务注册或扩容 |
| `QUOTA_EXCEEDED` | 服务配额已满 | 等待任务完成或选择其他服务 |
| `TASK_SEND_FAILED` | 任务发送失败 | 自动重试或重新调度 |
| `SERVICE_UNREACHABLE` | 服务不可达 | 标记服务为维护状态 |

#### 3.2 重试策略

```java
// 指数退避重试
private static final int MAX_RETRY_ATTEMPTS = 3;
private static final long INITIAL_RETRY_DELAY = 1000; // 1秒

for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
    try {
        return sendTaskToInferenceService(request, service);
    } catch (Exception e) {
        if (attempt == MAX_RETRY_ATTEMPTS) {
            throw e;
        }
        Thread.sleep(INITIAL_RETRY_DELAY * (1L << (attempt - 1)));
    }
}
```

## 🔍 监控和运维

### 1. 关键指标

#### 1.1 服务级指标

- **服务可用性**: 注册服务数量、活跃服务数量
- **配额利用率**: `currentQuota / maxQuota`
- **任务分发成功率**: 成功分发任务数 / 总分发请求数
- **平均响应时间**: 任务分发到执行的平均延迟

#### 1.2 任务级指标

- **任务状态分布**: ALLOCATED、RUNNING、STOPPED、ERROR
- **任务执行时长**: 从分配到完成的时间
- **失败任务重试次数**: 平均重试次数和成功率
- **孤儿任务数量**: 需要清理的孤儿任务数

### 2. 日志规范

#### 2.1 关键日志点

```java
// 服务注册
log.info("推理服务注册成功: serviceId={}, serviceName={}", serviceId, serviceName);

// 任务调度
log.info("任务调度成功: taskId={}, serviceId={}, allocationId={}", taskId, serviceId, allocationId);

// 配额更新
log.debug("服务配额更新: serviceId={}, oldQuota={}, newQuota={}", serviceId, oldQuota, newQuota);

// 故障处理
log.warn("发现孤儿任务: taskId={}, serviceId={}, reason={}", taskId, serviceId, reason);
```

#### 2.2 日志级别建议

- **ERROR**: 系统异常、任务分发失败、服务不可达
- **WARN**: 孤儿任务、配额异常、重试操作
- **INFO**: 服务注册、任务调度、状态变更
- **DEBUG**: 配额更新、健康检查、内部状态

## 🚀 部署和配置

### 1. 环境要求

#### 1.1 基础环境

- **Java**: JDK 11+
- **Python**: 3.9+
- **数据库**: MySQL 8.0+
- **消息队列**: Kafka 2.8+
- **容器**: Docker 20.10+

#### 1.2 网络配置

```yaml
# 端口规划
services:
  scheduler:
    ports: ["8080:8080"]
  inference:
    ports: ["9001:9001"]
  mysql:
    ports: ["3306:3306"]
  kafka:
    ports: ["9092:9092"]
```

### 2. 配置模板

#### 2.1 Scheduler配置

```yaml
# application.yml
spring:
  datasource:
    url: ****************************************
    username: scheduler
    password: ${DB_PASSWORD}
  
scheduler:
  strategy:
    default-mode: FILL_FIRST
    enable-dynamic-schedule: true
  
health-check:
  interval: 30
  timeout: 10
```

#### 2.2 Inference配置

```yaml
# config.yaml
service_registry:
  enabled: true
  scheduler:
    url: "http://scheduler:8080"
    registration_retry_interval: 30
  service:
    name: "${SERVICE_NAME:-inference-service-1}"
    max_quota: "${MAX_QUOTA:-10}"
    region: "${REGION:-default}"
    gpu_type: "${GPU_TYPE:-A10}"
```

### 3. 启动顺序

1. **基础设施**: MySQL、Kafka
2. **调度器**: Scheduler服务
3. **推理服务**: Inference服务（自动注册）
4. **客户端**: 业务应用

```bash
# 启动命令示例
docker-compose up -d mysql kafka
docker-compose up -d scheduler
docker-compose up -d inference
```

## 📚 最佳实践

### 1. 性能优化

- **连接池配置**: 合理设置HTTP连接池大小
- **批量操作**: 批量查询和更新数据库操作
- **缓存策略**: 缓存服务列表和配额信息
- **异步处理**: 非关键路径使用异步处理

### 2. 可靠性保证

- **幂等性设计**: 所有操作支持重复执行
- **事务管理**: 关键操作使用数据库事务
- **超时控制**: 设置合理的网络超时时间
- **熔断机制**: 防止级联故障

### 3. 监控告警

- **服务可用性监控**: 监控服务注册状态
- **配额使用率告警**: 配额使用率超过80%时告警
- **任务失败率告警**: 任务失败率超过5%时告警
- **孤儿任务告警**: 孤儿任务数量异常时告警

---

## 📞 联系信息

如有问题或建议，请联系开发团队或查看相关技术文档。
