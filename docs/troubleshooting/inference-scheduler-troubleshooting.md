# Inference-Scheduler 故障排查手册

## 📋 概述

本手册提供 Inference 推理服务与 Scheduler 调度器系统的常见故障诊断、排查步骤和解决方案，帮助运维人员快速定位和解决问题。

## 🚨 故障分类

### 1. 故障严重级别

| 级别 | 描述 | 响应时间 | 示例 |
|------|------|----------|------|
| **P0 - 紧急** | 系统完全不可用 | 15分钟 | 所有服务宕机、数据库崩溃 |
| **P1 - 高** | 核心功能受影响 | 1小时 | 任务调度失败、服务无法注册 |
| **P2 - 中** | 部分功能异常 | 4小时 | 个别推理服务异常、性能下降 |
| **P3 - 低** | 轻微影响 | 24小时 | 日志告警、监控指标异常 |

### 2. 故障类型

- **服务故障**: 服务启动失败、崩溃、无响应
- **网络故障**: 连接超时、网络不通、DNS解析失败
- **资源故障**: 内存不足、磁盘满、GPU异常
- **配置故障**: 配置错误、参数不当、权限问题
- **数据故障**: 数据库连接失败、数据不一致、事务异常

## 🔍 诊断工具

### 1. 快速诊断脚本

```bash
#!/bin/bash
# quick_diagnosis.sh - 快速系统诊断

echo "=== CV系统快速诊断 ==="
echo "诊断时间: $(date)"
echo

# 1. 服务状态检查
echo "1. 服务状态检查"
echo "==================="
services=("scheduler:8080" "inference:9001" "mysql:3306" "kafka:9092")
for service in "${services[@]}"; do
    IFS=':' read -r name port <<< "$service"
    if nc -z localhost $port 2>/dev/null; then
        echo "✅ $name ($port) - 正常"
    else
        echo "❌ $name ($port) - 异常"
    fi
done
echo

# 2. 资源使用检查
echo "2. 资源使用检查"
echo "==================="
echo "CPU使用率: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)%"
echo "内存使用: $(free | grep Mem | awk '{printf "%.1f%%", $3/$2 * 100.0}')"
echo "磁盘使用: $(df -h / | awk 'NR==2{print $5}')"
if command -v nvidia-smi &> /dev/null; then
    echo "GPU状态: $(nvidia-smi --query-gpu=utilization.gpu --format=csv,noheader,nounits | head -1)%"
fi
echo

# 3. 关键进程检查
echo "3. 关键进程检查"
echo "==================="
processes=("java.*scheduler" "python.*inference" "mysqld" "kafka")
for process in "${processes[@]}"; do
    if pgrep -f "$process" > /dev/null; then
        echo "✅ $process - 运行中"
    else
        echo "❌ $process - 未运行"
    fi
done
echo

# 4. 网络连通性检查
echo "4. 网络连通性检查"
echo "==================="
endpoints=("http://localhost:8080/actuator/health" "http://localhost:9001/health")
for endpoint in "${endpoints[@]}"; do
    if curl -s -f "$endpoint" > /dev/null; then
        echo "✅ $endpoint - 可访问"
    else
        echo "❌ $endpoint - 不可访问"
    fi
done
echo

# 5. 最近错误日志
echo "5. 最近错误日志"
echo "==================="
echo "Scheduler错误:"
tail -n 10 logs/scheduler/scheduler.log 2>/dev/null | grep -i error | tail -3
echo "Inference错误:"
tail -n 10 logs/inference/inference.log 2>/dev/null | grep -i error | tail -3
echo

echo "=== 诊断完成 ==="
```

### 2. 详细诊断脚本

```bash
#!/bin/bash
# detailed_diagnosis.sh - 详细系统诊断

# 生成诊断报告
REPORT_FILE="diagnosis_report_$(date +%Y%m%d_%H%M%S).txt"

{
    echo "=== CV系统详细诊断报告 ==="
    echo "生成时间: $(date)"
    echo "主机名: $(hostname)"
    echo "操作系统: $(uname -a)"
    echo

    # 系统信息
    echo "=== 系统信息 ==="
    echo "CPU信息:"
    lscpu | grep -E "Model name|CPU\(s\)|Thread"
    echo
    echo "内存信息:"
    free -h
    echo
    echo "磁盘信息:"
    df -h
    echo
    if command -v nvidia-smi &> /dev/null; then
        echo "GPU信息:"
        nvidia-smi
        echo
    fi

    # 网络信息
    echo "=== 网络信息 ==="
    echo "网络接口:"
    ip addr show | grep -E "inet |UP"
    echo
    echo "端口监听:"
    netstat -tlnp | grep -E ":(8080|9001|3306|9092)"
    echo

    # 服务状态
    echo "=== 服务状态 ==="
    echo "Docker容器:"
    docker ps -a 2>/dev/null || echo "Docker未安装或无权限"
    echo
    echo "Java进程:"
    jps -v 2>/dev/null || echo "Java未安装"
    echo
    echo "Python进程:"
    ps aux | grep python | grep -v grep
    echo

    # 配置文件
    echo "=== 配置文件检查 ==="
    echo "Scheduler配置:"
    if [ -f "configs/scheduler/application.yml" ]; then
        echo "配置文件存在"
        grep -E "url:|port:|password:" configs/scheduler/application.yml | sed 's/password:.*/password: ***/'
    else
        echo "配置文件不存在"
    fi
    echo
    echo "Inference配置:"
    if [ -f "configs/inference/config.yaml" ]; then
        echo "配置文件存在"
        grep -E "url:|port:|max_quota:" configs/inference/config.yaml
    else
        echo "配置文件不存在"
    fi
    echo

    # 日志分析
    echo "=== 日志分析 ==="
    echo "Scheduler最近错误 (最近100行):"
    tail -n 100 logs/scheduler/scheduler.log 2>/dev/null | grep -i -E "error|exception|failed" | tail -10
    echo
    echo "Inference最近错误 (最近100行):"
    tail -n 100 logs/inference/inference.log 2>/dev/null | grep -i -E "error|exception|failed" | tail -10
    echo

} > "$REPORT_FILE"

echo "详细诊断报告已生成: $REPORT_FILE"
```

## 🔧 常见故障排查

### 1. 服务启动失败

#### 1.1 Scheduler 启动失败

**症状**: Scheduler 服务无法启动或启动后立即退出

**排查步骤**:

```bash
# 1. 检查Java环境
java -version
echo $JAVA_HOME

# 2. 检查端口占用
netstat -tlnp | grep 8080
lsof -i :8080

# 3. 检查配置文件
cat configs/scheduler/application.yml | grep -E "url:|port:|password:"

# 4. 检查数据库连接
mysql -h localhost -u scheduler -p cv_scheduler -e "SELECT 1"

# 5. 查看启动日志
tail -f logs/scheduler/scheduler.log

# 6. 检查JVM参数
ps aux | grep java | grep scheduler
```

**常见原因及解决方案**:

| 原因 | 解决方案 |
|------|----------|
| 端口被占用 | `kill -9 $(lsof -ti:8080)` 或修改端口 |
| 数据库连接失败 | 检查数据库服务、用户名密码、网络连通性 |
| 配置文件错误 | 验证YAML格式，检查配置项 |
| 内存不足 | 调整JVM参数 `-Xms` `-Xmx` |
| 权限问题 | 检查文件权限 `chmod 755` |

#### 1.2 Inference 启动失败

**症状**: Inference 服务无法启动或GPU初始化失败

**排查步骤**:

```bash
# 1. 检查Python环境
python --version
pip list | grep -E "torch|transformers|opencv"

# 2. 检查GPU状态
nvidia-smi
nvidia-ml-py3 -c "import pynvml; pynvml.nvmlInit(); print('GPU可用')"

# 3. 检查模型文件
ls -la models/
du -sh models/*

# 4. 检查配置文件
python -c "import yaml; print(yaml.safe_load(open('configs/inference/config.yaml')))"

# 5. 测试模块导入
python -c "from vas.modules.yolov8_detector_module import YOLOv8DetectorModule"

# 6. 查看启动日志
tail -f logs/inference/inference.log
```

**常见原因及解决方案**:

| 原因 | 解决方案 |
|------|----------|
| GPU驱动问题 | 重新安装NVIDIA驱动和CUDA |
| 模型文件缺失 | 下载或复制模型文件到正确路径 |
| Python依赖缺失 | `pip install -r requirements.txt` |
| 内存不足 | 释放内存或增加交换空间 |
| 权限问题 | 检查模型文件和日志目录权限 |

### 2. 服务注册失败

#### 2.1 症状识别

```bash
# 检查服务注册状态
curl -s http://localhost:8080/api/v1/services | jq '.services[] | {serviceName, status, lastHeartbeat}'

# 检查Inference注册日志
grep -i "register" logs/inference/inference.log | tail -10
```

#### 2.2 排查步骤

```bash
# 1. 检查网络连通性
curl -v http://localhost:8080/actuator/health
telnet localhost 8080

# 2. 检查Scheduler服务状态
curl -s http://localhost:8080/actuator/health | jq .

# 3. 检查注册配置
grep -A 10 "service_registry" configs/inference/config.yaml

# 4. 手动测试注册接口
curl -X POST http://localhost:8080/api/v1/services/register \
  -H "Content-Type: application/json" \
  -d '{
    "serviceName": "test-service",
    "baseUrl": "http://localhost:9001",
    "maxQuota": 10,
    "region": "default"
  }'
```

#### 2.3 解决方案

| 问题 | 解决方案 |
|------|----------|
| 网络不通 | 检查防火墙、路由配置 |
| Scheduler未启动 | 启动Scheduler服务 |
| 配置错误 | 修正URL、端口等配置 |
| 数据库异常 | 检查数据库连接和表结构 |

### 3. 任务调度失败

#### 3.1 症状识别

```bash
# 检查任务分配状态
curl -s "http://localhost:8080/api/v1/scheduler/tasks" | jq '.tasks[] | {taskId, status, serviceId}'

# 检查服务配额
curl -s "http://localhost:8080/api/v1/services" | jq '.services[] | {serviceName, currentQuota, maxQuota}'
```

#### 3.2 排查步骤

```bash
# 1. 检查可用服务
curl -s "http://localhost:8080/api/v1/services?status=ACTIVE" | jq .

# 2. 检查任务配置
cat task_request.json | jq .

# 3. 测试任务创建
curl -X POST http://localhost:8080/api/v1/scheduler/schedule \
  -H "Content-Type: application/json" \
  -d @task_request.json

# 4. 检查Inference任务接收
curl -s http://localhost:9001/api/v1/tasks | jq .

# 5. 查看调度日志
grep -i "schedule" logs/scheduler/scheduler.log | tail -10
```

#### 3.3 常见问题

| 问题 | 原因 | 解决方案 |
|------|------|----------|
| NO_AVAILABLE_SERVICE | 没有可用的推理服务 | 检查服务注册状态，启动更多服务 |
| QUOTA_EXCEEDED | 所有服务配额已满 | 等待任务完成或增加服务实例 |
| TASK_SEND_FAILED | 任务发送到Inference失败 | 检查网络连接和Inference服务状态 |
| INVALID_TASK_CONFIG | 任务配置格式错误 | 验证JSON格式和必填字段 |

### 4. 性能问题

#### 4.1 高延迟问题

**排查步骤**:

```bash
# 1. 检查系统负载
top
htop
iostat -x 1

# 2. 检查网络延迟
ping localhost
curl -w "@curl-format.txt" -s -o /dev/null http://localhost:8080/actuator/health

# 3. 检查数据库性能
mysql -e "SHOW PROCESSLIST;" cv_scheduler
mysql -e "SHOW ENGINE INNODB STATUS\G" cv_scheduler

# 4. 检查JVM性能
jstat -gc $(pgrep -f scheduler) 1s 10
jmap -histo $(pgrep -f scheduler) | head -20

# 5. 检查GPU使用率
nvidia-smi -l 1
```

**优化建议**:

```bash
# JVM调优
export JAVA_OPTS="-Xms4g -Xmx8g -XX:+UseG1GC -XX:MaxGCPauseMillis=200 -XX:+PrintGCDetails"

# 数据库调优
# my.cnf
[mysqld]
innodb_buffer_pool_size = 4G
innodb_log_file_size = 512M
max_connections = 500
query_cache_size = 256M

# Python调优
export OMP_NUM_THREADS=8
export CUDA_VISIBLE_DEVICES=0,1
```

#### 4.2 内存泄漏问题

**检测方法**:

```bash
# 1. 监控内存使用趋势
while true; do
    echo "$(date): $(free -m | grep Mem | awk '{print $3}')" >> memory_usage.log
    sleep 60
done

# 2. Java堆内存分析
jmap -dump:format=b,file=heap_dump.hprof $(pgrep -f scheduler)
# 使用Eclipse MAT分析heap_dump.hprof

# 3. Python内存分析
pip install memory_profiler
python -m memory_profiler inference_service.py
```

### 5. 数据一致性问题

#### 5.1 孤儿任务问题

**检测脚本**:

```bash
#!/bin/bash
# check_orphan_tasks.sh

echo "=== 孤儿任务检测 ==="

# 获取Scheduler中的活跃任务
scheduler_tasks=$(curl -s "http://localhost:8080/api/v1/scheduler/tasks?status=RUNNING" | jq -r '.tasks[].taskId')

# 获取Inference中的任务
inference_tasks=$(curl -s "http://localhost:9001/api/v1/tasks" | jq -r '.tasks[].task_id')

echo "Scheduler活跃任务数: $(echo "$scheduler_tasks" | wc -l)"
echo "Inference任务数: $(echo "$inference_tasks" | wc -l)"

# 检查Scheduler孤儿任务
echo "Scheduler孤儿任务:"
for task in $scheduler_tasks; do
    if ! echo "$inference_tasks" | grep -q "$task"; then
        echo "  - $task (在Scheduler中但不在Inference中)"
    fi
done

# 检查Inference孤儿任务
echo "Inference孤儿任务:"
for task in $inference_tasks; do
    if ! echo "$scheduler_tasks" | grep -q "$task"; then
        echo "  - $task (在Inference中但不在Scheduler中)"
    fi
done
```

#### 5.2 配额不一致问题

**检测和修复**:

```bash
# 检测配额不一致
curl -s "http://localhost:8080/api/v1/services" | jq '.services[] | {serviceName, currentQuota, maxQuota, status}'

# 手动修复配额
mysql -e "
UPDATE inference_services 
SET current_quota = (
    SELECT COUNT(*) 
    FROM task_allocations 
    WHERE service_id = inference_services.service_id 
    AND task_status = 'RUNNING'
);" cv_scheduler
```

## 🔄 恢复程序

### 1. 服务重启程序

```bash
#!/bin/bash
# service_restart.sh

SERVICE=$1
if [ -z "$SERVICE" ]; then
    echo "用法: $0 <scheduler|inference|all>"
    exit 1
fi

restart_scheduler() {
    echo "重启Scheduler服务..."
    docker-compose stop scheduler
    sleep 5
    docker-compose start scheduler
    
    # 等待服务启动
    for i in {1..30}; do
        if curl -s http://localhost:8080/actuator/health > /dev/null; then
            echo "Scheduler服务启动成功"
            return 0
        fi
        sleep 2
    done
    echo "Scheduler服务启动失败"
    return 1
}

restart_inference() {
    echo "重启Inference服务..."
    docker-compose stop inference-1
    sleep 5
    docker-compose start inference-1
    
    # 等待服务启动
    for i in {1..60}; do
        if curl -s http://localhost:9001/health > /dev/null; then
            echo "Inference服务启动成功"
            return 0
        fi
        sleep 2
    done
    echo "Inference服务启动失败"
    return 1
}

case $SERVICE in
    scheduler)
        restart_scheduler
        ;;
    inference)
        restart_inference
        ;;
    all)
        restart_scheduler && restart_inference
        ;;
    *)
        echo "未知服务: $SERVICE"
        exit 1
        ;;
esac
```

### 2. 数据恢复程序

```bash
#!/bin/bash
# data_recovery.sh

BACKUP_DIR="/backup"
BACKUP_FILE="$1"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup_file.sql>"
    exit 1
fi

echo "开始数据恢复..."

# 1. 停止相关服务
echo "停止服务..."
docker-compose stop scheduler inference-1

# 2. 备份当前数据
echo "备份当前数据..."
mysqldump -h localhost -u root -p cv_scheduler > "current_backup_$(date +%Y%m%d_%H%M%S).sql"

# 3. 恢复数据
echo "恢复数据..."
mysql -h localhost -u root -p cv_scheduler < "$BACKUP_DIR/$BACKUP_FILE"

# 4. 验证数据
echo "验证数据..."
mysql -h localhost -u root -p cv_scheduler -e "
SELECT 
    (SELECT COUNT(*) FROM inference_services) as services_count,
    (SELECT COUNT(*) FROM task_allocations) as tasks_count;
"

# 5. 重启服务
echo "重启服务..."
docker-compose start scheduler
sleep 10
docker-compose start inference-1

echo "数据恢复完成"
```

## 📊 监控和预警

### 1. 关键指标监控

```bash
#!/bin/bash
# monitoring.sh

# 创建监控指标收集脚本
cat > collect_metrics.sh << 'EOF'
#!/bin/bash

TIMESTAMP=$(date +%s)
HOSTNAME=$(hostname)

# 系统指标
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
DISK_USAGE=$(df -h / | awk 'NR==2{print $5}' | cut -d'%' -f1)

# 服务指标
SCHEDULER_STATUS=$(curl -s http://localhost:8080/actuator/health | jq -r '.status // "DOWN"')
INFERENCE_STATUS=$(curl -s http://localhost:9001/health | jq -r '.status // "DOWN"')

# 业务指标
ACTIVE_SERVICES=$(curl -s "http://localhost:8080/api/v1/services?status=ACTIVE" | jq '.services | length')
RUNNING_TASKS=$(curl -s "http://localhost:8080/api/v1/scheduler/tasks?status=RUNNING" | jq '.tasks | length')

# 输出指标
echo "cv_system_cpu_usage{host=\"$HOSTNAME\"} $CPU_USAGE $TIMESTAMP"
echo "cv_system_memory_usage{host=\"$HOSTNAME\"} $MEM_USAGE $TIMESTAMP"
echo "cv_system_disk_usage{host=\"$HOSTNAME\"} $DISK_USAGE $TIMESTAMP"
echo "cv_scheduler_status{host=\"$HOSTNAME\"} $([ "$SCHEDULER_STATUS" = "UP" ] && echo 1 || echo 0) $TIMESTAMP"
echo "cv_inference_status{host=\"$HOSTNAME\"} $([ "$INFERENCE_STATUS" = "UP" ] && echo 1 || echo 0) $TIMESTAMP"
echo "cv_active_services{host=\"$HOSTNAME\"} $ACTIVE_SERVICES $TIMESTAMP"
echo "cv_running_tasks{host=\"$HOSTNAME\"} $RUNNING_TASKS $TIMESTAMP"
EOF

chmod +x collect_metrics.sh

# 添加到crontab
echo "* * * * * /path/to/collect_metrics.sh >> /var/log/cv_metrics.log" | crontab -
```

### 2. 告警脚本

```bash
#!/bin/bash
# alerting.sh

# 告警配置
ALERT_EMAIL="<EMAIL>"
ALERT_WEBHOOK="https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"

send_alert() {
    local severity=$1
    local message=$2
    local timestamp=$(date)
    
    # 发送邮件告警
    echo "[$severity] $message - $timestamp" | mail -s "CV系统告警" $ALERT_EMAIL
    
    # 发送Slack告警
    curl -X POST -H 'Content-type: application/json' \
        --data "{\"text\":\"🚨 [$severity] $message\\n时间: $timestamp\"}" \
        $ALERT_WEBHOOK
}

# 检查服务状态
check_services() {
    if ! curl -s http://localhost:8080/actuator/health > /dev/null; then
        send_alert "CRITICAL" "Scheduler服务不可用"
    fi
    
    if ! curl -s http://localhost:9001/health > /dev/null; then
        send_alert "CRITICAL" "Inference服务不可用"
    fi
}

# 检查资源使用
check_resources() {
    CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1)
    if (( $(echo "$CPU_USAGE > 80" | bc -l) )); then
        send_alert "WARNING" "CPU使用率过高: ${CPU_USAGE}%"
    fi
    
    MEM_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    if (( $(echo "$MEM_USAGE > 85" | bc -l) )); then
        send_alert "WARNING" "内存使用率过高: ${MEM_USAGE}%"
    fi
}

# 执行检查
check_services
check_resources
```

## 📚 故障案例库

### 案例1: 大量任务调度失败

**现象**: 突然出现大量任务调度失败，错误信息为"NO_AVAILABLE_SERVICE"

**排查过程**:
1. 检查服务注册状态 - 发现所有Inference服务状态为INACTIVE
2. 检查Inference服务 - 服务正常运行，但无法连接Scheduler
3. 检查网络 - 发现防火墙规则变更，阻止了8080端口访问

**解决方案**:
```bash
# 修复防火墙规则
sudo ufw allow 8080
sudo iptables -A INPUT -p tcp --dport 8080 -j ACCEPT

# 重启服务注册
docker-compose restart inference-1
```

**预防措施**: 
- 监控网络连通性
- 防火墙变更需要通知
- 增加网络层面的健康检查

### 案例2: 内存泄漏导致服务崩溃

**现象**: Inference服务运行一段时间后内存使用持续增长，最终OOM崩溃

**排查过程**:
1. 监控内存使用趋势 - 确认存在内存泄漏
2. 分析代码 - 发现图像处理后未及时释放内存
3. 使用内存分析工具 - 定位到具体的内存泄漏点

**解决方案**:
```python
# 修复内存泄漏
def process_frame(self, frame):
    try:
        # 处理图像
        result = self.model.predict(frame)
        return result
    finally:
        # 确保释放GPU内存
        torch.cuda.empty_cache()
        # 释放OpenCV内存
        cv2.destroyAllWindows()
```

**预防措施**:
- 定期内存使用监控
- 代码审查关注资源释放
- 增加内存使用告警

---

## 📞 应急联系

### 紧急故障联系方式

- **7x24技术支持**: +86-xxx-xxxx-xxxx
- **技术负责人**: <EMAIL>
- **运维团队**: <EMAIL>
- **Slack频道**: #cv-system-alerts

### 升级流程

1. **L1支持** (运维工程师): 基础故障排查和恢复
2. **L2支持** (系统工程师): 复杂问题分析和解决
3. **L3支持** (架构师/开发): 系统设计问题和代码修复

---

本手册将根据实际故障案例持续更新和完善。
