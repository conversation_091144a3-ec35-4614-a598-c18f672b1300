#!/bin/bash

# RTSP流测试脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 测试RTSP流
test_rtsp_stream() {
    local stream_name=$1
    local rtsp_url="rtsp://localhost:9554/$stream_name"
    
    log_info "测试RTSP流: $stream_name"
    
    # 使用ffprobe测试流
    if ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,width,height -of csv=p=0 "$rtsp_url" 2>/dev/null; then
        log_success "✅ $stream_name 流正常"
        return 0
    else
        log_error "❌ $stream_name 流异常"
        return 1
    fi
}

# 测试MediaMTX API
test_api() {
    log_info "测试MediaMTX API..."
    
    if curl -s http://localhost:8889/v3/config/global/get > /dev/null; then
        log_success "✅ MediaMTX API 正常"
        return 0
    else
        log_error "❌ MediaMTX API 异常"
        return 1
    fi
}

# 显示流信息
show_stream_info() {
    log_info "获取流信息..."
    
    echo ""
    echo "📡 可用的RTSP流:"
    
    # 获取流列表
    if command -v jq &> /dev/null; then
        curl -s http://localhost:8889/v3/paths/list | jq -r '.items[] | "  🎬 rtsp://localhost:9554/\(.name) - \(.tracks | join(", ")) - Ready: \(.ready)"'
    else
        echo "  🎬 rtsp://localhost:9554/knight"
        echo "  🎬 rtsp://localhost:9554/test"
        echo "  🎬 rtsp://localhost:9554/stream1"
        echo "  🎬 rtsp://localhost:9554/camera1"
    fi
    
    echo ""
    echo "🌐 管理接口:"
    echo "  📊 MediaMTX API: http://localhost:8889"
    echo "  🎮 WebRTC: http://localhost:8888"
    
    echo ""
    echo "🧪 测试命令:"
    echo "  ffplay rtsp://localhost:9554/knight"
    echo "  vlc rtsp://localhost:9554/knight"
    echo "  ffprobe rtsp://localhost:9554/knight"
}

# 主函数
main() {
    echo "🧪 RTSP服务器测试"
    echo "=================="
    
    # 检查依赖
    if ! command -v ffprobe &> /dev/null; then
        log_warning "ffprobe 未安装，跳过流测试"
        test_api
        show_stream_info
        return
    fi
    
    # 测试API
    if ! test_api; then
        log_error "MediaMTX API 测试失败"
        exit 1
    fi
    
    # 测试各个流
    local streams=("knight" "test" "stream1" "camera1")
    local failed_count=0
    
    for stream in "${streams[@]}"; do
        if ! test_rtsp_stream "$stream"; then
            ((failed_count++))
        fi
    done
    
    echo ""
    if [ $failed_count -eq 0 ]; then
        log_success "🎉 所有测试通过！"
    else
        log_warning "⚠️  $failed_count 个流测试失败"
    fi
    
    show_stream_info
}

# 执行主函数
main "$@"
