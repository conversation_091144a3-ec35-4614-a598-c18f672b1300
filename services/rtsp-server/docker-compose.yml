version: '3.3'

services:
  rtsp-server:
    build:
      context: .
      dockerfile: Dockerfile
    image: rtsp-server:latest
    container_name: live555-rtsp-server
    ports:
      - "9554:9554"
      - "8889:8889"
      - "8888:8888"
    volumes:
      - ../video:/video:ro
      - ./converted:/app/converted:ro
      - ./logs:/logs
      - ./config/mediamtx.yml:/mediamtx.yml:ro
    environment:
      - TZ=Asia/Shanghai
    networks:
      - rtsp-network
    restart: unless-stopped
    mem_limit: 512m

networks:
  rtsp-network:
    driver: bridge

volumes:
  rtsp_logs:
    driver: local
