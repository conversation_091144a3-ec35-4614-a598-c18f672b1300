# Live555 RTSP服务器

基于Live555的RTSP流媒体服务器，用于将视频文件模拟成RTSP流，供inference-mock服务进行真实的RTSP流处理测试。

## 功能特性

- 🎬 将本地视频文件转换为RTSP流
- 📡 支持多路RTSP流同时输出
- 🔄 自动视频格式转换（H.264）
- 🐳 Docker容器化部署
- 📊 进程监控和自动重启
- 🔗 设备ID到RTSP流的映射

## 快速开始

### 1. 启动RTSP服务器

```bash
# 单独启动RTSP服务器
cd rtsp-server
./start-rtsp-server.sh

# 或使用docker-compose
docker-compose up -d
```

### 2. 验证RTSP流

```bash
# 使用ffplay测试
ffplay rtsp://localhost:9554/knight.264

# 使用VLC测试
vlc rtsp://localhost:9554/knight.264

# 使用ffprobe检查流信息
ffprobe rtsp://localhost:9554/knight.264
```

### 3. 可用的RTSP流

- `rtsp://localhost:9554/knight.264` - 主视频流
- `rtsp://localhost:9554/test.264` - 测试流
- `rtsp://localhost:9554/stream1.264` - 流1
- `rtsp://localhost:9554/camera1.264` - 摄像头1

## 目录结构

```
rtsp-server/
├── Dockerfile              # Docker镜像构建文件
├── docker-compose.yml      # Docker Compose配置
├── start-rtsp-server.sh     # 启动脚本
├── scripts/
│   ├── start-rtsp.sh       # RTSP服务启动脚本
│   └── convert-video.sh    # 视频转换脚本
├── config/
│   └── supervisord.conf    # Supervisor配置
├── logs/                   # 日志目录
└── README.md              # 说明文档
```

## 配置说明

### Docker配置

- **端口映射**: `9554:9554` (RTSP端口)
- **视频目录**: `../video:/app/videos:ro` (只读挂载)
- **日志目录**: `./logs:/app/logs` (日志输出)

### 视频转换参数

```bash
ffmpeg -i input.mov \
    -c:v libx264 \      # H.264视频编码
    -preset fast \      # 编码速度
    -crf 23 \          # 质量控制
    -c:a aac \         # AAC音频编码
    -b:a 128k \        # 音频比特率
    -f mp4 \           # MP4格式
    output.264
```

## 与inference-mock集成

### 设备映射配置

在`inference-mock/config.yaml`中配置设备到RTSP流的映射：

```yaml
rtsp:
  enabled: true
  default_server: "localhost:9554"
  stream_mapping:
    "camera-001": "rtsp://localhost:9554/knight.264"
    "camera-002": "rtsp://localhost:9554/test.264"
    "camera-kafka-test-001": "rtsp://localhost:9554/stream1.264"
    "default": "rtsp://localhost:9554/knight.264"
```

### 自动URL解析

inference-mock会自动将设备ID映射到对应的RTSP流：

```python
# 自动解析RTSP URL
rtsp_url = RTSPHandler.resolve_rtsp_url(device_id, original_url)
```

## 故障排除

### 常见问题

1. **RTSP流无法连接**
   ```bash
   # 检查容器状态
   docker ps | grep live555
   
   # 查看日志
   docker-compose logs -f
   ```

2. **视频转换失败**
   ```bash
   # 检查视频文件
   ls -la ../video/knight.mov
   
   # 手动转换测试
   docker exec live555-rtsp-server /app/convert-video.sh
   ```

3. **端口被占用**
   ```bash
   # 检查端口占用
   netstat -an | grep 9554
   
   # 修改端口映射
   # 编辑docker-compose.yml中的ports配置
   ```

### 日志查看

```bash
# 查看所有日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f rtsp-server

# 查看容器内日志
docker exec live555-rtsp-server tail -f /app/logs/rtsp-server.out.log
```

## 性能优化

### 内存使用

- 容器默认内存限制：512MB
- 可通过docker-compose.yml调整

### 网络优化

- 使用TCP传输协议（更稳定）
- 调整缓冲区大小
- 设置合适的超时时间

### 并发连接

- Live555默认支持多个并发连接
- 可通过配置调整最大连接数

## 扩展功能

### 添加新的视频流

1. 将视频文件放入`../video/`目录
2. 修改`convert-video.sh`添加转换逻辑
3. 重启容器

### 自定义RTSP路径

修改`convert-video.sh`中的符号链接：

```bash
ln -sf your-video.264 custom-stream.264
```

然后可通过`rtsp://localhost:9554/custom-stream.264`访问。

## 监控和维护

### 健康检查

容器包含健康检查机制，自动监控RTSP服务状态。

### 自动重启

使用Supervisor管理进程，自动重启失败的服务。

### 日志轮转

建议配置日志轮转避免日志文件过大：

```bash
# 添加到crontab
0 0 * * * docker exec live555-rtsp-server logrotate /etc/logrotate.conf
```
