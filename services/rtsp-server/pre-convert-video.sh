#!/bin/bash

# 预转换视频脚本 - 在主机上运行
# 将视频预转换为RTSP优化格式，避免实时转码延迟

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v ffmpeg &> /dev/null; then
        log_error "FFmpeg 未安装或不在PATH中"
        log_info "请安装FFmpeg: https://ffmpeg.org/download.html"
        exit 1
    fi
    
    log_success "FFmpeg 已安装"
}

# 转换视频
convert_video() {
    local input_file="../video/knight.mov"
    local output_dir="./converted"
    
    log_info "开始预转换视频文件..."
    
    # 检查输入文件
    if [ ! -f "$input_file" ]; then
        log_error "找不到视频文件: $input_file"
        exit 1
    fi
    
    # 创建输出目录
    mkdir -p "$output_dir"
    
    log_info "输入文件: $input_file"
    log_info "输出目录: $output_dir"
    
    # 显示原始视频信息
    log_info "原始视频信息:"
    ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,width,height,bit_rate,duration -of csv=p=0 "$input_file" || true
    
    log_info "开始转换为RTSP优化格式..."
    
    # 优化的转换参数
    ffmpeg -i "$input_file" \
        -c:v libx264 \
        -preset medium \
        -profile:v baseline \
        -level 3.1 \
        -pix_fmt yuv420p \
        -g 30 \
        -keyint_min 30 \
        -sc_threshold 0 \
        -b:v 2000k \
        -maxrate 2500k \
        -bufsize 1000k \
        -c:a aac \
        -b:a 128k \
        -ar 44100 \
        -f mp4 \
        -movflags +faststart \
        -y "$output_dir/knight.mp4"
    
    if [ $? -eq 0 ]; then
        log_success "主视频转换成功: knight.mp4"
        
        # 创建其他流的副本
        log_info "创建其他流文件..."
        cp "$output_dir/knight.mp4" "$output_dir/test.mp4"
        cp "$output_dir/knight.mp4" "$output_dir/stream1.mp4"
        cp "$output_dir/knight.mp4" "$output_dir/camera1.mp4"
        
        log_success "所有流文件创建完成"
        
        # 显示文件信息
        echo ""
        log_info "转换后的文件:"
        ls -lh "$output_dir"/*.mp4
        
        echo ""
        log_info "转换后视频信息:"
        ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,profile,level,width,height,bit_rate -of csv=p=0 "$output_dir/knight.mp4"
        
        echo ""
        log_success "🎉 预转换完成！"
        log_info "现在启动RTSP服务器将使用预转换文件，大幅减少延迟"
        
    else
        log_error "视频转换失败"
        exit 1
    fi
}

# 清理转换文件
clean_converted() {
    local output_dir="./converted"
    
    if [ -d "$output_dir" ]; then
        log_info "清理转换文件..."
        rm -rf "$output_dir"
        log_success "转换文件已清理"
    else
        log_warning "没有找到转换文件目录"
    fi
}

# 显示帮助
show_help() {
    echo "视频预转换脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  convert   转换视频文件 (默认)"
    echo "  clean     清理转换后的文件"
    echo "  info      显示视频信息"
    echo "  help      显示此帮助信息"
    echo ""
    echo "说明:"
    echo "  此脚本将 ../video/knight.mov 预转换为RTSP优化格式"
    echo "  转换后的文件保存在 ./converted/ 目录中"
    echo "  RTSP服务器将使用预转换文件，避免实时编码延迟"
    echo ""
    echo "示例:"
    echo "  $0 convert    # 转换视频文件"
    echo "  $0 clean      # 清理转换文件"
}

# 显示视频信息
show_info() {
    local input_file="../video/knight.mov"
    local output_dir="./converted"
    
    if [ -f "$input_file" ]; then
        log_info "原始视频信息:"
        ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,width,height,bit_rate,duration -of csv=p=0 "$input_file"
        echo ""
    fi
    
    if [ -f "$output_dir/knight.mp4" ]; then
        log_info "转换后视频信息:"
        ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,profile,level,width,height,bit_rate -of csv=p=0 "$output_dir/knight.mp4"
        echo ""
        log_info "转换后的文件:"
        ls -lh "$output_dir"/*.mp4
    else
        log_warning "未找到转换后的文件，请先运行转换"
    fi
}

# 主函数
main() {
    case "${1:-convert}" in
        "convert")
            check_dependencies
            convert_video
            ;;
        "clean")
            clean_converted
            ;;
        "info")
            show_info
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
