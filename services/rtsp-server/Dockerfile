# MediaMTX RTSP服务器Dockerfile
FROM alpine:3.18

# 使用阿里云镜像源加速
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装ffmpeg和其他必要工具
RUN apk add --no-cache \
    ffmpeg \
    bash \
    curl \
    wget \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# 下载并安装MediaMTX
ARG MEDIAMTX_VERSION=v1.5.1
RUN wget -O /tmp/mediamtx.tar.gz "https://github.com/bluenviron/mediamtx/releases/download/${MEDIAMTX_VERSION}/mediamtx_${MEDIAMTX_VERSION}_linux_amd64.tar.gz" && \
    tar -xzf /tmp/mediamtx.tar.gz -C /usr/local/bin/ && \
    chmod +x /usr/local/bin/mediamtx && \
    rm /tmp/mediamtx.tar.gz

# 创建工作目录
WORKDIR /app
RUN mkdir -p /video /logs /app/converted

# 复制脚本
COPY scripts/start-simple-rtsp.sh /app/start-simple-rtsp.sh
COPY scripts/convert-video.sh /app/convert-video.sh
RUN chmod +x /app/start-simple-rtsp.sh /app/convert-video.sh

# 暴露RTSP端口
EXPOSE 9554

# 启动脚本
CMD ["/app/start-simple-rtsp.sh"]
