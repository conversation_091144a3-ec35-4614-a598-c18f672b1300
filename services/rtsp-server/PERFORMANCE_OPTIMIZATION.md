# RTSP服务器性能优化指南

## 🚀 已实施的优化

### 1. FFmpeg编码优化

已优化的编码参数：
- **preset ultrafast**: 最快编码速度
- **tune zerolatency**: 零延迟调优
- **profile baseline**: 更好的兼容性
- **GOP大小**: 30帧，减少关键帧间隔
- **比特率控制**: 2000k视频比特率，2500k最大比特率
- **TCP传输**: 强制使用TCP，更稳定

### 2. MediaMTX服务器优化

- **协议支持**: TCP和UDP双协议支持
- **缓冲优化**: 调整写队列大小到1024
- **超时设置**: 读写超时10秒
- **简化配置**: 移除不必要的配置项

### 3. VLC播放优化脚本

创建了 `vlc-optimized-play.sh` 脚本，包含以下优化：
- **网络缓存**: 减少到300ms
- **强制TCP**: 使用TCP传输
- **硬件解码**: 启用硬件加速
- **丢帧策略**: 丢弃延迟帧保持流畅

## 🎮 使用方法

### 启动优化的RTSP服务器
```bash
cd rtsp-server
./start-rtsp-server.sh
```

### 使用优化的VLC播放
```bash
# 播放默认流（knight）
./vlc-optimized-play.sh

# 播放指定流
./vlc-optimized-play.sh knight
./vlc-optimized-play.sh test
./vlc-optimized-play.sh stream1
./vlc-optimized-play.sh camera1

# 使用ffplay作为备选
./vlc-optimized-play.sh ffplay knight
```

### 手动VLC优化参数
如果你想手动启动VLC，使用以下参数：
```bash
vlc rtsp://localhost:9554/knight \
    --network-caching=300 \
    --rtsp-tcp \
    --live-caching=300 \
    --clock-jitter=0 \
    --clock-synchro=0 \
    --drop-late-frames \
    --skip-frames \
    --avcodec-hw=any \
    --no-audio-time-stretch \
    --rtsp-frame-buffer-size=500000
```

## 🔧 进一步优化建议

### 1. 网络优化
- 确保网络带宽充足（建议至少5Mbps）
- 使用有线连接而非WiFi
- 关闭其他占用带宽的应用

### 2. 系统优化
- 关闭不必要的后台程序
- 确保CPU和内存资源充足
- 使用SSD存储提高I/O性能

### 3. VLC设置优化
在VLC中手动设置：
1. 工具 → 偏好设置 → 输入/编解码器
2. 设置"网络缓存"为300ms
3. 启用"硬件解码"
4. 设置"跳过帧"为"自动"

### 4. 替代播放器
如果VLC仍然卡顿，可以尝试：
- **ffplay**: `ffplay rtsp://localhost:9554/knight`
- **MPV**: `mpv rtsp://localhost:9554/knight`
- **PotPlayer** (Windows)
- **IINA** (macOS)

## 📊 性能监控

### 检查流状态
```bash
# 查看流信息
curl -s http://localhost:8889/v3/paths/list | jq '.'

# 检查服务器状态
curl -s http://localhost:8889/v3/config/global/get
```

### 测试流性能
```bash
# 使用ffprobe检查流信息
ffprobe rtsp://localhost:9554/knight

# 测试所有流
./test-rtsp-streams.sh
```

## 🐛 故障排除

### 常见问题

1. **仍然卡顿**
   - 检查网络连接
   - 降低视频比特率
   - 尝试使用ffplay

2. **连接失败**
   - 检查防火墙设置
   - 确认端口9554未被占用
   - 重启RTSP服务器

3. **音视频不同步**
   - 使用TCP传输
   - 调整缓存设置
   - 检查系统时钟

### 日志查看
```bash
# 查看容器日志
docker logs live555-rtsp-server

# 查看实时日志
docker logs -f live555-rtsp-server
```

## 📈 性能指标

优化后的预期性能：
- **延迟**: < 1秒
- **帧率**: 稳定25-30fps
- **比特率**: 2Mbps
- **分辨率**: 1920x1080
- **编码**: H.264 Baseline

## 🎯 下一步优化

如果需要进一步优化：
1. 调整视频分辨率（降低到720p）
2. 减少比特率（1000k-1500k）
3. 使用硬件编码（如果支持）
4. 实施自适应比特率
5. 使用CDN分发（生产环境）
