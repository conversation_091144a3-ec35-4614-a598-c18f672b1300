#!/bin/bash

echo "🎬 预转换视频文件为RTSP优化格式..."

VIDEO_DIR="/video"
OUTPUT_DIR="/app/converted"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 检查输入视频文件
if [ ! -f "$VIDEO_DIR/knight.mov" ]; then
    echo "❌ 找不到视频文件: $VIDEO_DIR/knight.mov"
    exit 1
fi

echo "📹 转换 knight.mov 为优化的H.264格式..."

# 优化的转换参数，专为RTSP流设计
# -c:v libx264: H.264编码
# -preset medium: 平衡编码速度和质量
# -profile:v baseline: 最佳兼容性
# -level 3.1: H.264 level 3.1
# -pix_fmt yuv420p: 标准像素格式
# -g 30: GOP大小30帧
# -keyint_min 30: 最小关键帧间隔
# -sc_threshold 0: 禁用场景切换检测
# -b:v 2000k: 固定视频比特率
# -maxrate 2500k: 最大比特率
# -bufsize 1000k: 缓冲区大小
# -c:a aac: AAC音频编码
# -b:a 128k: 音频比特率128k
# -ar 44100: 音频采样率
# -f mp4: MP4容器格式
# -movflags +faststart: 优化流媒体播放

ffmpeg -i "$VIDEO_DIR/knight.mov" \
    -c:v libx264 \
    -preset medium \
    -profile:v baseline \
    -level 3.1 \
    -pix_fmt yuv420p \
    -g 30 \
    -keyint_min 30 \
    -sc_threshold 0 \
    -b:v 2000k \
    -maxrate 2500k \
    -bufsize 1000k \
    -c:a aac \
    -b:a 128k \
    -ar 44100 \
    -f mp4 \
    -movflags +faststart \
    -y "$OUTPUT_DIR/knight.mp4"

if [ $? -eq 0 ]; then
    echo "✅ 视频转换成功: knight.mp4"

    # 创建其他流的副本（或符号链接）
    cd "$OUTPUT_DIR"
    cp knight.mp4 test.mp4
    cp knight.mp4 stream1.mp4
    cp knight.mp4 camera1.mp4

    echo "🔗 创建流文件完成"
    echo "� 转换后的文件:"
    ls -lh *.mp4

    echo ""
    echo "📊 视频信息:"
    ffprobe -v quiet -select_streams v:0 -show_entries stream=codec_name,profile,level,width,height,bit_rate -of csv=p=0 "$OUTPUT_DIR/knight.mp4"

    echo ""
    echo "✅ 预转换完成！现在可以无延迟推送RTSP流"
else
    echo "❌ 视频转换失败"
    exit 1
fi
