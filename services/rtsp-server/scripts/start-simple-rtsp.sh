#!/bin/bash

echo "🚀 启动MediaMTX RTSP服务器..."

# 检查视频文件
if [ ! -f "/video/knight.mov" ]; then
    echo "❌ 视频文件不存在: /video/knight.mov"
    exit 1
fi

echo "📹 视频文件检查通过"

# 检查MediaMTX配置文件
if [ ! -f "/mediamtx.yml" ]; then
    echo "❌ MediaMTX配置文件不存在: /mediamtx.yml"
    exit 1
fi

echo "⚙️  MediaMTX配置文件检查通过"

# 启动MediaMTX服务器
echo "🎬 启动MediaMTX服务器..."
mediamtx /mediamtx.yml &

# 等待MediaMTX启动
echo "⏳ 等待MediaMTX服务器启动..."
sleep 5

# 检查MediaMTX是否启动成功
if ! curl -s http://localhost:8889/v3/config/global/get > /dev/null; then
    echo "❌ MediaMTX服务器启动失败"
    exit 1
fi

echo "✅ MediaMTX服务器启动成功"
echo "📡 RTSP服务器运行在端口 9554"
echo "🌐 API服务器运行在端口 8889"
echo "📊 WebRTC服务器运行在端口 8888"

# 检查预转换的视频文件
CONVERTED_DIR="/app/converted"
if [ ! -d "$CONVERTED_DIR" ] || [ ! -f "$CONVERTED_DIR/knight.mp4" ]; then
    echo "📹 预转换的视频文件不存在，开始转换..."
    /app/convert-video.sh
    if [ $? -ne 0 ]; then
        echo "❌ 视频转换失败"
        exit 1
    fi
fi

# 启动视频流推送（使用预转换的文件）
echo "📡 启动视频流推送（无转码模式）..."

# 优化的FFmpeg参数说明:
# -re: 以原始帧率读取输入
# -stream_loop -1: 无限循环播放
# -c copy: 直接复制流，无需重新编码（关键优化！）
# -f rtsp: RTSP输出格式
# -rtsp_transport tcp: 强制使用TCP传输

echo "🚀 使用预转换文件，无需实时编码，大幅减少延迟！"

# 启动knight流 - 直接复制，无编码延迟
ffmpeg -re -stream_loop -1 -i "$CONVERTED_DIR/knight.mp4" \
    -c copy \
    -f rtsp -rtsp_transport tcp \
    rtsp://localhost:9554/knight &

# 启动test流 - 直接复制，无编码延迟
ffmpeg -re -stream_loop -1 -i "$CONVERTED_DIR/test.mp4" \
    -c copy \
    -f rtsp -rtsp_transport tcp \
    rtsp://localhost:9554/test &

# 启动stream1流 - 直接复制，无编码延迟
ffmpeg -re -stream_loop -1 -i "$CONVERTED_DIR/stream1.mp4" \
    -c copy \
    -f rtsp -rtsp_transport tcp \
    rtsp://localhost:9554/stream1 &

# 启动camera1流 - 直接复制，无编码延迟
ffmpeg -re -stream_loop -1 -i "$CONVERTED_DIR/camera1.mp4" \
    -c copy \
    -f rtsp -rtsp_transport tcp \
    rtsp://localhost:9554/camera1 &

echo "✅ 所有视频流已启动"
echo "📡 可用的RTSP流:"
echo "   rtsp://localhost:9554/knight"
echo "   rtsp://localhost:9554/test"
echo "   rtsp://localhost:9554/stream1"
echo "   rtsp://localhost:9554/camera1"

echo "🎯 测试命令:"
echo "   ffplay rtsp://localhost:9554/knight"

# 保持容器运行
wait
