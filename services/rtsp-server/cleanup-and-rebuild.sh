#!/bin/bash

# RTSP服务器清理和重建脚本
# 用于解决ContainerConfig错误

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 停止并删除旧容器
cleanup_containers() {
    log_info "清理旧容器..."
    
    # 停止容器
    docker-compose down --remove-orphans 2>/dev/null || true
    
    # 删除特定容器
    docker rm -f live555-rtsp-server 2>/dev/null || true
    docker rm -f rtsp-server 2>/dev/null || true
    
    log_success "容器清理完成"
}

# 删除旧镜像
cleanup_images() {
    log_info "清理旧镜像..."
    
    # 删除rtsp-server镜像
    docker rmi rtsp-server:latest 2>/dev/null || true
    docker rmi $(docker images -q --filter "reference=rtsp-server*") 2>/dev/null || true
    
    log_success "镜像清理完成"
}

# 清理Docker系统
cleanup_system() {
    log_info "清理Docker系统缓存..."
    
    # 清理构建缓存
    docker builder prune -f 2>/dev/null || true
    
    # 清理未使用的网络
    docker network prune -f 2>/dev/null || true
    
    log_success "系统清理完成"
}

# 重新构建镜像
rebuild_image() {
    log_info "重新构建RTSP服务器镜像..."
    
    # 使用--no-cache确保完全重新构建
    docker-compose build --no-cache --pull
    
    if [ $? -eq 0 ]; then
        log_success "镜像重新构建成功"
    else
        log_error "镜像重新构建失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动RTSP服务器..."
    
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "RTSP服务器启动成功"
    else
        log_error "RTSP服务器启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待RTSP服务器就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8889/v3/config/global/get > /dev/null 2>&1; then
            log_success "RTSP服务器已就绪"
            return 0
        fi
        
        log_info "等待中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "RTSP服务器启动超时"
    return 1
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "可用的RTSP流:"
    echo "  📡 rtsp://localhost:9554/knight"
    echo "  📡 rtsp://localhost:9554/test"
    echo "  📡 rtsp://localhost:9554/stream1"
    echo "  📡 rtsp://localhost:9554/camera1"
    
    echo ""
    log_info "管理接口:"
    echo "  🌐 MediaMTX API: http://localhost:8889"
    echo "  📊 WebRTC: http://localhost:8888"
}

# 主函数
main() {
    log_info "开始清理和重建RTSP服务器..."
    
    cleanup_containers
    cleanup_images
    cleanup_system
    rebuild_image
    start_service
    wait_for_service
    show_status
    
    log_success "RTSP服务器清理和重建完成！"
}

# 执行主函数
main "$@"
