[supervisord]
nodaemon=true
logfile=/app/logs/supervisord.log
pidfile=/var/run/supervisord.pid
user=root

[program:rtsp-server]
command=/app/start-rtsp.sh
directory=/app
autostart=true
autorestart=true
stderr_logfile=/app/logs/rtsp-server.err.log
stdout_logfile=/app/logs/rtsp-server.out.log
user=root
environment=HOME="/root",USER="root"

[unix_http_server]
file=/var/run/supervisor.sock
chmod=0700

[supervisorctl]
serverurl=unix:///var/run/supervisor.sock

[rpcinterface:supervisor]
supervisor.rpcinterface_factory = supervisor.rpcinterface:make_main_rpcinterface
