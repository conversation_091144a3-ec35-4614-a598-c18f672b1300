# MediaMTX配置文件 - 优化版本

# 全局设置
logLevel: info
logDestinations: [stdout]
logFile: /logs/mediamtx.log

# 读写超时设置 - 优化网络性能
readTimeout: 10s
writeTimeout: 10s
writeQueueSize: 1024
udpMaxPayloadSize: 1472

# API设置
api: true
apiAddress: :8889

# 指标设置
metrics: true
metricsAddress: :9998

# RTSP设置 - 优化播放性能
rtspAddress: :9554
protocols: [tcp, udp]  # 支持TCP和UDP，让客户端选择
encryption: "no"
authMethods: [basic]

# WebRTC设置
webrtc: true
webrtcAddress: :8888

# HLS设置
hls: false

# 路径默认设置 - 简化配置
pathDefaults:
  record: false

# 路径配置 - 允许任何路径接收推流
paths:
  # 允许所有路径
  ~^.*$:
