#!/bin/bash

# VLC优化播放脚本
# 用于以最佳参数播放RTSP流，减少卡顿

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查VLC是否安装
check_vlc() {
    if ! command -v vlc &> /dev/null; then
        log_error "VLC 未安装或不在PATH中"
        log_info "请安装VLC: https://www.videolan.org/vlc/"
        exit 1
    fi
    log_success "VLC 已安装"
}

# 使用优化参数播放RTSP流
play_rtsp_optimized() {
    local stream_name=${1:-"knight"}
    local rtsp_url="rtsp://localhost:9554/$stream_name"
    
    log_info "使用优化参数播放: $stream_name"
    log_info "RTSP URL: $rtsp_url"
    
    # VLC优化参数说明:
    # --network-caching=300: 网络缓存300ms（减少缓冲）
    # --rtsp-tcp: 强制使用TCP传输（更稳定）
    # --live-caching=300: 实时流缓存300ms
    # --clock-jitter=0: 禁用时钟抖动补偿
    # --clock-synchro=0: 禁用时钟同步
    # --drop-late-frames: 丢弃延迟帧
    # --skip-frames: 跳过帧以保持同步
    # --avcodec-hw=any: 启用硬件解码
    # --no-audio-time-stretch: 禁用音频时间拉伸
    # --rtsp-frame-buffer-size=500000: RTSP帧缓冲区大小
    
    vlc "$rtsp_url" \
        --network-caching=300 \
        --rtsp-tcp \
        --live-caching=300 \
        --clock-jitter=0 \
        --clock-synchro=0 \
        --drop-late-frames \
        --skip-frames \
        --avcodec-hw=any \
        --no-audio-time-stretch \
        --rtsp-frame-buffer-size=500000 \
        --intf=dummy \
        --extraintf=http \
        --http-password=vlcremote
}

# 使用ffplay播放（备选方案）
play_ffplay_optimized() {
    local stream_name=${1:-"knight"}
    local rtsp_url="rtsp://localhost:9554/$stream_name"
    
    log_info "使用ffplay播放: $stream_name"
    log_info "RTSP URL: $rtsp_url"
    
    if ! command -v ffplay &> /dev/null; then
        log_error "ffplay 未安装"
        return 1
    fi
    
    # ffplay优化参数:
    # -rtsp_transport tcp: 使用TCP传输
    # -fflags nobuffer: 禁用缓冲
    # -flags low_delay: 低延迟模式
    # -framedrop: 丢帧以保持同步
    # -sync ext: 外部同步
    # -vf setpts=0: 重置时间戳
    
    ffplay "$rtsp_url" \
        -rtsp_transport tcp \
        -fflags nobuffer \
        -flags low_delay \
        -framedrop \
        -sync ext \
        -vf setpts=0
}

# 显示可用流
show_available_streams() {
    echo ""
    log_info "可用的RTSP流:"
    echo "  🎬 knight   - 主视频流"
    echo "  🎬 test     - 测试流"
    echo "  🎬 stream1  - 流1"
    echo "  🎬 camera1  - 摄像头1"
    echo ""
}

# 显示帮助
show_help() {
    echo "VLC优化播放脚本"
    echo ""
    echo "用法: $0 [选项] [流名称]"
    echo ""
    echo "选项:"
    echo "  vlc [流名称]     使用VLC播放 (默认)"
    echo "  ffplay [流名称]  使用ffplay播放"
    echo "  list            显示可用流"
    echo "  help            显示此帮助信息"
    echo ""
    echo "流名称:"
    echo "  knight          主视频流 (默认)"
    echo "  test            测试流"
    echo "  stream1         流1"
    echo "  camera1         摄像头1"
    echo ""
    echo "示例:"
    echo "  $0                    # 使用VLC播放knight流"
    echo "  $0 vlc test          # 使用VLC播放test流"
    echo "  $0 ffplay stream1    # 使用ffplay播放stream1流"
    echo ""
    echo "VLC优化说明:"
    echo "  - 减少网络缓存到300ms"
    echo "  - 强制使用TCP传输"
    echo "  - 启用硬件解码"
    echo "  - 丢弃延迟帧保持流畅"
}

# 主函数
main() {
    local command=${1:-"vlc"}
    local stream=${2:-"knight"}
    
    case "$command" in
        "vlc")
            check_vlc
            show_available_streams
            play_rtsp_optimized "$stream"
            ;;
        "ffplay")
            show_available_streams
            play_ffplay_optimized "$stream"
            ;;
        "list")
            show_available_streams
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            # 如果第一个参数是流名称，使用VLC播放
            if [[ "$command" =~ ^(knight|test|stream1|camera1)$ ]]; then
                check_vlc
                show_available_streams
                play_rtsp_optimized "$command"
            else
                log_error "未知选项: $command"
                show_help
                exit 1
            fi
            ;;
    esac
}

# 执行主函数
main "$@"
