#!/bin/bash

# RTSP服务器启动脚本
# 用于启动MediaMTX RTSP服务器

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装或不在PATH中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose 未安装或不在PATH中"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查视频文件
check_video_files() {
    log_info "检查视频文件..."
    
    if [ ! -f "../video/knight.mov" ]; then
        log_error "视频文件不存在: ../video/knight.mov"
        log_info "请确保视频文件存在于 video/ 目录中"
        exit 1
    fi
    
    log_success "视频文件检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    mkdir -p logs
    
    log_success "目录创建完成"
}

# 构建镜像
build_image() {
    log_info "构建RTSP服务器镜像..."
    
    docker-compose build
    
    if [ $? -eq 0 ]; then
        log_success "镜像构建成功"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 启动服务
start_service() {
    log_info "启动RTSP服务器..."
    
    docker-compose up -d
    
    if [ $? -eq 0 ]; then
        log_success "RTSP服务器启动成功"
    else
        log_error "RTSP服务器启动失败"
        exit 1
    fi
}

# 等待服务就绪
wait_for_service() {
    log_info "等待RTSP服务器就绪..."
    
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s http://localhost:8889/v3/config/global/get > /dev/null 2>&1; then
            log_success "RTSP服务器已就绪"
            return 0
        fi
        
        log_info "等待中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    log_error "RTSP服务器启动超时"
    return 1
}

# 显示服务状态
show_status() {
    log_info "服务状态:"
    docker-compose ps
    
    echo ""
    log_info "可用的RTSP流:"
    echo "  📡 rtsp://localhost:9554/knight"
    echo "  📡 rtsp://localhost:9554/test"
    echo "  📡 rtsp://localhost:9554/stream1"
    echo "  📡 rtsp://localhost:9554/camera1"
    
    echo ""
    log_info "管理接口:"
    echo "  🌐 MediaMTX API: http://localhost:8889"
    echo "  📊 WebRTC: http://localhost:8888"
    
    echo ""
    log_info "测试命令:"
    echo "  ffplay rtsp://localhost:9554/knight"
    echo "  vlc rtsp://localhost:9554/knight"
}

# 停止服务
stop_service() {
    log_info "停止RTSP服务器..."
    docker-compose down
    log_success "RTSP服务器已停止"
}

# 查看日志
show_logs() {
    docker-compose logs -f
}

# 显示帮助
show_help() {
    echo "RTSP服务器管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动RTSP服务器 (默认)"
    echo "  stop      停止RTSP服务器"
    echo "  restart   重启RTSP服务器"
    echo "  status    显示服务状态"
    echo "  logs      查看服务日志"
    echo "  build     构建Docker镜像"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start    # 启动服务"
    echo "  $0 stop     # 停止服务"
    echo "  $0 logs     # 查看日志"
}

# 主函数
main() {
    case "${1:-start}" in
        "start")
            check_dependencies
            check_video_files
            create_directories
            start_service
            wait_for_service
            show_status
            ;;
        "stop")
            stop_service
            ;;
        "restart")
            check_dependencies
            check_video_files
            stop_service
            sleep 2
            start_service
            wait_for_service
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs
            ;;
        "build")
            check_dependencies
            build_image
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
