#!/usr/bin/env python3
"""
测试S3存储功能
"""
import os
import tempfile
from datetime import datetime
from PIL import Image
import numpy as np

# 添加项目路径
import sys
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vas.common.s3_storage import S3StorageManager, upload_screenshot, upload_event_image


def create_test_image(width=640, height=480, filename="test_image.jpg"):
    """创建测试图片"""
    # 创建随机图片
    image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    image = Image.fromarray(image_array)
    
    # 保存到临时文件
    temp_file = os.path.join(tempfile.gettempdir(), filename)
    image.save(temp_file, 'JPEG')
    
    print(f"创建测试图片: {temp_file}")
    return temp_file


def test_s3_storage_manager():
    """测试S3存储管理器"""
    print("=== 测试S3存储管理器 ===")
    
    try:
        # 初始化S3管理器
        s3_manager = S3StorageManager()
        print("✅ S3管理器初始化成功")
        
        # 创建测试图片
        test_image = create_test_image(filename="test_screenshot.jpg")
        
        # 测试上传截图
        task_id = "test-task-001"
        timestamp = datetime.now()
        
        screenshot_url = s3_manager.upload_screenshot(
            local_file_path=test_image,
            task_id=task_id,
            timestamp=timestamp,
            custom_name="test_screenshot"
        )
        
        if screenshot_url:
            print(f"✅ 截图上传成功: {screenshot_url}")
        else:
            print("❌ 截图上传失败")
        
        # 测试上传事件图片
        event_image = create_test_image(filename="test_event.jpg")
        event_id = "event-001"
        
        event_url = s3_manager.upload_event_image(
            local_file_path=event_image,
            task_id=task_id,
            event_id=event_id,
            timestamp=timestamp
        )
        
        if event_url:
            print(f"✅ 事件图片上传成功: {event_url}")
        else:
            print("❌ 事件图片上传失败")
        
        # 测试通用文件上传
        general_file = create_test_image(filename="test_general.jpg")
        object_key = f"test/{task_id}/general_image_{timestamp.strftime('%Y%m%d_%H%M%S')}.jpg"
        
        general_url = s3_manager.upload_file(
            local_file_path=general_file,
            object_key=object_key
        )
        
        if general_url:
            print(f"✅ 通用文件上传成功: {general_url}")
        else:
            print("❌ 通用文件上传失败")
        
        # 测试列举对象
        print("\n--- 列举bucket中的对象 ---")
        objects = s3_manager.list_objects()
        for obj in objects[:10]:  # 只显示前10个
            print(f"  - {obj}")
        
        if len(objects) > 10:
            print(f"  ... 还有 {len(objects) - 10} 个对象")
        
        # 测试下载文件
        if screenshot_url:
            download_path = os.path.join(tempfile.gettempdir(), "downloaded_screenshot.jpg")
            # 从URL中提取object_key
            object_key = screenshot_url.split(f"{s3_manager.bucket_name}/")[-1]
            
            success = s3_manager.download_file(object_key, download_path)
            if success:
                print(f"✅ 文件下载成功: {download_path}")
                # 验证文件是否存在
                if os.path.exists(download_path):
                    print(f"✅ 下载文件验证成功，大小: {os.path.getsize(download_path)} bytes")
                else:
                    print("❌ 下载文件不存在")
            else:
                print("❌ 文件下载失败")
        
        # 清理临时文件
        for temp_file in [test_image, event_image, general_file]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
                print(f"清理临时文件: {temp_file}")
        
        print("\n✅ S3存储管理器测试完成")
        
    except Exception as e:
        print(f"❌ S3存储管理器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_convenience_functions():
    """测试便捷函数"""
    print("\n=== 测试便捷函数 ===")
    
    try:
        # 创建测试图片
        test_image = create_test_image(filename="convenience_test.jpg")
        
        # 测试便捷截图上传函数
        task_id = "convenience-task-001"
        timestamp = datetime.now()
        
        screenshot_url = upload_screenshot(
            local_file_path=test_image,
            task_id=task_id,
            timestamp=timestamp,
            custom_name="convenience_screenshot"
        )
        
        if screenshot_url:
            print(f"✅ 便捷截图上传成功: {screenshot_url}")
        else:
            print("❌ 便捷截图上传失败")
        
        # 测试便捷事件图片上传函数
        event_image = create_test_image(filename="convenience_event.jpg")
        event_id = "convenience-event-001"
        
        event_url = upload_event_image(
            local_file_path=event_image,
            task_id=task_id,
            event_id=event_id,
            timestamp=timestamp
        )
        
        if event_url:
            print(f"✅ 便捷事件图片上传成功: {event_url}")
        else:
            print("❌ 便捷事件图片上传失败")
        
        # 清理临时文件
        for temp_file in [test_image, event_image]:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        
        print("✅ 便捷函数测试完成")
        
    except Exception as e:
        print(f"❌ 便捷函数测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        s3_manager = S3StorageManager()
        
        # 测试上传不存在的文件
        result = s3_manager.upload_screenshot(
            local_file_path="/nonexistent/file.jpg",
            task_id="error-test-001"
        )
        
        if result is None:
            print("✅ 不存在文件的错误处理正确")
        else:
            print("❌ 不存在文件的错误处理异常")
        
        # 测试下载到无权限目录
        success = s3_manager.download_file(
            object_key="nonexistent/file.jpg",
            local_file_path="/root/no_permission.jpg"
        )
        
        if not success:
            print("✅ 下载错误处理正确")
        else:
            print("❌ 下载错误处理异常")
        
        print("✅ 错误处理测试完成")
        
    except Exception as e:
        print(f"❌ 错误处理测试失败: {e}")


def main():
    """主测试函数"""
    print("=== S3存储功能测试 ===")
    print(f"测试时间: {datetime.now().isoformat()}")
    
    # 检查依赖
    try:
        import boto3
        import yaml
        from PIL import Image
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install boto3 PyYAML Pillow")
        return
    
    # 运行测试
    test_s3_storage_manager()
    test_convenience_functions()
    test_error_handling()
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
