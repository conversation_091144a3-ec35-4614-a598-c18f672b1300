# OWL检测测试脚本使用指南

## 📋 脚本概述

提供了两个测试脚本来验证OWL检测API功能并生成可视化结果：

1. **`test_owl_detection_with_visualization.py`** - 完整功能版本
2. **`quick_test_owl_detection.py`** - 快速测试版本

## 🚀 快速开始

### 1. 启动OWL检测API服务
```bash
# 在A10服务器上启动服务
cd inference
python3 start_owl_detection_api.py --config config/owl_detection_api_config.yaml --debug
```

### 2. 运行快速测试（推荐）
```bash
# 测试resource目录下的所有图片
python3 quick_test_owl_detection.py

# 测试单张图片
python3 quick_test_owl_detection.py --image resource/vlcsnap-2025-07-27-09h00m37s504.png

# 自定义查询文本和阈值
python3 quick_test_owl_detection.py \
  --threshold 0.2 \
  --query-texts person car bicycle cat dog
```

## 📖 详细使用说明

### 快速测试脚本 (`quick_test_owl_detection.py`)

**功能特点**:
- 简单易用，一键测试
- 自动处理resource目录下所有图片
- 内置常用查询文本
- 生成带检测框的可视化结果
- 保存JSON格式的检测结果

**命令行参数**:
```bash
python3 quick_test_owl_detection.py [选项]

选项:
  --image PATH          指定单张图片路径
  --url URL            API服务地址 (默认: http://localhost:8082)
  --threshold FLOAT    检测阈值 (默认: 0.15)
  --query-texts TEXT   自定义查询文本列表
```

**使用示例**:
```bash
# 基础测试
python3 quick_test_owl_detection.py

# 测试特定图片
python3 quick_test_owl_detection.py --image resource/test.jpg

# 自定义参数
python3 quick_test_owl_detection.py \
  --threshold 0.25 \
  --query-texts "person wearing helmet" "person without helmet" "safety vest"

# 连接远程API
python3 quick_test_owl_detection.py --url http://*************:8082
```

### 完整测试脚本 (`test_owl_detection_with_visualization.py`)

**功能特点**:
- 完整的可视化功能
- 详细的检测摘要信息
- 支持批量处理
- 可保存JSON结果
- 高级可视化选项

**命令行参数**:
```bash
python3 test_owl_detection_with_visualization.py [选项]

选项:
  --url URL                API服务地址
  --input PATH             输入图片或目录路径
  --output DIR             输出目录
  --query-texts TEXT       查询文本列表
  --threshold FLOAT        检测阈值
  --nms-threshold FLOAT    NMS阈值
  --save-json             保存JSON结果文件
```

**使用示例**:
```bash
# 处理resource目录
python3 test_owl_detection_with_visualization.py \
  --input resource \
  --output detection_results \
  --query-texts person car bicycle cat dog \
  --save-json

# 处理单张图片
python3 test_owl_detection_with_visualization.py \
  --input resource/test.jpg \
  --output my_results \
  --threshold 0.2

# 完整参数示例
python3 test_owl_detection_with_visualization.py \
  --url http://localhost:8082 \
  --input resource \
  --output results \
  --query-texts "person" "car" "bicycle" "cat" "dog" "bird" \
  --threshold 0.15 \
  --nms-threshold 0.3 \
  --save-json
```

## 📊 输出结果

### 文件结构
```
quick_test_output/          # 快速测试输出目录
├── image1_detected.jpg     # 可视化结果图片
├── image1_result.json      # 检测结果JSON
├── image2_detected.jpg
└── image2_result.json

detection_output/           # 完整测试输出目录
├── image1_detection_result.jpg
├── image2_detection_result.jpg
└── detection_results.json  # 汇总结果
```

### 可视化效果
- **检测框**: 不同颜色的矩形框标识不同类别
- **标签**: 显示类别名称和置信度分数
- **摘要信息**: 图片左上角显示检测统计
- **高质量渲染**: 清晰的文字和边框

### JSON结果格式
```json
{
  "code": 200,
  "message": "检测成功",
  "data": {
    "detections": [
      {
        "bbox": [100.5, 200.3, 150.2, 180.7],
        "score": 0.85,
        "query_text": "person",
        "query_id": 0
      }
    ],
    "total_count": 1,
    "processing_time": 0.123,
    "parameters": {
      "query_texts": ["person", "car"],
      "threshold": 0.15,
      "nms_threshold": 0.3
    }
  }
}
```

## 🎯 常用查询文本

### 人物相关
```python
["person", "man", "woman", "child", "worker", "person wearing helmet", "person without helmet"]
```

### 车辆相关
```python
["car", "truck", "bus", "motorcycle", "bicycle", "vehicle"]
```

### 动物相关
```python
["cat", "dog", "bird", "horse", "cow", "sheep", "animal"]
```

### 物体相关
```python
["chair", "table", "laptop", "phone", "book", "bottle", "bag"]
```

### 安全相关
```python
["helmet", "safety vest", "hard hat", "protective equipment", "warning sign"]
```

## 🔧 故障排除

### 常见问题

1. **API连接失败**
```bash
# 检查服务是否启动
curl http://localhost:8082/health

# 检查端口是否正确
netstat -tlnp | grep 8082
```

2. **图片读取失败**
```bash
# 检查图片文件是否存在
ls -la resource/

# 检查图片格式是否支持
file resource/your_image.jpg
```

3. **检测结果为空**
```bash
# 降低检测阈值
python3 quick_test_owl_detection.py --threshold 0.1

# 尝试更通用的查询文本
python3 quick_test_owl_detection.py --query-texts object thing item
```

4. **内存不足**
```bash
# 检查GPU内存
nvidia-smi

# 重启Triton服务器
# 减少并发请求
```

### 调试模式

启用详细日志输出：
```bash
# 在API服务启动时添加--debug参数
python3 start_owl_detection_api.py --config config/owl_detection_api_config.yaml --debug
```

## 📈 性能优化建议

1. **查询文本优化**
   - 使用具体的描述词
   - 避免过于抽象的概念
   - 组合使用相关词汇

2. **阈值调整**
   - 起始阈值: 0.15
   - 高精度: 0.25+
   - 高召回: 0.1-

3. **批量处理**
   - 使用完整测试脚本处理多张图片
   - 避免频繁启停API服务

## 🎉 预期结果

成功运行后，你应该看到：
- 控制台输出检测进度和结果
- 生成带检测框的可视化图片
- 保存详细的JSON检测结果
- 处理时间通常在0.1-0.5秒之间

这些脚本为OWL检测API提供了完整的测试和验证方案，帮助你快速验证0样本检测功能的效果。
