# PyTorch相关依赖
torch==2.2.2 --index-url https://download.pytorch.org/whl/cu121
torchvision==0.17.2 --index-url https://download.pytorch.org/whl/cu121
torchaudio==2.2.2 --index-url https://download.pytorch.org/whl/cu121

# 使用清华镜像源加速下载
-i https://pypi.tuna.tsinghua.edu.cn/simple

# 核心依赖
pybase64
requests>=2.28.0
pydantic>=1.10.0,<2.0.0
tqdm>=4.64.0
setproctitle>=1.3.0
psutil>=5.9.0
lapx
numpy==1.26.1
opencv-python-headless==*********
pyarrow==10.0.0
scipy>=1.9.0

# Web框架
flask==2.2.5
werkzeug>=2.2.0,<3.0.0

# 推理相关
tritonclient[all]==2.59.0
transformers==4.40.0

# S3存储依赖
boto3>=1.26.0
botocore>=1.29.0

# Kafka消息队列依赖
kafka-python>=2.0.2

# 配置文件处理
PyYAML>=6.0

# 数据库相关
sqlitedict>=2.1.0

# 图像处理增强
Pillow>=9.0.0

# 日志和监控
colorlog>=6.7.0

# 数据处理
pandas>=1.5.0

# 时间处理
python-dateutil>=2.8.0

# 网络请求增强
urllib3>=1.26.0

# JSON处理增强
ujson>=5.0.0

# 多进程增强
multiprocess>=0.70.0

# DeepStream相关
pyds-1.1.11-py3-none-linux_x86_64.whl
