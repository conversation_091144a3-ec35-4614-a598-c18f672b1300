# 推理服务注册功能

本文档说明如何使用推理服务的自动注册功能，将服务注册到调度器。

## 功能概述

推理服务现在支持自动向调度器注册，包括：
- 服务启动时自动注册
- 定期健康检查和重新注册
- 服务关闭时自动注销
- 支持环境变量覆盖配置
- 与现有的启动方式完全兼容

## 配置说明

服务注册配置已添加到现有的 `config/config.yaml` 文件中：

```yaml
# 服务注册配置
service_registry:
    # 是否启用服务注册
    enabled: true
    # 调度器配置
    scheduler:
        url: "http://localhost:8080"      # 调度器地址，可通过环境变量SCHEDULER_URL覆盖
        registration_retry_interval: 30  # 注册重试间隔(秒)
    # 服务信息配置
    service:
        name: "inference-service-1"       # 服务名称，可通过环境变量SERVICE_NAME覆盖
        max_quota: 10                     # 最大配额，可通过环境变量MAX_QUOTA覆盖
        region: "default"                 # 区域，可通过环境变量REGION覆盖
        gpu_type: "A10"                   # GPU类型，可通过环境变量GPU_TYPE覆盖
        # 算法编排配置
        algorithm_orchestration:
            algorithm_chain:
                - algorithm_id: "yolov8_detection"
                  algorithm_name: "YOLOv8目标检测"
                  algorithm_type: "DETECTION"
                  order: 1
                - algorithm_id: "byte_tracking"
                  algorithm_name: "ByteTracker目标跟踪"
                  algorithm_type: "TRACKING"
                  order: 2
                - algorithm_id: "zone_intrusion"
                  algorithm_name: "区域入侵检测"
                  algorithm_type: "PIPELINE"
                  order: 3
```

## 使用方式

### 1. 使用 Docker 服务脚本（推荐）

```bash
# 设置环境变量
export SCHEDULER_URL=http://scheduler:8080
export SERVICE_NAME=inference-service-1
export MAX_QUOTA=10
export REGION=default
export GPU_TYPE=A10

# 启动服务（现有方式不变）
./service.sh start

# 查看服务状态
./service.sh status

# 查看日志
./service.sh logs -f
```

### 2. 直接启动（开发模式）

```bash
# 设置环境变量
export SCHEDULER_URL=http://scheduler:8080
export SERVICE_NAME=inference-service-dev

# 启动服务（现有方式不变）
python launch_server.py \
  --config-path config/config.yaml \
  --log-path logs \
  --host 0.0.0.0 \
  --port 9001
```

### 3. 禁用服务注册

如果不需要服务注册功能，可以在配置文件中设置：

```yaml
service_registry:
    enabled: false
```

或者删除整个 `service_registry` 配置块。

## 环境变量

以下环境变量可以覆盖配置文件中的设置：

- `SCHEDULER_URL`: 调度器地址
- `SERVICE_NAME`: 服务名称
- `MAX_QUOTA`: 最大配额
- `REGION`: 服务区域
- `GPU_TYPE`: GPU类型

## 测试功能

运行测试脚本验证服务注册功能：

```bash
python test_service_registration.py
```

测试脚本会检查：
- 配置文件加载
- 调度器连接性
- 服务注册功能
- 健康检查

## 注册数据格式

服务向调度器注册时发送的数据格式：

```json
{
  "serviceName": "inference-service-1",
  "baseUrl": "http://localhost:9001",
  "algorithmOrchestration": {
    "algorithm_chain": [
      {
        "algorithm_id": "yolov8_detection",
        "algorithm_name": "YOLOv8目标检测",
        "algorithm_type": "DETECTION",
        "order": 1
      }
    ]
  },
  "maxQuota": 10,
  "region": "default",
  "gpuType": "A10"
}
```

## 日志监控

服务注册相关的日志会输出到标准日志中，包括：
- 注册成功/失败信息
- 健康检查状态
- 重连尝试信息

## 故障排除

### 1. 注册失败
- 检查调度器URL是否正确
- 确认调度器服务是否正常运行
- 检查网络连接

### 2. 配置不生效
- 确认 `config/config.yaml` 中 `service_registry.enabled` 为 `true`
- 检查配置文件语法是否正确
- 查看启动日志中的配置加载信息

### 3. 环境变量不生效
- 确认环境变量在启动服务前已设置
- 检查环境变量名称是否正确

## Docker 部署

在 Docker 环境中使用环境变量：

```bash
docker run -d \
  --name inference-service \
  --restart unless-stopped \
  -p 9001:9001 \
  -v $(pwd)/config:/workspace/config:ro \
  -v $(pwd)/logs:/workspace/logs \
  --gpus all \
  -e SCHEDULER_URL=http://scheduler:8080 \
  -e SERVICE_NAME=inference-service-1 \
  -e MAX_QUOTA=10 \
  -e REGION=default \
  -e GPU_TYPE=A10 \
  video-analysis-inference:latest
```

## 兼容性

- 完全兼容现有的启动方式
- 不影响现有的配置文件结构
- 可以随时启用或禁用服务注册功能
- 不会影响推理服务的核心功能
