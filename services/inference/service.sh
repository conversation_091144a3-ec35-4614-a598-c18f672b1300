#!/bin/bash

# 视频分析推理服务启停脚本
# 用法: ./service.sh [start|stop|restart|status|logs]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置参数
SERVICE_NAME="inference-service"
IMAGE_NAME="video-analysis-inference"
IMAGE_TAG="latest"
CONTAINER_NAME="$SERVICE_NAME"
HOST_PORT="9001"
CONTAINER_PORT="9001"
CONFIG_PATH="$(pwd)/config"
LOGS_PATH="$(pwd)/logs"
TEMP_PATH="$(pwd)/temp"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    echo "视频分析推理服务启停脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start     启动服务"
    echo "  stop      停止服务"
    echo "  restart   重启服务"
    echo "  status    查看服务状态"
    echo "  logs      查看服务日志"
    echo "  health    检查服务健康状态"
    echo "  build     构建镜像"
    echo "  clean     清理容器和镜像"
    echo ""
    echo "选项:"
    echo "  -p, --port PORT       指定主机端口 (默认: 9001)"
    echo "  -t, --tag TAG         指定镜像标签 (默认: latest)"
    echo "  -d, --detach          后台运行 (默认启用)"
    echo "  -f, --follow          跟踪日志输出"
    echo "  --gpu                 启用GPU支持 (默认启用)"
    echo "  --no-gpu              禁用GPU支持"
    echo "  --no-cache            构建时不使用缓存"
    echo "  -h, --help            显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start              # 启动服务 (默认启用GPU)"
    echo "  $0 start --no-gpu     # 启动服务并禁用GPU"
    echo "  $0 start -p 8080      # 在8080端口启动服务"
    echo "  $0 logs -f            # 跟踪查看日志"
    echo "  $0 build --no-cache   # 无缓存构建镜像"
}

# 检查Docker是否可用
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker守护进程未运行或无权限访问"
        exit 1
    fi
}

# 检查镜像是否存在
check_image() {
    if ! docker images "$IMAGE_NAME:$IMAGE_TAG" --format "{{.Repository}}" | grep -q "$IMAGE_NAME"; then
        log_warning "镜像 $IMAGE_NAME:$IMAGE_TAG 不存在"
        log_info "正在构建镜像..."
        build_image
    fi
}

# 构建镜像
build_image() {
    local no_cache=""
    if [[ "$USE_NO_CACHE" == "true" ]]; then
        no_cache="--no-cache"
    fi
    
    log_info "构建Docker镜像: $IMAGE_NAME:$IMAGE_TAG"
    
    if docker build $no_cache -t "$IMAGE_NAME:$IMAGE_TAG" .; then
        log_success "镜像构建成功"
    else
        log_error "镜像构建失败"
        exit 1
    fi
}

# 获取宿主机IP
get_host_ip() {
    local host_ip=""

    # 方法1: 检查环境变量
    if [ -n "$HOST_IP" ]; then
        host_ip="$HOST_IP"
        log_info "使用环境变量HOST_IP: $host_ip" >&2
        echo "$host_ip"
        return 0
    fi

    # 方法2: 通过hostname -I获取 (Linux)
    if command -v hostname >/dev/null 2>&1; then
        host_ip=$(hostname -I 2>/dev/null | awk '{print $1}')
        if [ -n "$host_ip" ] && [ "$host_ip" != "127.0.0.1" ]; then
            log_info "通过hostname -I获取到IP: $host_ip" >&2
            echo "$host_ip"
            return 0
        fi
    fi

    # 方法3: 通过ip route获取默认路由的IP
    if command -v ip >/dev/null 2>&1; then
        host_ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
        if [ -n "$host_ip" ] && [ "$host_ip" != "127.0.0.1" ]; then
            log_info "通过ip route获取到IP: $host_ip" >&2
            echo "$host_ip"
            return 0
        fi
    fi

    # 方法4: 通过ifconfig获取（如果可用）
    if command -v ifconfig >/dev/null 2>&1; then
        host_ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n1)
        if [ -n "$host_ip" ]; then
            log_info "通过ifconfig获取到IP: $host_ip" >&2
            echo "$host_ip"
            return 0
        fi
    fi

    log_warning "无法自动获取宿主机IP，将使用localhost" >&2
    echo "localhost"
    return 1
}

# 创建必要的目录
create_directories() {
    mkdir -p "$LOGS_PATH" "$TEMP_PATH"

    # 设置目录权限，确保容器内的inference用户可以写入
    # Docker容器内的inference用户UID通常是999或1000
    chmod 777 "$LOGS_PATH" "$TEMP_PATH"

    log_info "创建目录: $LOGS_PATH, $TEMP_PATH"
    log_info "设置目录权限为777，确保容器内用户可以写入"
}

# 启动服务
start_service() {
    log_info "启动视频分析推理服务..."

    # 检查容器是否已经运行
    if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_warning "服务已经在运行中"
        return 0
    fi

    # 检查是否有同名的停止容器
    if docker ps -a --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "删除已存在的容器: $CONTAINER_NAME"
        docker rm "$CONTAINER_NAME" > /dev/null 2>&1
    fi

    # 检查镜像
    check_image

    # 创建目录
    create_directories

    # 获取宿主机IP用于服务注册
    local service_host_ip=$(get_host_ip)
    if [ $? -ne 0 ]; then
        log_warning "无法获取宿主机IP，scheduler可能无法访问此服务"
        log_warning "建议手动设置HOST_IP环境变量: export HOST_IP=你的宿主机IP"
    fi
    log_info "服务注册将使用IP: $service_host_ip"
    
    # 构建Docker运行参数
    local docker_args=(
        "run"
        "-d"
        "--name" "$CONTAINER_NAME"
        "--restart" "unless-stopped"
        "-p" "$HOST_PORT:$CONTAINER_PORT"
        "-v" "$CONFIG_PATH:/workspace/config:ro"
        "-v" "$LOGS_PATH:/workspace/logs"
        "-v" "$TEMP_PATH:/workspace/temp"
        "-e" "HOST_IP=$service_host_ip"
        "--privileged"
    )
    
    # 添加GPU支持
    if [[ "$USE_GPU" == "true" ]]; then
        docker_args+=("--gpus" "all")
        log_info "启用GPU支持"
    fi
    
    # 添加镜像名
    docker_args+=("$IMAGE_NAME:$IMAGE_TAG")
    
    # 启动容器
    if docker "${docker_args[@]}"; then
        log_success "服务启动成功"
        log_info "容器名称: $CONTAINER_NAME"
        log_info "本地访问地址: http://localhost:$HOST_PORT"
        log_info "外部访问地址: http://$service_host_ip:$HOST_PORT"
        log_info "服务注册IP: $service_host_ip (scheduler将使用此地址访问)"

        # 等待服务启动
        log_info "等待服务启动..."
        sleep 5

        # 检查健康状态
        check_health
    else
        log_error "服务启动失败"
        exit 1
    fi
}

# 停止服务
stop_service() {
    log_info "停止视频分析推理服务..."
    
    if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        if docker stop "$CONTAINER_NAME"; then
            log_success "服务停止成功"
            
            # 删除容器
            if docker rm "$CONTAINER_NAME" > /dev/null 2>&1; then
                log_info "容器已删除"
            fi
        else
            log_error "服务停止失败"
            exit 1
        fi
    else
        log_warning "服务未在运行"
    fi
}

# 重启服务
restart_service() {
    log_info "重启视频分析推理服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
show_status() {
    log_info "查看服务状态..."
    
    echo ""
    echo "=== 容器状态 ==="
    if docker ps -a --filter "name=$CONTAINER_NAME" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}"; then
        echo ""
    else
        log_warning "未找到容器: $CONTAINER_NAME"
    fi
    
    echo "=== 镜像信息 ==="
    if docker images "$IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"; then
        echo ""
    else
        log_warning "未找到镜像: $IMAGE_NAME"
    fi
    
    # 检查端口占用
    echo "=== 端口状态 ==="
    if command -v netstat &> /dev/null; then
        netstat -tlnp 2>/dev/null | grep ":$HOST_PORT " || echo "端口 $HOST_PORT 未被占用"
    elif command -v ss &> /dev/null; then
        ss -tlnp | grep ":$HOST_PORT " || echo "端口 $HOST_PORT 未被占用"
    else
        echo "无法检查端口状态（缺少netstat或ss命令）"
    fi
}

# 查看日志
show_logs() {
    if docker ps --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        log_info "查看服务日志..."
        
        if [[ "$FOLLOW_LOGS" == "true" ]]; then
            docker logs -f "$CONTAINER_NAME"
        else
            docker logs --tail 50 "$CONTAINER_NAME"
        fi
    else
        log_error "服务未在运行"
        exit 1
    fi
}

# 检查健康状态
check_health() {
    log_info "检查服务健康状态..."
    
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s -f "http://localhost:$HOST_PORT/health" > /dev/null 2>&1; then
            log_success "服务健康检查通过"
            
            # 显示服务信息
            echo ""
            echo "=== 服务信息 ==="
            curl -s "http://localhost:$HOST_PORT/" | python3 -m json.tool 2>/dev/null || echo "无法获取服务信息"
            return 0
        fi
        
        log_info "健康检查失败，重试 $attempt/$max_attempts..."
        sleep 3
        ((attempt++))
    done
    
    log_error "服务健康检查失败"
    log_info "请检查服务日志: $0 logs"
    return 1
}

# 清理容器和镜像
clean_service() {
    log_info "清理容器和镜像..."
    
    # 停止并删除容器
    if docker ps -a --format "{{.Names}}" | grep -q "^$CONTAINER_NAME$"; then
        docker stop "$CONTAINER_NAME" > /dev/null 2>&1 || true
        docker rm "$CONTAINER_NAME" > /dev/null 2>&1 || true
        log_info "容器已删除"
    fi
    
    # 删除镜像
    if docker images "$IMAGE_NAME:$IMAGE_TAG" --format "{{.Repository}}" | grep -q "$IMAGE_NAME"; then
        docker rmi "$IMAGE_NAME:$IMAGE_TAG" > /dev/null 2>&1 || true
        log_info "镜像已删除"
    fi
    
    # 清理悬空镜像
    local dangling_images=$(docker images -f "dangling=true" -q)
    if [[ -n "$dangling_images" ]]; then
        docker rmi $dangling_images > /dev/null 2>&1 || true
        log_info "悬空镜像已清理"
    fi
    
    log_success "清理完成"
}

# 解析命令行参数
parse_args() {
    COMMAND=""
    USE_GPU=true
    USE_NO_CACHE=false
    FOLLOW_LOGS=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            start|stop|restart|status|logs|health|build|clean)
                COMMAND="$1"
                shift
                ;;
            -p|--port)
                HOST_PORT="$2"
                shift 2
                ;;
            -t|--tag)
                IMAGE_TAG="$2"
                shift 2
                ;;
            --gpu)
                USE_GPU=true
                shift
                ;;
            --no-gpu)
                USE_GPU=false
                shift
                ;;
            --no-cache)
                USE_NO_CACHE=true
                shift
                ;;
            -f|--follow)
                FOLLOW_LOGS=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知参数: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    if [[ -z "$COMMAND" ]]; then
        log_error "请指定命令"
        show_help
        exit 1
    fi
}

# 主函数
main() {
    # 解析参数
    parse_args "$@"
    
    # 检查Docker
    check_docker
    
    # 执行命令
    case $COMMAND in
        start)
            start_service
            ;;
        stop)
            stop_service
            ;;
        restart)
            restart_service
            ;;
        status)
            show_status
            ;;
        logs)
            show_logs
            ;;
        health)
            check_health
            ;;
        build)
            build_image
            ;;
        clean)
            clean_service
            ;;
        *)
            log_error "未知命令: $COMMAND"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
