#!/usr/bin/env python3
"""
OWL检测API服务启动脚本
"""

import os
import sys
import argparse

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from owl_detection_api import OWLDetectionAPI


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="启动OWL检测HTTP API服务")
    parser.add_argument(
        "--config", 
        type=str, 
        default=os.path.join(current_dir, "config", "owl_detection_api_config.yaml"),
        help="配置文件路径"
    )
    parser.add_argument(
        "--host",
        type=str,
        help="服务器主机地址（覆盖配置文件）"
    )
    parser.add_argument(
        "--port",
        type=int,
        help="服务器端口（覆盖配置文件）"
    )
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    # 检查配置文件是否存在
    if not os.path.exists(args.config):
        print(f"错误: 配置文件不存在: {args.config}")
        print(f"请确保配置文件存在，或使用 --config 指定正确的配置文件路径")
        sys.exit(1)
    
    try:
        print("=" * 60)
        print("OWL检测API服务")
        print("=" * 60)
        print(f"配置文件: {args.config}")
        
        # 创建API服务
        api_service = OWLDetectionAPI(args.config)
        
        # 覆盖命令行参数
        if args.host:
            api_service.config["server"]["host"] = args.host
        if args.port:
            api_service.config["server"]["port"] = args.port
        if args.debug:
            api_service.config["server"]["debug"] = True
            api_service.app.config["DEBUG"] = True
        
        # 显示服务信息
        server_config = api_service.config.get("server", {})
        host = server_config.get("host", "0.0.0.0")
        port = server_config.get("port", 8082)
        
        print(f"服务地址: http://{host}:{port}")
        print(f"健康检查: http://{host}:{port}/health")
        print(f"检测接口: http://{host}:{port}/api/v1/detect")
        print(f"服务信息: http://{host}:{port}/api/v1/info")
        print("=" * 60)
        
        # 启动服务
        api_service.run()
        
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"服务启动失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
