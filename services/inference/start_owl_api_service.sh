#!/bin/bash

# OWL检测API服务启动脚本

set -e

# 脚本目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    echo -e "${BLUE}[DEBUG]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
OWL检测API服务启动脚本

用法: $0 [选项]

选项:
    -h, --help              显示此帮助信息
    -m, --mode MODE         启动模式: local|docker|compose (默认: local)
    -c, --config CONFIG     配置文件路径 (默认: config/owl_detection_api_config.yaml)
    -p, --port PORT         服务端口 (默认: 8082)
    -H, --host HOST         服务主机 (默认: 0.0.0.0)
    -d, --debug             启用调试模式
    --build                 重新构建Docker镜像 (仅docker/compose模式)
    --no-deps               不启动依赖服务 (仅compose模式)

启动模式说明:
    local   - 本地Python进程启动
    docker  - Docker容器启动
    compose - Docker Compose启动（包含Triton服务器）

示例:
    $0 --mode local --debug
    $0 --mode docker --build
    $0 --mode compose --no-deps

EOF
}

# 默认参数
MODE="local"
CONFIG="config/owl_detection_api_config.yaml"
PORT=""
HOST=""
DEBUG=""
BUILD=""
NO_DEPS=""

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -m|--mode)
            MODE="$2"
            shift 2
            ;;
        -c|--config)
            CONFIG="$2"
            shift 2
            ;;
        -p|--port)
            PORT="$2"
            shift 2
            ;;
        -H|--host)
            HOST="$2"
            shift 2
            ;;
        -d|--debug)
            DEBUG="--debug"
            shift
            ;;
        --build)
            BUILD="--build"
            shift
            ;;
        --no-deps)
            NO_DEPS="--no-deps"
            shift
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 验证启动模式
if [[ ! "$MODE" =~ ^(local|docker|compose)$ ]]; then
    log_error "无效的启动模式: $MODE"
    show_help
    exit 1
fi

# 检查配置文件
if [[ ! -f "$CONFIG" ]]; then
    log_error "配置文件不存在: $CONFIG"
    exit 1
fi

# 创建必要的目录
log_info "创建必要的目录..."
mkdir -p logs/owl_api
mkdir -p data/images

# 根据模式启动服务
case $MODE in
    local)
        log_info "以本地模式启动OWL检测API服务..."
        
        # 检查Python依赖
        if ! python3 -c "import flask, yaml, cv2, torch, transformers, tritonclient" 2>/dev/null; then
            log_error "缺少必要的Python依赖包"
            log_info "请运行: pip install -r requirements_owl_api.txt"
            exit 1
        fi
        
        # 构建启动命令
        CMD="python3 start_owl_detection_api.py --config $CONFIG"
        if [[ -n "$HOST" ]]; then
            CMD="$CMD --host $HOST"
        fi
        if [[ -n "$PORT" ]]; then
            CMD="$CMD --port $PORT"
        fi
        if [[ -n "$DEBUG" ]]; then
            CMD="$CMD $DEBUG"
        fi
        
        log_info "启动命令: $CMD"
        exec $CMD
        ;;
        
    docker)
        log_info "以Docker模式启动OWL检测API服务..."
        
        # 检查Docker
        if ! command -v docker &> /dev/null; then
            log_error "Docker未安装或不可用"
            exit 1
        fi
        
        # 构建镜像
        if [[ -n "$BUILD" ]]; then
            log_info "构建Docker镜像..."
            docker build -f Dockerfile.owl_api -t owl-detection-api:latest .
        fi
        
        # 运行容器
        DOCKER_CMD="docker run --rm -it"
        DOCKER_CMD="$DOCKER_CMD -p ${PORT:-8082}:8082"
        DOCKER_CMD="$DOCKER_CMD -v $(pwd)/$CONFIG:/workspace/owl_detection_api/config/owl_detection_api_config.yaml:ro"
        DOCKER_CMD="$DOCKER_CMD -v $(pwd)/models:/workspace/video_analysis_server/models:ro"
        DOCKER_CMD="$DOCKER_CMD -v $(pwd)/logs/owl_api:/workspace/owl_detection_api/logs"
        DOCKER_CMD="$DOCKER_CMD --name owl-detection-api"
        DOCKER_CMD="$DOCKER_CMD owl-detection-api:latest"
        
        log_info "Docker命令: $DOCKER_CMD"
        exec $DOCKER_CMD
        ;;
        
    compose)
        log_info "以Docker Compose模式启动服务..."
        
        # 检查Docker Compose
        if ! command -v docker-compose &> /dev/null && ! command -v docker &> /dev/null; then
            log_error "Docker Compose未安装或不可用"
            exit 1
        fi
        
        # 选择compose命令
        COMPOSE_CMD="docker-compose"
        if command -v docker &> /dev/null && docker compose version &> /dev/null; then
            COMPOSE_CMD="docker compose"
        fi
        
        # 构建服务
        if [[ -n "$BUILD" ]]; then
            log_info "构建Docker镜像..."
            $COMPOSE_CMD -f docker-compose.owl_api.yml build
        fi
        
        # 启动服务
        COMPOSE_ARGS="-f docker-compose.owl_api.yml up"
        if [[ -n "$NO_DEPS" ]]; then
            COMPOSE_ARGS="$COMPOSE_ARGS --no-deps owl-detection-api"
        fi
        
        log_info "启动命令: $COMPOSE_CMD $COMPOSE_ARGS"
        exec $COMPOSE_CMD $COMPOSE_ARGS
        ;;
esac
