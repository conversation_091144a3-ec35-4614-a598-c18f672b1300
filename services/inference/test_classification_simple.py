#!/usr/bin/env python3
"""
简单的分类API测试脚本
"""

import os
import json
import requests

def test_classification_api():
    """测试分类API"""
    
    # API配置
    base_url = "http://175.168.10.52:8090"
    
    # 测试图片路径
    test_image_path = "data/images/person.png"
    if not os.path.exists(test_image_path):
        print(f"测试图片不存在: {test_image_path}")
        return False
    
    print("=== OWL分类API简单测试 ===")
    
    # 1. 健康检查
    print("\n1. 健康检查...")
    try:
        response = requests.get(f"{base_url}/health", timeout=10)
        if response.status_code == 200:
            print("✅ 服务健康检查通过")
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False
    
    # 2. 测试分类接口
    print("\n2. 测试分类接口...")
    
    try:
        # 准备请求数据
        files = {"image": open(test_image_path, "rb")}
        data = {
            "bboxes": json.dumps([{"id": "A", "x": 100, "y": 100, "w": 200, "h": 200}]),
            "positive_texts": json.dumps(["person"]),
            "negative_texts": json.dumps(["car", "bicycle"]),
            "threshold": 0.15
        }
        
        # 发送请求
        response = requests.post(f"{base_url}/api/v1/classify", files=files, data=data, timeout=30)
        files["image"].close()
        
        if response.status_code == 200:
            result = response.json()
            print("✅ 分类成功")
            
            # 显示结果
            data = result["data"]
            print(f"📊 处理了 {data['summary']['total_bboxes']} 个区域")
            print(f"⚠️  预警数量: {data['summary']['alert_count']}")
            
            # 显示分类结果
            for bbox_result in data["results"]:
                bbox_id = bbox_result["bbox_id"]
                alert = bbox_result["alert"]
                print(f"📦 区域 {bbox_id}: {'🚨 预警' if alert else '✅ 正常'}")
                
                # 显示分类分数
                for cls in bbox_result["classifications"]:
                    emoji = "🔴" if cls["type"] == "positive" else "🔵"
                    print(f"   {emoji} {cls['text']}: {cls['score']:.3f}")
            
        else:
            print(f"❌ 分类失败: {response.status_code}")
            try:
                error_info = response.json()
                print(f"   错误信息: {error_info.get('message', 'Unknown error')}")
            except:
                print(f"   响应内容: {response.text}")
                
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    print("\n=== 测试完成 ===")
    return True


if __name__ == "__main__":
    test_classification_api()
