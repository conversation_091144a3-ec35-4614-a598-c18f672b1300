# task_service.py
from __future__ import annotations
from typing import List, Dict, Any, Optional
from enum import Enum
from datetime import datetime
from pydantic import BaseModel

# ----------------------- 枚举 -----------------------
class AlgorithmType(str, Enum):
    DETECTION = "DETECTION"                               # 检测
    CLASSIFICATION = "CLASSIFICATION"                     # 分类
    TRACKING = "TRACKING"                                 # 跟踪
    RULE = "RULE"                                         # 规则类算法（绊线、区域入侵等）
    RECOGNITION = "RECOGNITION"                           # 识别
    SEGMENTATION = "SEGMENTATION"                         # 分割
    PREPROCESSING = "PREPROCESSING"                       # 预处理
    POSTPROCESSING = "POSTPROCESSING"                     # 后处理

class DetectionType(str, Enum):
    PERSON = "PERSON"                                     # 人员检测
    VEHICLE = "VEHICLE"                                   # 车辆检测
    NON_MOTOR_VEHICLE = "NON_MOTOR_VEHICLE"              # 非机动车检测

class OrchestrationType(str, Enum):
    YOLO_TRACKING_CLIP = "YOLO_TRACKING_CLIP"             # YOLO检测 + 跟踪 + CLIP分类
    OVIT_CLIP = "OVIT_CLIP"                               # O-VIT万物检测 + CLIP分类

class TaskStatus(str, Enum):
    PENDING = "PENDING"                                   # 等待中
    RUNNING = "RUNNING"                                   # 运行中
    STOPPED = "STOPPED"                                   # 已停止
    ERROR = "ERROR"                                       # 错误
    CREATED = "CREATED"                                   # 已创建（保持兼容）

class DirectionType(str, Enum):
    CLOCKWISE = "CLOCKWISE"                               # 顺时针方向（正向）
    COUNTERCLOCKWISE = "COUNTERCLOCKWISE"                 # 逆时针方向（逆向）
    BOTH = "BOTH"                                         # 双向

# ----------------------- 公共子模型 -----------------------
class Point(BaseModel):
    x: int                                                # X坐标
    y: int                                                # Y坐标

class Direction(BaseModel):
    directionType: DirectionType                          # 方向类型：CLOCKWISE（顺时针）/COUNTERCLOCKWISE（逆时针）/BOTH（双向）

class PolygonRule(BaseModel):
    polygonId: str                                        # 多边形ID
    polygonName: str                                      # 多边形名称
    points: List[Point]                                   # 顶点坐标列表

class LineRule(BaseModel):
    lineId: str                                           # 线段ID
    lineName: str                                         # 线段名称
    startPoint: Point                                     # 起点坐标
    endPoint: Point                                       # 终点坐标
    direction: Direction                                  # 检测方向

class ConfidenceThreshold(BaseModel):
    minConfidence: float                                  # 最小置信度
    maxConfidence: float                                  # 最大置信度

class DataCollectionConfig(BaseModel):
    enabled: bool                                         # 是否启用数据收集
    thresholds: ConfidenceThreshold                       # 置信度阈值配置
    samplingRate: float                                   # 采样率
    maxSamplesPerDay: int                                 # 每日最大样本数

class TrainingConfig(BaseModel):
    labels: list[str]                                     # 标签列表
    dataCollection: DataCollectionConfig                  # 数据集配置
    modelVersion: Optional[str] = None                    # 模型版本

class RuleConfig(BaseModel):
    ruleType: str                                         # 规则类型
    polygons: List[PolygonRule] = []                      # 多边形区域规则配置
    lines: List[LineRule] = []                            # 拌线线段规则配置

class AlertConfig(BaseModel):
    labels: list[str] = []                                # 告警标签列表
    confidence: float = 0.0                               # 告警阈值
    positiveLabels: list[str] = []                        # 零样本分类正例标签
    negativeLabels: list[str] = []                        # 零样本分类负例标签

class DecoderConfig(BaseModel):
    keyFrameOnly: bool = False
    decodeStep: int = 4

# ----------------------- 算法 -----------------------
class Algorithm(BaseModel):
    algorithmId: str = ""
    algorithmName: str = ""
    algorithmType: str = ""  # 改为str以匹配mock
    order: int = 1
    required: bool = True
    dependsOn: List[str] = []
    config: Optional[Dict[str, Any]] = None
    alertConfig: Optional[AlertConfig] = None
    trainingConfig: Optional[TrainingConfig] = None
    ruleConfig: Optional[RuleConfig] = None

# ----------------------- 算法编排 -----------------------
class AlgorithmOrchestration(BaseModel):
    orchestrationId: str = ""
    orchestrationName: Optional[str] = None
    orchestrationType: OrchestrationType
    algorithmChain: List[Algorithm] = []
    decoderConfig: Optional[DecoderConfig] = None
    status: Optional[TaskStatus] = TaskStatus.CREATED

# ----------------------- 任务 -----------------------
class TaskMeta(BaseModel):
    enabled: bool = True
    taskLevel: str = "MEDIUM"
    protocol: str = "VIDEO"
    eventTypeId: str = ""
    eventAction: List[str] = ["ALERT"]

# ----------------------- 流信息 -----------------------
class StreamConfig(BaseModel):
    resolution: str = "1920x1080"
    frameRate: int = 25
    protocol: str = "RTSP"
    url: str
    decoderConf: Optional[DecoderConfig] = None

# ----------------------- 设备信息 -----------------------
class Device(BaseModel):
    deviceId: str = ""
    deviceName: Optional[str] = None
    streamConfig: Optional[StreamConfig] = None

# ----------------------- 任务创建请求 -----------------------
class SimplifiedAtomicTask(BaseModel):
    taskId: str = ""
    taskName: str = ""
    taskDescription: Optional[str] = None
    taskMeta: TaskMeta
    algorithmOrchestration: AlgorithmOrchestration
    device: Device


# ----------------------- 任务请求包装 -----------------------
class TaskRequest(BaseModel):
    """任务请求模型"""
    taskRequest: SimplifiedAtomicTask
    config: Optional[Dict[str, Any]] = None
    priority: Optional[int] = 1
    region: Optional[str] = "default"


# ----------------------- 任务操作请求 -----------------------
class TaskActionRequest(BaseModel):
    taskId: str                                           # 任务ID
    action: str = "STOP"                                  # 任务操作


# ----------------------- 截图模型 -----------------------
class Screenshot(BaseModel):
    """截图模型"""
    timestamp: datetime
    filename: str
    filePath: str
    s3Url: Optional[str] = None  # S3存储URL
    events: List[Any] = []  # 关联的事件列表


# ----------------------- 任务响应模型 -----------------------
class TaskResponse(BaseModel):
    """任务响应模型"""
    taskId: str
    status: TaskStatus
    deviceId: str
    rtspUrl: str
    startTime: Optional[datetime] = None
    lastEventTime: Optional[datetime] = None
    eventCount: int = 0
    screenshots: List[Screenshot] = []  # 截图列表


# ----------------------- 健康检查响应 -----------------------
class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    details: Dict[str, Any] = {}
    timestamp: int
