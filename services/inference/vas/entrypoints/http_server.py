# Copyright 2023-2024 SGLang Team
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""
This file implements HTTP APIs for video analysis server via flask.
"""

import os
import dataclasses
import multiprocessing as mp
from multiprocessing.context import BaseContext
import threading
import signal
import time
import yaml
import psutil
from typing import Dict, List, Any, Optional
from datetime import datetime
import flask
from flask import Flask, request, jsonify
# Fix a bug of Python threading
setattr(threading, "_register_atexit", lambda *args, **kwargs: None)
from vas.common.utils import set_ulimit, kill_process_tree
from vas.modules.launch_modules import launch_sub_processes
from vas.common.server_args import ServerArgs, PortArgs
from vas.common.logger_tools import get_logger
from vas.entrypoints.task_interface import *
from vas.entrypoints.event_interface import *
from vas.common.kv_db import KVStore
from vas.modules.base_module import *
from vas.services.service_registry import start_service_registration, stop_service_registration


logger = get_logger(__name__)


# Store global states
@dataclasses.dataclass
class GlobalState:
    mp_context: BaseContext
    task_infos: Dict  # 存储任务进程信息和状态
    server_args: ServerArgs
    stats: Dict = dataclasses.field(default_factory=lambda: {
        "total_tasks": 0,
        "active_tasks": 0,
        "completed_tasks": 0,
        "failed_tasks": 0,
        "last_event_time": None,
        "start_time": datetime.now()
    })


# 辅助函数
def update_task_stats(global_state: GlobalState, task_id: str, event_type: str = "event"):
    """更新任务统计信息"""
    try:
        if task_id in global_state.task_infos:
            task_info = global_state.task_infos[task_id]
            task_info["event_count"] = task_info.get("event_count", 0) + 1
            task_info["last_event_time"] = datetime.now()
            global_state.stats["last_event_time"] = task_info["last_event_time"].isoformat()
            logger.debug(f"Updated task {task_id} stats: event_count={task_info['event_count']}")
    except Exception as e:
        logger.error(f"Failed to update task stats: {e}")


def get_task_info(task_id: str) -> Optional[Dict]:
    """根据任务ID获取任务信息"""
    global_state = get_global_state()
    return global_state.task_infos.get(task_id) if global_state else None





_global_state = None


def set_global_state(global_state: GlobalState):
    global _global_state
    _global_state = global_state


def get_global_state() -> GlobalState:
    global _global_state
    return _global_state


# flask app
app = Flask(__name__)
flask_version = tuple(map(int, flask.__version__.split(".")))

# 根据版本选择不同的配置方式
if flask_version >= (2, 3):
    # Flask >= 2.3 推荐方式
    app.json.ensure_ascii = False
else:
    # Flask <= 2.2 的旧方式（仍兼容）
    app.config['JSON_AS_ASCII'] = False


def start_task_processes(atomic_task: SimplifiedAtomicTask, config: Optional[Dict[str, Any]] = None):
    global_state = get_global_state()
    mp_context = global_state.mp_context
    cmd_args = global_state.server_args
    task_infos = global_state.task_infos
    stats = global_state.stats

    task_id = atomic_task.taskId
    if task_id in task_infos:
        message = f"任务: {task_id} 已经存在"
        logger.warning(message)
        return message, TaskStatus.ERROR

    # 初始化任务信息
    device_id = atomic_task.device.deviceId
    rtsp_url = atomic_task.device.streamConfig.url if atomic_task.device.streamConfig else ""

    task_info = {
        "atomic_task": atomic_task,
        "task_id": task_id,
        "device_id": device_id,
        "rtsp_url": rtsp_url,
        "status": TaskStatus.PENDING,
        "start_time": datetime.now(),
        "last_event_time": None,
        "event_count": 0,
        "config": config,
        "processes": [],
        "queues": [],
        "screenshots": []  # 截图列表
    }

    # 根据algorithm_chain动态构建module_list
    algorithm_chain = atomic_task.algorithmOrchestration.algorithmChain

    # 算法类型到模块的映射
    ALGORITHM_TYPE_TO_MODULE = {
        "CLASSIFICATION": CLASSIFICATION_MODULE,
        "TRACKING": BYTE_TRACKER_MODULE,
        "DETECTION": YOLOV8_DETECTOR_MODULE,  # 默认YOLO检测
        "OWL_DETECTION": OWL_DETECTOR_MODULE,
        "RULE": None,  # 规则类型需要特殊处理
        "PIPELINE": DSL_PIPELINE_MODULE
    }

    # 支持的模块类型
    SUPPORTED_MODULES = {
        CLASSIFICATION_MODULE,
        BYTE_TRACKER_MODULE,
        YOLOV8_DETECTOR_MODULE,
        DSL_PIPELINE_MODULE,
        OWL_DETECTOR_MODULE,
        TRIPWIRE_INTRUSION_MODULE,
        ZONE_INTRUSION_MODULE
    }

    try:
        # 按order逆序排列算法链
        sorted_algorithms = sorted(algorithm_chain, key=lambda x: x.order, reverse=True)

        module_list = []
        for algorithm in sorted_algorithms:
            algo_type = algorithm.algorithmType
            algo_order = algorithm.order

            # 特殊处理：根据算法ID或名称确定具体的检测模块
            if algo_type == "DETECTION":
                # OWL/OVIT检测：通过算法ID区分
                if ("owl" in algorithm.algorithmId.lower() or "owl" in algorithm.algorithmName.lower() or
                    "ovit" in algorithm.algorithmId.lower() or "ovit" in algorithm.algorithmName.lower()):
                    module_name = OWL_DETECTOR_MODULE
                # YOLO检测：统一使用"detection"算法ID，内部通过detection_type配置区分人车非
                else:
                    module_name = YOLOV8_DETECTOR_MODULE
            elif algo_type == "RULE":
                # 根据算法ID确定具体的规则模块
                if ("tripwire" in algorithm.algorithmId.lower() or "tripwire" in algorithm.algorithmName.lower()):
                    module_name = TRIPWIRE_INTRUSION_MODULE
                elif ("zone" in algorithm.algorithmId.lower() or "zone" in algorithm.algorithmName.lower() or
                      "intrusion" in algorithm.algorithmId.lower() or "intrusion" in algorithm.algorithmName.lower()):
                    module_name = ZONE_INTRUSION_MODULE
                else:
                    # 默认使用区域入侵模块
                    module_name = ZONE_INTRUSION_MODULE
            else:
                module_name = ALGORITHM_TYPE_TO_MODULE.get(algo_type)

            if module_name is None:
                message = f"不支持的算法类型: {algo_type}"
                task_info["status"] = TaskStatus.ERROR
                task_infos[task_id] = task_info
                return message, TaskStatus.ERROR

            if module_name not in SUPPORTED_MODULES:
                message = f"不支持的模块类型: {module_name}"
                task_info["status"] = TaskStatus.ERROR
                task_infos[task_id] = task_info
                return message, TaskStatus.ERROR

            module_list.append((module_name, algo_order))

        # DSL_PIPELINE_MODULE不在算法编排链里，而是在device配置中，所以总是 order 0
        # 初始化时最后初始化，放到module_list的最后
        module_list.append((DSL_PIPELINE_MODULE, 0))

        if not module_list:
            message = "算法链为空，无法创建任务"
            task_info["status"] = TaskStatus.ERROR
            task_infos[task_id] = task_info
            return message, TaskStatus.ERROR

        logger.info(f"构建 task:{task_id} 模块列表: {module_list}")

    except Exception as e:
        message = f"解析算法链失败: {str(e)}"
        logger.error(message)
        task_info["status"] = TaskStatus.ERROR
        task_infos[task_id] = task_info
        return message, TaskStatus.ERROR

    try:
        processes, queues = launch_sub_processes(mp_context=mp_context,
                                                cmd_args=cmd_args,
                                                module_list=module_list,
                                                task_info=atomic_task)
        task_info["processes"] = processes
        task_info["queues"] = queues

        # 更新任务状态
        task_info["status"] = TaskStatus.RUNNING
        task_infos[task_id] = task_info

        # 更新统计信息
        stats["total_tasks"] += 1
        stats["active_tasks"] += 1

        message = f"任务创建成功: {task_id}"
        logger.info(message)
        return message, TaskStatus.RUNNING

    except Exception as e:
        message = f"任务创建失败: {str(e)}"
        logger.error(message)
        task_info["status"] = TaskStatus.ERROR
        task_infos[task_id] = task_info
        stats["failed_tasks"] += 1
        return message, TaskStatus.ERROR


def stop_task_processes(task_id: str):
    global_state = get_global_state()
    task_infos = global_state.task_infos
    stats = global_state.stats

    if task_id not in task_infos:
        message = f"当前没有任务: {task_id}"
        logger.warning(message)
        return message, TaskStatus.ERROR

    try:
        task_info = task_infos[task_id]
        module_processes = task_info.get("processes", [])

        for item in reversed(module_processes):
            process_id = item.pid
            try:
                if psutil.pid_exists(process_id):
                    process_name = psutil.Process(process_id).name()
                    logger.info(f"try to kill task: {task_id}, process: {process_name}")
                    os.kill(process_id, signal.SIGKILL)
                    item.join()
                    logger.info(f"success kill task {task_id}, process: {process_name}")
                else:
                    logger.info(f"task {task_id}, process: {process_id} not exists")
            except Exception as e:
                message = f"停止任务: {process_id} 失败: {str(e)}"
                logger.error(message)

        # 更新任务状态
        task_info["status"] = TaskStatus.STOPPED

        # 更新统计信息
        stats["active_tasks"] = max(0, stats["active_tasks"] - 1)
        stats["completed_tasks"] += 1

        # 清理任务信息
        del task_infos[task_id]

        message = f"任务停止成功: {task_id}"
        logger.info(message)
        return message, TaskStatus.STOPPED

    except Exception as e:
        message = f"停止任务失败: {str(e)}"
        logger.error(message)
        return message, TaskStatus.ERROR


##### Mock-compatible API endpoints #####

@app.route("/health", methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        global_state = get_global_state()
        stats = global_state.stats
        task_infos = global_state.task_infos

        # 计算活跃任务数
        active_tasks = len([t for t in task_infos.values() if t.get("status") == TaskStatus.RUNNING])
        total_tasks = len(task_infos)

        # 判断整体状态
        status_value = "UP"
        details = {
            "active_tasks": active_tasks,
            "total_tasks": total_tasks,
            "completed_tasks": stats.get("completed_tasks", 0),
            "failed_tasks": stats.get("failed_tasks", 0),
            "start_time": stats.get("start_time", datetime.now()).isoformat() if stats.get("start_time") else None,
            "last_event_time": stats.get("last_event_time")
        }

        return jsonify(HealthResponse(
            status=status_value,
            details=details,
            timestamp=int(time.time() * 1000)
        ).dict()), 200

    except Exception as e:
        logger.error(f"健康检查异常: {e}")
        return jsonify(HealthResponse(
            status="DOWN",
            details={"error": str(e)},
            timestamp=int(time.time() * 1000)
        ).dict()), 500


@app.route("/api/v1/tasks", methods=['POST'])
def create_task():
    """创建任务接口 - 兼容mock格式"""
    try:
        request_data = request.get_json(force=True)

        # 支持两种格式：直接SimplifiedAtomicTask或TaskRequest包装
        if "taskRequest" in request_data:
            # Mock格式：TaskRequest包装
            task_request = TaskRequest.parse_obj(request_data)
            atomic_task = task_request.taskRequest
            config = task_request.config
        else:
            # 原始格式：直接SimplifiedAtomicTask
            atomic_task = SimplifiedAtomicTask.parse_obj(request_data)
            config = None

        message, status = start_task_processes(atomic_task, config)

        # 返回mock格式的响应
        return jsonify({
            "success": True,
            "message": message,
            "taskId": atomic_task.taskId
        }), 200

    except Exception as e:
        logger.error(f"创建任务异常: {e}")
        return jsonify({
            "success": False,
            "message": f"创建任务异常: {str(e)}"
        }), 400


@app.route("/api/v1/tasks/<task_id>", methods=['GET'])
def get_task(task_id: str):
    """获取任务状态接口"""
    try:
        global_state = get_global_state()
        task_infos = global_state.task_infos

        if task_id not in task_infos:
            return jsonify({"error": f"任务不存在: {task_id}"}), 404

        task_info = task_infos[task_id]
        return jsonify(TaskResponse(
            taskId=task_info["task_id"],
            status=task_info["status"],
            deviceId=task_info["device_id"],
            rtspUrl=task_info["rtsp_url"],
            startTime=task_info["start_time"],
            lastEventTime=task_info["last_event_time"],
            eventCount=task_info["event_count"],
            screenshots=task_info.get("screenshots", [])
        ).dict()), 200

    except Exception as e:
        logger.error(f"获取任务异常: {e}")
        return jsonify({"error": f"获取任务异常: {str(e)}"}), 500


@app.route("/api/v1/tasks", methods=['GET'])
def list_tasks():
    """获取任务列表接口"""
    try:
        global_state = get_global_state()
        task_infos = global_state.task_infos

        task_list = []
        for task_info in task_infos.values():
            task_list.append({
                "task_id": task_info["task_id"],
                "device_id": task_info["device_id"],
                "status": task_info["status"].value,
                "start_time": task_info["start_time"].isoformat() if task_info["start_time"] else None,
                "event_count": task_info["event_count"]
            })

        return jsonify({
            "tasks": task_list,
            "total": len(task_list)
        }), 200

    except Exception as e:
        logger.error(f"获取任务列表异常: {e}")
        return jsonify({"error": f"获取任务列表异常: {str(e)}"}), 500


@app.route("/api/v1/tasks/<task_id>", methods=['DELETE'])
def delete_task(task_id: str):
    """删除任务接口"""
    try:
        message, status = stop_task_processes(task_id)

        if status == TaskStatus.STOPPED:
            return jsonify({"success": True, "message": message}), 200
        else:
            return jsonify({"success": False, "message": message}), 404

    except Exception as e:
        logger.error(f"删除任务异常: {e}")
        return jsonify({"error": f"删除任务异常: {str(e)}"}), 500


@app.route("/api/v1/tasks/<task_id>/events", methods=['GET'])
def get_task_events(task_id: str):
    """获取任务事件接口"""
    try:
        global_state = get_global_state()
        task_infos = global_state.task_infos

        if task_id not in task_infos:
            return jsonify({"error": "任务不存在"}), 404

        task_info = task_infos[task_id]

        # 模拟返回一些事件数据
        events = []
        event_count = task_info.get("event_count", 0)
        if event_count > 0:
            # 这里可以从实际的事件存储中获取事件
            # 目前返回模拟数据
            events = [
                {
                    "eventId": f"event_{task_id}_{i}",
                    "timestamp": task_info["start_time"].isoformat() if task_info["start_time"] else None,
                    "eventType": "ALERT",
                    "confidence": 0.85
                }
                for i in range(min(event_count, 10))  # 最多返回10个事件
            ]

        return jsonify({
            "task_id": task_id,
            "total_events": event_count,
            "events": events
        }), 200

    except Exception as e:
        logger.error(f"获取任务事件异常: {e}")
        return jsonify({"error": f"获取任务事件异常: {str(e)}"}), 500


@app.route("/api/v1/stats", methods=['GET'])
def get_stats():
    """获取服务统计信息"""
    try:
        global_state = get_global_state()
        stats = global_state.stats
        task_infos = global_state.task_infos

        # 计算实时统计
        active_tasks = len([t for t in task_infos.values() if t.get("status") == TaskStatus.RUNNING])

        task_stats = {
            "total_tasks": len(task_infos),
            "active_tasks": active_tasks,
            "completed_tasks": stats.get("completed_tasks", 0),
            "failed_tasks": stats.get("failed_tasks", 0),
            "last_event_time": stats.get("last_event_time"),
            "start_time": stats.get("start_time", datetime.now()).isoformat() if stats.get("start_time") else None
        }

        return jsonify({
            "task_manager": task_stats,
            "timestamp": int(time.time() * 1000)
        }), 200

    except Exception as e:
        logger.error(f"获取统计信息异常: {e}")
        return jsonify({"error": f"获取统计信息异常: {str(e)}"}), 500


@app.route("/", methods=['GET'])
def root():
    """根路径"""
    return jsonify({
        "service": "Video Analysis Inference Service",
        "version": "1.0.0",
        "status": "running",
        "endpoints": {
            "health": "/health",
            "tasks": "/api/v1/tasks",
            "stats": "/api/v1/stats"
        }
    }), 200


def load_service_registry_config(config_path: str) -> Optional[Dict[str, Any]]:
    """从配置文件加载服务注册配置"""
    try:
        if not config_path or not os.path.exists(config_path):
            logger.warning(f"配置文件不存在: {config_path}")
            return None

        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)

        service_registry_config = config.get('service_registry')
        if not service_registry_config:
            logger.info("配置文件中未找到service_registry配置")
            return None

        if not service_registry_config.get('enabled', False):
            logger.info("服务注册功能已禁用")
            return None

        logger.info("成功加载服务注册配置")
        return service_registry_config

    except Exception as e:
        logger.error(f"加载服务注册配置失败: {e}")
        return None


def set_envs_and_config(server_args: ServerArgs):
    # Set global environments

    # Configure global logger with the log path from server args
    from vas.common.logger_tools import config_logger
    config_logger("vas", server_args.log_path, "info")

    # Set ulimit
    set_ulimit()

    # set module environments

    # Register the signal handler.
    def sigchld_handler(signum, frame):
        pid, exitcode = os.waitpid(0, os.WNOHANG)
        if exitcode != 0:
            logger.warning(f"Child process unexpectedly failed with {exitcode=}. {pid=}")

    signal.signal(signal.SIGCHLD, sigchld_handler)

    # Register the signal handler.
    # The child processes will send SIGQUIT to this process when any error happens
    # This process then clean up the whole process tree
    def sigquit_handler(signum, frame):
        logger.error(
            "Received sigquit from a child process. It usually means the child failed."
        )
        kill_process_tree(os.getpid())

    signal.signal(signal.SIGQUIT, sigquit_handler)


# def launch_server(server_args: ServerArgs, port_args: Optional[PortArgs] = None):
def launch_server(server_args: ServerArgs, port_args: Optional[PortArgs] = None):

    # Configure global environment
    set_envs_and_config(server_args)

    # Load service registry configuration
    service_registry_config = load_service_registry_config(server_args.config_path)

    # Set mp start method
    start_method = "spawn"
    mp.set_start_method(start_method, force=True)
    mp_ctx = mp.get_context(start_method)

    # Allocate ports for inter-process communications
    # if port_args is None:
    #     port_args = PortArgs.init_new(server_args)
    #     logger.info(f"{server_args=}")

    # launch sub module processes
    global_processes = {}
    global_queues = {}
    default_processes, default_queues = launch_sub_processes(mp_ctx, server_args, [])
    global_processes["default"] = default_processes
    global_queues["default"] = default_queues

    # load old task processes

    # set global state
    set_global_state(
        GlobalState(
            mp_context=mp_ctx,
            task_infos={},
            server_args=server_args,
            stats={
                "total_tasks": 0,
                "active_tasks": 0,
                "completed_tasks": 0,
                "failed_tasks": 0,
                "last_event_time": None,
                "start_time": datetime.now()
            }
        )
    )

    # Start service registration if enabled
    service_registry = None
    try:
        if service_registry_config and service_registry_config["enabled"]:
            logger.info("启动服务注册...")
            service_registry = start_service_registration(
                service_registry_config,
                server_args.host,
                server_args.port
            )
        else:
            logger.info("服务注册功能未启用")
    except Exception as e:
        logger.error(f"启动服务注册失败: {e}")

    try:
        app.run(host=server_args.host, port=server_args.port)
    finally:
        # Stop service registration when server shuts down
        if service_registry:
            logger.info("停止服务注册...")
            stop_service_registration()