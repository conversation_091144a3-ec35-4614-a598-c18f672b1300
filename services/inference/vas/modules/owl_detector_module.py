import cv2
import time
import logging
import torch
import numpy as np
from torchvision.ops.boxes import batched_nms
from functools import lru_cache
from typing import List, Tuple
from transformers import Owlv2Processor, AltCLIPProcessor
from vas.entrypoints.task_interface import *
from vas.common.opencv_utils import save_detect_result
from vas.modules.base_module import BaseModule, MessageInfo, MetaInfo, FrameInfo


class OWLDetectorModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        self.text_model = self.config[self.module_type]["text_model"]
        self.text_processor = AltCLIPProcessor.from_pretrained(self.text_model["model_path"], local_files_only=True)
        self.visual_model = self.config[self.module_type]["visual_model"]
        self.visual_processor = Owlv2Processor.from_pretrained(self.visual_model["model_path"], local_files_only=True)
        self.query_texts = self.config[self.module_type]["query_texts"]
        self.policy = self.config[self.module_type]["policy"]

        # check text model ready
        model_name = self.text_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")

        # check visual model ready
        model_name = self.visual_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")


    def get_preprocess_shape(self, oldh: int, oldw: int, long_side_length: int) -> tuple[int, int]:
        """返回等比缩放后、**未 padding 前**的 (newh, neww)。"""
        scale = long_side_length * 1.0 / max(oldh, oldw)
        newh, neww = oldh * scale, oldw * scale
        return int(newh), int(neww)


    def de_normalize_bbox(self, img: np.ndarray, bboxes: np.ndarray, target_size: tuple):
        ori_h, ori_w, _ = img.shape
        preprocess_h, preprocess_w = self.get_preprocess_shape(ori_h, ori_w, target_size[0])
        # 预处理后的图片会放到左上位置，不需要做padding, 直接按比例缩放即可
        scale_h = ori_h / preprocess_h
        scale_w = ori_w / preprocess_w
        scale = scale_h if scale_h > scale_w else scale_w

        bboxes[:, 0] = bboxes[:, 0] * target_size[1] * scale
        bboxes[:, 1] = bboxes[:, 1] * target_size[0] * scale
        bboxes[:, 2] = bboxes[:, 2] * target_size[1] * scale
        bboxes[:, 3] = bboxes[:, 3] * target_size[0] * scale
        bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 0]
        bboxes[:, 3] = bboxes[:, 3] - bboxes[:, 1]
        return bboxes


    @lru_cache(maxsize=2048)
    def get_text_embed(self, text: str):
        """一次只处理一条文本, 便于lrucache"""
        altclip_inputs = self.text_processor(text=text,
                                            images=None,
                                            return_tensors="np",
                                            padding='max_length',
                                            max_length=77,
                                            truncation=True)
        # altclip_inputs shape: [1, 77]
        model_inputs = {
            "input_ids": altclip_inputs.input_ids.astype(np.int32),
            "attention_mask": altclip_inputs.attention_mask.astype(np.int32),
        }
        model_name = self.text_model["model_name"]
        status, infer_results = self.infer_client.infer(model_name, model_inputs)
        query_embed = infer_results["query_embed"]
        return query_embed


    def get_visual_embeds(self, pixel_values: np.ndarray,
                          query_embeds: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """get visual embeds
        Args:
            pixel_values (np.ndarray): shape is [bs, channel, h, w]
            query_embeds (np.ndarray): shape is [bs, querynum, maxtoken(77 for chn model)]

        Returns:
            Tuple[np.ndarray, np.ndarray, np.ndarray]: target_class_logits, objectnesses, target_boxes_as_corners
        """
        model_name = self.visual_model["model_name"]
        model_inputs = {
            "pixel_values": pixel_values,
            "query_embeds": query_embeds
        }
        # model_outputs = ["target_class_logits", "objectnesses", "boxes"]
        status, infer_results = self.infer_client.infer(model_name, model_inputs)
        target_class_logits = infer_results["target_class_logits"]
        objectnesses = infer_results["objectnesses"]
        target_boxes_as_corners = infer_results["boxes"]
        return target_class_logits, objectnesses, target_boxes_as_corners


    def detect_by_query_embedding(self, image, text_embeds, owl_para):
        pixel_values = self.visual_processor(images=image, return_tensors="np").pixel_values
        target_class_logits, _, target_boxes_as_corners = self.get_visual_embeds(pixel_values, text_embeds)

        # as tensor, 后面要做 nms
        b, hw, q = target_class_logits.shape
        target_class_logits = torch.tensor(target_class_logits)
        target_boxes_as_corners = torch.tensor(target_boxes_as_corners).unsqueeze(2).expand(-1, -1, q, -1)

        # logger.info("target_class_logits shape = %s, max = %s", target_class_logits.shape,
        #             torch.max(target_class_logits))

        # Cut Threshold
        conf_thresh = owl_para["conf_thresh"] 
        target_class_sigmoids = torch.sigmoid(target_class_logits)
        # logger.blueinfo("target_class_sigmoids max score = %s", torch.max(target_class_sigmoids))
        target_box_nms_idxs = torch.arange(b * q).reshape(b, 1, q).expand(-1, hw, -1)[target_class_sigmoids > conf_thresh]
        target_boxes_as_corners = target_boxes_as_corners[target_class_sigmoids > conf_thresh]
        target_class_sigmoids = target_class_sigmoids[target_class_sigmoids > conf_thresh]

        # NMS
        nms_thresh = owl_para["nms_thresh"]
        keep_inds = batched_nms(target_boxes_as_corners,
                                target_class_sigmoids,
                                idxs=target_box_nms_idxs,
                                iou_threshold=nms_thresh)
        bboxes = target_boxes_as_corners[keep_inds]
        scores = target_class_sigmoids[keep_inds]
        query_ids = target_box_nms_idxs[keep_inds]

        scores = np.array(scores).reshape(-1, 1)
        bboxes = np.array(bboxes).reshape(-1, 4)
        query_ids = np.array(query_ids).reshape(-1, 1)

        # 恢复 bbox 到原始坐标
        target_size = tuple(pixel_values.shape[2:])
        bboxes = self.de_normalize_bbox(image, bboxes=bboxes, target_size=target_size)
        merged_output = np.concatenate((bboxes, scores, query_ids), axis=1).astype(np.float32)
        return merged_output


    def get_owl_para(self, message: MessageInfo):
        # 初始化owl算法参数
        owl_para = {}
        owl_para["query_texts"] = self.query_texts
        owl_para["conf_thresh"] = self.policy["embeddings_query"]["threshold"]
        owl_para["nms_thresh"] = self.policy["nms"]["threshold"]

        # 从任务信息获取当前模块的算法配置，并更新owl参数
        algo_config = self.get_module_algo_config(message=message)

        if algo_config is None or algo_config.config is None:
            return owl_para

        # 更新owl参数
        owl_para["query_texts"] = algo_config.config.get("query_texts", owl_para["query_texts"]) 
        owl_para["conf_thresh"] = algo_config.config.get("confidence", owl_para["conf_thresh"])
        owl_para["nms_thresh"] = algo_config.config.get("nms_threshold", owl_para["nms_thresh"])
        return owl_para


    def process_execute_task_message(self, message: MessageInfo):

        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"

        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.info(f"[OWL] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
            return message

        # 初始化 owl 参数
        owl_para = self.get_owl_para(message=message)

        # 图像预处理bgr转rgb
        frame, _, _ = self.extract_frame_info(message)
        self.logger.debug(f"[OWL] Frame {frame_id}: 提取帧信息完成，frame shape: {frame.shape}")
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        self.logger.debug(f"[OWL] Frame {frame_id}: 颜色空间转换完成 BGR->RGB")

        # 获取文本嵌入
        query_texts = owl_para["query_texts"]
        self.logger.info(f"[OWL] Frame {frame_id}: 🦉 开始OWL目标检测，查询文本: {query_texts}")
        text_embeds = np.concatenate([self.get_text_embed(text) for text in query_texts], axis=0)
        self.logger.debug(f"[OWL] Frame {frame_id}: 文本嵌入生成完成，shape: {text_embeds.shape}")

        # 使用文本嵌入和图像检测目标
        self.logger.debug(f"[OWL] Frame {frame_id}: 开始查询嵌入检测")
        detect_res = self.detect_by_query_embedding(rgb_frame, text_embeds, owl_para)
        self.logger.debug(f"[OWL] Frame {frame_id}: 检测完成")
        # save_detect_result(frame, detect_res, "owl_detect_result.jpg")

        # OWL检测逻辑：如果有检测对象，那么往下传；如果没有检测对象，标记跳过
        if detect_res is not None and len(detect_res) > 0:
            self.logger.info(f"[OWL] Frame {frame_id}: ✅ 检测到 {len(detect_res)} 个目标，设置skip_processing=False，继续传递")
            self.update_frame_info(message, bboxes=detect_res)
            message.skip_processing = False

            # 打印检测结果详情
            for i, bbox in enumerate(detect_res):
                if len(bbox) >= 6:
                    x, y, w, h, score, query_id = bbox[:6]
                    query_text = query_texts[int(query_id)] if int(query_id) < len(self.query_texts) else "unknown"
                    self.logger.debug(f"[OWL] Frame {frame_id}: 目标{i+1} - 位置:({x:.1f},{y:.1f},{w:.1f},{h:.1f}), 置信度:{score:.3f}, 查询:'{query_text}'")
        else:
            self.logger.info(f"[OWL] Frame {frame_id}: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理")
            message.skip_processing = True

        return message


    # for debug
    def process_none_message(self, message: MessageInfo):
        # test_file = "/workspace/video_analysis_server/data/images/owl_glove_small.jpg"
        # test_file ="/workspace/video_analysis_server/data/images/vlcsnap-2025-07-27-09h00m37s504.png"
        test_file ="/workspace/data/images/cat_224_224.jpg"
        video_frame = cv2.imread(test_file)
        frame_info = self.prepare_frame_info(video_frame)
        exec_task_message = self.prepare_execute_task_message(frame_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message
