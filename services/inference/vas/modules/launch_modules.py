import os
import yaml
import time
import signal
import psutil
import logging
import faulthandler
import setproctitle
import multiprocessing as mp
from vas.entrypoints.task_interface import *
from vas.common.utils import get_exception_traceback
from vas.common.logger_tools import config_logger
from vas.modules.base_module import VIDEO_CAP_MODULE, DSL_PIPELINE_MODULE, CLASSIFICATION_MODULE, \
    OWL_DETECTOR_MODULE, YOLOV8_DETECTOR_MODULE, BYTE_TRACKER_MODULE, TRIPWIRE_INTRUSION_MODULE, ZONE_INTRUSION_MODULE
from vas.modules.video_cap_module import VideoCapModule
from vas.modules.dsl_pipeline_module import DSLPipelineModule
from vas.modules.classification_module import ClassificationModule
from vas.modules.yolov8_detector_module import Yolov8DetectorModule
from vas.modules.byte_tracker_module import ByteTrackerModule
from vas.modules.owl_detector_module import OWLDetectorModule
from vas.modules.tripwire_intrusion_module import TripwireIntrusionModule
from vas.modules.zone_intrusion_module import ZoneIntrusionModule

MODULE_CLASS = {}
MODULE_CLASS[VIDEO_CAP_MODULE] = VideoCapModule
MODULE_CLASS[DSL_PIPELINE_MODULE] = DSLPipelineModule
MODULE_CLASS[CLASSIFICATION_MODULE] = ClassificationModule
MODULE_CLASS[YOLOV8_DETECTOR_MODULE] = Yolov8DetectorModule
MODULE_CLASS[BYTE_TRACKER_MODULE] = ByteTrackerModule
MODULE_CLASS[OWL_DETECTOR_MODULE] = OWLDetectorModule
MODULE_CLASS[TRIPWIRE_INTRUSION_MODULE] = TripwireIntrusionModule
MODULE_CLASS[ZONE_INTRUSION_MODULE] = ZoneIntrusionModule


def run_module_process(cmd_args, module_info, queues, task_info: None | SimplifiedAtomicTask = None):

    module_type = module_info["type"]
    module_order = module_info["order"]
    proc_name = module_type + "_" + str(module_order)
    log_path = cmd_args.log_path
    log_level = cmd_args.log_level
    task_log_path = log_path
    if task_info is not None:
        task_log_path = os.path.join(log_path, task_info.taskId)
    
    os.makedirs(task_log_path, exist_ok=True)
    logger = config_logger(proc_name, task_log_path, log_level)
    setproctitle.setproctitle(f"{proc_name}")
    faulthandler.enable()
    current_process = psutil.Process()
    parent_process = current_process.parent()
    try:
        module = MODULE_CLASS[module_type](module_info, cmd_args, queues, logger, task_info)
        logger.info(f"enter {proc_name} event loop")
        module.event_loop()
        logger.info(f"exit {proc_name} event loop")
    except Exception:
        traceback = get_exception_traceback()
        logger.error(f"Scheduler hit an exception: {traceback}")
        parent_process.send_signal(signal.SIGQUIT)
    finally:
        # 确保文件被关闭
        logger.info("Closing log file...")
        logger.info(f"Open files: {current_process.open_files()}")
        logger.info(f"Connections: {current_process.connections()}")
        logger.info(f"Memory info: {current_process.memory_info()}")
        logger.info(f"CPU times: {current_process.cpu_times()}")
        logger.handlers[0].close()
        logger.removeHandler(logger.handlers[0])
        logger.info("Log file closed")


def launch_sub_processes(mp_context, cmd_args, module_list:list = [], task_info: None | SimplifiedAtomicTask = None):

    # init module queues
    module_queues = {}
    for index in range(len(module_list)):
        if isinstance(module_list[index], tuple):
            module_type = module_list[index][0]
            module_order = module_list[index][1]
        else:
            module_type = module_list[index]
            module_order = index
        queue_name = module_type + "_" + str(module_order)
        queue = mp_context.Queue(maxsize = cmd_args.queue_size)
        module_queues[queue_name] = queue

    module_processes = []
    # run module processes
    for index in range(len(module_list)):
        if isinstance(module_list[index], tuple):
            module_type = module_list[index][0]
            module_order = module_list[index][1]
        else:
            module_type = module_list[index]
            module_order = index
        module_info = {}
        module_info["type"] = module_type
        module_info["order"] = module_order
        proc = mp_context.Process(target=run_module_process, args=(cmd_args, module_info, module_queues, task_info,),)
        proc.start()
        module_processes.append(proc)

    return module_processes, module_queues
