import cv2
import time
import logging
from vas.entrypoints.task_interface import *
from vas.modules.base_module import BaseModule, MetaInfo, FrameInfo

# for debug
class VideoCapModule(BaseModule):

    def __init__(self, module_info, cmd_args, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, cmd_args, queue, logger, task_info)
        self.work_mode = "loop"
        # 移除自维护的frame_id，使用DSL传递的frame_id
        self.video_cap = None
        self.video_uri = self.config[self.module_type]["video_uri"]
        self.frame_rate = self.config[self.module_type]["frame_rate"]
        self.open_video()


    def open_video(self):
        self.close_video()
        self.video_cap = cv2.VideoCapture(self.video_uri)
        if not self.video_cap.isOpened():
            raise RuntimeError(f"open {self.video_uri} fail")


    def close_video(self):
        if self.video_cap is not None:
            self.video_cap.release()
            self.video_cap = None


    def cap_frame(self):
        ret, frame = self.video_cap.read()
        if not ret:
            self.logger.warning("reached end of video.")
            self.close_video()
            self.open_video()
            _, frame = self.video_cap.read()
        return frame


    def process_none_message(self, message):
        time_interval = 1.0 / self.frame_rate
        time.sleep(time_interval)
        video_frame = self.cap_frame()
        frame_info = self.prepare_frame_info(video_frame)

        # 使用传入消息的frame_id，如果没有则使用默认值
        current_frame_id = message.meta_info.frame_id if message and message.meta_info else 0
        meta_info = MetaInfo(current_frame_id)
        exec_task_message = self.prepare_execute_task_message(frame_info, meta_info)
        return exec_task_message