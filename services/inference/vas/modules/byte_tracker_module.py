import cv2
import time
import logging
import numpy as np
from vas.common.opencv_utils import save_detect_result
from vas.entrypoints.task_interface import *
from vas.modules.base_module import *
from vas.bytetracker.byte_tracker import BYTETracker


class ByteTrackerModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        self.track_thresh = self.config[self.module_type]["track_thresh"]
        self.track_buffer = self.config[self.module_type]["track_buffer"]
        self.match_thresh = self.config[self.module_type]["match_thresh"]
        self.frame_rate = self.config[self.module_type]["frame_rate"]
        self.tracker = BYTETracker(track_thresh=self.track_thresh, track_buffer=self.track_buffer,
            match_thresh=self.match_thresh, frame_rate=self.frame_rate)

        # 添加调试信息：显示当前全局trackId计数器
        from vas.bytetracker.basetrack import BaseTrack
        self.logger.info(f"🔧 [TRACK_INIT] ByteTracker初始化完成，当前全局trackId计数器: {BaseTrack._count}")


    def process_execute_task_message(self, message: MessageInfo):
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"

        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.debug(f"[TRACK] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
            return message

        self.logger.debug(f"[TRACK] Frame {frame_id}: 🎯 开始目标跟踪")

        frame, bboxes, _ = self.extract_frame_info(message)
        bbox_count = len(bboxes) if bboxes is not None else 0
        self.logger.debug(f"[TRACK] Frame {frame_id}: 提取帧信息完成，frame shape: {frame.shape}, 检测框数量: {bbox_count}")

        if bbox_count > 0:
            self.logger.debug(f"[TRACK] Frame {frame_id}: 开始更新跟踪器")
            # 传递DSL的原始frame_id给tracker
            track_objs = self.tracker.update(bboxes, frame_id=frame_id)
            track_count = len(track_objs) if track_objs is not None else 0

            # 只在有跟踪结果时输出info，否则debug
            if track_count > 0:
                self.logger.info(f"[TRACK] Frame {frame_id}: ✅ 跟踪完成，输入检测框: {bbox_count}, 输出跟踪ID: {track_count}")
                # 打印跟踪结果详情
                if track_objs is not None and len(track_objs) > 0:
                    track_id_list = []
                    for i, track_result in enumerate(track_objs):
                        if len(track_result) > 6:
                            actual_track_id = track_result[6]  # 正确的trackId位置
                            track_id_list.append(str(actual_track_id))
                            self.logger.debug(f"[TRACK] Frame {frame_id}: 跟踪目标{i+1} - ID: {actual_track_id}")

                    # 输出所有trackId的摘要
                    self.logger.info(f"🎯 [TRACK] Frame {frame_id}: 活跃trackIds: [{', '.join(track_id_list)}]")
            else:
                self.logger.debug(f"[TRACK] Frame {frame_id}: 跟踪完成，输入检测框: {bbox_count}, 无跟踪输出")

            self.update_frame_info(message, track_objs=track_objs)
        else:
            self.logger.debug(f"[TRACK] Frame {frame_id}: 无检测框输入，跳过跟踪更新")

        # Track模块默认往下传，不修改skip_processing标记
        self.logger.debug(f"[TRACK] Frame {frame_id}: 🔄 跟踪模块默认传递消息，skip_processing保持: {message.skip_processing}")
        return message


    # for debug
    def process_none_message(self, message: MessageInfo):
        # 使用传入消息的frame_id，如果没有则使用默认值
        current_frame_id = message.meta_info.frame_id if message and message.meta_info else 0

        init_x = 100
        init_y = 100
        init_w = 50
        init_h = 50
        x1 = init_x + current_frame_id * 10
        y1 = init_x + current_frame_id * 10
        w = init_w
        h = init_h
        bboxes = np.array([x1, y1, w, h, 1.0, 0]).reshape((1, 6))

        # 保持原有的frame_id，不自增
        meta_info = MetaInfo(current_frame_id)
        frame_info = self.prepare_frame_info(bboxes=bboxes)
        exec_task_message = self.prepare_execute_task_message(frame_info=frame_info, meta_info=meta_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message
