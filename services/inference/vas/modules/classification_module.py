import cv2
import json
import numpy as np
from vas.entrypoints.task_interface import *
from vas.modules.base_module import *
from vas.modules.cnclip.model import CnCLIPModel


class ClassificationModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        self.preprocessor = self.config[self.module_type]["preprocessor"]
        self.text_model = self.config[self.module_type]["text_model"]
        self.visual_model = self.config[self.module_type]["visual_model"]

        # preprocessor
        self.model = CnCLIPModel(self.preprocessor["vocab_file"])

        # check text model ready
        model_name = self.text_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")

        # check visual model ready
        model_name = self.visual_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")

        # tiny model ready
        self.tiny_model = self.config[self.module_type].get("tiny_model", None)

        # trackId 告警时间间隔控制和避免重复告警
        self.track_last_alert_frame = {}  # 记录每个trackId上次告警的frame_id

        # trackId 训练时间间隔控制和避免重复训练数据收集
        self.track_last_collect_frame = {}  # 记录每个trackId上次训练数据收集上报的frame_id

        self.classification_interval_frames = 25  # 默认25帧间隔（1秒@25fps），可配置
        self.frame_rate = 25  # 默认帧率
        self.decode_step = 1  # 默认跳帧步长


    def should_track_alert(self, track_id: int, current_frame_id: int) -> bool:
        """
        检查是否应该对指定trackId进行告警
        Args:
            track_id: 跟踪ID
            current_frame_id: 当前帧ID
        Returns:
            bool: True表示应该分类，False表示跳过
        """
        # 如果是第一次遇到这个trackId，直接告警
        if track_id not in self.track_last_alert_frame:
            return True

        # 检查时间间隔
        last_frame = self.track_last_alert_frame[track_id]
        frame_diff = current_frame_id - last_frame

        # 考虑frame_id可能跳跃的情况，使用绝对差值
        if abs(frame_diff) >= self.classification_interval_frames:
            self.track_last_alert_frame.pop(track_id, None)
            return True
        return False


    def update_track_alert_time(self, track_id: int, frame_id: int):
        """更新trackId的上次告警时间"""
        self.track_last_alert_frame[track_id] = frame_id


    def should_track_collect(self, track_id: int, current_frame_id: int) -> bool:
        """
        检查是否应该对指定trackId进行收集训练数据
        Args:
            track_id: 跟踪ID
            current_frame_id: 当前帧ID
        Returns:
            bool: True表示应该分类，False表示跳过
        """
        # 如果是第一次遇到这个trackId，直接告警
        if track_id not in self.track_last_collect_frame:
            return True

        # 检查时间间隔
        last_frame = self.track_last_collect_frame[track_id]
        frame_diff = current_frame_id - last_frame

        # 考虑frame_id可能跳跃的情况，使用绝对差值
        if abs(frame_diff) >= self.classification_interval_frames:
            self.track_last_collect_frame.pop(track_id, None)
            return True
        return False


    def update_track_collect_time(self, track_id: int, frame_id: int):
        """更新trackId的上次告警时间"""
        self.track_last_collect_frame[track_id] = frame_id


    def update_classification_interval(self, task_info):
        """根据任务信息更新分类间隔配置"""
        if task_info and hasattr(task_info, 'device') and hasattr(task_info.device, 'streamConfig'):
            frame_rate = getattr(task_info.device.streamConfig, 'frameRate', 25)
            decode_step = getattr(task_info.device.streamConfig.decoderConf, 'decodeStep', 1)

            # 更新配置
            self.frame_rate = frame_rate
            self.decode_step = decode_step
            # 考虑跳帧的情况，实际间隔 = 帧率 * 跳帧步长
            self.classification_interval_frames = frame_rate * decode_step

            self.logger.info(f"[CLASSIFICATION] 更新分类间隔: {self.classification_interval_frames} 帧 (帧率:{frame_rate}, 跳帧:{decode_step})")
        else:
            self.logger.debug(f"[CLASSIFICATION] 无法获取帧率配置，保持当前间隔: {self.classification_interval_frames} 帧")


    def image_clip(self, img: np.ndarray, x: int, y: int) -> tuple:
        if x < 0:
            x = 0
        if x >= img.shape[1]:
            x = img.shape[1]
        if y < 0:
            y = 0
        if y >= img.shape[0]:
            y = img.shape[0]
        return (x, y)


    def image_feature_extract(self,
                              image: np.ndarray,
                              rect: list | None = None,
                              need_norm: bool = True) -> np.ndarray:

        # crop
        if rect is None:
            rect = [0, 0, image.shape[1], image.shape[0]]

        x1, y1 = self.image_clip(image, rect[0], rect[1])
        x2, y2 = self.image_clip(image, rect[0] + rect[2], rect[1] + rect[3])

        # 检查裁剪区域是否有效
        if x1 >= x2 or y1 >= y2:
            # 如果裁剪区域无效，使用整个图像
            crop_img = image
        else:
            crop_img = image[y1:y2, x1:x2, ...]

        # 检查裁剪后的图像是否有效
        if crop_img.size == 0 or crop_img.shape[0] == 0 or crop_img.shape[1] == 0:
            self.logger.warning(f"裁剪后的图像尺寸无效: {crop_img.shape}, 使用整个图像")
            crop_img = image

        # 再次检查图像是否有效
        if crop_img.size == 0 or crop_img.shape[0] == 0 or crop_img.shape[1] == 0:
            self.logger.error(f"输入图像尺寸无效: {crop_img.shape}, 无法进行特征提取")
            # 返回一个默认的特征向量
            return np.zeros((512,), dtype=np.float32)

        preprocess_image = self.model.image_preprocess(crop_img)
        model_name = self.visual_model["model_name"]
        model_inputs = {
            "image": preprocess_image
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        embed = model_outputs["unnorm_image_features"]

        if need_norm:
            embed = embed / np.linalg.norm(embed)
        return embed


    def text_feature_extract(self, text: str, need_norm: bool = True) -> np.ndarray:
        preprocess_text = self.model.text_preprocess(text)
        preprocess_text = preprocess_text.astype(np.int64)
        model_name = self.text_model["model_name"]
        model_inputs = {
            "text": preprocess_text
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        embed = model_outputs["unnorm_text_features"]
        if need_norm:
            embed = embed / np.linalg.norm(embed)
        return embed


    def get_texts_features(self, texts: list[str]) -> list[np.ndarray]:
        text_features = []
        for text in texts:
            text_feature = self.text_feature_extract(text)
            text_features.append(text_feature)
        return text_features


    def get_images_freatures(self, images: list[np.ndarray]) -> list[np.ndarray]:
        image_features = []
        for image in images:
            image_feature = self.image_feature_extract(image)
            image_features.append(image_feature)
        return image_features


    def classify(self, image: np.ndarray, bbox: np.ndarray):
        rect = bbox[0:4].astype(np.int32).tolist()
        image_feature = self.image_feature_extract(image, rect)
        model_name = self.tiny_model["model_name"]
        model_inputs = {
            "input": image_feature
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        classify_res = model_outputs["output"][0]
        return classify_res[0], classify_res[1]


    def process_classification_algo(self, algo_config: Algorithm, message: MessageInfo):
        meta_info = message.meta_info
        frame, bboxes, track_objs = self.extract_frame_info(message=message)
        # 只在debug模式下输出详细信息
        self.logger.debug(f"receive frame {meta_info.frame_id}, frame: {frame.shape}, " \
            f"bboxes: {bboxes.shape}, track_objs: {track_objs.shape}")

        for bbox in bboxes:
            true_conf, false_conf = self.classify(frame, bbox)


    def zeroshot_classify(self, text: str, image: np.ndarray, bbox):
        text_feature = self.text_feature_extract(text)
        # 处理bbox参数，支持list和numpy array两种类型
        if isinstance(bbox, list):
            rect = [int(x) for x in bbox[0:4]]
        else:
            rect = bbox[0:4].astype(np.int32).tolist()
        image_feature = self.image_feature_extract(image, rect)
        cos_sim = np.dot(text_feature, image_feature.T) / (np.linalg.norm(text_feature) * np.linalg.norm(image_feature))
        return float(cos_sim[0][0])


    def check_satisfy_alert(self, algo_config, frame_id, track_objs, alert_scores, positive_labels, negative_labels):

        alert_config = algo_config.alertConfig
        alert_res = []
        for track_idx, track_obj in enumerate(track_objs):
            # 获取当前检测框对应的trackId
            # track_ids的结构是 [x, y, w, h, score, cls, track_id]，track_id在索引6
            track_result = track_objs[track_idx]
            track_id = track_result[6]  # 提取track_id（第7个元素，索引6）

            # 将numpy类型转换为Python基本类型，确保可以用作字典键
            if isinstance(track_id, np.ndarray):
                if track_id.size == 1:
                    track_id = track_id.item()  # 单元素数组转换为标量
                else:
                    track_id = int(track_id.flatten()[0])  # 多元素数组取第一个元素
            elif hasattr(track_id, 'item') and hasattr(track_id, 'size'):
                # numpy标量类型
                track_id = track_id.item()
            else:
                # 其他类型，直接转换为int
                track_id = int(track_id)

            if not self.should_track_alert(track_id, frame_id):
                # 检查具体的跳过原因
                last_frame = self.track_last_alert_frame.get(track_id, -1)
                frame_diff = abs(frame_id - last_frame) if last_frame != -1 else 0
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} 间隔不足 (当前:{frame_id}, 上次:{last_frame}, 差值:{frame_diff}, 需要:{self.classification_interval_frames})")
                continue

            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 开始对trackId {track_id} 进行告警分类")

            alert_flag = False
            bbox_coords = track_obj[0:4].astype(np.int32).tolist()
            classifications = []

            # 告警文本标签和图像特征的相似度
            # scores shape is [1, label_num]
            scores = alert_scores[track_idx,:]

            # 正例分类
            label_index = 0
            max_pos_conf = -2.0  # 余弦相似度范围是[-1,1]，初始化为-2确保能被正确更新
            pos_scores = []
            for label in positive_labels:
                pos_conf = scores[label_index]
                class_res = ClassificationDetectionAttr(pos_conf, "positive", label, obj_id=track_id)
                max_pos_conf = pos_conf if pos_conf > max_pos_conf else max_pos_conf
                classifications.append(class_res)
                pos_scores.append(f"{label}:{pos_conf:.3f}")
                label_index += 1

            # 负例分类
            max_neg_conf = -2.0  # 余弦相似度范围是[-1,1]，初始化为-2确保能被正确更新
            neg_scores = []
            for label in negative_labels:
                neg_conf = scores[label_index]
                class_res = ClassificationDetectionAttr(neg_conf, "negative", label, obj_id=track_id)  # 修复bug: 使用neg_conf而不是pos_conf
                max_neg_conf = neg_conf if neg_conf > max_neg_conf else max_neg_conf
                classifications.append(class_res)
                neg_scores.append(f"{label}:{neg_conf:.3f}")
                label_index += 1

            # 判断是否需要告警结果
            if max_pos_conf > max_neg_conf and max_pos_conf > alert_config.confidence:
                alert_flag = True
                
            # 只在有告警时输出详细信息，否则只输出debug级别
            if alert_flag:
                self.logger.info(f"🔥 [CLASSIFICATION] Frame {frame_id}: TrackID {track_id} 触发告警 | 正例:{max_pos_conf:.3f} 负例:{max_neg_conf:.3f}")
                # 更新该trackId的告警时间
                self.update_track_alert_time(track_id, frame_id)
            else:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} - 最高正例:{max_pos_conf:.3f} 最高负例:{max_neg_conf:.3f} 无告警")
                continue

            bbox_classify = DetectionClassificationAlert(alert=alert_flag,
                                                        bbox=bbox_coords,
                                                        classifications=classifications)
            alert_res.append(bbox_classify)

        alert_count = len(alert_res)
        # 只在有告警或有检测框时输出信息
        if alert_count > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 分类完成，总计告警{alert_count}框")

        return alert_res


    def check_satisfy_train_collect(self, algo_config, frame_id, track_objs, train_scores, train_labels):

        train_config = algo_config.trainingConfig
        collect_config = train_config.dataCollection
        train_collect_res = []
        for track_idx, track_obj in enumerate(track_objs):
            # 获取当前检测框对应的trackId
            # track_ids的结构是 [x, y, w, h, score, cls, track_id]，track_id在索引6
            track_result = track_objs[track_idx]
            track_id = track_result[6]  # 提取track_id（第7个元素，索引6）

            # 将numpy类型转换为Python基本类型，确保可以用作字典键
            if isinstance(track_id, np.ndarray):
                if track_id.size == 1:
                    track_id = track_id.item()  # 单元素数组转换为标量
                else:
                    track_id = int(track_id.flatten()[0])  # 多元素数组取第一个元素
            elif hasattr(track_id, 'item') and hasattr(track_id, 'size'):
                # numpy标量类型
                track_id = track_id.item()
            else:
                # 其他类型，直接转换为int
                track_id = int(track_id)

            if not self.should_track_collect(track_id, frame_id):
                # 检查具体的跳过原因
                last_frame = self.track_last_alert_frame.get(track_id, -1)
                frame_diff = abs(frame_id - last_frame) if last_frame != -1 else 0
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} 间隔不足 (当前:{frame_id}, 上次:{last_frame}, 差值:{frame_diff}, 需要:{self.classification_interval_frames})")
                continue

            collect_flag = False
            bbox_coords = track_obj[0:4].astype(np.int32).tolist()
            classifications = []

            # 训练收集文本标签和图像特征的相似度
            # scores shape is [1, label_num]
            scores = train_scores[track_idx,:]

            # 正例分类
            label_index = 0
            max_conf = -2.0  # 余弦相似度范围是[-1,1]，初始化为-2确保能被正确更新
            for label in train_labels:
                obj_conf = scores[label_index]
                class_res = ClassificationDetectionAttr(obj_conf, "positive", label, obj_id=track_id)
                max_conf = obj_conf if obj_conf > max_conf else max_conf
                classifications.append(class_res)
                label_index += 1

            # 判断是否需要收集训练数据
            collect_min_conf = collect_config.thresholds.minConfidence
            collect_max_conf = collect_config.thresholds.maxConfidence
            if collect_min_conf < max_conf and max_conf < collect_max_conf:
                collect_flag = True

            # 只在有收集训练数据时输出详细信息，否则只输出debug级别
            if collect_flag:
                self.logger.info(f"🔥 [CLASSIFICATION] Frame {frame_id}: trackId {track_id} 触发收集训练数据，最大得分:{max_conf:.3f}")
                # 更新该trackId的告警时间
                self.update_track_collect_time(track_id, frame_id)
            else:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} conf:{max_conf} 无训练数据收集")
                continue

            bbox_classify = DetectionClassificationAlert(alert=collect_flag,
                                                        bbox=bbox_coords,
                                                        classifications=classifications)
            train_collect_res.append(bbox_classify)

        # 只在有需要收集目标框时输出信息
        if len(train_collect_res) > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 训练收集完成，总计{len(train_collect_res)}框")

        return train_collect_res


    def process_zeroshot_classification_algo(self, algo_config: Algorithm,
                                            message: MessageInfo) -> list[DetectionClassificationAlert]:
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"
        frame, bboxes, track_objs = self.extract_frame_info(message=message)
        train_config = algo_config.trainingConfig
        alert_config = algo_config.alertConfig
        alert_res = []
        train_collect_res = []

        # train collect 标签
        train_labels = train_config.labels if train_config is not None else []

        # alert 标签兼容性处理：如果positiveLabels和negativeLabels为空，尝试从labels推导
        positive_labels = alert_config.positiveLabels if alert_config.positiveLabels else []
        negative_labels = alert_config.negativeLabels if alert_config.negativeLabels else []

        # 如果正负例标签都为空，从labels字段推导（兼容旧版本）
        if not positive_labels and not negative_labels and alert_config.labels:
            # 默认将labels作为负例标签（表示要检测的异常情况）
            negative_labels = alert_config.labels
            # 可以根据业务需求设置默认的正例标签
            positive_labels = ["person", "normal"]  # 默认正例标签
            self.logger.warning(f"[CLASSIFICATION] Frame {frame_id}: ⚠️ 使用兼容模式，从labels推导正负例标签")

        # 只在有检测框时输出分类开始信息
        if len(bboxes) > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 开始分类，检测框:{len(bboxes)}, "
                              f"track_objs shape:{track_objs.shape if track_objs is not None else 'None'}, "
                              f"正例:{positive_labels}, 负例:{negative_labels}, 阈值:{alert_config.confidence}")
            # 调试track objs的内容
            if track_objs is not None and len(track_objs) > 0:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: track_ids内容: {track_objs[:min(5, len(track_objs))]}")  # 只显示前5个
            else:
                # 当无跟踪信息时，使用bboxes初始化track objs，方便后续的处理
                bbox_num = bboxes.shape[0]
                id_col = np.arange(bbox_num).reshape(bbox_num, 1)
                track_objs = np.concatenate([bboxes, id_col], axis=1)
        else:
            # 无检测框时不进行分类
            self.logger.error(f"[CLASSIFICATION] Frame {frame_id}: ❌ 检测框为空，无法进行分类")
            return alert_res, train_collect_res

        # 验证标签配置
        if not positive_labels and not negative_labels:
            self.logger.error(f"[CLASSIFICATION] Frame {frame_id}: ❌ 正例和负例标签都为空，无法进行分类")
            return alert_res, train_collect_res

        # 获取所有告警文本标签的特征
        alert_labels = positive_labels + negative_labels
        alert_text_features = self.get_texts_features(texts=alert_labels)
        alert_text_features = np.concatenate(alert_text_features, axis=0) if len(alert_text_features) else np.array([])

        # 获取所有训练文本标签特征
        train_text_features = self.get_texts_features(texts=train_labels)
        train_text_features = np.concatenate(train_text_features, axis=0) if len(train_text_features) else np.array([])

        # 获取所有目标图像特征
        obj_img_features = []
        for track_idx, track_obj in enumerate(track_objs):
            # track_ids的结构是 [x, y, w, h, score, cls, track_id]，track_id在索引6
            bbox_coords = track_obj[0:4].astype(np.int32).tolist()

            # 获取当前框的图像特征
            image_feature = self.image_feature_extract(frame, bbox_coords)
            obj_img_features.append(image_feature)

        obj_img_features = np.concatenate(obj_img_features, axis=0) if len(obj_img_features) else np.array([])

        # 计算告警文本和图像特征的相似度
        # alert scores shape is [obj_num, label_num]
        alert_scores = np.array([])
        if alert_text_features.size > 0 and obj_img_features.size > 0:
            alert_scores = np.dot(obj_img_features, alert_text_features.T)

        # 计算训练文本和图像特征的相似度
        # train collect scores shape is [obj_num, label_num]
        train_scores = np.array([])
        if train_text_features.size > 0 and obj_img_features.size > 0:
            train_scores = np.dot(obj_img_features, alert_text_features.T)

        # 判断是否产生告警事件
        alert_res = self.check_satisfy_alert(algo_config=algo_config, frame_id=frame_id, track_objs=track_objs, 
            alert_scores=alert_scores, positive_labels=positive_labels, negative_labels=negative_labels)

        # 判断是否产生训练收集事件
        train_collect_res = self.check_satisfy_train_collect(algo_config=algo_config, frame_id=frame_id, track_objs=track_objs, 
            train_scores=train_scores, train_labels=train_labels)

        return alert_res, train_collect_res


    def process_execute_task_message(self, message: MessageInfo):
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"
        frame, _, _ = self.extract_frame_info(message)

        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
            return message

        self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 🏷️ 开始分类处理")

        task_info = message.task_info
        alert_res = []
        collect_res = []
        has_alert = False  # 标记是否有预警条件满足
        has_collect = False

        # 分类间隔配置只在首次处理时更新
        if task_info is not None and not hasattr(self, '_interval_updated'):
            self.update_classification_interval(task_info)
            self._interval_updated = True

        if task_info is not None:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 任务信息存在，开始处理算法链")

            for algo_config in task_info.algorithmOrchestration.algorithmChain:
                if algo_config.algorithmType != AlgorithmType.CLASSIFICATION:
                    continue

                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 处理分类算法配置: {algo_config.algorithmId}")

                # self.process_classification_algo(algo_config, message)
                alert_res, collect_res = self.process_zeroshot_classification_algo(algo_config, message)
                has_alert = True if len(alert_res) > 0 else False
                has_collect = True if len(collect_res) > 0 else False
        else:
            self.logger.warning(f"[CLASSIFICATION] Frame {frame_id}: ⚠️ 消息不包含任务信息")

        # Classification模块逻辑：如果满足预警条件往下传，否则标记跳过
        alert_count = len(alert_res)
        if has_alert and alert_count > 0:
            self.logger.debug(f"✅ [CLASSIFICATION] Frame {frame_id}: 检测到告警条件 ({alert_count}个目标满足条件)")
            message.skip_processing = False
            if self.next_module is None:
                message.alert_info = alert_res
        else:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: ❌ 未检测到告警条件 (0个目标满足条件)，标记跳过后续处理")
            message.skip_processing = True

        # upload train collect event if exists
        if has_collect:
            collect_count = len(collect_res)
            self.logger.info(f"✅ [CLASSIFICATION] Frame {frame_id}: 检测到训练收集条件 ({collect_count}个目标满足条件)")
            collect_event = self.generate_alert_event(frame=frame, frame_meta=message.meta_info, algo_config=algo_config, 
                task_info=task_info, alert_res=collect_res, topic="train_collect_event", final_reuslt=False)
            if collect_event is not None:
                self.logger.info(f"[{self.name}] 📦 [COLLECT] Frame {frame_id}: 训练数据数据回流事件成功，事件ID: {collect_event.atomicEventInstanceId}")
            else:
                self.logger.warning(f"[{self.name}] Frame {frame_id}: ❌ 训练数据数据回流事件生成失败")
        else:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: ❌ 未检测到训练收集条件")

        return message


    def process_none_message(self, message: MessageInfo):
        task_json_file = "/workspace/video_analysis_server/data/config/yolo_tracking_clip_task.json"
        with open(task_json_file, 'r', encoding='utf-8') as file:
            task_json = json.load(file)
        task_info = SimplifiedAtomicTask.parse_obj(task_json)
        test_file = "/workspace/video_analysis_server/data/images/cat_224_224.jpg"
        video_frame = cv2.imread(test_file)
        bboxes = np.array([75.25, 9.45, 90.825, 211.75, 0.80078125, 16.0]).reshape(1, 6)
        track_objs = np.array([75.25, 9.45, 90.825, 211.75, 0.80078125, 16.0, 1]).reshape((1, 7))
        frame_info = self.prepare_frame_info(video_frame, bboxes, track_objs)
        exec_task_message = self.prepare_execute_task_message(frame_info=frame_info, task_info=task_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message