import numpy as np
import collections
from typing import List, Tuple, Dict, Any
from vas.entrypoints.task_interface import *
from vas.modules.base_module import BaseModule, MessageInfo, MetaInfo, FrameInfo

Point = Tuple[float, float]

class TripwireDetector:
    """
    无 complex 版多目标拌线检测
    """
    def __init__(self,
                 line: List[Point],
                 mode: str = 'both',
                 max_missed: int = 30,
                 max_history: int = 100):
        assert line and len(line) == 2, "line 必须为 2 个点"
        self.line = line  # ((x1, y1), (x2, y2))
        self.mode = mode.lower()
        if self.mode not in {'cw', 'ccw', 'both'}:
            raise ValueError("mode must be 'cw', 'ccw' or 'both'")
        self.max_missed = max_missed
        self.max_history = max_history
        self._targets: Dict[int, collections.deque] = collections.defaultdict(
            lambda: collections.deque(maxlen=self.max_history)
        )

    # ----------------------------------------------------------
    # 公共接口：一次处理多个目标
    # ----------------------------------------------------------
    def update(self,
               frame_id: int,
               targets: list[Tuple[int, float, float]]
               ) -> List[Tuple[int, str]]:
        touched = set()
        for tid, x, y in targets:
            touched.add(tid)
            self._record(frame_id, tid, (x, y))

        self._garbage_collect(frame_id, touched)

        triggered = []
        for tid in touched:
            direction = self._check_trip(tid)
            if direction and (self.mode == 'both' or self.mode == direction):
                triggered.append((tid, direction))
        return triggered

    # ----------------------------------------------------------
    # 内部
    # ----------------------------------------------------------
    def _record(self, frame_id: int, tid: int, pt: Point):
        dq = self._targets[tid]
        if dq and dq[-1][0] >= frame_id:
            dq[-1] = (frame_id, pt)
        else:
            dq.append((frame_id, pt))

    def _check_trip(self, tid: int) -> str | None:
        dq = self._targets[tid]
        if len(dq) < 2:
            return None
        (f0, p0), (f1, p1) = dq[-2], dq[-1]
        if f0 == f1:
            return None

        # 计算点到有向线段的有向距离符号
        (x1, y1), (x2, y2) = self.line
        ax, ay = x2 - x1, y2 - y1
        bx0, by0 = p0[0] - x1, p0[1] - y1
        bx1, by1 = p1[0] - x1, p1[1] - y1

        # 叉积二维公式：cross = ax * by - ay * bx
        cross0 = ax * by0 - ay * bx0
        cross1 = ax * by1 - ay * bx1

        if cross0 * cross1 > 0:  # 同侧
            return None
        # 跨线：由符号变化方向判断
        if cross0 > 0 and cross1 <= 0:
            return 'cw'
        if cross0 < 0 and cross1 >= 0:
            return 'ccw'
        return None

    def _garbage_collect(self, frame_id: int, touched: set[int]):
        to_del = [
            tid for tid, dq in self._targets.items()
            if tid not in touched and (frame_id - dq[-1][0] > self.max_missed)
        ]
        for tid in to_del:
            del self._targets[tid]


class TripwireIntrusionModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        # 默认拌线检测方向
        self.direction = self.config[self.module_type].get("direction", "both")
        # 默认拌线检测目标最大生命周期
        self.max_age = self.config[self.module_type].get("max_age", 5)

        # 任务状态
        self.task_state = {}

        # 默认拌线入侵检测器,调试使用
        default_tripwire = [(0, 0), (200, 200)]
        self.task_state["default"] = TripwireDetector(default_tripwire, direction=self.direction, 
            max_age=self.max_age)
        self.frame_id = 0


    def process_default_tripwire_intrusion(self, message: MessageInfo):
        _, bboxes, track_objs = self.extract_frame_info(message)
        meta_info = message.meta_info
        frame_id = meta_info.frame_id
        bbox_list = []
        track_id_list = []
        for track_obj in track_objs:
            bbox = track_obj[0:4]
            track_id = track_obj[6]
            bbox_list.append((bbox[0], bbox[1], bbox[0] + bbox[2], bbox[1] + bbox[3]))
            track_id_list.append(track_id)
        alarm_list = self.task_state["default"].check_intrusion(bbox_list, track_id_list, frame_id)
        return alarm_list


    def process_custom_tripwire_intrusion(self, message: MessageInfo):
        frame, bboxes, track_objs = self.extract_frame_info(message)
        self.log_error("current not support custom tripwire")


    def process_execute_task_message(self, message: MessageInfo):
        if message.task_info is None:
            alarm_res = self.process_default_tripwire_intrusion(message)
        else:
            alarm_res = self.process_custom_tripwire_intrusion(message)
        return message


    def process_none_message(self, message: MessageInfo):
        # 使用传入消息的frame_id，如果没有则使用默认值
        current_frame_id = message.meta_info.frame_id if message and message.meta_info else 0

        init_x = 100
        init_y = 100
        init_w = 50
        init_h = 50
        x1 = init_x + current_frame_id * 10
        y1 = init_x + current_frame_id * 10
        w = init_w
        h = init_h
        bboxes = np.array([x1, y1, w, h, 1.0, 0]).reshape((1, 6))
        track_objs = np.array([x1, y1, w, h, 1.0, 0, 1]).reshape((1, 7))

        # 保持原有的frame_id，不自增
        meta_info = MetaInfo(current_frame_id)
        frame_info = self.prepare_frame_info(bboxes=bboxes, track_objs=track_objs)
        exec_task_message = self.prepare_execute_task_message(frame_info=frame_info, meta_info=meta_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message