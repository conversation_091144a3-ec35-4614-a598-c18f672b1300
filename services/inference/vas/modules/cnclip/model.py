from typing import Union, List
import torch
import cv2
import numpy as np
from vas.modules.cnclip.tokenizer import FullTokenizer


def resize_and_pad_image(img, target_width, target_height):
    """
    Resize an image to maintain the aspect ratio and then pad it to the target size.
    Padding is added to the right and bottom to align the image at the top-left.

    Args:
    img (np.array): Original image.
    target_width (int): Target width for the new image.
    target_height (int): Target height for the new image.

    Returns:
    np.array: Resized and padded image.
    """

    # 获取原始图像的尺寸和长宽比
    h, w = img.shape[:2]

    # 检查图像尺寸是否有效
    if h == 0 or w == 0:
        # 如果图像尺寸无效，返回一个黑色的目标尺寸图像
        return np.zeros((target_height, target_width, img.shape[2] if len(img.shape) > 2 else 1), dtype=img.dtype)

    original_ratio = w / h
    target_ratio = target_width / target_height

    # 判断是按宽度还是高度来保持长宽比
    if target_ratio > original_ratio:
        # 目标更宽，按高度缩放
        new_width = int(original_ratio * target_height)
        new_height = target_height
    else:
        # 目标更高或相同，按宽度缩放
        new_width = target_width
        new_height = int(target_width / original_ratio)

    # 确保计算出的尺寸有效
    if new_width <= 0 or new_height <= 0:
        # 如果计算出的尺寸无效，返回一个黑色的目标尺寸图像
        return np.zeros((target_height, target_width, img.shape[2] if len(img.shape) > 2 else 1), dtype=img.dtype)

    # 使用cv2.resize()进行图像缩放，使用CUBIC插值
    resized_img = cv2.resize(img, (new_width, new_height), interpolation=cv2.INTER_CUBIC)

    # 创建一个与目标尺寸相同的黑色画布
    canvas = np.zeros((target_height, target_width, 3), dtype=np.uint8)

    # 计算右下角的起始点坐标
    x_offset = target_width - new_width
    y_offset = target_height - new_height

    # 将resize后的图像复制到画布的右下角
    canvas[y_offset:target_height, x_offset:target_width] = resized_img

    return canvas

# 示例用法
# 假设你已经有一个名为original_img的numpy数组图像
# processed_img = resize_and_pad_image(original_img, target_width=500, target_height=400)

class CnCLIPModel(object):
    def __init__(self, vocab_file: str):
        self.tokenizer = FullTokenizer(vocab_file)

    def tokenize(self, texts: Union[str, List[str]], context_length: int = 52) -> np.ndarray:
        """
        Returns the tokenized representation of given input string(s)
        Parameters
        ----------
        texts : Union[str, List[str]]
            An input string or a list of input strings to tokenize
        context_length : int
            The context length to use; all baseline models use 52 as the context length
        Returns
        -------
        A two-dimensional tensor containing the resulting tokens, shape = [number of input strings, context_length]
        """
        if isinstance(texts, str):
            texts = [texts]

        all_tokens = []
        for text in texts:
            all_tokens.append([self.tokenizer.vocab['[CLS]']] +
                              self.tokenizer.convert_tokens_to_ids(self.tokenizer.tokenize(text))[:context_length - 2] +
                              [self.tokenizer.vocab['[SEP]']])

        result = torch.zeros(len(all_tokens), context_length, dtype=torch.long)

        for i, tokens in enumerate(all_tokens):
            assert len(tokens) <= context_length
            result[i, :len(tokens)] = torch.tensor(tokens)

        return result.numpy()

    def image_preprocess(self, img: np.ndarray) -> np.ndarray:
        # cnclip 的实现
        """
        normalize = Normalize((0.48145466, 0.4578275, 0.40821073), (0.26862954, 0.26130258, 0.27577711))
        return Compose([
                Resize((resolution, resolution), interpolation=InterpolationMode.BICUBIC),
                _convert_to_rgb,
                ToTensor(),
                normalize,
            ])
        """
        # 下面的实现不保证和 cnclip 严对齐
        img = resize_and_pad_image(img, 224, 224)
        # img = img[..., ::-1]  # bgr->rgb
        # img = img / 255
        # img = (img - np.array((0.48145466, 0.4578275, 0.40821073))) / np.array((0.26862954, 0.26130258, 0.27577711))
        img = img.transpose((2, 0, 1))  # 224,224,3 -> 3,224,224
        img = np.expand_dims(img, 0)  # 1, 3, 224,224
        img = img.astype(np.float32)
        return img

    def text_preprocess(self, text: str) -> np.ndarray:
        return self.tokenize(text)

