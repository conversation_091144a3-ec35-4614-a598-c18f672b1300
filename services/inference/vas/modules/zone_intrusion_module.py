import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
from vas.entrypoints.task_interface import *
from vas.modules.base_module import BaseModule, MessageInfo, MetaInfo, FrameInfo


class IntrusionDetector:
    """
    区域入侵检查器（时间 & 帧数 双模式，支持重复报警）。
    用法：
        checker = IntrusionDetector(polygon, alarm_thresh=3.0, fps=25)
        alarm_list = checker.check_intrusion(boxes, ids, frame_idx=cur_frame)
    """
    def __init__(self,
                 polygon: List[Tuple[int, int]],
                 alarm_thresh: float = None,          # 告警阈值（秒）
                 alarm_frames: int = None,            # 告警阈值（帧）
                 fps: float = None,                   # 仅在 alarm_thresh 非 None 时需要
                 re_alarm_thresh: float = None,       # 再次报警间隔（秒）
                 re_alarm_frames: int = None):        # 再次报警间隔（帧）
        assert polygon and len(polygon) >= 3, "polygon 至少 3 个点"
        assert (alarm_thresh is not None) or (alarm_frame is not None), "必须指定 alarm_thresh 或 alarm_frame"
        if alarm_thresh is not None:
            assert fps is not None, "使用秒模式必须提供 fps"
            self.alarm_frames = int(alarm_thresh * fps)
        else:
            self.alarm_frames = alarm_frames

        self.polygon = np.array(polygon, dtype=np.int32)

        # 再次报警间隔
        self.re_alarm_frames = int(re_alarm_thresh * fps) if re_alarm_thresh else re_alarm_frames
        if self.re_alarm_frames is None:
            self.re_alarm_frames = 0  # 关闭重复报警

        # 内部状态：{track_id: (first_enter_frame, last_alarm_frame)}
        self._state: Dict[int, Tuple[int, int]] = {}


    def check_intrusion(self,
                        boxes: List[Tuple[int, int, int, int]],
                        ids: List[int],
                        frame_idx: int) -> List[Tuple[int, str]]:
        """
        :param boxes: [(x1,y1,x2,y2), ...]
        :param ids:   [track_id, ...]
        :param frame_idx: 当前帧号（从 0 开始）
        :return: 触发报警的列表 [(track_id, info), ...]
        """
        alarms = []
        active_ids = set()

        for (x1, y1, x2, y2), tid in zip(boxes, ids):
            cx, cy = int((x1 + x2) / 2), int((y1 + y2) / 2)
            active_ids.add(tid)

            if cv2.pointPolygonTest(self.polygon, (cx, cy), False) > 0:
                # 在区域内
                if tid not in self._state:
                    # 第一次进入
                    self._state[tid] = (frame_idx, -1)

                first_f, last_f = self._state[tid]
                since_last = frame_idx - last_f if last_f >= 0 else float('inf')

                if (frame_idx - first_f >= self.alarm_frames) and \
                   (since_last >= self.re_alarm_frames):
                    alarms.append((tid, f"区域入侵 帧{frame_idx}"))
                    self._state[tid] = (first_f, frame_idx)  # 更新 last_alarm
            else:
                # 离开区域 → 重置
                self._state.pop(tid, None)

        # 清理已消失目标（防止内存泄漏）
        for tid in list(self._state.keys()):
            if tid not in active_ids:
                self._state.pop(tid, None)

        return alarms


class ZoneIntrusionModule(BaseModule):

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        # 默认入侵停留时间告警阈值
        self.alarm_thresh = self.config[self.module_type].get("alarm_thresh", None)
        # 默认入侵停留帧数告警阈值
        self.alarm_frames = self.config[self.module_type].get("alarm_frames", None)
        # 默认再次告警阈值（秒）
        self.re_alarm_thresh = self.config[self.module_type].get("re_alarm_thresh", None)
        # 默认再次告警阈值（帧）
        self.re_alarm_frames = self.config[self.module_type].get("re_alarm_frames", None)
        # 默认帧率
        self.fps = self.config[self.module_type].get("fps", 25)

        # 任务状态
        self.task_state = {}

        # 默认区域入侵检测器,调试使用
        default_polygon = [(0, 0), (200, 0), (200, 200), (0, 200)]
        self.task_state["default"] = IntrusionDetector(default_polygon, alarm_thresh=self.alarm_thresh, 
            alarm_frames=self.alarm_frames, fps=self.fps, re_alarm_thresh=self.re_alarm_thresh,
            re_alarm_frames=self.re_alarm_frames)


    def process_default_zone_intrusion(self, message: MessageInfo):
        _, bboxes, track_objs = self.extract_frame_info(message)
        meta_info = message.meta_info
        frame_id = meta_info.frame_id
        bbox_list = []
        track_id_list = []
        for track_obj in track_objs:
            bbox = track_obj[0:4]
            track_id = track_obj[6]
            bbox_list.append((bbox[0], bbox[1], bbox[0] + bbox[2], bbox[1] + bbox[3]))
            track_id_list.append(track_id)
        alarm_list = self.task_state["default"].check_intrusion(bbox_list, track_id_list, frame_id)
        return alarm_list


    def process_custom_zone_intrusion(self, message: MessageInfo):
        frame, bboxes, track_objs = self.extract_frame_info(message)
        self.logger.error("current not support custom task")


    def process_execute_task_message(self, message: MessageInfo):
        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.debug(f"区域入侵模块跳过处理，直接传递消息")
            return message

        if message.task_info is None:
            alarm_res = self.process_default_zone_intrusion(message)
        else:
            alarm_res = self.process_custom_zone_intrusion(message)

        # 区域入侵模块通常是最后一个节点，根据告警结果决定是否跳过
        # 这里假设alarm_res包含告警信息，如果有告警则不跳过
        if alarm_res:
            self.logger.info(f"区域入侵模块检测到告警，继续传递")
            message.skip_processing = False
        else:
            self.logger.info(f"区域入侵模块未检测到告警，标记跳过后续处理")
            message.skip_processing = True

        return message


    def process_none_message(self, message: MessageInfo):
        # 使用传入消息的frame_id，如果没有则使用默认值
        current_frame_id = message.meta_info.frame_id if message and message.meta_info else 0

        bboxes = np.array([10, 10, 60, 60, 1.0, 0]).reshape((1, 6))
        track_objs = np.array([10, 10, 60, 60, 1.0, 0, 1]).reshape((1, 7))

        # 保持原有的frame_id，不自增
        meta_info = MetaInfo(current_frame_id)
        frame_info = self.prepare_frame_info(bboxes=bboxes, track_objs=track_objs)
        exec_task_message = self.prepare_execute_task_message(frame_info=frame_info, meta_info=meta_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message