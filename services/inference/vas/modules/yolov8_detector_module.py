import cv2
import time
import logging
import numpy as np
from vas.common.opencv_utils import save_detect_result
from vas.entrypoints.task_interface import *
from vas.modules.base_module import *


class Yolov8DetectorModule(BaseModule):

    # COCO数据集类别ID映射
    DETECTION_TYPE_CLASS_IDS = {
        "PERSON": [0],                    # 人员
        "VEHICLE": [2, 3, 5, 7],         # 车辆：汽车、摩托车、公交车、卡车
        "NON_MOTOR_VEHICLE": [1]         # 非机动车：自行车
    }

    def __init__(self, module_info, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_info, config, queue, logger, task_info)
        self.model_name = self.config[self.module_type]["model_name"]

        # 支持两种配置方式：detection_type 或 class_ids
        if "detection_type" in self.config[self.module_type]:
            detection_type = self.config[self.module_type]["detection_type"]
            self.class_ids = self.DETECTION_TYPE_CLASS_IDS.get(detection_type, [0])
            self.logger.info(f"使用检测类型配置: {detection_type}, 对应class_ids: {self.class_ids}")
        else:
            self.class_ids = self.config[self.module_type].get("class_ids", [0])
            self.logger.info(f"使用直接class_ids配置: {self.class_ids}")

        self.input_width = self.config[self.module_type]["input_width"]
        self.input_height = self.config[self.module_type]["input_height"]
        # self.model_inputs = ["images", ]
        # self.model_outputs = ["num_dets", "det_boxes", "det_scores", "det_classes"]
        model_ready = self.check_model_ready(self.model_name)
        self.logger.info(f"infer server model:{self.model_name} status is {model_ready}")


    def letterbox(self, img, new_shape: tuple = (640, 640)):
        """
        Resize and reshape images while maintaining aspect ratio by adding padding.

        Args:
            img (np.ndarray): Input image to be resized.
            new_shape (Tuple[int, int]): Target shape (height, width) for the image.

        Returns:
            img (np.ndarray): Resized and padded image.
            pad (Tuple[int, int]): Padding values (top, left) applied to the image.
        """
        shape = img.shape[:2]  # current shape [height, width]

        # Scale ratio (new / old)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])

        # Compute padding
        new_unpad = int(round(shape[1] * r)), int(round(shape[0] * r))
        dw, dh = (new_shape[1] - new_unpad[0]) / 2, (new_shape[0] - new_unpad[1]) / 2  # wh padding

        if shape[::-1] != new_unpad:  # resize
            img = cv2.resize(img, new_unpad, interpolation=cv2.INTER_LINEAR)
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        img = cv2.copyMakeBorder(img, top, bottom, left, right, cv2.BORDER_CONSTANT, value=(114, 114, 114))

        return img, (r, r, left, top)


    def preprocess(self, input_image):
        """
        Preprocess the input image before performing inference.

        This method reads the input image, converts its color space, applies letterboxing to maintain aspect ratio,
        normalizes pixel values, and prepares the image data for model input.

        Returns:
            image_data (np.ndarray): Preprocessed image data ready for inference with shape (1, 3, height, width).
            pad (Tuple[float, float, int, int]): Padding values (scale, scale, top, left) applied during letterboxing.
        """
        # Convert the image color space from BGR to RGB
        img = cv2.cvtColor(input_image, cv2.COLOR_BGR2RGB)

        img, resize_para = self.letterbox(img, (self.input_width, self.input_height))

        # Normalize the image data by dividing it by 255.0
        image_data = np.array(img).astype(np.float32) / 255.0

        # Transpose the image to have the channel dimension as the first dimension
        image_data = np.transpose(image_data, (2, 0, 1))  # Channel first

        # Expand the dimensions of the image data to match the expected input shape
        image_data = np.expand_dims(image_data, axis=0)

        # Return the preprocessed image data
        return image_data, resize_para


    def postprocess(self, outputs, frame, resize_para):
        num_dets = outputs["num_dets"][0][0]
        # tritonclient result read only, need clone
        det_boxes = outputs["det_boxes"][0][0:num_dets].copy()
        det_scores = outputs["det_scores"][0][0:num_dets].copy()
        det_classes = outputs["det_classes"][0][0:num_dets].copy()

        # 最大输出框数量
        max_output_boxes = det_classes.shape[0]

        # rescale to real bbox
        scale, _, pad_w, pad_h = resize_para
        # 先去除padding偏移
        det_boxes[:, 0] = det_boxes[:, 0] - pad_w  # x1
        det_boxes[:, 1] = det_boxes[:, 1] - pad_h  # y1
        det_boxes[:, 2] = det_boxes[:, 2] - pad_w  # x2
        det_boxes[:, 3] = det_boxes[:, 3] - pad_h  # y2
        # 然后缩放到原始图像尺寸
        det_boxes = det_boxes / scale
        # 最后转换坐标格式 (x1,y1,x2,y2) -> (x,y,w,h)
        det_boxes[:, 2] = det_boxes[:, 2] - det_boxes[:, 0]  # w = x2 - x1
        det_boxes[:, 3] = det_boxes[:, 3] - det_boxes[:, 1]  # h = y2 - y1

        # 创建一个掩码，用于过滤指定的类别 ID
        class_mask = np.isin(det_classes, self.class_ids)
        indices = np.arange(max_output_boxes)
        indices = indices[class_mask]

        # 提取有效检测框的坐标、分数和类别
        valid_boxes = det_boxes.reshape(-1, 4)[indices]
        valid_scores = det_scores.reshape(-1, 1)[indices]
        valid_classes = det_classes.reshape(-1, 1)[indices]

        # 合并为一个张量
        merged_output = np.concatenate((valid_boxes, valid_scores, valid_classes), axis=1).astype(np.float32)
        return merged_output


    def process_execute_task_message(self, message: MessageInfo):
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"

        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.info(f"[YOLO] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
            return message

        self.logger.debug(f"[YOLO] Frame {frame_id}: 🔍 开始YOLO目标检测")

        frame, _, _ = self.extract_frame_info(message)
        self.logger.debug(f"[YOLO] Frame {frame_id}: 提取帧信息完成，frame shape: {frame.shape}")

        preprocess_input, resize_para = self.preprocess(frame)
        self.logger.debug(f"[YOLO] Frame {frame_id}: 预处理完成，input shape: {preprocess_input.shape}")

        model_inputs = {
            "images": preprocess_input,
        }

        self.logger.debug(f"[YOLO] Frame {frame_id}: 开始模型推理")
        status, infer_results = self.infer_client.infer(self.model_name, model_inputs)
        self.logger.debug(f"[YOLO] Frame {frame_id}: 模型推理完成，status: {status}")

        detect_res = self.postprocess(infer_results, frame, resize_para)
        self.logger.debug(f"[YOLO] Frame {frame_id}: 后处理完成")
        # save_detect_result(frame, detect_res, "yolov8_detect_result.jpg")

        # YOLO检测逻辑：如果有检测对象，那么往下传；如果没有检测对象，标记跳过
        if detect_res is not None and len(detect_res) > 0:
            self.logger.info(f"[YOLO] Frame {frame_id}: ✅ 检测到 {len(detect_res)} 个目标，设置skip_processing=False，继续传递")
            self.update_frame_info(message, bboxes=detect_res)
            message.skip_processing = False

            # 打印检测结果详情
            for i, bbox in enumerate(detect_res):
                if len(bbox) >= 6:
                    x, y, w, h, score, class_id = bbox[:6]
                    self.logger.debug(f"[YOLO] Frame {frame_id}: 目标{i+1} - 位置:({x:.1f},{y:.1f},{w:.1f},{h:.1f}), 置信度:{score:.3f}, 类别:{int(class_id)}")
        else:
            self.logger.info(f"[YOLO] Frame {frame_id}: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理")
            message.skip_processing = True

        return message


    # for debug
    def process_none_message(self, message: MessageInfo):
        test_file = "/workspace/video_analysis_server/data/images/cat_224_224.jpg"
        video_frame = cv2.imread(test_file)
        frame_info = self.prepare_frame_info(video_frame)
        exec_task_message = self.prepare_execute_task_message(frame_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message
