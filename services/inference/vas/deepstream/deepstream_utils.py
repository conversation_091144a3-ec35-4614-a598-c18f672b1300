#!/usr/bin/env python3

################################################################################
# SPDX-FileCopyrightText: Copyright (c) 2020-2021 NVIDIA CORPORATION & AFFILIATES. All rights reserved.
# SPDX-License-Identifier: Apache-2.0
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
# http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
################################################################################

import sys
sys.path.append('../')
import gi
import configparser
gi.require_version("Gst", "1.0")
gi.require_version("GstRtspServer", "1.0")
from gi.repository import GObject, Gst, GstRtspServer, GLib
from ctypes import *
import time
import sys
import math
import numpy as np
import cv2
import platform
from common.is_aarch_64 import is_aarch64
from common.bus_call import bus_call
from common.FPS import GETFPS
from ini_parser import ParseConfigFile

import pyds

MAX_DISPLAY_LEN=64
PGIE_CLASS_ID_VEHICLE = 0
PGIE_CLASS_ID_BICYCLE = 1
PGIE_CLASS_ID_PERSON = 2
PGIE_CLASS_ID_ROADSIGN = 3
MUXER_OUTPUT_WIDTH=1920
MUXER_OUTPUT_HEIGHT=1080
MUXER_BATCH_TIMEOUT_USEC=4000000
TILED_OUTPUT_WIDTH=1280
TILED_OUTPUT_HEIGHT=720
GST_CAPS_FEATURES_NVMM="memory:NVMM"
OSD_PROCESS_MODE= 1
OSD_DISPLAY_TEXT= 1
OSD_DISPLAY_BBOX= 1
OSD_DISPLAY_CLOCK= 1
OSD_CLOCK_FONT= "Arial"
OSD_CLOCK_FONT_SIZE= 15
OSD_CLOCK_X_OFFSET= int(TILED_OUTPUT_WIDTH*0.05)
OSD_CLOCK_Y_OFFSET= int(TILED_OUTPUT_HEIGHT*0.05)
ALARM_TIME_OUT = 4
ALARM_TIME_START = 2


def prepare_task_pipeline_info(task_info, class_id):
    pipeline_info = {}
    task_id = task_info["task_id"]
    algo_type = task_info["algo_type"]
    push_url = task_info["push_url"]
    algo_config = task_info["algo_config"]
    polygon_info = algo_config["polygon_info"]
    stream_sources = []
    stream_sources.append(task_info["rtsp_url"])

    nvdsanalytics_config_template_file = "./config_template/config_nvdsanalytics.txt"
    config = ParseConfigFile(nvdsanalytics_config_template_file)
    for i in range(len(stream_sources)):
        section_name = "roi-filtering-stream-{}".format(i)
        config.add_section(section_name)
        config.set_element_value(section_name, "enable", "1")
        config.set_element_value(section_name, "inverse-roi", "0")
        config.set_element_value(section_name, "class-id", class_id)
        for j in range(len(polygon_info)):
            roi_name = "roi-{}".format(polygon_info[j]["id"])
            config.set_element_value(section_name, roi_name, polygon_info[j]["points"])

    nvdsanalytics_config_file = "./config/{}_{}_config_nvdsanalytics.txt".format(task_id, algo_type)
    with open(nvdsanalytics_config_file, "w") as f:
        config.write_config(f)

    pipeline_info["task_id"] = task_id
    pipeline_info["algo_type"] = algo_type
    pipeline_info["push_url"] = push_url
    pipeline_info["algo_config"] = algo_config
    pipeline_info["class_id"] = class_id
    pipeline_info["sources"] = stream_sources
    pipeline_info["time_start"] = 0.5 * algo_config["duration_time"]
    pipeline_info["time_out"] = 1.0 * algo_config["duration_time"]
    if "vehicle_count" in algo_config.keys():
        pipeline_info["vehicle_count"] = algo_config["vehicleCount"]
    pipeline_info["analytics_config"] = nvdsanalytics_config_file
    pipeline_info["tracker_config"] = "./config/dsnvanalytics_tracker_config.txt"
    pipeline_info["pgie_config"] = "./config/dsnvanalytics_pgie_config.txt"
    return pipeline_info


def prepare_pipeline_info(task_info):
    pipeline_info = {}
    if task_info["algo_type"] == "vehicle_park":
        pipeline_info = prepare_task_pipeline_info(task_info, "2;4;5")
    elif task_info["algo_type"] == "non_vehicle_park":
        pipeline_info = prepare_task_pipeline_info(task_info, "1;3;6;7")
    elif task_info["algo_type"] == "roadside_booths":
        pipeline_info = prepare_task_pipeline_info(task_info, "7;9")
    elif task_info["algo_type"] == "place_mess":
        pipeline_info = prepare_task_pipeline_info(task_info, "8;10")
    elif task_info["algo_type"] == "traffic_jam":
        pipeline_info = prepare_task_pipeline_info(task_info, "2;4;5")
    else:
        pass
    return pipeline_info


class DeepStreamPipeline:
    def __init__(self, pipeline_info, alert_queue = None, debug = False):
        import ctypes
        ctypes.cdll.LoadLibrary("./engine/libmyplugins.so")
        self.pipeline_info = pipeline_info
        self.alert_queue = alert_queue
        self.pipeline = None
        self.debug = debug
        self.fps_streams = {}
        self.obj_info = {}


    def get_image_from_gst_buffer(self, gst_buffer, frame_meta):
        # Getting Image data using nvbufsurface
        # the input should be address of buffer and batch_id
        n_frame = pyds.get_nvds_buf_surface(hash(gst_buffer), frame_meta.batch_id)
        # convert python array into numpy array format in the copy mode.
        frame_copy = np.array(n_frame, copy=True, order='C')
        # convert the array into cv2 default color format
        frame_copy = cv2.cvtColor(frame_copy, cv2.COLOR_RGBA2BGR)
        return frame_copy


    def update_obj_info(self, gst_buffer, obj_meta, frame_meta, ori_status):
        obj_id = str(obj_meta.object_id)
        if ori_status not in self.obj_info.keys():
            self.obj_info[ori_status] = {}
            self.obj_info[ori_status]["alarm_flag"] = False
        roi_obj_info = self.obj_info[ori_status]
        if obj_id not in roi_obj_info.keys():
            obj_info = {}
            obj_info["id"] = obj_id
            obj_info["start_time"] = time.time()
            obj_info["last_time"] = time.time()
            obj_info["elapse_time"] = 0
            roi_obj_info[obj_id] = obj_info
        else:
            rect_info = pyds.NvOSD_RectParams.cast(obj_meta.rect_params)
            bbox = {}
            bbox["left"] = int(rect_info.left)
            bbox["top"] = int(rect_info.top)
            bbox["right"] = int(rect_info.left + rect_info.width)
            bbox["bottom"] = int(rect_info.top + rect_info.height)
            roi_obj_info[obj_id]["last_time"] = time.time()
            roi_obj_info[obj_id]["elapse_time"] = roi_obj_info[obj_id]["last_time"] - roi_obj_info[obj_id]["start_time"]
            print("------{} elaspe time is {} --------".format(obj_id, roi_obj_info[obj_id]["elapse_time"]))
            if roi_obj_info[obj_id]["elapse_time"] > self.pipeline_info["time_out"]:
                roi_obj_info[obj_id]["end_frame"] = self.get_image_from_gst_buffer(gst_buffer, frame_meta)
                roi_obj_info[obj_id]["end_bbox"] = bbox
                roi_obj_info["alarm_flag"] = True
            elif roi_obj_info[obj_id]["elapse_time"] > self.pipeline_info["time_start"]:
                roi_obj_info[obj_id]["start_frame"] = self.get_image_from_gst_buffer(gst_buffer, frame_meta)
                roi_obj_info[obj_id]["start_bbox"] = bbox
            else:
                pass


    def clear_obj_info(self):
        clear_names = []
        alert_names = []
        for roi_id in self.obj_info.keys():
            roi_obj_info = self.obj_info[roi_id]
            # print("{} have {} objs".format(roi_id, len(roi_obj_info.keys())))
            if roi_obj_info["alarm_flag"]:
                for obj_id in roi_obj_info.keys():
                    if obj_id == "alarm_flag":
                        continue
                    if "start_frame" in roi_obj_info[obj_id].keys() and \
                        "end_frame" in roi_obj_info[obj_id].keys():
                        print("------push {}:{} into alert queue--------".format(obj_id, roi_id))
                        alert_names.append(obj_id)

            else:
                for obj_id in roi_obj_info.keys():
                    if obj_id == "alarm_flag":
                        continue
                    curr_time = time.time()
                    # print("..........{}..........{}".format(curr_time, roi_obj_info[obj_id]))
                    interval_time = curr_time - roi_obj_info[obj_id]["last_time"]
                    if interval_time > self.pipeline_info["time_out"]:
                        print(".... {} ......clear {} ".format(interval_time, obj_id))
                        clear_names.append(obj_id)

            
            for alert_name in alert_names:
                if self.alert_queue != None and not self.alert_queue.full():
                    self.alert_queue.put(roi_obj_info[alert_name], False)
                roi_obj_info.pop(alert_name)

            for clear_name in clear_names:
                roi_obj_info.pop(clear_name)


    def update_obj_info_new(self, gst_buffer, obj_meta, frame_meta, roi_status):
        if roi_status not in self.obj_info.keys():
            self.obj_info[roi_status] = {}
            self.obj_info[roi_status]["roi_id"] = roi_status
            self.obj_info[roi_status]["avg_num"] = 0
            self.obj_info[roi_status]["frame_count"] = 0
            self.obj_info[roi_status]["start_time"] = time.time()
            self.obj_info[roi_status]["last_time"] = time.time()
            self.obj_info[roi_status]["elapse_time"] = 0
            self.obj_info[roi_status]["obj_dict"] = {}
            self.obj_info[roi_status]["start_frame"] = None
            self.obj_info[roi_status]["end_frame"] = None
            self.obj_info[roi_status]["current_frame"] = None

        rect_info = pyds.NvOSD_RectParams.cast(obj_meta.rect_params)
        bbox = {}
        bbox["left"] = int(rect_info.left)
        bbox["top"] = int(rect_info.top)
        bbox["right"] = int(rect_info.left + rect_info.width)
        bbox["bottom"] = int(rect_info.top + rect_info.height)
        obj_id = str(obj_meta.object_id)
        if obj_id not in self.obj_info[roi_status]["obj_dict"].keys():
            obj_info = {}
            obj_info["obj_id"] = obj_id
            obj_info["roi_id"] = roi_status
            obj_info["current_bbox"] = bbox
            obj_info["start_bbox"] = {}
            obj_info["end_bbox"] = {}
            obj_info["start_time"] = time.time()
            obj_info["last_time"] = time.time()
            obj_info["elapse_time"] = 0
            obj_info["start_frame"] = None
            obj_info["end_frame"] = None
            self.obj_info[roi_status]["obj_dict"][obj_id] = obj_info
        else:
            obj_info = self.obj_info[roi_status]["obj_dict"][obj_id]
            obj_info["last_time"] = time.time()
            obj_info["elapse_time"] = obj_info["last_time"] - obj_info["start_time"]
            obj_info["current_bbox"] = bbox

        curr_time = time.time()
        if curr_time - self.obj_info[roi_status]["last_time"] > self.pipeline_info["time_start"]:
            self.obj_info[roi_status]["start_time"] = curr_time
        self.obj_info[roi_status]["last_time"] = time.time()
        self.obj_info[roi_status]["elapse_time"] = self.obj_info[roi_status]["last_time"] - self.obj_info[roi_status]["start_time"]
        self.obj_info[roi_status]["current_frame"] = self.get_image_from_gst_buffer(gst_buffer, frame_meta)
        print("obj {} elapse time {}".format(obj_id, obj_info["elapse_time"]))
        print("roi {} elapse time {}".format(roi_status, self.obj_info[roi_status]["elapse_time"]))


    def clear_obj_info_new(self):
        clear_roi_names = {}
        alert_roi_names = {}
        for roi_id in self.obj_info.keys():
            alert_roi_names[roi_id] = []
            clear_roi_names[roi_id] = []
            roi_info = self.obj_info[roi_id]
            roi_info["avg_num"] = (roi_info["avg_num"] * roi_info["frame_count"] + len(roi_info["obj_dict"])) / (roi_info["frame_count"] + 1)
            roi_info["frame_count"] += 1
            if "vehicle_count" in self.pipeline_info.keys():
                if roi_info["elapse_time"] > self.pipeline_info["time_out"] and roi_info["avg_num"] >= self.pipeline_info["vehicle_count"]:
                    roi_info["end_frame"] = roi_info["current_frame"]
                    alert_roi_names[roi_id] = list(roi_info["obj_dict"].keys())
                elif roi_info["avg_num"] >= self.pipeline_info["vehicle_count"]:
                    if roi_info["start_frame"] is None:
                        roi_info["start_frame"] = roi_info["current_frame"]
                else:
                    clear_roi_names[roi_id] = list(roi_info["obj_dict"].keys())
            else:
                for obj_id in roi_info["obj_dict"].keys():
                    obj_info = roi_info["obj_dict"][obj_id]
                    if obj_info["elapse_time"] > self.pipeline_info["time_out"]:
                        obj_info["end_frame"] = roi_info["current_frame"]
                        obj_info["end_bbox"] = obj_info["current_bbox"]
                        alert_roi_names[roi_id].append(obj_id)
                    elif roi_info["last_time"] - obj_info["last_time"] > self.pipeline_info["time_out"]:
                        clear_roi_names[roi_id].append(obj_id)
                    else:
                        if obj_info["start_frame"] is None:
                            obj_info["start_frame"] = roi_info["current_frame"]
                            obj_info["start_bbox"] = obj_info["current_bbox"]

        if "vehicle_count" in self.pipeline_info.keys():
            for roi_id in alert_roi_names.keys():
                if roi_id in self.obj_info.keys() and len(alert_roi_names[roi_id]):
                    roi_info = self.obj_info[roi_id]
                    if self.alert_queue != None and not self.alert_queue.full():
                        self.alert_queue.put(roi_info, False)
                    self.obj_info.pop(roi_id)

            for roi_id in clear_roi_names.keys():
                if roi_id in self.obj_info.keys() and len(clear_roi_names[roi_id]):
                    self.obj_info.pop(roi_id)
        else:
            for roi_id in alert_roi_names.keys():
                if roi_id not in self.obj_info.keys() or len(alert_roi_names[roi_id]) <= 0:
                    continue
                alert_roi_info = {}
                roi_info = self.obj_info[roi_id]
                # put obj info
                for obj_id in alert_roi_names[roi_id]:
                    if obj_id in roi_info["obj_dict"].keys():
                        if self.alert_queue != None and not self.alert_queue.full():
                            self.alert_queue.put(roi_info["obj_dict"][obj_id], False)
                        roi_info["obj_dict"].pop(obj_id)

            for roi_id in clear_roi_names.keys():
                if roi_id in self.obj_info.keys():
                    roi_info = self.obj_info[roi_id]
                    for obj_id in clear_roi_names[roi_id]:
                        if obj_id in roi_info["obj_dict"].keys():
                            roi_info["obj_dict"].pop(obj_id)


    # nvanlytics_src_pad_buffer_probe  will extract metadata received on nvtiler sink pad
    # and update params for drawing rectangle, object information etc.
    # def nvanalytics_src_pad_buffer_probe(pad,info,u_data):
    def tiler_sink_pad_buffer_probe(self, pad, info, u_data):
        self.clear_obj_info_new()
        frame_number=0
        num_rects=0
        gst_buffer = info.get_buffer()
        if not gst_buffer:
            print("Unable to get GstBuffer ")
            return
        # Retrieve batch metadata from the gst_buffer
        # Note that pyds.gst_buffer_get_nvds_batch_meta() expects the
        # C address of gst_buffer as input, which is obtained with hash(gst_buffer)
        batch_meta = pyds.gst_buffer_get_nvds_batch_meta(hash(gst_buffer))
        l_frame = batch_meta.frame_meta_list

        while l_frame:
            try:
                # Note that l_frame.data needs a cast to pyds.NvDsFrameMeta
                # The casting is done by pyds.NvDsFrameMeta.cast()
                # The casting also keeps ownership of the underlying memory
                # in the C code, so the Python garbage collector will leave
                # it alone.
                frame_meta = pyds.NvDsFrameMeta.cast(l_frame.data)
            except StopIteration:
                break

            frame_number=frame_meta.frame_num
            l_obj=frame_meta.obj_meta_list
            num_rects = frame_meta.num_obj_meta
            while l_obj:
                try: 
                    # Note that l_obj.data needs a cast to pyds.NvDsObjectMeta
                    # The casting is done by pyds.NvDsObjectMeta.cast()
                    obj_meta=pyds.NvDsObjectMeta.cast(l_obj.data)
                except StopIteration:
                    break
                # obj_counter[obj_meta.class_id] += 1
                l_user_meta = obj_meta.obj_user_meta_list
                # Extract object level meta data from NvDsAnalyticsObjInfo
                while l_user_meta:
                    try:
                        user_meta = pyds.NvDsUserMeta.cast(l_user_meta.data)
                        if user_meta.base_meta.meta_type == pyds.nvds_get_user_meta_type("NVIDIA.DSANALYTICSOBJ.USER_META"):
                            user_meta_data = pyds.NvDsAnalyticsObjInfo.cast(user_meta.user_meta_data)
                            if user_meta_data.roiStatus:
                                # print("Object {0} roi status: {1}".format(obj_meta.object_id, user_meta_data.roiStatus))
                                self.update_obj_info_new(gst_buffer, obj_meta, frame_meta, user_meta_data.roiStatus[0])
                    except StopIteration:
                        break
                    try:
                        l_user_meta = l_user_meta.next
                    except StopIteration:
                        break
                try: 
                    l_obj = l_obj.next
                except StopIteration:
                    break
            # print("Frame Number=", frame_number, "stream id=", frame_meta.pad_index, "Number of Objects=",num_rects)
            # Get frame rate through this probe
            self.fps_streams["stream{0}".format(frame_meta.pad_index)].get_fps()
            try:
                l_frame = l_frame.next
            except StopIteration:
                break
        return Gst.PadProbeReturn.OK


    def cb_newpad(self, decodebin, decoder_src_pad, data):
        print("In cb_newpad\n")
        caps=decoder_src_pad.get_current_caps()
        gststruct=caps.get_structure(0)
        gstname=gststruct.get_name()
        source_bin=data
        features=caps.get_features(0)

        # Need to check if the pad created by the decodebin is for video and not
        # audio.
        print("gstname=", gstname)
        if(gstname.find("video")!=-1):
            # Link the decodebin pad only if decodebin has picked nvidia
            # decoder plugin nvdec_*. We do this by checking if the pad caps contain
            # NVMM memory features.
            print("features=",features)
            if features.contains("memory:NVMM"):
                # Get the source bin ghost pad
                bin_ghost_pad=source_bin.get_static_pad("src")
                if not bin_ghost_pad.set_target(decoder_src_pad):
                    sys.stderr.write("Failed to link decoder src pad to source bin ghost pad\n")
            else:
                sys.stderr.write(" Error: Decodebin did not pick nvidia decoder plugin.\n")


    def decodebin_child_added(self, child_proxy, Object, name, user_data):
        print("Decodebin child added:", name, "\n")
        if(name.find("decodebin") != -1):
            Object.connect("child-added", self.decodebin_child_added, user_data)


    def create_source_bin(self, index, uri):
        print("Creating source bin")
        # Create a source GstBin to abstract this bin's content from the rest of the
        # pipeline
        bin_name="source-bin-%02d" %index
        print(bin_name)
        nbin=Gst.Bin.new(bin_name)
        if not nbin:
            sys.stderr.write(" Unable to create source bin \n")

        # Source element for reading from the uri.
        # We will use decodebin and let it figure out the container format of the
        # stream and the codec and plug the appropriate demux and decode plugins.
        uri_decode_bin=Gst.ElementFactory.make("uridecodebin", "uri-decode-bin")
        if not uri_decode_bin:
            sys.stderr.write(" Unable to create uri decode bin \n")
        # We set the input uri to the source element
        uri_decode_bin.set_property("uri",uri)
        # Connect to the "pad-added" signal of the decodebin which generates a
        # callback once a new pad for raw data has beed created by the decodebin
        uri_decode_bin.connect("pad-added", self.cb_newpad, nbin)
        uri_decode_bin.connect("child-added", self.decodebin_child_added, nbin)

        # We need to create a ghost pad for the source bin which will act as a proxy
        # for the video decoder src pad. The ghost pad will not have a target right
        # now. Once the decode bin creates the video decoder and generates the
        # cb_newpad callback, we will set the ghost pad target to the video decoder
        # src pad.
        Gst.Bin.add(nbin,uri_decode_bin)
        bin_pad=nbin.add_pad(Gst.GhostPad.new_no_target("src",Gst.PadDirection.SRC))
        if not bin_pad:
            sys.stderr.write(" Failed to add ghost pad in source bin \n")
            return None
        return nbin


    def pipeline_create(self):
        # Standard GStreamer initialization
        GObject.threads_init()
        Gst.init(None)

        # Create gstreamer elements */
        # Create Pipeline element that will form a connection of other elements
        print("Creating Pipeline \n ")
        self.pipeline = Gst.Pipeline()
        is_live = False

        if not self.pipeline:
            sys.stderr.write(" Unable to create Pipeline \n")
        print("Creating streamux \n ")

        # Create nvstreammux instance to form batches from one or more sources.
        streammux = Gst.ElementFactory.make("nvstreammux", "Stream-muxer")
        if not streammux:
            sys.stderr.write(" Unable to create NvStreamMux \n")
        self.pipeline.add(streammux)

        # get source stream from pipeline info
        source_info = self.pipeline_info["sources"]
        number_sources = len(source_info)

        # fps calculator
        for i in range(0,number_sources):
            self.fps_streams["stream{0}".format(i)]=GETFPS(i)

        # Create source bin
        for i in range(number_sources):
            print("Creating source_bin ",i," \n ")
            uri_name = source_info[i]
            if uri_name.find("rtsp://") == 0 :
                is_live = True
            source_bin = self.create_source_bin(i, uri_name)
            if not source_bin:
                sys.stderr.write("Unable to create source bin \n")
            self.pipeline.add(source_bin)
            padname="sink_%u" %i
            sinkpad= streammux.get_request_pad(padname)
            if not sinkpad:
                sys.stderr.write("Unable to create sink pad bin \n")
            srcpad=source_bin.get_static_pad("src")
            if not srcpad:
                sys.stderr.write("Unable to create src pad bin \n")
            srcpad.link(sinkpad)

        # Create Pgie
        print("Creating Pgie \n ")
        pgie = Gst.ElementFactory.make("nvinfer", "primary-inference")
        if not pgie:
            sys.stderr.write(" Unable to create pgie \n")

        # Create nvtracker
        print("Creating nvtracker \n ")
        tracker = Gst.ElementFactory.make("nvtracker", "tracker")
        if not tracker:
            sys.stderr.write(" Unable to create tracker \n")

        # Create nvdsanalytics
        print("Creating nvdsanalytics \n ")
        nvanalytics = Gst.ElementFactory.make("nvdsanalytics", "analytics")
        if not nvanalytics:
            sys.stderr.write(" Unable to create nvanalytics \n")
        nvanalytics.set_property("config-file", self.pipeline_info["analytics_config"])

        # Add nvvidconv1 and filter1 to convert the frames to RGBA
        # which is easier to work with in Python.
        print("Creating nvvidconv1 \n ")
        nvvidconv1 = Gst.ElementFactory.make("nvvideoconvert", "convertor1")
        if not nvvidconv1:
            sys.stderr.write(" Unable to create nvvidconv1 \n")

        print("Creating filter1 \n ")
        caps1 = Gst.Caps.from_string("video/x-raw(memory:NVMM), format=RGBA")
        filter1 = Gst.ElementFactory.make("capsfilter", "filter1")
        if not filter1:
            sys.stderr.write(" Unable to get the caps filter1 \n")
        filter1.set_property("caps", caps1)

        # Create tiler
        print("Creating tiler \n ")
        tiler=Gst.ElementFactory.make("nvmultistreamtiler", "nvtiler")
        if not tiler:
            sys.stderr.write(" Unable to create tiler \n")

        # Create nvvidconv
        print("Creating nvvidconv \n ")
        nvvidconv = Gst.ElementFactory.make("nvvideoconvert", "convertor")
        if not nvvidconv:
            sys.stderr.write(" Unable to create nvvidconv \n")

        # Create nvosd
        print("Creating nvosd \n ")
        nvosd = Gst.ElementFactory.make("nvdsosd", "onscreendisplay")
        if not nvosd:
            sys.stderr.write(" Unable to create nvosd \n")
        if self.debug:
            nvosd.set_property('process-mode',OSD_PROCESS_MODE)
            nvosd.set_property('display-text',OSD_DISPLAY_TEXT)
            nvosd.set_property('display-bbox',OSD_DISPLAY_BBOX)
            nvosd.set_property('display-clock',OSD_DISPLAY_CLOCK)
            nvosd.set_property('clock-font',OSD_CLOCK_FONT)
            nvosd.set_property('clock-font-size',OSD_CLOCK_FONT_SIZE)
            nvosd.set_property('x-clock-offset',OSD_CLOCK_X_OFFSET)
            nvosd.set_property('y-clock-offset',OSD_CLOCK_Y_OFFSET)

        if(is_aarch64()):
            print("Creating transform \n ")
            transform=Gst.ElementFactory.make("nvegltransform", "nvegl-transform")
            if not transform:
                sys.stderr.write(" Unable to create transform \n")
        
        if not self.debug:
            # print("Creating EGLSink \n")
            # sink = Gst.ElementFactory.make("nveglglessink", "nvvideo-renderer")
            # if not sink:
            #     sys.stderr.write(" Unable to create egl sink \n")
            if is_live:
                print("Atleast one of the sources is live")
                streammux.set_property('live-source', 1)
            if self.pipeline_info["push_url"] == "":
                print("Creating FakeSink \n")
                sink = Gst.ElementFactory.make("fakesink", "fakesink")
                if not sink:
                    sys.stderr.write(" Unable to create fake sink \n")
            else:
                codec = "H264"
                bitrate = 4000000
                nvvidconv_postosd = Gst.ElementFactory.make("nvvideoconvert", "convertor_postosd")
                if not nvvidconv_postosd:
                    sys.stderr.write(" Unable to create nvvidconv_postosd \n")
                # Create a caps filter
                caps = Gst.ElementFactory.make("capsfilter", "filter")
                caps.set_property("caps", Gst.Caps.from_string("video/x-raw(memory:NVMM), format=I420"))
                # Make the encoder
                if codec == "H264":
                    encoder = Gst.ElementFactory.make("nvv4l2h264enc", "encoder")
                    print("Creating H264 Encoder")
                elif codec == "H265":
                    encoder = Gst.ElementFactory.make("nvv4l2h265enc", "encoder")
                    print("Creating H265 Encoder")
                if not encoder:
                    sys.stderr.write(" Unable to create encoder")
                
                parser = Gst.ElementFactory.make("h264parse", "parser")
                if not parser:
                    sys.stderr.write(" Unable to create parser")
                flvmuxer = Gst.ElementFactory.make("flvmux", "flvmuxer")
                if not flvmuxer:
                    sys.stderr.write(" Unable to create flvmuxer")
                print("Creating RtmpSink {}\n".format(self.pipeline_info["push_url"]))
                sink = Gst.ElementFactory.make("rtmpsink", "rtmpsink")
                if not sink:
                    sys.stderr.write(" Unable to create rtmp sink \n")
                sink.set_property("location", self.pipeline_info["push_url"])

        # debug for rtsp sink
        else:
            codec = "H264"
            bitrate = 4000000
            nvvidconv_postosd = Gst.ElementFactory.make("nvvideoconvert", "convertor_postosd")
            if not nvvidconv_postosd:
                sys.stderr.write(" Unable to create nvvidconv_postosd \n")
            # Create a caps filter
            caps = Gst.ElementFactory.make("capsfilter", "filter")
            caps.set_property("caps", Gst.Caps.from_string("video/x-raw(memory:NVMM), format=I420"))

            # Make the encoder
            if codec == "H264":
                encoder = Gst.ElementFactory.make("nvv4l2h264enc", "encoder")
                print("Creating H264 Encoder")
            elif codec == "H265":
                encoder = Gst.ElementFactory.make("nvv4l2h265enc", "encoder")
                print("Creating H265 Encoder")
            if not encoder:
                sys.stderr.write(" Unable to create encoder")
            encoder.set_property("bitrate", bitrate)
            if is_aarch64():
                encoder.set_property("preset-level", 1)
                encoder.set_property("insert-sps-pps", 1)
                encoder.set_property("bufapi-version", 1)

            # Make the payload-encode video into RTP packets
            if codec == "H264":
                rtppay = Gst.ElementFactory.make("rtph264pay", "rtppay")
                print("Creating H264 rtppay")
            elif codec == "H265":
                rtppay = Gst.ElementFactory.make("rtph265pay", "rtppay")
                print("Creating H265 rtppay")
            if not rtppay:
                sys.stderr.write(" Unable to create rtppay")

            # Make the UDP sink
            updsink_port_num = 5400
            sink = Gst.ElementFactory.make("udpsink", "udpsink")
            if not sink:
                sys.stderr.write(" Unable to create udpsink")

            sink.set_property("host", "***************")
            sink.set_property("port", updsink_port_num)
            sink.set_property("async", False)
            sink.set_property("sync", 1)
            sink.set_property("qos",0)


        streammux.set_property('width', 1920)
        streammux.set_property('height', 1080)
        streammux.set_property('batch-size', number_sources)
        streammux.set_property('batched-push-timeout', 4000000)
        pgie.set_property('config-file-path', self.pipeline_info["pgie_config"])
        pgie_batch_size = pgie.get_property("batch-size")
        if(pgie_batch_size != number_sources):
            print("WARNING: Overriding infer-config batch-size",pgie_batch_size," with number of sources ", number_sources," \n")
            pgie.set_property("batch-size",number_sources)
        tiler_rows = int(math.sqrt(number_sources))
        tiler_columns = int(math.ceil((1.0*number_sources)/tiler_rows))
        tiler.set_property("rows",tiler_rows)
        tiler.set_property("columns",tiler_columns)
        tiler.set_property("width", TILED_OUTPUT_WIDTH)
        tiler.set_property("height", TILED_OUTPUT_HEIGHT)

        if not is_aarch64():
            # Use CUDA unified memory in the pipeline so frames
            # can be easily accessed on CPU in Python.
            mem_type = int(pyds.NVBUF_MEM_CUDA_UNIFIED)
            streammux.set_property("nvbuf-memory-type", mem_type)
            nvvidconv.set_property("nvbuf-memory-type", mem_type)
            nvvidconv1.set_property("nvbuf-memory-type", mem_type)
            tiler.set_property("nvbuf-memory-type", mem_type)

        #Set properties of tracker
        config = configparser.ConfigParser()
        config.read(self.pipeline_info["tracker_config"])
        config.sections()
        for key in config['tracker']:
            if key == 'tracker-width' :
                tracker_width = config.getint('tracker', key)
                tracker.set_property('tracker-width', tracker_width)
            if key == 'tracker-height' :
                tracker_height = config.getint('tracker', key)
                tracker.set_property('tracker-height', tracker_height)
            if key == 'gpu-id' :
                tracker_gpu_id = config.getint('tracker', key)
                tracker.set_property('gpu_id', tracker_gpu_id)
            if key == 'll-lib-file' :
                tracker_ll_lib_file = config.get('tracker', key)
                tracker.set_property('ll-lib-file', tracker_ll_lib_file)
            if key == 'll-config-file' :
                tracker_ll_config_file = config.get('tracker', key)
                tracker.set_property('ll-config-file', tracker_ll_config_file)
            if key == 'enable-batch-process' :
                tracker_enable_batch_process = config.getint('tracker', key)
                tracker.set_property('enable_batch_process', tracker_enable_batch_process)
            if key == 'enable-past-frame' :
                tracker_enable_past_frame = config.getint('tracker', key)
                tracker.set_property('enable_past_frame', tracker_enable_past_frame)

        print("Adding elements to Pipeline \n")
        self.pipeline.add(pgie)
        self.pipeline.add(tracker)
        self.pipeline.add(nvanalytics)
        self.pipeline.add(tiler)
        self.pipeline.add(nvvidconv)
        self.pipeline.add(filter1)
        self.pipeline.add(nvvidconv1)
        self.pipeline.add(nvosd)

        if self.debug:
            self.pipeline.add(nvvidconv_postosd)
            self.pipeline.add(caps)
            self.pipeline.add(encoder)
            self.pipeline.add(rtppay)
        else:
            if self.pipeline_info["push_url"] != "":
                self.pipeline.add(nvvidconv_postosd)
                self.pipeline.add(caps)
                self.pipeline.add(encoder)
                self.pipeline.add(parser)
                self.pipeline.add(flvmuxer)
        if is_aarch64():
            self.pipeline.add(transform)
        self.pipeline.add(sink)

        # We link elements in the following order:
        # sourcebin -> streammux -> nvinfer -> nvtracker -> nvdsanalytics ->
        # nvtiler -> nvvidconv1 -> filter1 -> tiler ->
        # nvvidconv-> nvdsosd -> [transform] -> sink
        print("Linking elements in the Pipeline \n")
        streammux.link(pgie)
        pgie.link(tracker)
        tracker.link(nvanalytics)
        nvanalytics.link(nvvidconv1)
        nvvidconv1.link(filter1)
        filter1.link(tiler)
        tiler.link(nvvidconv)
        nvvidconv.link(nvosd)
        if not self.debug:
            if is_aarch64():
                nvosd.link(transform)
                transform.link(sink)
            else:
                if self.pipeline_info["push_url"] != "":
                    nvosd.link(nvvidconv_postosd)
                    nvvidconv_postosd.link(caps)
                    caps.link(encoder)
                    encoder.link(parser)
                    parser.link(flvmuxer)
                    flvmuxer.link(sink)
                else:
                    nvosd.link(sink)
        else:
            nvosd.link(nvvidconv_postosd)
            nvvidconv_postosd.link(caps)
            caps.link(encoder)
            encoder.link(rtppay)
            rtppay.link(sink)

        # add callback on tiler sink pad
        tiler_sink_pad = tiler.get_static_pad("sink")
        if not tiler_sink_pad:
            sys.stderr.write("Unable to get sink pad \n")
        else:
            tiler_sink_pad.add_probe(Gst.PadProbeType.BUFFER, self.tiler_sink_pad_buffer_probe, 0)
            # if self.pipeline_info["algo_type"] in ("vehicle_park", "non_vehicle_park", "roadside_booths", "place_mess"):
            #     tiler_sink_pad.add_probe(Gst.PadProbeType.BUFFER, self.tiler_sink_pad_buffer_probe_type1, 0)
            # elif self.pipeline_info["algo_type"] in ("traffic_jam", ):
            #     tiler_sink_pad.add_probe(Gst.PadProbeType.BUFFER, self.tiler_sink_pad_buffer_probe_type2, 0)
            # else:
            #     sys.stderr.write("Unable add prob func for algo type:{}\n".format(self.pipeline_info["algo_type"]))


    def pipeline_run(self):
        result = True
        if self.debug:
            updsink_port_num = 5400
            codec = "H264"
            # Start rtsp streaming
            rtsp_port_num = 9554
            server = GstRtspServer.RTSPServer.new()
            server.props.service = "%d" % rtsp_port_num
            server.attach(None)
            factory = GstRtspServer.RTSPMediaFactory.new()
            factory.set_launch(
                '( udpsrc name=pay0 port=%d buffer-size=524288 caps="application/x-rtp, media=video, clock-rate=90000, encoding-name=(string)%s, payload=96 " )'
                % (updsink_port_num, codec)
            )
            factory.set_shared(True)
            server.get_mount_points().add_factory("/debug", factory)
            print("\n *** DeepStream: Launched RTSP Streaming at rtsp://localhost:{}/debug ***\n\n".format(rtsp_port_num))

        # create an event loop and feed gstreamer bus mesages to it
        loop = GObject.MainLoop()
        bus = self.pipeline.get_bus()
        bus.add_signal_watch()
        bus.connect ("message", bus_call, loop)

        print("Starting pipeline \n")
        # start play back and listed to events
        self.pipeline.set_state(Gst.State.PLAYING)
        try:
            loop.run()
        except:
            result = False
        # cleanup
        print("Stoping pipeline\n")
        self.pipeline.set_state(Gst.State.NULL)
        return result


if __name__ == "__main__":
    task_info = {}
    task_info['task_id'] = "000000"
    task_info["algo_type"] = "non_vehicle_park"
    polygon_info = []
    polygon_item = {}
    polygon_item["points"] = '295;643;579;634;642;913;56;828'
    polygon_item["id"] = "0"
    polygon_info.append(polygon_item)
    task_info["algo_config"] = {}
    task_info['algo_config']["polygon_info"] = polygon_info
    task_info["algo_config"]["duration_time"] = 2
    souce_bin = "file:///opt/nvidia/deepstream/deepstream-6.0/samples/streams/sample_720p.h264"
    task_info["rtsp_url"] = souce_bin
    pipeline_info = prepare_pipeline_info(task_info)
    print(pipeline_info)
    alert_queue = None
    debug = True
    ds_pipeline = DeepStreamPipeline(pipeline_info, alert_queue, debug)
    ds_pipeline.pipeline_create()
    ds_pipeline.pipeline_run()