"""
OWL检测服务类
独立的检测服务，不依赖现有的模块系统，专门用于HTTP API
"""

import cv2
import time
import logging
import torch
import base64
import numpy as np
from torchvision.ops.boxes import batched_nms
from functools import lru_cache
from typing import List, Tuple, Dict, Any, Optional, Union
from transformers import Owlv2Processor, AltCLIPProcessor

from vas.common.triton_client_wrapper import TritonClientWrapper
from vas.modules.cnclip.model import CnCLIPModel


class OWLDetectionService:
    """
    OWL检测服务类
    封装OWL检测的核心逻辑，提供简单的检测接口
    """
    
    def __init__(self, config: Dict[str, Any], logger: Optional[logging.Logger] = None):
        """
        初始化OWL检测服务
        
        Args:
            config: 配置字典
            logger: 日志记录器，如果为None则创建新的
        """
        self.config = config
        self.logger = logger or self._create_logger()
        
        # 初始化模型配置
        owl_config = config["owl_detector"]
        self.text_model_config = owl_config["text_model"]
        self.visual_model_config = owl_config["visual_model"]
        self.default_policy = owl_config["default_policy"]
        
        # 初始化处理器
        self._init_processors()

        # 初始化分类相关配置
        self._init_classification_config()

        # 初始化Triton客户端
        self._init_triton_client()

        # 检查模型状态
        self._check_models_ready()
        
        # 缓存配置
        cache_config = config.get("cache", {})
        self.cache_size = cache_config.get("text_embedding_cache_size", 2048)
        
        # 模型预热
        if cache_config.get("enable_model_warmup", True):
            self._warmup_models(cache_config.get("warmup_texts", ["person", "car"]))
    
    def _create_logger(self) -> logging.Logger:
        """创建默认日志记录器"""
        logger = logging.getLogger(self.__class__.__name__)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '[%(asctime)s] - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger

    def _init_classification_config(self):
        """初始化分类相关配置"""
        try:
            # 从配置文件获取分类配置
            self.classification_config = self.config.get("classification", {
                "preprocessor": {
                    "vocab_file": "/workspace/video_analysis_server/models/AI-ModelScope/chinese-clip-vit-large-patch14/vocab.txt"
                },
                "text_model": {
                    "model_name": "chinese_clip_text",
                    "model_path": "/workspace/video_analysis_server/models/AI-ModelScope/chinese-clip-vit-large-patch14"
                },
                "visual_model": {
                    "model_name": "chinese_clip_visual",
                    "model_path": "/workspace/video_analysis_server/models/AI-ModelScope/chinese-clip-vit-large-patch14"
                }
            })

            # 初始化CnCLIP模型
            self.cnclip_model = CnCLIPModel(self.classification_config["preprocessor"]["vocab_file"])
            self.logger.info("分类配置初始化完成")

        except Exception as e:
            self.logger.error(f"分类配置初始化失败: {e}")
            raise

    def _init_processors(self):
        """初始化文本和视觉处理器"""
        try:
            self.logger.info("初始化AltCLIP文本处理器...")
            self.text_processor = AltCLIPProcessor.from_pretrained(
                self.text_model_config["model_path"], 
                local_files_only=True
            )
            
            self.logger.info("初始化OWLv2视觉处理器...")
            self.visual_processor = Owlv2Processor.from_pretrained(
                self.visual_model_config["model_path"], 
                local_files_only=True
            )
            
            self.logger.info("处理器初始化完成")
        except Exception as e:
            self.logger.error(f"处理器初始化失败: {e}")
            raise
    
    def _init_triton_client(self):
        """初始化Triton推理客户端"""
        try:
            triton_config = self.config["triton_server"]
            ip_addr = triton_config["ip"]
            grpc_port = triton_config["port"]
            use_http = triton_config.get("use_http", False)
            verbose = triton_config.get("verbose", False)
            
            self.logger.info(f"连接Triton服务器: {ip_addr}:{grpc_port}")
            self.triton_client = TritonClientWrapper(
                ip_addr, grpc_port, use_http=use_http, verbose=verbose
            )
            
            # 检查服务器状态
            server_ready = self.triton_client.is_server_ready()
            self.logger.info(f"Triton服务器状态: {server_ready}")
            
            if not server_ready:
                raise RuntimeError("Triton服务器未就绪")
                
        except Exception as e:
            self.logger.error(f"Triton客户端初始化失败: {e}")
            raise
    
    def _check_models_ready(self):
        """检查模型是否就绪"""
        # 检查文本模型
        text_model_name = self.text_model_config["model_name"]
        text_ready = self.triton_client.is_model_ready(text_model_name)
        self.logger.info(f"文本模型 {text_model_name} 状态: {text_ready}")
        
        # 检查视觉模型
        visual_model_name = self.visual_model_config["model_name"]
        visual_ready = self.triton_client.is_model_ready(visual_model_name)
        self.logger.info(f"视觉模型 {visual_model_name} 状态: {visual_ready}")
        
        if not (text_ready and visual_ready):
            self.logger.warning(f"try to load {text_model_name}, {visual_model_name}")
            self.triton_client.load_model(text_model_name)
            self.triton_client.load_model(visual_model_name)

            # 重新检查模型状态
            text_ready = self.triton_client.is_model_ready(text_model_name)
            visual_ready = self.triton_client.is_model_ready(visual_model_name)

            if not (text_ready and visual_ready):
                raise RuntimeError("模型加载失败，未就绪")

        # cnclip text模型
        cnclip_text_model_name = self.classification_config["text_model"]["model_name"]
        text_ready = self.triton_client.is_model_ready(cnclip_text_model_name)
        self.logger.info(f"cnclip 文本模型 {cnclip_text_model_name} 状态: {text_ready}")

        # cnclip visul模型
        cnclip_visual_model_name = self.classification_config["visual_model"]["model_name"]
        visual_ready = self.triton_client.is_model_ready(cnclip_visual_model_name)
        self.logger.info(f"cnclip 视觉模型 {cnclip_visual_model_name} 状态: {visual_ready}")

        if not (text_ready and visual_ready):
            self.logger.warning(f"try to load {cnclip_text_model_name}, {cnclip_visual_model_name}")
            self.triton_client.load_model(cnclip_text_model_name)
            self.triton_client.load_model(cnclip_visual_model_name)

            # 重新检查模型状态
            text_ready = self.triton_client.is_model_ready(cnclip_text_model_name)
            visual_ready = self.triton_client.is_model_ready(cnclip_visual_model_name)

            if not (text_ready and visual_ready):
                raise RuntimeError("模型加载失败，未就绪")
    
    def _warmup_models(self, warmup_texts: List[str]):
        """模型预热"""
        try:
            self.logger.info("开始模型预热...")
            for text in warmup_texts:
                self.get_text_embed(text)
            self.logger.info("模型预热完成")
        except Exception as e:
            self.logger.warning(f"模型预热失败: {e}")
    
    @lru_cache(maxsize=2048)
    def get_text_embed(self, text: str) -> np.ndarray:
        """
        获取文本嵌入
        使用LRU缓存提高性能
        
        Args:
            text: 查询文本
            
        Returns:
            文本嵌入向量
        """
        try:
            altclip_inputs = self.text_processor(
                text=text,
                images=None,
                return_tensors="np",
                padding='max_length',
                max_length=77,
                truncation=True
            )
            
            model_inputs = {
                "input_ids": altclip_inputs.input_ids.astype(np.int32),
                "attention_mask": altclip_inputs.attention_mask.astype(np.int32),
            }
            
            model_name = self.text_model_config["model_name"]
            status, infer_results = self.triton_client.infer(model_name, model_inputs)
            
            if status:
                raise RuntimeError(f"文本推理失败: {status}")
                
            return infer_results["query_embed"]
            
        except Exception as e:
            self.logger.error(f"文本嵌入生成失败: {e}")
            raise
    
    def get_visual_embeds(self, pixel_values: np.ndarray, 
                         query_embeds: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """
        获取视觉嵌入和检测结果
        
        Args:
            pixel_values: 图像像素值 [bs, channel, h, w]
            query_embeds: 查询嵌入 [bs, querynum, maxtoken]
            
        Returns:
            (target_class_logits, objectnesses, target_boxes_as_corners)
        """
        try:
            model_name = self.visual_model_config["model_name"]
            model_inputs = {
                "pixel_values": pixel_values,
                "query_embeds": query_embeds
            }
            
            status, infer_results = self.triton_client.infer(model_name, model_inputs)
            
            if status:
                raise RuntimeError(f"视觉推理失败: {status}")
            
            return (
                infer_results["target_class_logits"],
                infer_results["objectnesses"], 
                infer_results["boxes"]
            )
            
        except Exception as e:
            self.logger.error(f"视觉推理失败: {e}")
            raise

    def detect_objects(self, image: np.ndarray, query_texts: List[str],
                      threshold: Optional[float] = None,
                      nms_threshold: Optional[float] = None) -> List[Dict[str, Any]]:
        """
        检测图像中的目标对象

        Args:
            image: 输入图像 (H, W, C) RGB格式
            query_texts: 查询文本列表
            threshold: 检测阈值，如果为None则使用默认值
            nms_threshold: NMS阈值，如果为None则使用默认值

        Returns:
            检测结果列表，每个元素包含：
            {
                "bbox": [x, y, w, h],
                "score": float,
                "query_text": str,
                "query_id": int
            }
        """
        start_time = time.time()

        try:
            # 使用默认阈值如果未提供
            if threshold is None:
                threshold = self.default_policy["embeddings_query"]["threshold"]
            if nms_threshold is None:
                nms_threshold = self.default_policy["nms"]["threshold"]

            # 获取文本嵌入
            text_embeds = np.concatenate([
                self.get_text_embed(text) for text in query_texts
            ], axis=0)

            # 执行检测
            detections = self._detect_by_query_embedding(
                image, text_embeds, threshold, nms_threshold
            )

            # 格式化结果
            results = []
            for detection in detections:
                x, y, w, h, score, query_id = detection
                query_id = int(query_id)

                # 确保query_id在有效范围内
                if 0 <= query_id < len(query_texts):
                    results.append({
                        "bbox": [float(x), float(y), float(w), float(h)],
                        "score": float(score),
                        "query_text": query_texts[query_id],
                        "query_id": query_id
                    })

            processing_time = time.time() - start_time
            self.logger.info(f"检测完成，耗时: {processing_time:.3f}s，检测到 {len(results)} 个目标")

            return results

        except Exception as e:
            self.logger.error(f"目标检测失败: {e}")
            raise

    def _detect_by_query_embedding(self, image: np.ndarray, text_embeds: np.ndarray,
                                  threshold: float, nms_threshold: float) -> np.ndarray:
        """
        基于查询嵌入进行检测
        复用原有OWL模块的核心检测逻辑

        Args:
            image: 输入图像
            text_embeds: 文本嵌入
            threshold: 检测阈值
            nms_threshold: NMS阈值

        Returns:
            检测结果数组 [N, 6] (x, y, w, h, score, query_id)
        """
        # 图像预处理
        pixel_values = self.visual_processor(images=image, return_tensors="np").pixel_values

        # 视觉推理
        target_class_logits, _, target_boxes_as_corners = self.get_visual_embeds(
            pixel_values, text_embeds
        )

        # 转换为tensor进行后处理
        b, hw, q = target_class_logits.shape
        target_class_logits = torch.tensor(target_class_logits)
        target_boxes_as_corners = torch.tensor(target_boxes_as_corners).unsqueeze(2).expand(-1, -1, q, -1)

        # 阈值过滤
        target_class_sigmoids = torch.sigmoid(target_class_logits)
        target_box_nms_idxs = torch.arange(b * q).reshape(b, 1, q).expand(-1, hw, -1)[target_class_sigmoids > threshold]
        target_boxes_as_corners = target_boxes_as_corners[target_class_sigmoids > threshold]
        target_class_sigmoids = target_class_sigmoids[target_class_sigmoids > threshold]

        # 如果没有检测到任何目标，返回空数组
        if len(target_class_sigmoids) == 0:
            return np.array([]).reshape(0, 6)

        # NMS去重
        keep_inds = batched_nms(
            target_boxes_as_corners,
            target_class_sigmoids,
            idxs=target_box_nms_idxs,
            iou_threshold=nms_threshold
        )

        bboxes = target_boxes_as_corners[keep_inds]
        scores = target_class_sigmoids[keep_inds]
        query_ids = target_box_nms_idxs[keep_inds]

        # 转换为numpy数组
        scores = np.array(scores).reshape(-1, 1)
        bboxes = np.array(bboxes).reshape(-1, 4)
        query_ids = np.array(query_ids).reshape(-1, 1)

        # 坐标转换：归一化坐标 -> 像素坐标，corners -> xywh格式
        bboxes = self.de_normalize_bbox(image, bboxes)

        # 合并结果
        merged_output = np.concatenate((bboxes, scores, query_ids), axis=1).astype(np.float32)
        return merged_output

    def get_preprocess_shape(self, ori_h: int, ori_w: int, target_size: int) -> tuple:
        """
        计算预处理后的图像尺寸（保持宽高比）

        Args:
            ori_h: 原始图像高度
            ori_w: 原始图像宽度
            target_size: 目标尺寸（正方形）

        Returns:
            (preprocess_h, preprocess_w): 预处理后的高度和宽度
        """
        # 计算缩放比例，保持宽高比
        scale = min(target_size / ori_h, target_size / ori_w)

        # 计算预处理后的尺寸
        preprocess_h = int(ori_h * scale)
        preprocess_w = int(ori_w * scale)

        return preprocess_h, preprocess_w

    def de_normalize_bbox(self, img: np.ndarray, bboxes: np.ndarray) -> np.ndarray:
        """
        将归一化的边界框坐标转换回原始图像坐标

        Args:
            img: 原始图像
            bboxes: 归一化的边界框坐标 [N, 4] (x1, y1, x2, y2)

        Returns:
            转换后的边界框坐标 [N, 4] (x, y, w, h)
        """
        ori_h, ori_w, _ = img.shape
        # OWL模型的目标尺寸是1008x1008
        target_size = (1008, 1008)

        preprocess_h, preprocess_w = self.get_preprocess_shape(ori_h, ori_w, target_size[0])

        # 计算缩放比例
        scale_h = ori_h / preprocess_h
        scale_w = ori_w / preprocess_w
        scale = scale_h if scale_h > scale_w else scale_w

        # 将归一化坐标转换为目标尺寸坐标
        bboxes[:, 0] = bboxes[:, 0] * target_size[1]  # x1
        bboxes[:, 1] = bboxes[:, 1] * target_size[0]  # y1
        bboxes[:, 2] = bboxes[:, 2] * target_size[1]  # x2
        bboxes[:, 3] = bboxes[:, 3] * target_size[0]  # y2

        # 转换为xywh格式
        bboxes[:, 2] = bboxes[:, 2] - bboxes[:, 0]  # w = x2 - x1
        bboxes[:, 3] = bboxes[:, 3] - bboxes[:, 1]  # h = y2 - y1

        # 缩放到原始图像尺寸
        bboxes[:, 0] = bboxes[:, 0] * scale  # x
        bboxes[:, 1] = bboxes[:, 1] * scale  # y
        bboxes[:, 2] = bboxes[:, 2] * scale  # w
        bboxes[:, 3] = bboxes[:, 3] * scale  # h

        return bboxes

    def validate_image(self, image: np.ndarray) -> bool:
        """
        验证图像是否有效

        Args:
            image: 输入图像

        Returns:
            是否有效
        """
        if image is None:
            return False

        if len(image.shape) != 3:
            return False

        if image.shape[2] != 3:
            return False

        if image.size == 0:
            return False

        return True

    def get_service_info(self) -> Dict[str, Any]:
        """
        获取服务信息

        Returns:
            服务信息字典
        """
        return {
            "service_name": "OWL Detection & Classification Service",
            "features": ["object_detection", "zero_shot_classification"],
            "text_model": self.text_model_config["model_name"],
            "visual_model": self.visual_model_config["model_name"],
            "cache_size": self.cache_size,
            "default_threshold": self.default_policy["embeddings_query"]["threshold"],
            "default_nms_threshold": self.default_policy["nms"]["threshold"]
        }

    def classify_regions(self, image: np.ndarray, bboxes: List[Dict[str, Any]],
                        positive_texts: List[str], negative_texts: List[str] = None,
                        threshold: float = 0.15) -> List[Dict[str, Any]]:
        """
        对图像中的指定区域进行零样本分类

        Args:
            image: 输入图像 (H, W, C) RGB格式
            bboxes: 边界框列表，格式: [{"id": "A", "x": 100, "y": 100, "w": 200, "h": 200}]
            positive_texts: 正例文本列表
            negative_texts: 负例文本列表，可为空
            threshold: 预警阈值

        Returns:
            分类结果列表
        """
        start_time = time.time()

        try:
            if negative_texts is None:
                negative_texts = []

            results = []

            for bbox_info in bboxes:
                bbox_id = bbox_info["id"]
                x, y, w, h = bbox_info["x"], bbox_info["y"], bbox_info["w"], bbox_info["h"]

                # 裁剪图像区域
                cropped_image = self._crop_image_region(image, x, y, w, h)

                # 对裁剪区域进行分类
                classifications = []

                # 正例分类
                max_pos_conf = -100
                for text in positive_texts:
                    score = self._zeroshot_classify_single(text, cropped_image)
                    classifications.append({
                        "text": text,
                        "type": "positive",
                        "score": float(score)
                    })
                    max_pos_conf = max(max_pos_conf, score)

                # 负例分类
                max_neg_conf = -100
                for text in negative_texts:
                    score = self._zeroshot_classify_single(text, cropped_image)
                    classifications.append({
                        "text": text,
                        "type": "negative",
                        "score": float(score)
                    })
                    max_neg_conf = max(max_neg_conf, score)

                # 判断是否预警
                alert, alert_reason = self._determine_alert_cnclip(
                    max_pos_conf, max_neg_conf, threshold
                )

                results.append({
                    "bbox_id": bbox_id,
                    "bbox": {"x": x, "y": y, "w": w, "h": h},
                    "classifications": classifications,
                    "alert": alert,
                    "alert_reason": alert_reason
                })

            processing_time = time.time() - start_time
            self.logger.info(f"分类完成，耗时: {processing_time:.3f}s，处理了 {len(bboxes)} 个区域")

            return results

        except Exception as e:
            self.logger.error(f"区域分类失败: {e}")
            raise

    def _crop_image_region(self, image: np.ndarray, x: int, y: int, w: int, h: int) -> np.ndarray:
        """裁剪图像区域"""
        img_h, img_w = image.shape[:2]

        # 确保坐标在图像范围内
        x = max(0, min(x, img_w - 1))
        y = max(0, min(y, img_h - 1))
        x2 = max(x + 1, min(x + w, img_w))
        y2 = max(y + 1, min(y + h, img_h))

        return image[y:y2, x:x2]

    def _zeroshot_classify_single(self, text: str, image: np.ndarray) -> float:
        """
        对单个图像区域进行零样本分类
        参考classification_module.py的zeroshot_classify方法
        """
        try:
            # 获取文本特征
            text_feature = self._extract_text_feature_cnclip(text)

            # 获取图像特征
            image_feature = self._extract_image_feature_cnclip(image)

            # 计算余弦相似度
            cos_sim = np.dot(text_feature, image_feature.T) / (
                np.linalg.norm(text_feature) * np.linalg.norm(image_feature)
            )

            return float(cos_sim[0][0])

        except Exception as e:
            self.logger.error(f"分类失败: {e}")
            return 0.0

    def _extract_text_feature_cnclip(self, text: str, need_norm: bool = True) -> np.ndarray:
        """提取文本特征，使用CnCLIP模型"""
        try:
            # 文本预处理
            preprocess_text = self.cnclip_model.text_preprocess(text)
            preprocess_text = preprocess_text.astype(np.int64)

            # 推理
            model_name = self.classification_config["text_model"]["model_name"]
            model_inputs = {"text": preprocess_text}

            status, model_outputs = self.triton_client.infer(model_name, model_inputs)
            if status:
                raise RuntimeError(f"文本特征提取失败 {status}")

            embed = model_outputs["unnorm_text_features"]

            if need_norm:
                embed = embed / np.linalg.norm(embed)

            return embed

        except Exception as e:
            self.logger.error(f"文本特征提取失败: {e}")
            raise

    def _extract_image_feature_cnclip(self, image: np.ndarray, need_norm: bool = True) -> np.ndarray:
        """提取图像特征，使用CnCLIP模型"""
        try:
            # 图像预处理
            preprocess_image = self.cnclip_model.image_preprocess(image)

            # 推理
            model_name = self.classification_config["visual_model"]["model_name"]
            model_inputs = {"image": preprocess_image}

            status, model_outputs = self.triton_client.infer(model_name, model_inputs)
            if status:
                raise RuntimeError(f"图像特征提取失败 {status}")

            embed = model_outputs["unnorm_image_features"]

            if need_norm:
                embed = embed / np.linalg.norm(embed)

            return embed

        except Exception as e:
            self.logger.error(f"图像特征提取失败: {e}")
            raise

    def _determine_alert_cnclip(self, max_pos_conf: float, max_neg_conf: float,
                               threshold: float) -> tuple:
        """判断是否需要预警（CnCLIP版本）"""
        # 检查正例是否超过阈值
        if max_pos_conf < threshold:
            return False, f"正例标签最高分数{max_pos_conf:.3f}未超过阈值{threshold}"

        # 检查正例是否超过负例
        if max_neg_conf > -100 and max_pos_conf <= max_neg_conf:
            return False, f"正例标签最高分数{max_pos_conf:.3f}未超过负例标签最高分数{max_neg_conf:.3f}"

        # 触发预警
        if max_neg_conf > -100:
            return True, f"正例标签最高分数{max_pos_conf:.3f}超过阈值{threshold}，且大于负例标签最高分数{max_neg_conf:.3f}"
        else:
            return True, f"正例标签最高分数{max_pos_conf:.3f}超过阈值{threshold}，无负例竞争"

    def get_regions_features(self, image: np.ndarray, bboxes: List[Dict[str, Any]]) -> List[Dict[str, Any]]:

        # 图像特征列表
        feature_infos = []
        for bbox_info in bboxes:
            bbox_id = bbox_info["id"]
            x, y, w, h = bbox_info["x"], bbox_info["y"], bbox_info["w"], bbox_info["h"]
            # 截取bbox子图
            bbox_image = self._crop_image_region(image, x, y, w, h)

            # 获取图像特征
            norm_feature = self._extract_image_feature_cnclip(bbox_image)

            # 转为字符串
            feature_bytes = norm_feature.tobytes()
            base64_str = base64.b64encode(feature_bytes).decode('utf-8')
            bbox_info["feature"] = base64_str
            feature_infos.append(bbox_info)

        return feature_infos

    def _next_version(self, model_dir: str) -> int:
        if not os.path.isdir(model_dir):
            return 1
        versions = [int(n) for n in os.listdir(model_dir)
                    if n.isdigit() and os.path.isdir(os.path.join(model_dir, n))]
        return max(versions, default=0) + 1

    def _prepare_tiny_model(self,
        tar_source: Union[str, bytes],
        repo_root: str,
        model_name: str = "my_model") -> str:
        """
        事务化：成功返回版本目录；失败时仅删除本次新建目录/文件。
        """
        # ---------- 1. 准备临时 tar ----------
        tar_path = None
        if isinstance(tar_source, bytes):
            fd, tar_path = tempfile.mkstemp(suffix=".tar")
            try:
                with os.fdopen(fd, "wb") as f:
                    f.write(tar_source)
            except Exception:
                os.close(fd)
                raise
        else:
            tar_path = str(tar_source)

        # ---------- 2. 计算目标路径 ----------
        model_dir = os.path.join(repo_root, model_name)
        version = _next_version(model_dir)
        version_dir = os.path.join(model_dir, str(version))

        try:
            # ---------- 3. 创建版本目录 ----------
            os.makedirs(version_dir, exist_ok=False)

            # ---------- 4. 解压 ----------
            with tarfile.open(tar_path, "r:*") as tf:
                tf.extractall(version_dir)

            # ---------- 5. 把 model.onnx / model.pt 提到根 ----------
            for fname in ("model.onnx", "model.pt"):
                nested = os.path.join(version_dir, fname)
                if os.path.isfile(nested):
                    continue
                for root, _, files in os.walk(version_dir):
                    if fname in files:
                        shutil.move(os.path.join(root, fname), os.path.join(version_dir, fname))
                        break

            # ---------- 6. 成功返回 ----------
            return version_dir

        except Exception:
            # ---------- 7. 回滚：仅删除本次新建目录 ----------
            if os.path.isdir(version_dir):
                shutil.rmtree(version_dir, ignore_errors=True)
            raise
        finally:
            # ---------- 8. 清理临时 tar ----------
            if isinstance(tar_source, bytes) and tar_path and os.path.isfile(tar_path):
                try:
                    os.remove(tar_path)
                except OSError:
                    pass

