"""
服务注册模块 - 用于向调度器注册推理服务
"""
import os
import requests
import threading
import time
from typing import Optional, Dict, Any
from vas.common.logger_tools import get_logger

logger = get_logger(__name__)


class ServiceRegistry:
    """服务注册管理器"""
    
    def __init__(self, config: Dict[str, Any], server_host: str, server_port: int):
        self.config = config
        self.server_host = server_host
        self.server_port = server_port
        self.registered = False
        self.registration_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
    
    def _build_registration_data(self) -> Dict[str, Any]:
        """构建服务注册数据"""
        service_config = self.config.get('service', {})
        
        # 从环境变量获取配置（优先级更高）
        service_name = os.getenv('SERVICE_NAME', service_config.get('name', 'inference-service-1'))
        max_quota = int(os.getenv('MAX_QUOTA', service_config.get('max_quota', 10)))
        region = os.getenv('REGION', service_config.get('region', 'default'))
        gpu_type = os.getenv('GPU_TYPE', service_config.get('gpu_type', 'A10'))
        
        # 构建服务URL - 用于scheduler访问的外部地址
        # 注意：这里的service_host是给scheduler访问用的，不是HTTP服务器绑定地址
        service_host = os.getenv('SERVICE_HOST')
        if not service_host:
            # 在Docker环境中，优先使用HOST_IP环境变量
            service_host = os.getenv('HOST_IP')
            if not service_host:
                # 如果都没有设置，尝试自动获取（但在Docker中可能不准确）
                from vas.common.server_args import get_host_ip
                service_host = get_host_ip()
                logger.warning(f"未设置SERVICE_HOST或HOST_IP环境变量，自动获取到: {service_host}")
                logger.warning("在Docker环境中建议手动设置SERVICE_HOST环境变量为宿主机IP")

        base_url = f"http://{service_host}:{self.server_port}"
        logger.info(f"服务注册URL: {base_url} (用于scheduler访问)")
        
        # 获取算法编排配置
        algorithm_orchestration = service_config.get('algorithm_orchestration', {
            "algorithm_chain": [
                {
                    "algorithm_id": "yolov8_detection",
                    "algorithm_name": "YOLOv8目标检测",
                    "algorithm_type": "DETECTION",
                    "order": 1
                }
            ]
        })
        
        return {
            "serviceName": service_name,
            "baseUrl": base_url,
            "algorithmOrchestration": algorithm_orchestration,
            "maxQuota": max_quota,
            "region": region,
            "gpuType": gpu_type
        }
    
    def register_service(self) -> bool:
        """注册服务到调度器"""
        try:
            scheduler_config = self.config.get('scheduler', {})
            scheduler_url = os.getenv('SCHEDULER_URL', scheduler_config.get('url', 'http://localhost:8080'))
            registration_url = f"{scheduler_url}/api/v1/services/register"
            
            registration_data = self._build_registration_data()
            
            logger.info(f"正在注册服务到调度器: {registration_url}")
            logger.debug(f"注册数据: {registration_data}")
            
            response = requests.post(
                registration_url,
                json=registration_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                self.registered = True
                logger.info(f"服务注册成功: {registration_data['serviceName']}")
                logger.debug(f"注册响应: {response.text}")
                return True
            else:
                logger.error(f"服务注册失败: HTTP {response.status_code}, {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"服务注册网络错误: {e}")
            return False
        except Exception as e:
            logger.error(f"服务注册异常: {e}")
            return False
    
    def _health_check(self) -> bool:
        """检查与调度器的连接状态"""
        try:
            scheduler_config = self.config.get('scheduler', {})
            scheduler_url = os.getenv('SCHEDULER_URL', scheduler_config.get('url', 'http://localhost:8080'))
            health_url = f"{scheduler_url}/actuator/health"
            
            response = requests.get(health_url, timeout=5)
            return response.status_code == 200
                
        except Exception as e:
            logger.warning(f"调度器连接检查失败: {e}")
            return False
    
    def _registration_loop(self):
        """服务注册循环（在独立线程中运行）"""
        scheduler_config = self.config.get('scheduler', {})
        retry_interval = scheduler_config.get('registration_retry_interval', 30)
        
        while not self.stop_event.is_set():
            try:
                if not self.registered:
                    logger.info("尝试注册服务...")
                    success = self.register_service()
                    
                    if not success:
                        logger.warning(f"服务注册失败，{retry_interval}秒后重试...")
                        self.stop_event.wait(retry_interval)
                    else:
                        # 注册成功后，等待更长时间再进行下次检查
                        self.stop_event.wait(retry_interval * 2)
                else:
                    # 已注册，定期检查连接状态
                    if not self._health_check():
                        logger.warning("调度器健康检查失败，标记为未注册状态")
                        self.registered = False
                    
                    self.stop_event.wait(retry_interval)
                    
            except Exception as e:
                logger.error(f"注册循环异常: {e}")
                self.stop_event.wait(retry_interval)
    
    def start(self):
        """启动服务注册"""
        if self.registration_thread is None or not self.registration_thread.is_alive():
            self.stop_event.clear()
            self.registration_thread = threading.Thread(
                target=self._registration_loop,
                name="ServiceRegistration",
                daemon=True
            )
            self.registration_thread.start()
            logger.info("服务注册线程已启动")
    
    def stop(self):
        """停止服务注册"""
        if self.registration_thread and self.registration_thread.is_alive():
            self.stop_event.set()
            self.registration_thread.join(timeout=5)
            logger.info("服务注册线程已停止")


# 全局服务注册实例
_service_registry: Optional[ServiceRegistry] = None


def init_service_registry(config: Dict[str, Any], server_host: str, server_port: int) -> ServiceRegistry:
    """初始化服务注册器"""
    global _service_registry
    _service_registry = ServiceRegistry(config, server_host, server_port)
    return _service_registry


def get_service_registry() -> Optional[ServiceRegistry]:
    """获取服务注册器实例"""
    return _service_registry


def start_service_registration(config: Dict[str, Any], server_host: str, server_port: int):
    """启动服务注册"""
    registry = init_service_registry(config, server_host, server_port)
    registry.start()
    return registry


def stop_service_registration():
    """停止服务注册"""
    if _service_registry:
        _service_registry.stop()
