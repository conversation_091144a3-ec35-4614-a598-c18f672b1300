import numpy as np
import tritonclient.http as httpclient
import tritonclient.grpc as grpcclient


class TritonClientWrapper:

    def __init__(self, ip_addr, grpc_port, http_port=8080, use_http=False, verbose=False):
        self.http_client = None
        self.grpc_client = None
        if use_http:
            http_url = ip_addr + ":" + str(http_port)
            self.http_client = httpclient.InferenceServerClient(url=http_url, verbose=verbose, network_timeout=3)
        else:
            grpc_url = ip_addr + ":" + str(grpc_port)
            self.grpc_client = grpcclient.InferenceServerClient(url=grpc_url, verbose=verbose)


    def __del__(self):
        if self.http_client is not None:
            self.http_client.close()
        if self.grpc_client is not None:
            self.grpc_client.close()


    def is_server_alive(self, headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.is_server_live(headers=headers, query_params=query_params)
            else:
                return self.grpc_client.is_server_live(headers=headers)
        except Exception as e:
            return str(e)


    def is_server_ready(self, headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.is_server_ready(headers=headers, query_params=query_params)
            else:
                return self.grpc_client.is_server_ready(headers=headers)
        except Exception as e:
            return str(e)


    def is_model_ready(self, model_name, model_version="", headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.is_model_ready(model_name=model_name, model_version=model_version, 
                    headers=headers, query_params=query_params)
            else:
                return self.grpc_client.is_model_ready(model_name=model_name, model_version=model_version, 
                    headers=headers)
        except Exception as e:
            return str(e)


    def get_server_metadata(self, headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.get_server_metadata(headers=headers, query_params=query_params)
            else:
                return self.grpc_client.get_server_metadata(headers=headers, as_json=True)
        except Exception as e:
            return str(e)


    def get_model_metadata(self, model_name, model_version="", headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.get_model_metadata(model_name=model_name, model_version=model_version, 
                    headers=headers, query_params=query_params)
            else:
                return self.grpc_client.get_model_metadata(model_name=model_name, model_version=model_version, 
                    headers=headers, as_json=True)
        except Exception as e:
            return str(e)


    def get_model_config(self, model_name, model_version="", headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.get_model_config(model_name=model_name, model_version=model_version, 
                    headers=headers, query_params=query_params)
            else:
                return self.grpc_client.get_model_config(model_name=model_name, model_version=model_version, 
                    headers=headers, as_json=True)
        except Exception as e:
            return str(e)


    def load_model(self, model_name, headers=None, query_params=None, config=None, files=None, use_http=False):
        try:
            if use_http:
                self.http_client.load_model(model_name=model_name, headers=headers, 
                    query_params=query_params, config=config, files=files)
            else:
                self.grpc_client.load_model(model_name=model_name, headers=headers, 
                    config=config, files=files)
            return True
        except Exception as e:
            return str(e)


    def unload_model(self, model_name, headers=None, query_params=None, unload_dependents=False, use_http=False):
        try:
            if use_http:
                return self.http_client.unload_model(model_name=model_name, headers=headers, 
                    query_params=query_params, unload_dependents=unload_dependents)
            else:
                return self.grpc_client.unload_model(model_name=model_name, headers=headers, 
                    unload_dependents=unload_dependents)
        except Exception as e:
            return str(e)


    def get_inference_statistics(self, model_name="", model_version="", headers=None, query_params=None, use_http=False):
        try:
            if use_http:
                return self.http_client.get_inference_statistics(model_name=model_name, model_version=model_version, 
                    headers=headers, query_params=query_params)
            else:
                return self.grpc_client.get_inference_statistics(model_name=model_name, model_version=model_version, 
                    headers=headers, as_json=True)
        except Exception as e:
            return str(e)


    def convertNpTypeToTritonType(self, np_type):
        mapping = {
            np.dtype("float32"): "FP32",
            np.dtype("float16"): "FP16",
            np.dtype("float64"): "FP64",
            np.dtype("int8"): "INT8",
            np.dtype("int16"): "INT16",
            np.dtype("int32"): "INT32",
            np.dtype("int64"): "INT64",
            np.dtype("uint8"): "UINT8",
            np.dtype("uint16"): "UINT16",
            np.dtype("uint32"): "UINT32",
            np.dtype("uint64"): "UINT64",
            np.dtype("bool"): "BOOL",
            np.dtype("str"): "BYTES"
        }
        return mapping.get(np_type, "UNKNOWN")


    def http_infer(self, model_name, model_inputs, model_outputs, model_version="", headers=None):
        # set inputs
        inputs = []
        for name in model_inputs:
            np_data = model_inputs[name]
            type = self.convertNpTypeToTritonType(np_data.dtype)
            input = httpclient.InferInput(name, np_data.shape, type)
            input.set_data_from_numpy(np_data, binary_data=True)
            inputs.append(input)

        # set outputs
        outputs = None
        if model_outputs is not None:
            outputs = []
            for index in range(len(model_outputs)):
                name = model_outputs[index]
                output = httpclient.InferRequestedOutput(name, binary_data=True)
                outputs.append(output)

        # infer
        infer_results = self.http_client.infer(model_name=model_name, model_version=model_version, inputs=inputs, 
            outputs=outputs, headers=None)

        # parse results
        results = {}
        expect_outputs = model_outputs
        if expect_outputs is None:
            expect_outputs = [out.name() for out in infer_results.get_response().outputs]
        for index in range(len(expect_outputs)):
            name = expect_outputs[index]
            results[name] = infer_results.as_numpy(name)
        return results


    def grpc_infer(self, model_name, model_inputs, model_outputs, model_version="", headers=None):
        # set inputs
        inputs = []
        for name in model_inputs:
            np_data = model_inputs[name]
            type = self.convertNpTypeToTritonType(np_data.dtype)
            input = grpcclient.InferInput(name, np_data.shape, type)
            input.set_data_from_numpy(np_data)
            inputs.append(input)

        # set outputs
        outputs = None
        if model_outputs is not None:
            outputs = []
            for index in range(len(model_outputs)):
                name = model_outputs[index]
                output = grpcclient.InferRequestedOutput(name)
                outputs.append(output)

        # infer
        infer_results = self.grpc_client.infer(model_name=model_name, model_version=model_version, inputs=inputs, 
            outputs=outputs, headers=None)

        # get output names
        output_names = model_outputs
        if model_outputs is None or 0 == len(model_outputs):
            output_names = [out.name for out in infer_results.get_response().outputs]

        # get outputs
        results = {}
        for index in range(len(output_names)):
            name = output_names[index]
            results[name] = infer_results.as_numpy(name)
        return results


    def infer(self, model_name, model_inputs, model_outputs=None, model_version="", headers=None, use_http=False):
        try:
            if use_http:
                results = self.http_infer(model_name, model_inputs, model_outputs, model_version, header)
            else:
                results = self.grpc_infer(model_name, model_inputs, model_outputs, model_version, headers)
            return "", results
        except Exception as e:
            return str(e), {}


    def get_inputs(self, model_name, model_version=""):
        metadata = self.grpc_client.get_model_metadata(model_name, model_version)
        inputs = []
        for inp in metadata.inputs:
            input_meta = {}
            input_meta["name"] = inp.name
            input_meta["shape"] = list(inp.shape)
            input_meta["type"] = inp.datatype
            inputs.append(input_meta)
        return inputs


    def get_outputs(self, model_name, model_version=""):
        metadata = self.grpc_client.get_model_metadata(model_name, model_version)
        outputs = []
        for outp in metadata.outputs:
            output_meta = {}
            output_meta["name"] = outp.name
            output_meta["shape"] = list(outp.shape)
            output_meta["type"] = outp.datatype
            outputs.append(output_meta)
        return outputs
