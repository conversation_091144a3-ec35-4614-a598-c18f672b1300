# Copyright 2023-2024 SGLang Team
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# ==============================================================================
"""The arguments of the server."""

import os
import json
import argparse
import dataclasses
import logging
import socket
import random
import tempfile
from typing import List, Literal, Optional, Union

logger = logging.getLogger(__name__)


def get_host_ip() -> str:
    """
    获取用于服务注册的IP地址

    注意：这个函数主要用于服务注册，获取scheduler可以访问的IP地址
    在Docker环境中，建议通过环境变量HOST_IP或SERVICE_HOST手动指定宿主机IP

    Returns:
        str: 用于服务注册的IP地址
    """
    try:
        # 方法1: 尝试通过环境变量获取（推荐方式）
        host_ip = os.getenv('HOST_IP')
        if host_ip:
            logger.info(f"从环境变量HOST_IP获取到IP: {host_ip}")
            return host_ip

        # 检测是否在Docker容器中
        is_docker = os.path.exists('/.dockerenv') or os.getenv('DOCKER_CONTAINER') == 'true'
        if is_docker:
            logger.warning("检测到Docker环境，但未设置HOST_IP环境变量")
            logger.warning("建议设置HOST_IP环境变量为宿主机IP，例如: docker run -e HOST_IP=*************")

        # 方法2: 尝试连接外部地址来获取本机IP
        with socket.socket(socket.AF_INET, socket.SOCK_DGRAM) as s:
            # 连接到一个外部地址（不会实际发送数据）
            s.connect(("*******", 80))
            host_ip = s.getsockname()[0]
            if is_docker and host_ip.startswith('172.'):
                logger.warning(f"在Docker环境中获取到容器IP: {host_ip}，这可能不是scheduler可访问的地址")
            else:
                logger.info(f"通过socket连接获取到IP: {host_ip}")
            return host_ip
    except Exception as e:
        logger.warning(f"获取IP失败: {e}")

    # 方法3: 获取所有网络接口，选择非回环地址
    try:
        import subprocess
        result = subprocess.run(['hostname', '-I'], capture_output=True, text=True, timeout=5)
        if result.returncode == 0:
            ips = result.stdout.strip().split()
            for ip in ips:
                if not ip.startswith('127.') and not ip.startswith('169.254.'):
                    logger.info(f"通过hostname命令获取到IP: {ip}")
                    return ip
    except Exception as e:
        logger.warning(f"通过hostname命令获取IP失败: {e}")

    # 方法4: 使用socket.gethostbyname
    try:
        hostname = socket.gethostname()
        host_ip = socket.gethostbyname(hostname)
        if not host_ip.startswith('127.'):
            logger.info(f"通过gethostbyname获取到IP: {host_ip}")
            return host_ip
    except Exception as e:
        logger.warning(f"通过gethostbyname获取IP失败: {e}")

    # 如果所有方法都失败，返回localhost作为备选
    logger.warning("无法获取合适的IP地址，使用localhost作为备选")
    logger.warning("请设置HOST_IP或SERVICE_HOST环境变量指定正确的IP地址")
    return "localhost"


@dataclasses.dataclass
class ServerArgs:
    # logger config
    log_path: str = ""
    log_level: str = "info"

    # module config
    config_path: str = ""
    queue_size: int = 5

    # http server config
    host: str = "0.0.0.0"  # 默认绑定所有接口，允许外部访问
    port: int = 9001

    # config for debug use
    mock_plasma: bool = False


    @staticmethod
    def add_cli_args(parser: argparse.ArgumentParser):
        # logger args
        parser.add_argument(
            "--log-path",
            type=str,
            default=ServerArgs.log_path,
            help="The logging file path of all loggers.",
        )
        parser.add_argument(
            "--log-level",
            type=str,
            default=ServerArgs.log_level,
            help="The logging level of all loggers.",
        )

        # module args
        parser.add_argument(
            "--config-path",
            type=str,
            default=ServerArgs.config_path,
            help="The path of the config file.",
            required=True,
        )
        parser.add_argument(
            "--queue-size",
            type=int,
            default=ServerArgs.queue_size,
            help="The queue size for all modules.",
        )

        # http server args
        parser.add_argument(
            "--host",
            type=str,
            default=ServerArgs.host,
            help="The host of the HTTP server.",
        )
        parser.add_argument(
            "--port",
            type=int,
            default=ServerArgs.port,
            help="The port of the HTTP server.",
        )

        # debug args
        parser.add_argument(
            "--mock-plasma",
            type=bool,
            default=ServerArgs.mock_plasma,
            help="The flag for plasma mock.",
        )
        

    @classmethod
    def from_cli_args(cls, args: argparse.Namespace):
        attrs = [attr.name for attr in dataclasses.fields(cls)]
        return cls(**{attr: getattr(args, attr) for attr in attrs})


def prepare_server_args(argv: List[str]) -> ServerArgs:
    """
    Prepare the server arguments from the command line arguments.

    Args:
        args: The command line arguments. Typically, it should be `sys.argv[1:]`
            to ensure compatibility with `parse_args` when no arguments are passed.

    Returns:
        The server arguments.
    """
    parser = argparse.ArgumentParser()
    ServerArgs.add_cli_args(parser)
    raw_args = parser.parse_args(argv)
    server_args = ServerArgs.from_cli_args(raw_args)
    return server_args


ZMQ_TCP_PORT_DELTA = 233


def is_port_available(port):
    """Return whether a port is available."""
    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
        try:
            s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            s.bind(("", port))
            s.listen(1)
            return True
        except socket.error:
            return False
        except OverflowError:
            return False


@dataclasses.dataclass
class PortArgs:
    # The ipc filename for tokenizer to receive inputs from detokenizer (zmq)
    tokenizer_ipc_name: str
    # The ipc filename for scheduler (rank 0) to receive inputs from tokenizer (zmq)
    scheduler_input_ipc_name: str
    # The ipc filename for detokenizer to receive inputs from scheduler (zmq)
    detokenizer_ipc_name: str

    # The ipc filename for rpc call between Engine and Scheduler
    rpc_ipc_name: str

    # The ipc filename for Scheduler to send metrics
    metrics_ipc_name: str

    @staticmethod
    def init_new(server_args, dp_rank: Optional[int] = None) -> "PortArgs":
        if server_args.nccl_port is None:
            port = server_args.port + random.randint(100, 1000)
            while True:
                if is_port_available(port):
                    break
                if port < 60000:
                    port += 42
                else:
                    port -= 43
        else:
            port = server_args.nccl_port

        if not server_args.enable_dp_attention:
            # Normal case, use IPC within a single node
            return PortArgs(
                tokenizer_ipc_name=f"ipc://{tempfile.NamedTemporaryFile(delete=False).name}",
                scheduler_input_ipc_name=f"ipc://{tempfile.NamedTemporaryFile(delete=False).name}",
                detokenizer_ipc_name=f"ipc://{tempfile.NamedTemporaryFile(delete=False).name}",
                nccl_port=port,
                rpc_ipc_name=f"ipc://{tempfile.NamedTemporaryFile(delete=False).name}",
                metrics_ipc_name=f"ipc://{tempfile.NamedTemporaryFile(delete=False).name}",
            )
        else:
            # DP attention. Use TCP + port to handle both single-node and multi-node.
            if server_args.nnodes == 1 and server_args.dist_init_addr is None:
                dist_init_addr = ("127.0.0.1", server_args.port + ZMQ_TCP_PORT_DELTA)
            elif server_args.dist_init_addr.startswith("["):  # ipv6 address
                port_num, host = configure_ipv6(server_args.dist_init_addr)
                dist_init_addr = (host, str(port_num))
            else:
                dist_init_addr = server_args.dist_init_addr.split(":")

            assert (
                len(dist_init_addr) == 2
            ), "please provide --dist-init-addr as host:port of head node"

            dist_init_host, dist_init_port = dist_init_addr
            port_base = int(dist_init_port) + 1
            if dp_rank is None:
                # TokenizerManager to DataParallelController
                scheduler_input_port = port_base + 4
            else:
                scheduler_input_port = port_base + 4 + 1 + dp_rank

            return PortArgs(
                tokenizer_ipc_name=f"tcp://{dist_init_host}:{port_base}",
                scheduler_input_ipc_name=f"tcp://{dist_init_host}:{scheduler_input_port}",
                detokenizer_ipc_name=f"tcp://{dist_init_host}:{port_base + 1}",
                nccl_port=port,
                rpc_ipc_name=f"tcp://{dist_init_host}:{port_base + 2}",
                metrics_ipc_name=f"tcp://{dist_init_host}:{port_base + 3}",
            )

