import os
import logging
import numpy as np
from logging.handlers import RotatingFileHandler


def config_logger(logger_name: str, log_path: str = "", log_level: str = "info"):
    logger = logging.getLogger(logger_name)
    if logger.handlers:
        return logger
    format = f"[%(asctime)s] - [pid:%(process)d] - [tid:%(thread)d] - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s %(message)s"
    formatter = logging.Formatter(format)

    if "" == log_path:
        log_path = "/tmp"
    log_file = logger_name + ".log"
    if not os.path.isdir(log_path) or not os.path.exists(log_path):
        os.makedirs(log_path, exist_ok=True)
    log_full_path = os.path.join(log_path, log_file)

    log_level_map = {"info": logging.INFO, "debug": logging.DEBUG, "warning": logging.WARNING, "error": logging.ERROR}
    if log_level not in log_level_map:
        log_level_val = logging.INFO
    else:
        log_level_val = log_level_map[log_level]

    max_bytes = 1 * 1024 * 1024
    backup_count = 3
    handler = RotatingFileHandler(log_full_path, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8")
    handler.setFormatter(formatter)
    logger.setLevel(log_level_val)
    logger.addHandler(handler)
    return logger


def get_logger(logger_name: str, log_path: str = "", log_level: str = "info"):

    logger = logging.getLogger(logger_name)
    if logger.handlers:
        return logger
    format = f"[%(asctime)s] - [pid:%(process)d] - [tid:%(thread)d] - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s %(message)s"
    formatter = logging.Formatter(format)

    if "" == log_path:
        log_path = "/tmp"
    log_file = logger_name + ".log"
    if not os.path.isdir(log_path) or not os.path.exists(log_path):
        os.makedirs(log_path, exist_ok=True)
    log_full_path = os.path.join(log_path, log_file)

    log_level_map = {"info": logging.INFO, "debug": logging.DEBUG, "warning": logging.WARNING, "error": logging.ERROR}
    if log_level not in log_level_map:
        log_level_val = logging.INFO
    else:
        log_level_val = log_level_map[log_level]

    max_bytes = 10 * 1024 * 1024
    backup_count = 3
    handler = RotatingFileHandler(log_full_path, maxBytes=max_bytes, backupCount=backup_count, encoding="utf-8")
    handler.setFormatter(formatter)
    handler.setLevel(log_level_val)
    logger.addHandler(handler)
    return logger