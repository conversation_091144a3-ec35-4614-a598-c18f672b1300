import numpy as np
from typing import List
import pyarrow.plasma as plasma


class PlasmaClientWrapper:
    """Shared memory wrapper using ``pyarrow`` Plasma.

    This public class is an example implementation of the
    :py:mod:`IPCWrapper <mosec.ipc.IPCWrapper>`.
    It utilizes ``pyarrow.plasma`` as the in-memory object store for
    potentially more efficient data transfer.
    """

    def __init__(self, shm_path: str, mock_flag: bool) -> None:
        """Initialize the IPC Wrapper as a plasma client.

        Args:
            shm_path (str): path of the plasma server.
        """
        self.mock_flag = mock_flag
        if False == mock_flag:
            self.client = plasma.connect(shm_path)


    def _put_plasma_list(self, data: List[bytes]) -> List[plasma.ObjectID]:
        """Batch put into plasma memory store."""
        return [self.client.put(x) for x in data]
    

    def _put_plasma_item(self, data: bytes) -> plasma.ObjectID:
        """Single item put into plasma memory store."""
        return self._put_plasma_list([data])[0]


    def _get_plasma_list(self, object_ids: List[plasma.ObjectID]) -> List[bytes]:
        """Batch get from plasma memory store."""
        objects = self.client.get(object_ids)
        return objects
    

    def _get_plasma_item(self, object_id: plasma.ObjectID) -> bytes:
        """Single item get from plasma memory store."""
        return self._get_plasma_list([object_id])[0]


    def put(self, data: List[bytes]) -> List[bytes]:
        """Save datas to the plasma memory store and return the IDs."""
        if self.mock_flag:
            return data
        object_ids = self._put_plasma_list(data)
        return [id.binary() for id in object_ids]


    def put_item(self, data: bytes) -> bytes:
        """Save data to the plasma memory store and return the ID."""
        if self.mock_flag:
            return data
        object_id = self._put_plasma_item([data])
        return object_id.binary()


    def get(self, ids: List[bytes]) -> List[bytes]:
        """Get datas from the plasma memory store by IDs."""
        if self.mock_flag:
            return ids
        object_ids = [plasma.ObjectID(id) for id in ids]
        return self._get_plasma(object_ids)


    def get_item(self, id: bytes) -> bytes:
        """Get data from the plasma memory store by ID."""
        if self.mock_flag:
            return id
        object_id = plasma.ObjectID
        return self._get_plasma_item(object_id)


    def delete(self, ids: List[bytes]):
        """Delete datas from the plasma memory store by IDss."""
        if self.mock_flag:
            return
        object_ids = [plasma.ObjectID(id) for id in ids]
        self.client.delete(object_ids)


    def delete_item(self, id: bytes):
        """Delete data from the plasma memory store by ID."""
        if self.mock_flag:
            return
        object_id = plasma.ObjectID(id)
        self.client.delete([object_id])