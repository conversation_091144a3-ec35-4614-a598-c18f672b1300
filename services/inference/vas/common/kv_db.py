from sqlitedict import SqliteDict
import threading

class KVStore:
    """
    轻量级本地 KV 存储封装，基于 sqlitedict
    线程安全，支持任意可序列化对象
    """

    def __init__(self, db_path: str, table_name: str = "kvstore", autocommit: bool = True):
        self._db = SqliteDict(db_path, tablename=table_name, autocommit=autocommit)
        self._lock = threading.Lock()


    # ---------- 对外接口 ----------
    def save(self, key, value):
        """保存 / 覆盖"""
        with self._lock:
            self._db[key] = value
        return True


    def load(self, key, default=None):
        """读取，不存在返回 default"""
        with self._lock:
            return self._db.get(key, default)


    def remove(self, key):
        """删除，不存在返回 False"""
        with self._lock:
            if key in self._db:
                del self._db[key]
                return True
            return False


    def keys(self):
        """返回所有键列表"""
        with self._lock:
            return list(self._db.keys())
        
    
    def values(self):
        """返回所有值列表"""
        values = []
        keys = self.keys()
        for index in range(len(keys)):
            values.append(self.load(keys[index]))


    def close(self):
        """显式关闭数据库连接"""
        with self._lock:
            self._db.close()


    # ---------- 上下文支持 ----------
    def __enter__(self):
        return self


    def __exit__(self, exc_type, exc_val, exc_tb):
        self.close()
