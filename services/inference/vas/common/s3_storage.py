"""
S3存储工具类
用于上传截图和事件图片到S3存储
"""
import os
import boto3
import yaml
from datetime import datetime
from typing import Optional, Dict, Any
from botocore.exceptions import ClientError, NoCredentialsError
from vas.common.logger_tools import get_logger

logger = get_logger(__name__)


class S3StorageManager:
    """S3存储管理器"""
    
    def __init__(self, config_path: str = None):
        """
        初始化S3存储管理器
        
        Args:
            config_path: 配置文件路径，默认为None时使用默认路径
        """
        self.config = self._load_config(config_path)
        self.s3_client = None
        self.s3_resource = None
        self.bucket_name = self.config.get('bucket_name')
        self._init_s3_client()
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载S3配置"""
        if config_path is None:
            # 默认配置文件路径
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                s3_config = config.get('s3_storage', {})
                
                if not s3_config:
                    raise ValueError("S3配置不存在于config.yaml中")
                
                logger.info("S3配置加载成功")
                return s3_config
                
        except Exception as e:
            logger.error(f"加载S3配置失败: {e}")
            raise
    
    def _init_s3_client(self):
        """初始化S3客户端"""
        try:
            # 创建S3资源和客户端
            self.s3_resource = boto3.resource(
                's3',
                endpoint_url=self.config['endpoint'],
                aws_access_key_id=self.config['access_key'],
                aws_secret_access_key=self.config['secret_key'],
                region_name=self.config.get('region_name', 'us-east-1')
            )
            
            self.s3_client = boto3.client(
                's3',
                endpoint_url=self.config['endpoint'],
                aws_access_key_id=self.config['access_key'],
                aws_secret_access_key=self.config['secret_key'],
                region_name=self.config.get('region_name', 'us-east-1')
            )
            
            # 自动创建bucket（如果配置允许）
            if self.config.get('auto_create_bucket', True):
                self._ensure_bucket_exists()
            
            logger.info("S3客户端初始化成功")
            
        except Exception as e:
            logger.error(f"S3客户端初始化失败: {e}")
            raise
    
    def _ensure_bucket_exists(self):
        """确保bucket存在，不存在则创建"""
        try:
            # 检查bucket是否存在
            self.s3_client.head_bucket(Bucket=self.bucket_name)
            logger.info(f"Bucket {self.bucket_name} 已存在")
            
        except ClientError as e:
            error_code = e.response['Error']['Code']
            if error_code == '404':
                # Bucket不存在，创建它
                try:
                    self.s3_resource.create_bucket(Bucket=self.bucket_name)
                    logger.info(f"Bucket {self.bucket_name} 创建成功")
                except Exception as create_error:
                    logger.error(f"创建Bucket失败: {create_error}")
                    raise
            else:
                logger.error(f"检查Bucket失败: {e}")
                raise
    
    def upload_screenshot(self, 
                         local_file_path: str, 
                         task_id: str, 
                         timestamp: datetime = None,
                         custom_name: str = None) -> Optional[str]:
        """
        上传截图到S3
        
        Args:
            local_file_path: 本地文件路径
            task_id: 任务ID
            timestamp: 时间戳，默认为当前时间
            custom_name: 自定义文件名，不包含扩展名
            
        Returns:
            上传成功返回S3对象URL，失败返回None
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        try:
            # 生成S3对象名
            if custom_name:
                filename = custom_name
            else:
                filename = f"{task_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            
            # 获取文件扩展名
            _, ext = os.path.splitext(local_file_path)
            if not ext:
                ext = '.jpg'  # 默认扩展名
            
            # 构建S3对象键
            screenshot_prefix = self.config.get('screenshot_prefix', 'screenshots/')
            object_key = f"{screenshot_prefix}{filename}{ext}"
            
            # 上传文件
            self.s3_resource.Bucket(self.bucket_name).upload_file(
                local_file_path, 
                object_key
            )
            
            # 构建访问URL
            object_url = f"{self.config['endpoint']}/{self.bucket_name}/{object_key}"
            
            logger.info(f"截图上传成功: {local_file_path} -> {object_url}")
            return object_url
            
        except Exception as e:
            logger.error(f"上传截图失败: {e}")
            return None
    
    def upload_event_image(self, 
                          local_file_path: str, 
                          task_id: str, 
                          event_id: str,
                          timestamp: datetime = None) -> Optional[str]:
        """
        上传事件图片到S3
        
        Args:
            local_file_path: 本地文件路径
            task_id: 任务ID
            event_id: 事件ID
            timestamp: 时间戳，默认为当前时间
            
        Returns:
            上传成功返回S3对象URL，失败返回None
        """
        if timestamp is None:
            timestamp = datetime.now()
        
        try:
            # 获取文件扩展名
            _, ext = os.path.splitext(local_file_path)
            if not ext:
                ext = '.jpg'  # 默认扩展名
            
            # 构建S3对象键
            event_prefix = self.config.get('event_prefix', 'events/')
            filename = f"{task_id}_{event_id}_{timestamp.strftime('%Y%m%d_%H%M%S')}"
            object_key = f"{event_prefix}{filename}{ext}"
            
            # 上传文件
            self.s3_resource.Bucket(self.bucket_name).upload_file(
                local_file_path, 
                object_key
            )
            
            # 构建访问URL
            object_url = f"{self.config['endpoint']}/{self.bucket_name}/{object_key}"
            
            logger.info(f"事件图片上传成功: {local_file_path} -> {object_url}")
            return object_url
            
        except Exception as e:
            logger.error(f"上传事件图片失败: {e}")
            return None
    
    def upload_file(self, 
                   local_file_path: str, 
                   object_key: str) -> Optional[str]:
        """
        通用文件上传方法
        
        Args:
            local_file_path: 本地文件路径
            object_key: S3对象键（完整路径）
            
        Returns:
            上传成功返回S3对象URL，失败返回None
        """
        try:
            # 上传文件
            self.s3_resource.Bucket(self.bucket_name).upload_file(
                local_file_path, 
                object_key
            )
            
            # 构建访问URL
            object_url = f"{self.config['endpoint']}/{self.bucket_name}/{object_key}"
            
            logger.info(f"文件上传成功: {local_file_path} -> {object_url}")
            return object_url
            
        except Exception as e:
            logger.error(f"上传文件失败: {e}")
            return None
    
    def list_objects(self, prefix: str = "") -> list:
        """
        列举bucket中的对象
        
        Args:
            prefix: 对象前缀过滤
            
        Returns:
            对象键列表
        """
        try:
            objects = []
            for obj in self.s3_resource.Bucket(self.bucket_name).objects.filter(Prefix=prefix):
                objects.append(obj.key)
            
            logger.info(f"列举对象成功，找到 {len(objects)} 个对象")
            return objects
            
        except Exception as e:
            logger.error(f"列举对象失败: {e}")
            return []
    
    def download_file(self, object_key: str, local_file_path: str) -> bool:
        """
        从S3下载文件
        
        Args:
            object_key: S3对象键
            local_file_path: 本地保存路径
            
        Returns:
            下载成功返回True，失败返回False
        """
        try:
            self.s3_resource.Bucket(self.bucket_name).download_file(
                object_key, 
                local_file_path
            )
            
            logger.info(f"文件下载成功: {object_key} -> {local_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"下载文件失败: {e}")
            return False


# 全局S3存储管理器实例
_s3_manager = None


def get_s3_manager() -> S3StorageManager:
    """获取全局S3存储管理器实例"""
    global _s3_manager
    if _s3_manager is None:
        _s3_manager = S3StorageManager()
    return _s3_manager


def upload_screenshot(local_file_path: str, 
                     task_id: str, 
                     timestamp: datetime = None,
                     custom_name: str = None) -> Optional[str]:
    """
    便捷函数：上传截图
    
    Args:
        local_file_path: 本地文件路径
        task_id: 任务ID
        timestamp: 时间戳
        custom_name: 自定义文件名
        
    Returns:
        上传成功返回S3对象URL，失败返回None
    """
    return get_s3_manager().upload_screenshot(local_file_path, task_id, timestamp, custom_name)


def upload_event_image(local_file_path: str, 
                      task_id: str, 
                      event_id: str,
                      timestamp: datetime = None) -> Optional[str]:
    """
    便捷函数：上传事件图片
    
    Args:
        local_file_path: 本地文件路径
        task_id: 任务ID
        event_id: 事件ID
        timestamp: 时间戳
        
    Returns:
        上传成功返回S3对象URL，失败返回None
    """
    return get_s3_manager().upload_event_image(local_file_path, task_id, event_id, timestamp)
