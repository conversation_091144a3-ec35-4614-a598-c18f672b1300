"""
Kafka客户端工具类
用于推送事件实例到Kafka
"""
import json
import yaml
import time
from typing import Dict, Any, Optional
from datetime import datetime
from kafka import KafkaProducer
from kafka.errors import KafkaError, KafkaTimeoutError
from vas.common.logger_tools import get_logger
from vas.entrypoints.event_interface import AtomicEventInstance

logger = get_logger(__name__)


class KafkaEventProducer:
    """Kafka事件生产者"""
    
    def __init__(self, config_path: str = None):
        """
        初始化Kafka事件生产者
        
        Args:
            config_path: 配置文件路径，默认为None时使用默认路径
        """
        self.config = self._load_config(config_path)
        self.producer = None
        self.topic = self.config.get('topic', 'video_analysis_events')
        self.enabled = self.config.get('enable_kafka', True)
        
        if self.enabled:
            self._init_producer()
    
    def _load_config(self, config_path: str = None) -> Dict[str, Any]:
        """加载Kafka配置"""
        if config_path is None:
            # 默认配置文件路径
            import os
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_path = os.path.join(current_dir, '../../config/config.yaml')
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                kafka_config = config.get('kafka', {})
                
                if not kafka_config:
                    logger.warning("Kafka配置不存在于config.yaml中，将使用默认配置")
                    return self._get_default_config()
                
                logger.info("Kafka配置加载成功")
                return kafka_config
                
        except Exception as e:
            logger.error(f"加载Kafka配置失败: {e}")
            logger.info("使用默认Kafka配置")
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认Kafka配置"""
        return {
            'bootstrap_servers': ['localhost:9092'],
            'topic': 'video_analysis_events',
            'producer': {
                'acks': 1,
                'retries': 3,
                'batch_size': 16384,
                'linger_ms': 10,
                'buffer_memory': 33554432,
                'compression_type': 'gzip',
                'request_timeout_ms': 30000,
                'delivery_timeout_ms': 120000
            },
            'serialization': {
                'key_serializer': 'string',
                'value_serializer': 'json'
            },
            'enable_kafka': True,
            'max_retries': 3,
            'retry_backoff_ms': 1000
        }
    
    def _init_producer(self):
        """初始化Kafka生产者"""
        try:
            producer_config = self.config.get('producer', {})
            serialization_config = self.config.get('serialization', {})
            
            # 构建生产者配置
            kafka_config = {
                'bootstrap_servers': self.config.get('bootstrap_servers', ['localhost:9092']),
                'acks': producer_config.get('acks', 1),
                'retries': producer_config.get('retries', 3),
                'batch_size': producer_config.get('batch_size', 16384),
                'linger_ms': producer_config.get('linger_ms', 10),
                'buffer_memory': producer_config.get('buffer_memory', 33554432),
                'max_request_size': producer_config.get('max_request_size', 1048576),
                'compression_type': producer_config.get('compression_type', 'gzip'),
                'request_timeout_ms': producer_config.get('request_timeout_ms', 30000),
                'delivery_timeout_ms': producer_config.get('delivery_timeout_ms', 120000),
            }
            
            # 设置序列化器
            value_serializer = serialization_config.get('value_serializer', 'json')
            if value_serializer == 'json':
                kafka_config['value_serializer'] = lambda v: json.dumps(v, ensure_ascii=False, default=str).encode('utf-8')
            elif value_serializer == 'string':
                kafka_config['value_serializer'] = lambda v: str(v).encode('utf-8')
            else:  # bytes
                kafka_config['value_serializer'] = lambda v: v if isinstance(v, bytes) else str(v).encode('utf-8')
            
            key_serializer = serialization_config.get('key_serializer', 'string')
            if key_serializer == 'string':
                kafka_config['key_serializer'] = lambda k: str(k).encode('utf-8') if k else None
            else:  # bytes
                kafka_config['key_serializer'] = lambda k: k if isinstance(k, bytes) else str(k).encode('utf-8') if k else None
            
            # 创建生产者
            self.producer = KafkaProducer(**kafka_config)
            logger.info("Kafka生产者初始化成功")
            
        except Exception as e:
            logger.error(f"Kafka生产者初始化失败: {e}")
            self.producer = None
            self.enabled = False
    
    def send_event(self, event: AtomicEventInstance, key: str = None, topic: str = None) -> bool:
        """
        发送事件到Kafka
        
        Args:
            event: 原子事件实例
            key: 消息键，默认使用事件ID
            
        Returns:
            发送成功返回True，失败返回False
        """
        if not self.enabled or not self.producer:
            logger.debug("Kafka未启用或生产者未初始化，跳过事件推送")
            return False
        
        try:
            # 转换事件为字典
            event_dict = self._event_to_dict(event)
            
            # 使用事件ID作为默认键
            if key is None:
                key = event.atomicEventInstanceId

            # 使用初始化topic
            if topic is None:
                topic = self.topic
            
            # 发送消息
            future = self.producer.send(
                topic=topic,
                value=event_dict,
                key=key
            )
            
            # 等待发送完成（可选，用于确认发送成功）
            record_metadata = future.get(timeout=10)
            
            logger.info(f"事件推送成功: topic={record_metadata.topic}, "
                       f"partition={record_metadata.partition}, "
                       f"offset={record_metadata.offset}, "
                       f"event_id={event.atomicEventInstanceId}")
            
            return True
            
        except KafkaTimeoutError:
            logger.error(f"Kafka推送超时: event_id={event.atomicEventInstanceId}")
            return False
        except KafkaError as e:
            logger.error(f"Kafka推送失败: {e}, event_id={event.atomicEventInstanceId}")
            return False
        except Exception as e:
            logger.error(f"事件推送异常: {e}, event_id={event.atomicEventInstanceId}")
            return False
    
    def send_event_async(self, event: AtomicEventInstance, key: str = None, callback=None):
        """
        异步发送事件到Kafka
        
        Args:
            event: 原子事件实例
            key: 消息键，默认使用事件ID
            callback: 回调函数，接收(record_metadata, exception)参数
        """
        if not self.enabled or not self.producer:
            logger.debug("Kafka未启用或生产者未初始化，跳过事件推送")
            return
        
        try:
            # 转换事件为字典
            event_dict = self._event_to_dict(event)
            
            # 使用事件ID作为默认键
            if key is None:
                key = event.atomicEventInstanceId
            
            # 定义默认回调函数
            def default_callback(record_metadata, exception):
                if exception:
                    logger.error(f"异步事件推送失败: {exception}, event_id={event.atomicEventInstanceId}")
                else:
                    logger.info(f"异步事件推送成功: topic={record_metadata.topic}, "
                               f"partition={record_metadata.partition}, "
                               f"offset={record_metadata.offset}, "
                               f"event_id={event.atomicEventInstanceId}")
            
            # 发送消息
            future = self.producer.send(
                topic=self.topic,
                value=event_dict,
                key=key
            )
            
            # 添加回调
            future.add_callback(callback or default_callback)
            
        except Exception as e:
            logger.error(f"异步事件推送异常: {e}, event_id={event.atomicEventInstanceId}")
    
    def _event_to_dict(self, event: AtomicEventInstance) -> Dict[str, Any]:
        """
        将事件实例转换为字典
        
        Args:
            event: 原子事件实例
            
        Returns:
            事件字典
        """
        try:
            # 使用pydantic的dict方法
            if hasattr(event, 'dict'):
                return event.dict()
            
            # 手动转换
            event_dict = {
                'atomicEventInstanceId': event.atomicEventInstanceId,
                'eventTypeId': event.eventTypeId,
                'taskId': event.taskId,
                'deviceId': event.deviceId,
                'timestamp': event.timestamp,
                'imageUri': event.imageUri,
                'entities': [entity.dict() if hasattr(entity, 'dict') else entity for entity in event.entities],
                'taskInfo': event.taskInfo.dict() if hasattr(event.taskInfo, 'dict') else event.taskInfo
            }
            
            # 添加推送时间戳
            event_dict['_kafka_timestamp'] = datetime.now().isoformat()
            
            return event_dict
            
        except Exception as e:
            logger.error(f"事件转换字典失败: {e}")
            # 返回基本信息
            return {
                'atomicEventInstanceId': getattr(event, 'atomicEventInstanceId', 'unknown'),
                'eventTypeId': getattr(event, 'eventTypeId', 'unknown'),
                'taskId': getattr(event, 'taskId', 'unknown'),
                'timestamp': getattr(event, 'timestamp', datetime.now().isoformat()),
                '_kafka_timestamp': datetime.now().isoformat(),
                '_conversion_error': str(e)
            }
    
    def flush(self, timeout: float = None):
        """
        刷新生产者缓冲区
        
        Args:
            timeout: 超时时间（秒）
        """
        if self.producer:
            try:
                self.producer.flush(timeout=timeout)
                logger.debug("Kafka生产者缓冲区刷新完成")
            except Exception as e:
                logger.error(f"Kafka生产者缓冲区刷新失败: {e}")
    
    def close(self):
        """关闭Kafka生产者"""
        if self.producer:
            try:
                self.producer.close()
                logger.info("Kafka生产者已关闭")
            except Exception as e:
                logger.error(f"关闭Kafka生产者失败: {e}")
            finally:
                self.producer = None


# 全局Kafka生产者实例
_kafka_producer = None


def get_kafka_producer() -> KafkaEventProducer:
    """获取全局Kafka生产者实例"""
    global _kafka_producer
    if _kafka_producer is None:
        _kafka_producer = KafkaEventProducer()
    return _kafka_producer


def push_event_to_kafka(event: AtomicEventInstance, key: str = None, topic: str = None) -> bool:
    """
    便捷函数：推送事件到Kafka
    
    Args:
        event: 原子事件实例
        key: 消息键
        
    Returns:
        推送成功返回True，失败返回False
    """
    return get_kafka_producer().send_event(event, key, topic)


def push_event_to_kafka_async(event: AtomicEventInstance, key: str = None, callback=None):
    """
    便捷函数：异步推送事件到Kafka
    
    Args:
        event: 原子事件实例
        key: 消息键
        callback: 回调函数
    """
    get_kafka_producer().send_event_async(event, key, callback)
