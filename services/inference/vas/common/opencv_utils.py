import cv2
import numpy as np

MAX_CLASS_NUM=80
colors = np.random.uniform(0, 255, size=(MAX_CLASS_NUM, 3))


def draw_bboxes(image: np.ndarray, class_id: int, confidence: 
    float, x: int, y: int, x_plus_w: int, y_plus_h: int) -> None:
    """
    Draw bounding boxes on the input image based on the provided arguments.

    Args:
        img (np.ndarray): The input image to draw the bounding box on.
        class_id (int): Class ID of the detected object.
        confidence (float): Confidence score of the detected object.
        x (int): X-coordinate of the top-left corner of the bounding box.
        y (int): Y-coordinate of the top-left corner of the bounding box.
        x_plus_w (int): X-coordinate of the bottom-right corner of the bounding box.
        y_plus_h (int): Y-coordinate of the bottom-right corner of the bounding box.
    """
    label = f"{class_id} ({confidence:.2f})"
    color = colors[class_id]
    cv2.rectangle(image, (x, y), (x_plus_w, y_plus_h), color, 2)
    cv2.putText(image, label, (x - 10, y - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 2)


def save_detect_result(image: np.ndarray, detect_res: np.ndarray, save_file: str):
    image_clone = image.copy()
    for det_item in detect_res:
        score = det_item[4]
        class_id = int(det_item[5])
        x1 = int(det_item[0])
        y1 = int(det_item[1])
        x2 = int(det_item[0] + det_item[2])
        y2 = int(det_item[1] + det_item[3])
        draw_bboxes(image_clone, class_id, score, x1, y1, x2 , y2)
    cv2.imwrite(save_file, image_clone)