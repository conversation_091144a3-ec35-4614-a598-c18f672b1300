#!/usr/bin/env python3
"""
测试Kafka推送功能
"""
import os
import sys
import json
import uuid
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vas.modules.base_module import push_kafka, BaseModule
from vas.common.kafka_client import KafkaEventProducer, get_kafka_producer
from vas.entrypoints.event_interface import AtomicEventInstance, EntityInstance, BoundingBox
from vas.entrypoints.task_interface import SimplifiedAtomicTask, TaskMeta, Device, AlgorithmOrchestration


def create_test_event():
    """创建测试事件实例"""
    # 创建实体
    bbox = BoundingBox(x=100, y=100, width=200, height=150)
    entity = EntityInstance(
        entityInstanceId="entity_001",
        entityType="person",
        algorithmId="yolo-v8",
        boundingBox=bbox
    )
    
    # 创建任务信息（简化版）
    task_info = {
        "taskId": "test-task-001",
        "taskName": "测试任务",
        "deviceId": "test-camera-001"
    }
    
    # 创建事件实例
    event = AtomicEventInstance(
        atomicEventInstanceId=f"event_{uuid.uuid4().hex[:8]}",
        eventTypeId="intrusion_detection",
        taskId="test-task-001",
        deviceId="test-camera-001",
        timestamp=datetime.now().isoformat(),
        imageUri="http://example.com/test-image.jpg",
        entities=[entity],
        taskInfo=task_info
    )
    
    return event


def test_kafka_producer():
    """测试Kafka生产者"""
    print("=== 测试Kafka生产者 ===")
    
    try:
        # 创建Kafka生产者
        producer = KafkaEventProducer()
        
        if not producer.enabled:
            print("❌ Kafka未启用，跳过测试")
            return False
        
        print("✅ Kafka生产者创建成功")
        
        # 创建测试事件
        test_event = create_test_event()
        print(f"✅ 测试事件创建成功: {test_event.atomicEventInstanceId}")
        
        # 发送事件
        success = producer.send_event(test_event)
        
        if success:
            print(f"✅ 事件发送成功: {test_event.atomicEventInstanceId}")
        else:
            print(f"❌ 事件发送失败: {test_event.atomicEventInstanceId}")
        
        # 刷新缓冲区
        producer.flush(timeout=5)
        print("✅ 缓冲区刷新完成")
        
        return success
        
    except Exception as e:
        print(f"❌ Kafka生产者测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_async_kafka_producer():
    """测试异步Kafka生产者"""
    print("\n=== 测试异步Kafka生产者 ===")
    
    try:
        # 获取全局生产者
        producer = get_kafka_producer()
        
        if not producer.enabled:
            print("❌ Kafka未启用，跳过测试")
            return False
        
        # 创建测试事件
        test_event = create_test_event()
        print(f"✅ 测试事件创建成功: {test_event.atomicEventInstanceId}")
        
        # 定义回调函数
        def callback(record_metadata, exception):
            if exception:
                print(f"❌ 异步发送失败: {exception}")
            else:
                print(f"✅ 异步发送成功: topic={record_metadata.topic}, "
                      f"partition={record_metadata.partition}, offset={record_metadata.offset}")
        
        # 异步发送事件
        producer.send_event_async(test_event, callback=callback)
        print("✅ 异步发送请求已提交")
        
        # 等待一段时间让异步操作完成
        import time
        time.sleep(2)
        
        # 刷新缓冲区
        producer.flush(timeout=5)
        print("✅ 异步操作完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步Kafka生产者测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_global_push_kafka():
    """测试全局push_kafka函数"""
    print("\n=== 测试全局push_kafka函数 ===")
    
    try:
        # 创建测试事件
        test_event = create_test_event()
        print(f"✅ 测试事件创建成功: {test_event.atomicEventInstanceId}")
        
        # 使用全局函数推送
        success = push_kafka(test_event, key="test-key")
        
        if success:
            print(f"✅ 全局函数推送成功: {test_event.atomicEventInstanceId}")
        else:
            print(f"❌ 全局函数推送失败: {test_event.atomicEventInstanceId}")
        
        return success
        
    except Exception as e:
        print(f"❌ 全局push_kafka函数测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_base_module_push_kafka():
    """测试BaseModule的push_kafka方法"""
    print("\n=== 测试BaseModule.push_kafka方法 ===")
    
    try:
        # 创建模拟的命令行参数
        class MockArgs:
            def __init__(self):
                self.config_path = "config/config.yaml"
                self.mock_plasma = True
        
        # 创建模拟的队列
        mock_queues = {}
        
        # 创建模拟的logger
        import logging
        logger = logging.getLogger("test")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # 创建BaseModule实例
        base_module = BaseModule(
            module_name="test_module",
            cmd_args=MockArgs(),
            queues=mock_queues,
            logger=logger
        )
        
        # 创建测试事件
        test_event = create_test_event()
        print(f"✅ 测试事件创建成功: {test_event.atomicEventInstanceId}")
        
        # 使用BaseModule方法推送
        success = base_module.push_kafka(test_event, key="base-module-test")
        
        if success:
            print(f"✅ BaseModule推送成功: {test_event.atomicEventInstanceId}")
        else:
            print(f"❌ BaseModule推送失败: {test_event.atomicEventInstanceId}")
        
        return success
        
    except Exception as e:
        print(f"❌ BaseModule.push_kafka方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_batch_events():
    """测试批量事件推送"""
    print("\n=== 测试批量事件推送 ===")
    
    try:
        producer = get_kafka_producer()
        
        if not producer.enabled:
            print("❌ Kafka未启用，跳过测试")
            return False
        
        # 创建多个测试事件
        events = []
        for i in range(5):
            event = create_test_event()
            events.append(event)
        
        print(f"✅ 创建了 {len(events)} 个测试事件")
        
        # 批量发送
        success_count = 0
        for i, event in enumerate(events):
            success = push_kafka(event, key=f"batch-{i}")
            if success:
                success_count += 1
        
        print(f"✅ 批量推送完成: {success_count}/{len(events)} 成功")
        
        # 刷新缓冲区
        producer.flush(timeout=10)
        print("✅ 批量推送缓冲区刷新完成")
        
        return success_count == len(events)
        
    except Exception as e:
        print(f"❌ 批量事件推送测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试无效事件
        invalid_event = None
        success = push_kafka(invalid_event)
        print(f"无效事件推送结果: {success}")
        
        # 测试空事件ID
        test_event = create_test_event()
        test_event.atomicEventInstanceId = ""
        success = push_kafka(test_event)
        print(f"空事件ID推送结果: {success}")
        
        print("✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("=== Kafka推送功能测试 ===")
    print(f"测试时间: {datetime.now().isoformat()}")
    
    # 检查依赖
    try:
        from kafka import KafkaProducer
        print("✅ Kafka依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少Kafka依赖: {e}")
        print("请安装: pip install kafka-python")
        return
    
    # 运行测试
    results = []
    
    results.append(test_kafka_producer())
    results.append(test_async_kafka_producer())
    results.append(test_global_push_kafka())
    results.append(test_base_module_push_kafka())
    results.append(test_batch_events())
    results.append(test_error_handling())
    
    # 统计结果
    passed = sum(1 for r in results if r)
    total = len(results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
