"""
OWL检测HTTP API服务
提供0样本检测的HTTP接口
"""

import os
import sys
import json
import time
import yaml
import base64
import requests
import logging
import traceback
from typing import Dict, Any, List
from werkzeug.utils import secure_filename
from werkzeug.exceptions import RequestEntityTooLarge

import cv2
import numpy as np
from flask import Flask, request, jsonify, current_app

# 添加项目路径到sys.path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vas.services.owl_detection_service import OWLDetectionService


class OWLDetectionAPI:
    """OWL检测API服务类"""
    
    def __init__(self, config_path: str):
        """
        初始化API服务
        
        Args:
            config_path: 配置文件路径
        """
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
        # 初始化Flask应用
        self.app = Flask(__name__)
        self._setup_flask_config()
        
        # 初始化检测服务
        self.detection_service = None
        self._init_detection_service()
        
        # 注册路由
        self._register_routes()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            print(f"配置文件加载成功: {config_path}")
            return config
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            raise
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志"""
        log_config = self.config.get("logging", {})
        log_level = log_config.get("level", "info").upper()
        log_path = log_config.get("log_path", "./logs")
        
        # 创建日志目录
        os.makedirs(log_path, exist_ok=True)
        
        # 配置日志
        logger = logging.getLogger("OWLDetectionAPI")
        logger.setLevel(getattr(logging, log_level))
        
        if not logger.handlers:
            # 文件处理器
            log_file = os.path.join(log_path, "owl_detection_api.log")
            file_handler = logging.FileHandler(log_file, encoding='utf-8')
            
            # 控制台处理器
            console_handler = logging.StreamHandler()
            
            # 格式化器
            formatter = logging.Formatter(
                log_config.get("format", 
                "[%(asctime)s] - [pid:%(process)d] - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s %(message)s")
            )
            
            file_handler.setFormatter(formatter)
            console_handler.setFormatter(formatter)
            
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
        
        return logger
    
    def _setup_flask_config(self):
        """设置Flask配置"""
        api_config = self.config.get("api", {})
        
        # 文件上传配置
        self.app.config['MAX_CONTENT_LENGTH'] = api_config.get("max_file_size", 10 * 1024 * 1024)  # 10MB
        self.app.config['UPLOAD_EXTENSIONS'] = api_config.get("allowed_extensions", ['.jpg', '.jpeg', '.png', '.bmp', '.tiff'])
        
        # 其他配置
        server_config = self.config.get("server", {})
        self.app.config['DEBUG'] = server_config.get("debug", False)
        
        # 错误处理
        @self.app.errorhandler(RequestEntityTooLarge)
        def handle_file_too_large(e):
            return jsonify({
                "code": 413,
                "message": "文件太大，请上传小于10MB的图片",
                "data": {}
            }), 413
        
        @self.app.errorhandler(Exception)
        def handle_exception(e):
            self.logger.error(f"未处理的异常: {str(e)}\n{traceback.format_exc()}")
            return jsonify({
                "code": 500,
                "message": "服务器内部错误",
                "data": {}
            }), 500
    
    def _init_detection_service(self):
        """初始化检测服务"""
        try:
            self.logger.info("初始化OWL检测服务...")
            self.detection_service = OWLDetectionService(self.config, self.logger)
            self.logger.info("OWL检测服务初始化完成")
        except Exception as e:
            self.logger.error(f"检测服务初始化失败: {e}")
            raise
    
    def _register_routes(self):
        """注册API路由"""
        
        @self.app.route('/health', methods=['GET'])
        def health_check():
            """健康检查接口"""
            return jsonify({
                "code": 200,
                "message": "服务正常",
                "data": {
                    "status": "healthy",
                    "timestamp": time.time(),
                    "service_info": self.detection_service.get_service_info()
                }
            })
        
        @self.app.route('/api/v1/detect', methods=['POST'])
        def detect_objects():
            """目标检测接口"""
            return self._handle_detection_request()

        @self.app.route('/api/v1/classify', methods=['POST'])
        def classify_regions():
            """区域分类接口"""
            return self._handle_classification_request()

        @self.app.route('/api/v1/get_feature', methods=['POST'])
        def get_feature():
            """获取图像特征接口"""
            return self._handle_feature_request()

        @self.app.route('/api/v1/get_score', methods=['POST'])
        def get_score():
            """获取小模型相似度相似度得分"""
            return self._handle_score_request()

        @self.app.route('/api/v1/info', methods=['GET'])
        def get_service_info():
            """获取服务信息"""
            return jsonify({
                "code": 200,
                "message": "获取服务信息成功",
                "data": self.detection_service.get_service_info()
            })
    
    def _handle_detection_request(self):
        """处理检测请求"""
        start_time = time.time()
        
        try:
            # 验证请求
            if 'image' not in request.files:
                return jsonify({
                    "code": 400,
                    "message": "缺少图片文件",
                    "data": {}
                }), 400
            
            file = request.files['image']
            if file.filename == '':
                return jsonify({
                    "code": 400,
                    "message": "未选择文件",
                    "data": {}
                }), 400
            
            # 获取参数
            query_texts = self._get_query_texts_from_request()
            threshold = self._get_float_param('threshold')
            nms_threshold = self._get_float_param('nms_threshold')
            
            # 验证参数
            validation_error = self._validate_detection_params(query_texts, threshold, nms_threshold)
            if validation_error:
                return validation_error
            
            # 读取和验证图片
            image = self._read_and_validate_image(file)
            if image is None:
                return jsonify({
                    "code": 400,
                    "message": "无效的图片文件",
                    "data": {}
                }), 400
            
            # 图片转换成RGB格式
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
            
            # 执行检测
            detections = self.detection_service.detect_objects(
                image, query_texts, threshold, nms_threshold
            )
            
            # 过滤结果（只返回分数大于阈值的检测框）
            final_threshold = threshold or self.detection_service.default_policy["embeddings_query"]["threshold"]
            filtered_detections = [d for d in detections if d["score"] >= final_threshold]
            
            processing_time = time.time() - start_time
            
            return jsonify({
                "code": 200,
                "message": "检测成功",
                "data": {
                    "detections": filtered_detections,
                    "total_count": len(filtered_detections),
                    "processing_time": round(processing_time, 3),
                    "parameters": {
                        "query_texts": query_texts,
                        "threshold": final_threshold,
                        "nms_threshold": nms_threshold or self.detection_service.default_policy["nms"]["threshold"]
                    }
                }
            })
            
        except Exception as e:
            self.logger.error(f"检测请求处理失败: {str(e)}\n{traceback.format_exc()}")
            return jsonify({
                "code": 500,
                "message": f"检测失败: {str(e)}",
                "data": {}
            }), 500

    def _get_query_texts_from_request(self) -> List[str]:
        """从请求中获取查询文本"""
        query_texts_str = request.form.get('query_texts', '[]')
        try:
            query_texts = json.loads(query_texts_str)
            if not isinstance(query_texts, list):
                raise ValueError("query_texts必须是数组")
            return [str(text).strip() for text in query_texts if str(text).strip()]
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"query_texts格式错误: {e}")

    def _get_float_param(self, param_name: str) -> float:
        """从请求中获取浮点数参数"""
        value = request.form.get(param_name)
        if value is None:
            return None
        try:
            return float(value)
        except ValueError:
            raise ValueError(f"{param_name}必须是有效的数字")

    def _validate_detection_params(self, query_texts: List[str],
                                 threshold: float, nms_threshold: float):
        """验证检测参数"""
        api_config = self.config.get("api", {})

        # 验证查询文本
        if not query_texts:
            return jsonify({
                "code": 400,
                "message": "query_texts不能为空",
                "data": {}
            }), 400

        max_query_texts = api_config.get("max_query_texts", 10)
        if len(query_texts) > max_query_texts:
            return jsonify({
                "code": 400,
                "message": f"查询文本数量不能超过{max_query_texts}个",
                "data": {}
            }), 400

        # 验证阈值
        if threshold is not None and (threshold < 0 or threshold > 1):
            return jsonify({
                "code": 400,
                "message": "threshold必须在0-1之间",
                "data": {}
            }), 400

        if nms_threshold is not None and (nms_threshold < 0 or nms_threshold > 1):
            return jsonify({
                "code": 400,
                "message": "nms_threshold必须在0-1之间",
                "data": {}
            }), 400

        return None

    def _read_and_validate_image(self, file) -> np.ndarray:
        """读取和验证图片"""
        try:
            # 检查文件扩展名
            filename = secure_filename(file.filename)
            if not self._allowed_file(filename):
                raise ValueError("不支持的文件格式")

            # 读取图片数据
            file_bytes = file.read()
            if len(file_bytes) == 0:
                raise ValueError("文件为空")

            # 解码图片
            nparr = np.frombuffer(file_bytes, np.uint8)
            image = cv2.imdecode(nparr, cv2.IMREAD_COLOR)

            if image is None:
                raise ValueError("无法解码图片")

            # 验证图片
            if not self.detection_service.validate_image(image):
                raise ValueError("图片格式无效")

            return image

        except Exception as e:
            self.logger.error(f"图片读取失败: {e}")
            return None

    def _handle_classification_request(self):
        """处理分类请求"""
        start_time = time.time()

        try:
            # 验证请求
            if 'image' not in request.files:
                return jsonify({
                    "code": 400,
                    "message": "缺少图片文件",
                    "data": {}
                }), 400

            file = request.files['image']
            if file.filename == '':
                return jsonify({
                    "code": 400,
                    "message": "未选择文件",
                    "data": {}
                }), 400

            # 获取参数
            bboxes = self._get_bboxes_from_request()
            positive_texts = self._get_positive_texts_from_request()
            negative_texts = self._get_negative_texts_from_request()
            threshold = self._get_float_param('threshold') or 0.15

            # 验证参数
            validation_error = self._validate_classification_params(
                bboxes, positive_texts, negative_texts, threshold
            )
            if validation_error:
                return validation_error

            # 读取和验证图片
            image = self._read_and_validate_image(file)
            if image is None:
                return jsonify({
                    "code": 400,
                    "message": "无效的图片文件",
                    "data": {}
                }), 400

            # 图片转换成RGB格式
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 执行分类
            results = self.detection_service.classify_regions(
                image, bboxes, positive_texts, negative_texts, threshold
            )

            processing_time = time.time() - start_time

            # 统计预警数量
            alert_count = sum(1 for r in results if r["alert"])

            return jsonify({
                "code": 200,
                "message": "分类成功",
                "data": {
                    "results": results,
                    "summary": {
                        "total_bboxes": len(bboxes),
                        "alert_count": alert_count,
                        "processing_time": round(processing_time, 3)
                    },
                    "parameters": {
                        "positive_texts": positive_texts,
                        "negative_texts": negative_texts,
                        "threshold": threshold
                    }
                }
            })

        except Exception as e:
            self.logger.error(f"分类请求处理失败: {str(e)}\n{traceback.format_exc()}")
            return jsonify({
                "code": 500,
                "message": f"分类失败: {str(e)}",
                "data": {}
            }), 500

    def _load_image_from_url(self, image_url):
        try:
            resp = requests.get(image_url, timeout=30)
            resp.raise_for_status()
            img_np = np.frombuffer(resp.content, dtype=np.uint8)
            img_bgr = cv2.imdecode(img_np, cv2.IMREAD_COLOR)
            if img_bgr is None:
                raise ValueError("cv2.imdecode failed —— 可能不是有效图片格式")
            return img_bgr
        except Exception as e:
            self.logger.error(f"图片读取失败: {e}")
            return None

    def _handle_feature_request(self):
        """处理图像特征请求"""
        start_time = time.time()

        try:
            # 如果上传了图像文件
            if 'image' in request.files:

                file = request.files['image']
                if file.filename == '':
                    return jsonify({
                        "code": 400,
                        "message": "未选择文件",
                        "data": {}
                    }), 400

                # 读取和验证图片
                image = self._read_and_validate_image(file)
                if image is None:
                    return jsonify({
                        "code": 400,
                        "message": "无效的图片文件",
                        "data": {}
                    }), 400
            # 如果设置了图像的url
            else:
                image_url = request.form.get('image', '')
                image = self._load_image_from_url(image_url=image_url)
                if image is None:
                    return jsonify({
                        "code": 400,
                        "message": f"加载图片失败, 图片地址{image_url}",
                        "data": {}
                    }), 400

            # 获取bbox参数
            bboxes = self._get_bboxes_from_request()

            # 验证边界框
            if not bboxes:
                return jsonify({
                    "code": 400,
                    "message": "bboxes不能为空",
                    "data": {}
                }), 400

            max_box_num = 50
            if len(bboxes) > max_box_num:
                return jsonify({
                    "code": 400,
                    "message": f"边界框数量不能超过{max_box_num}个",
                    "data": {}
                }), 400

            # 图片转换成RGB格式
            image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

            # 提取bboxes图像特征
            feature_infos = self.detection_service.get_regions_features(image=image, bboxes=bboxes)
            processing_time = time.time() - start_time

            return jsonify({
                "code": 200,
                "message": "获取特征成功",
                "data": {
                    "results": feature_infos,
                }
            })

        except Exception as e:
            self.logger.error(f"图像特征请求处理失败: {str(e)}\n{traceback.format_exc()}")
            return jsonify({
                "code": 500,
                "message": f"获取图像特征失败: {str(e)}",
                "data": {}
            }), 500

    def _handle_score_request(self):
        """处理小模型推理请求"""
        start_time = time.time()

        try:
            return jsonify({
                "code": 200,
                "message": "获取特征成功",
                "data": {
                    "score": [1.0, 0.0],
                }
            })
        except Exception as e:
            self.logger.error(f"图像特征请求处理失败: {str(e)}\n{traceback.format_exc()}")
            return jsonify({
                "code": 500,
                "message": f"获取图像特征失败: {str(e)}",
                "data": {}
            }), 500

    def _allowed_file(self, filename: str) -> bool:
        """检查文件是否允许"""
        allowed_extensions = self.config.get("api", {}).get("allowed_extensions", [])
        return '.' in filename and \
               filename.rsplit('.', 1)[1].lower() in [ext.lower().lstrip('.') for ext in allowed_extensions]

    def _get_bboxes_from_request(self) -> List[Dict[str, Any]]:
        """从请求中获取边界框列表"""
        bboxes_str = request.form.get('bboxes', '[]')
        try:
            bboxes = json.loads(bboxes_str)
            if not isinstance(bboxes, list):
                raise ValueError("bboxes必须是数组")

            # 验证每个边界框的格式
            for bbox in bboxes:
                if not isinstance(bbox, dict):
                    raise ValueError("边界框必须是对象")
                required_fields = ['id', 'x', 'y', 'w', 'h']
                for field in required_fields:
                    if field not in bbox:
                        raise ValueError(f"边界框缺少必需字段: {field}")
                    if field != 'id' and not isinstance(bbox[field], (int, float)):
                        raise ValueError(f"边界框字段{field}必须是数字")

            return bboxes
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"bboxes格式错误: {e}")

    def _get_positive_texts_from_request(self) -> List[str]:
        """从请求中获取正例文本列表"""
        positive_texts_str = request.form.get('positive_texts', '[]')
        try:
            positive_texts = json.loads(positive_texts_str)
            if not isinstance(positive_texts, list):
                raise ValueError("positive_texts必须是数组")
            return [str(text).strip() for text in positive_texts if str(text).strip()]
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"positive_texts格式错误: {e}")

    def _get_negative_texts_from_request(self) -> List[str]:
        """从请求中获取负例文本列表"""
        negative_texts_str = request.form.get('negative_texts', '[]')
        try:
            negative_texts = json.loads(negative_texts_str)
            if not isinstance(negative_texts, list):
                raise ValueError("negative_texts必须是数组")
            return [str(text).strip() for text in negative_texts if str(text).strip()]
        except (json.JSONDecodeError, ValueError) as e:
            raise ValueError(f"negative_texts格式错误: {e}")

    def _validate_classification_params(self, bboxes: List[Dict[str, Any]],
                                      positive_texts: List[str], negative_texts: List[str],
                                      threshold: float):
        """验证分类参数"""
        api_config = self.config.get("api", {})

        # 验证边界框
        if not bboxes:
            return jsonify({
                "code": 400,
                "message": "bboxes不能为空",
                "data": {}
            }), 400

        max_bboxes = api_config.get("max_bboxes", 50)
        if len(bboxes) > max_bboxes:
            return jsonify({
                "code": 400,
                "message": f"边界框数量不能超过{max_bboxes}个",
                "data": {}
            }), 400

        # 验证正例文本
        if not positive_texts:
            return jsonify({
                "code": 400,
                "message": "positive_texts不能为空",
                "data": {}
            }), 400

        max_positive_texts = api_config.get("max_positive_texts", 20)
        if len(positive_texts) > max_positive_texts:
            return jsonify({
                "code": 400,
                "message": f"正例文本数量不能超过{max_positive_texts}个",
                "data": {}
            }), 400

        # 验证负例文本
        max_negative_texts = api_config.get("max_negative_texts", 20)
        if len(negative_texts) > max_negative_texts:
            return jsonify({
                "code": 400,
                "message": f"负例文本数量不能超过{max_negative_texts}个",
                "data": {}
            }), 400

        # 验证阈值
        if threshold < 0 or threshold > 1:
            return jsonify({
                "code": 400,
                "message": "threshold必须在0-1之间",
                "data": {}
            }), 400

        return None

    def run(self):
        """启动服务"""
        server_config = self.config.get("server", {})
        host = server_config.get("host", "0.0.0.0")
        port = server_config.get("port", 8082)
        debug = server_config.get("debug", False)

        self.logger.info(f"启动OWL检测API服务: http://{host}:{port}")
        self.app.run(host=host, port=port, debug=debug, threaded=True)


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description="OWL检测HTTP API服务")
    parser.add_argument(
        "--config",
        type=str,
        default="config/owl_detection_api_config.yaml",
        help="配置文件路径"
    )

    args = parser.parse_args()

    try:
        # 创建并启动API服务
        api_service = OWLDetectionAPI(args.config)
        api_service.run()
    except KeyboardInterrupt:
        print("\n服务已停止")
    except Exception as e:
        print(f"服务启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
