# A10服务器验证测试指南

## 📋 测试准备

### 1. 同步代码
```bash
# 在A10服务器上执行
cd /path/to/your/project
git pull origin master

# 确认新文件已同步
ls -la inference/vas/services/
ls -la inference/owl_detection_api.py
ls -la inference/config/owl_detection_api_config.yaml
```

### 2. 检查环境依赖
```bash
# 检查Python环境
python3 --version
which python3

# 检查CUDA和GPU
nvidia-smi
nvcc --version

# 检查现有依赖
python3 -c "import torch; print(torch.__version__)"
python3 -c "import transformers; print(transformers.__version__)"
```

### 3. 安装新依赖
```bash
cd inference
pip3 install -r requirements_owl_api.txt

# 或者如果使用conda环境
conda activate your_env_name
pip install -r requirements_owl_api.txt
```

## 🔧 配置调整

### 1. 修改配置文件
```bash
# 编辑配置文件
vim config/owl_detection_api_config.yaml

# 主要需要确认的配置项：
```

```yaml
# 确保Triton服务器地址正确
triton_server:
  ip: "**********"  # 确认这个IP是否正确
  port: 9991

# 确保模型路径正确
owl_detector:
  visual_model: 
    model_path: "/workspace/video_analysis_server/models/google/owlv2-large-patch14-ensemble"
  text_model: 
    model_path: "/workspace/video_analysis_server/models/BAAI/AltCLIP"

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8082
```

### 2. 检查模型文件
```bash
# 检查模型文件是否存在
ls -la /workspace/video_analysis_server/models/google/owlv2-large-patch14-ensemble/
ls -la /workspace/video_analysis_server/models/BAAI/AltCLIP/

# 如果路径不对，需要调整配置文件中的model_path
```

### 3. 检查Triton服务器状态
```bash
# 检查Triton服务器是否运行
curl -f http://**********:9991/v2/health/ready

# 检查模型是否加载
curl http://**********:9991/v2/models/owlv2_visual/ready
curl http://**********:9991/v2/models/altclip_text/ready
```

## 🚀 启动服务

### 方式1: 直接启动
```bash
cd inference
python3 start_owl_detection_api.py --config config/owl_detection_api_config.yaml --debug
```

### 方式2: 使用启动脚本
```bash
cd inference
chmod +x start_owl_api_service.sh
./start_owl_api_service.sh --mode local --debug
```

### 方式3: 指定端口和主机
```bash
python3 start_owl_detection_api.py \
  --config config/owl_detection_api_config.yaml \
  --host 0.0.0.0 \
  --port 8082 \
  --debug
```

## 🧪 功能验证

### 1. 基础健康检查
```bash
# 在另一个终端或本地机器执行
curl http://A10_SERVER_IP:8082/health

# 期望输出：
# {
#   "code": 200,
#   "message": "服务正常",
#   "data": {
#     "status": "healthy",
#     "service_info": {...}
#   }
# }
```

### 2. 服务信息检查
```bash
curl http://A10_SERVER_IP:8082/api/v1/info

# 期望看到模型信息和配置
```

### 3. 准备测试图片
```bash
# 在inference目录下创建测试图片目录
mkdir -p test_images

# 上传一张测试图片到test_images/目录
# 或者使用现有的测试图片
ls -la data/images/
```

### 4. 运行自动化测试
```bash
cd inference

# 基础API测试
python3 test_owl_detection_api.py --url http://localhost:8082

# 带图片的完整测试
python3 test_owl_detection_api.py \
  --url http://localhost:8082 \
  --image test_images/your_test_image.jpg \
  --query-texts person car cat dog
```

### 5. 客户端示例测试
```bash
# 使用示例客户端
python3 example_client.py \
  --url http://localhost:8082 \
  --image test_images/your_test_image.jpg \
  --query-texts person car bicycle cat \
  --threshold 0.15 \
  --output result.json

# 查看结果
cat result.json
```

### 6. curl命令测试
```bash
# 使用curl进行检测
curl -X POST http://localhost:8082/api/v1/detect \
  -F "image=@test_images/your_test_image.jpg" \
  -F "query_texts=[\"person\", \"car\", \"cat\"]" \
  -F "threshold=0.15"
```

## 🔍 故障排除

### 常见问题及解决方案

#### 1. 模型加载失败
```bash
# 检查错误日志
tail -f logs/owl_detection_api.log

# 常见原因：
# - 模型路径不正确
# - Triton服务器未启动
# - 模型未在Triton中加载
```

#### 2. 内存不足
```bash
# 检查GPU内存使用
nvidia-smi

# 如果内存不足，可以：
# - 重启Triton服务器
# - 减少并发请求
# - 调整模型配置
```

#### 3. 网络连接问题
```bash
# 检查端口是否被占用
netstat -tlnp | grep 8082

# 检查防火墙设置
sudo ufw status

# 如果需要开放端口
sudo ufw allow 8082
```

#### 4. 依赖包问题
```bash
# 重新安装依赖
pip3 install --upgrade -r requirements_owl_api.txt

# 检查特定包
python3 -c "import flask; print(flask.__version__)"
python3 -c "import tritonclient; print('tritonclient ok')"
```

## 📊 性能测试

### 1. 单次检测性能
```bash
# 测试单次检测时间
time python3 example_client.py \
  --url http://localhost:8082 \
  --image test_images/test.jpg \
  --query-texts person car
```

### 2. 并发性能测试
```bash
# 简单的并发测试脚本
for i in {1..5}; do
  python3 example_client.py \
    --url http://localhost:8082 \
    --image test_images/test.jpg \
    --query-texts person car &
done
wait
```

### 3. 监控资源使用
```bash
# 监控GPU使用
watch -n 1 nvidia-smi

# 监控CPU和内存
htop

# 监控网络
netstat -i
```

## ✅ 验证清单

- [ ] 代码同步完成
- [ ] 依赖包安装成功
- [ ] 配置文件调整正确
- [ ] Triton服务器连接正常
- [ ] 模型文件路径正确
- [ ] 服务启动成功
- [ ] 健康检查通过
- [ ] 基础API测试通过
- [ ] 图片检测功能正常
- [ ] 性能表现符合预期

## 📝 测试报告模板

```
OWL检测API验证测试报告
========================

测试环境：
- 服务器：A10 GPU
- Python版本：
- CUDA版本：
- 服务地址：http://A10_IP:8082

测试结果：
1. 服务启动：✅/❌
2. 健康检查：✅/❌
3. 基础API：✅/❌
4. 图片检测：✅/❌
5. 性能表现：处理时间 X.XXX秒

问题记录：
- 问题1：描述和解决方案
- 问题2：描述和解决方案

总结：
服务运行状态良好/存在问题需要解决
```

## 🚀 下一步

验证成功后，可以考虑：
1. 集成到现有系统
2. 添加更多功能特性
3. 性能优化调整
4. 生产环境部署
