#!/usr/bin/env python3
"""
测试日志功能的脚本
模拟各个模块的日志输出
"""

import logging
import sys
from dataclasses import dataclass, field
from typing import Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 简化的数据结构
@dataclass
class ShmDataInfo:
    shape: list = field(default_factory=list)
    type: str = "float64"
    id: bytes = b''

@dataclass
class FrameInfo:
    frame: ShmDataInfo = field(default_factory=ShmDataInfo)
    bboxes: ShmDataInfo = field(default_factory=ShmDataInfo)
    track_objs: ShmDataInfo = field(default_factory=ShmDataInfo)

@dataclass
class MetaInfo:
    frame_id: int = -1
    timestamp: str = ""

@dataclass
class MessageInfo:
    meta_info: MetaInfo = field(default_factory=MetaInfo)
    frame_info: FrameInfo = field(default_factory=FrameInfo)
    skip_processing: bool = False

def simulate_yolo_detection(frame_id: int, has_detection: bool = True):
    """模拟YOLO检测模块的日志"""
    logger.info(f"[YOLO] Frame {frame_id}: 🔍 开始YOLO目标检测")
    
    if has_detection:
        detection_count = 2
        logger.info(f"[YOLO] Frame {frame_id}: ✅ 检测到 {detection_count} 个目标，设置skip_processing=False，继续传递")
        logger.debug(f"[YOLO] Frame {frame_id}: 目标1 - 位置:(100.0,50.0,80.0,120.0), 置信度:0.856, 类别:0")
        logger.debug(f"[YOLO] Frame {frame_id}: 目标2 - 位置:(200.0,100.0,60.0,100.0), 置信度:0.743, 类别:0")
        return False  # skip_processing = False
    else:
        logger.info(f"[YOLO] Frame {frame_id}: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理")
        return True   # skip_processing = True

def simulate_tracking(frame_id: int, skip_processing: bool):
    """模拟跟踪模块的日志"""
    if skip_processing:
        logger.info(f"[TRACK] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
        return skip_processing
    
    logger.info(f"[TRACK] Frame {frame_id}: 🎯 开始目标跟踪")
    input_count = 2
    output_count = 2
    logger.info(f"[TRACK] Frame {frame_id}: ✅ 跟踪完成，输入检测框: {input_count}, 输出跟踪ID: {output_count}")
    logger.debug(f"[TRACK] Frame {frame_id}: 跟踪目标1 - ID: 1")
    logger.debug(f"[TRACK] Frame {frame_id}: 跟踪目标2 - ID: 2")
    logger.info(f"[TRACK] Frame {frame_id}: 🔄 跟踪模块默认传递消息，skip_processing保持: {skip_processing}")
    return skip_processing

def simulate_classification(frame_id: int, skip_processing: bool, has_alert: bool = True):
    """模拟分类模块的日志"""
    if skip_processing:
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
        return skip_processing
    
    logger.info(f"[CLASSIFICATION] Frame {frame_id}: 🏷️ 开始分类处理")
    
    total_count = 2
    if has_alert:
        alert_count = 1
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框1满足预警条件")
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框2不满足预警条件")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: ✅ 检测到预警条件 ({alert_count}/{total_count}个目标满足条件)，设置skip_processing=False，继续传递")
        return False
    else:
        alert_count = 0
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框1不满足预警条件")
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框2不满足预警条件")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: ❌ 未检测到预警条件 ({alert_count}/{total_count}个目标满足条件)，设置skip_processing=True，标记跳过后续处理")
        return True

def simulate_final_node(frame_id: int, skip_processing: bool, module_name: str = "CLASSIFICATION"):
    """模拟最后节点的日志"""
    logger.info(f"[{module_name}] Frame {frame_id}: 当前是最后一个节点")
    
    if not skip_processing:
        logger.info(f"[{module_name}] Frame {frame_id}: 满足告警条件 - 消息不为空且未跳过处理，开始推送告警")
        logger.info(f"[{module_name}] Frame {frame_id}: ✅ 检测到 2 个目标，开始生成告警事件")
        logger.info(f"[{module_name}] Frame {frame_id}: ✅ 告警事件生成成功，事件ID: event_12345")
    else:
        logger.info(f"[{module_name}] Frame {frame_id}: 消息被标记跳过处理，不推送告警")

def simulate_message_flow(frame_id: int, module_name: str, next_module: str, skip_processing: bool):
    """模拟消息传递的日志"""
    if next_module:
        logger.info(f"[{module_name}] Frame {frame_id}: 传递消息到下一个模块 {next_module}, skip_processing={skip_processing}")
    else:
        simulate_final_node(frame_id, skip_processing, module_name)

def test_normal_flow():
    """测试正常检测流程"""
    print("\n" + "="*60)
    print("🔍 测试场景1: 正常检测流程 (有目标检测 + 满足预警条件)")
    print("="*60)
    
    frame_id = 100
    
    # YOLO检测
    skip_processing = simulate_yolo_detection(frame_id, has_detection=True)
    simulate_message_flow(frame_id, "YOLO", "byte_tracker_module", skip_processing)
    
    # 跟踪
    skip_processing = simulate_tracking(frame_id, skip_processing)
    simulate_message_flow(frame_id, "TRACK", "classification_module", skip_processing)
    
    # 分类
    skip_processing = simulate_classification(frame_id, skip_processing, has_alert=True)
    simulate_message_flow(frame_id, "CLASSIFICATION", None, skip_processing)

def test_no_detection_flow():
    """测试无检测目标流程"""
    print("\n" + "="*60)
    print("❌ 测试场景2: 无检测目标流程")
    print("="*60)
    
    frame_id = 101
    
    # YOLO检测
    skip_processing = simulate_yolo_detection(frame_id, has_detection=False)
    simulate_message_flow(frame_id, "YOLO", "byte_tracker_module", skip_processing)
    
    # 跟踪
    skip_processing = simulate_tracking(frame_id, skip_processing)
    simulate_message_flow(frame_id, "TRACK", "classification_module", skip_processing)
    
    # 分类
    skip_processing = simulate_classification(frame_id, skip_processing, has_alert=False)
    simulate_message_flow(frame_id, "CLASSIFICATION", None, skip_processing)

def test_no_alert_flow():
    """测试有检测但无预警流程"""
    print("\n" + "="*60)
    print("⚠️ 测试场景3: 有检测目标但不满足预警条件")
    print("="*60)
    
    frame_id = 102
    
    # YOLO检测
    skip_processing = simulate_yolo_detection(frame_id, has_detection=True)
    simulate_message_flow(frame_id, "YOLO", "byte_tracker_module", skip_processing)
    
    # 跟踪
    skip_processing = simulate_tracking(frame_id, skip_processing)
    simulate_message_flow(frame_id, "TRACK", "classification_module", skip_processing)
    
    # 分类
    skip_processing = simulate_classification(frame_id, skip_processing, has_alert=False)
    simulate_message_flow(frame_id, "CLASSIFICATION", None, skip_processing)

def main():
    """主函数"""
    print("🚀 跳过处理逻辑日志测试")
    print("本测试模拟各个模块的日志输出，展示关键判定环节的日志信息")
    
    # 设置DEBUG级别以显示详细信息
    logger.setLevel(logging.DEBUG)
    
    # 测试各种场景
    test_normal_flow()
    test_no_detection_flow() 
    test_no_alert_flow()
    
    print("\n" + "="*60)
    print("✅ 日志测试完成")
    print("="*60)
    print("\n📋 日志说明:")
    print("- INFO级别: 关键业务流程和状态变化")
    print("- DEBUG级别: 详细的处理过程和数据信息")
    print("- 状态图标: 🔍检测 🎯跟踪 🏷️分类 ⏭️跳过 ✅成功 ❌失败 ⚠️警告")
    print("\n📖 详细说明请参考: LOGGING_GUIDE.md")

if __name__ == "__main__":
    main()
