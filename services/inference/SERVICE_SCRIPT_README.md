# 视频分析推理服务启停脚本使用说明

## 概述

`service.sh` 是一个用于管理视频分析推理服务的启停脚本，提供了完整的服务生命周期管理功能。

## 功能特性

- ✅ **服务管理**: 启动、停止、重启服务
- ✅ **状态监控**: 查看服务状态、健康检查
- ✅ **日志管理**: 查看和跟踪服务日志
- ✅ **镜像管理**: 构建和清理Docker镜像
- ✅ **GPU支持**: 可选的GPU加速支持
- ✅ **端口配置**: 灵活的端口配置
- ✅ **自动化**: 自动创建目录、检查依赖

## 基本用法

### 1. 查看帮助

```bash
./service.sh --help
```

### 2. 启动服务

```bash
# 基本启动
./service.sh start

# 启用GPU支持
./service.sh start --gpu

# 指定端口
./service.sh start -p 8080

# 指定镜像标签
./service.sh start -t v1.0.0
```

### 3. 停止服务

```bash
./service.sh stop
```

### 4. 重启服务

```bash
./service.sh restart
```

### 5. 查看状态

```bash
./service.sh status
```

### 6. 查看日志

```bash
# 查看最近50行日志
./service.sh logs

# 跟踪日志输出
./service.sh logs -f
```

### 7. 健康检查

```bash
./service.sh health
```

### 8. 构建镜像

```bash
# 基本构建
./service.sh build

# 无缓存构建
./service.sh build --no-cache
```

### 9. 清理资源

```bash
./service.sh clean
```

## 命令详解

### start - 启动服务

启动视频分析推理服务容器。

**功能**:
- 检查Docker环境
- 自动构建镜像（如果不存在）
- 创建必要的目录
- 启动容器
- 进行健康检查

**选项**:
- `--gpu`: 启用GPU支持
- `-p, --port PORT`: 指定主机端口（默认9001）
- `-t, --tag TAG`: 指定镜像标签（默认latest）

**示例**:
```bash
./service.sh start --gpu -p 8080 -t production
```

### stop - 停止服务

停止并删除服务容器。

**功能**:
- 优雅停止容器
- 删除容器实例
- 保留镜像和数据

**示例**:
```bash
./service.sh stop
```

### restart - 重启服务

停止服务后重新启动。

**功能**:
- 执行stop操作
- 等待2秒
- 执行start操作

**示例**:
```bash
./service.sh restart --gpu
```

### status - 查看状态

显示服务的详细状态信息。

**显示内容**:
- 容器运行状态
- 镜像信息
- 端口占用情况

**示例**:
```bash
./service.sh status
```

### logs - 查看日志

查看服务运行日志。

**选项**:
- `-f, --follow`: 跟踪日志输出

**示例**:
```bash
# 查看最近日志
./service.sh logs

# 实时跟踪日志
./service.sh logs -f
```

### health - 健康检查

检查服务是否正常运行。

**功能**:
- 发送HTTP健康检查请求
- 最多重试10次
- 显示服务基本信息

**示例**:
```bash
./service.sh health
```

### build - 构建镜像

构建Docker镜像。

**选项**:
- `--no-cache`: 不使用缓存构建

**示例**:
```bash
# 使用缓存构建
./service.sh build

# 无缓存构建
./service.sh build --no-cache
```

### clean - 清理资源

清理容器和镜像。

**功能**:
- 停止并删除容器
- 删除镜像
- 清理悬空镜像

**示例**:
```bash
./service.sh clean
```

## 配置说明

### 默认配置

脚本中的默认配置可以通过参数覆盖：

```bash
SERVICE_NAME="inference-service"
IMAGE_NAME="video-analysis-inference"
IMAGE_TAG="latest"
HOST_PORT="9001"
CONTAINER_PORT="9001"
```

### 目录映射

脚本会自动创建并映射以下目录：

- `./config` → `/workspace/config` (只读)
- `./logs` → `/workspace/logs` (读写)
- `./temp` → `/workspace/temp` (读写)

### 端口配置

- **默认端口**: 9001
- **自定义端口**: 使用 `-p` 参数指定
- **端口检查**: 自动检查端口占用情况

## 使用场景

### 1. 开发环境

```bash
# 启动开发服务
./service.sh start

# 查看实时日志
./service.sh logs -f

# 重启服务（代码更新后）
./service.sh restart
```

### 2. 生产环境

```bash
# 构建生产镜像
./service.sh build --no-cache -t production

# 启动生产服务
./service.sh start -t production --gpu

# 监控服务状态
./service.sh status
./service.sh health
```

### 3. 测试环境

```bash
# 在不同端口启动测试服务
./service.sh start -p 8080 -t test

# 运行测试后清理
./service.sh clean
```

### 4. 故障排除

```bash
# 查看服务状态
./service.sh status

# 查看详细日志
./service.sh logs

# 健康检查
./service.sh health

# 重启服务
./service.sh restart
```

## 常见问题

### 1. 端口被占用

```bash
# 检查端口占用
netstat -tlnp | grep 9001

# 使用其他端口
./service.sh start -p 8080
```

### 2. 镜像不存在

脚本会自动构建镜像，如果构建失败：

```bash
# 手动构建
./service.sh build --no-cache

# 检查Dockerfile和依赖
```

### 3. 服务启动失败

```bash
# 查看详细日志
./service.sh logs

# 检查配置文件
ls -la config/

# 重新构建镜像
./service.sh build --no-cache
```

### 4. GPU支持问题

```bash
# 检查NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi

# 启动时启用GPU
./service.sh start --gpu
```

## 高级用法

### 1. 自定义配置

```bash
# 修改脚本中的默认配置
vim service.sh

# 或使用环境变量
export HOST_PORT=8080
./service.sh start
```

### 2. 集成到CI/CD

```bash
# 在CI/CD流水线中使用
./service.sh build --no-cache -t $BUILD_TAG
./service.sh start -t $BUILD_TAG --gpu
./service.sh health
```

### 3. 监控脚本

```bash
#!/bin/bash
# monitor.sh
while true; do
    if ! ./service.sh health > /dev/null 2>&1; then
        echo "Service unhealthy, restarting..."
        ./service.sh restart
    fi
    sleep 60
done
```

## 注意事项

1. **权限要求**: 需要Docker执行权限
2. **目录结构**: 确保config目录存在且包含配置文件
3. **端口冲突**: 避免与其他服务的端口冲突
4. **资源限制**: 注意容器的资源使用情况
5. **日志管理**: 定期清理日志文件避免磁盘空间不足

## 脚本维护

### 更新脚本

```bash
# 备份当前脚本
cp service.sh service.sh.bak

# 更新脚本后测试
./service.sh --help
./service.sh status
```

### 自定义扩展

可以根据需要修改脚本，添加新的功能：

- 数据库连接检查
- 外部服务依赖检查
- 自动备份功能
- 性能监控集成

## 总结

`service.sh` 脚本提供了完整的服务管理功能，简化了Docker容器的操作流程。通过合理使用各种命令和选项，可以高效地管理视频分析推理服务的整个生命周期。
