# 端口变更说明

## 变更概述

将视频分析推理服务的默认端口从 **8080** 更改为 **9001**，以避免与其他服务的端口冲突。

## 变更详情

### 1. 修改的文件

#### **Dockerfile**
- ✅ 健康检查端口：`8080` → `9001`
- ✅ 暴露端口：`EXPOSE 8080` → `EXPOSE 9001`
- ✅ 启动命令：添加 `--port 9001` 参数

#### **docker-compose.yml**
- ✅ 推理服务端口映射：`8080:8080` → `9001:9001`
- ✅ 推理服务健康检查：`localhost:8080` → `localhost:9001`
- ✅ MinIO控制台端口：`9001:9001` → `9002:9002`（避免冲突）
- ✅ Kafka UI端口：`8081:8080` → `8080:8080`

#### **vas/common/server_args.py**
- ✅ 默认端口：`port: int = 30000` → `port: int = 9001`

#### **DEPLOYMENT_README.md**
- ✅ 所有示例命令中的端口引用
- ✅ 健康检查URL
- ✅ 端口映射说明

#### **build.sh**
- ✅ 构建完成后的示例命令

### 2. 端口分配表

| 服务 | 原端口 | 新端口 | 说明 |
|------|--------|--------|------|
| 推理服务 | 8080 | **9001** | 主要HTTP服务端口 |
| Kafka | 9092 | 9092 | 保持不变 |
| MinIO S3 | 9000 | 9000 | 保持不变 |
| MinIO控制台 | 9001 | **9002** | 避免与推理服务冲突 |
| Kafka UI | 8081 | **8080** | 现在可以使用8080 |
| Triton Server HTTP | 8000 | 8000 | 保持不变 |
| Triton Server GRPC | 8001 | 8001 | 保持不变 |
| Triton Server Metrics | 8002 | 8002 | 保持不变 |

### 3. 访问地址变更

#### **推理服务**
- 原地址：`http://localhost:8080`
- **新地址：`http://localhost:9001`**

#### **健康检查**
- 原地址：`http://localhost:8080/health`
- **新地址：`http://localhost:9001/health`**

#### **API接口**
- 原地址：`http://localhost:8080/api/v1/tasks`
- **新地址：`http://localhost:9001/api/v1/tasks`**

#### **MinIO控制台**
- 原地址：`http://localhost:9001`
- **新地址：`http://localhost:9002`**

#### **Kafka UI**
- 原地址：`http://localhost:8081`
- **新地址：`http://localhost:8080`**

## 使用说明

### 1. 构建和启动

```bash
# 构建镜像
./build.sh

# 启动服务
docker-compose up -d

# 验证服务
curl http://localhost:9001/health
```

### 2. 单独运行容器

```bash
# 基本运行
docker run -p 9001:9001 video-analysis-inference:latest

# 带GPU支持
docker run --gpus all -p 9001:9001 video-analysis-inference:latest

# 带配置文件挂载
docker run -p 9001:9001 -v $(pwd)/config:/workspace/config:ro video-analysis-inference:latest
```

### 3. 手动指定端口

如果需要使用其他端口，可以通过以下方式：

#### **Docker运行时指定**
```bash
# 使用8080端口
docker run -p 8080:9001 video-analysis-inference:latest

# 使用自定义端口
docker run -p 8888:9001 video-analysis-inference:latest
```

#### **启动参数指定**
```bash
# 在容器内修改端口
docker run -p 8080:8080 video-analysis-inference:latest \
  python3 -m vas.entrypoints.http_server --config_path config/config.yaml --port 8080
```

#### **docker-compose自定义**
```yaml
services:
  inference:
    ports:
      - "8080:9001"  # 主机端口:容器端口
```

### 4. 配置文件方式

也可以通过修改配置文件来指定端口（如果支持）：

```yaml
# config/config.yaml
server:
  host: "0.0.0.0"
  port: 9001
```

## 兼容性说明

### 1. 向后兼容

- ✅ 所有API接口路径保持不变，只是端口变更
- ✅ 请求和响应格式完全兼容
- ✅ 功能特性无任何变化

### 2. 客户端适配

如果有现有的客户端代码，只需要修改基础URL：

```python
# 原代码
base_url = "http://localhost:8080"

# 新代码
base_url = "http://localhost:9001"
```

```javascript
// 原代码
const baseURL = 'http://localhost:8080';

// 新代码
const baseURL = 'http://localhost:9001';
```

```bash
# 原curl命令
curl http://localhost:8080/health

# 新curl命令
curl http://localhost:9001/health
```

### 3. 负载均衡配置

如果使用了负载均衡器，需要更新配置：

#### **Nginx**
```nginx
upstream inference_backend {
    server localhost:9001;  # 原来是8080
}
```

#### **HAProxy**
```
backend inference_servers
    server inference1 localhost:9001 check  # 原来是8080
```

## 验证步骤

### 1. 服务启动验证

```bash
# 1. 启动服务
docker-compose up -d

# 2. 检查容器状态
docker-compose ps

# 3. 检查端口监听
netstat -tlnp | grep 9001

# 4. 健康检查
curl http://localhost:9001/health
```

### 2. 功能验证

```bash
# 1. 获取服务信息
curl http://localhost:9001/

# 2. 获取任务列表
curl http://localhost:9001/api/v1/tasks

# 3. 获取统计信息
curl http://localhost:9001/api/v1/stats
```

### 3. 其他服务验证

```bash
# MinIO控制台（新端口9002）
curl http://localhost:9002/

# Kafka UI（新端口8080）
curl http://localhost:8080/

# Kafka服务
telnet localhost 9092
```

## 注意事项

1. **防火墙规则**：如果有防火墙，需要开放9001端口
2. **监控配置**：更新监控系统中的端口配置
3. **文档更新**：更新相关的API文档和使用说明
4. **测试脚本**：更新自动化测试脚本中的端口配置
5. **部署脚本**：更新CI/CD流水线中的端口配置

## 回滚方案

如果需要回滚到原来的8080端口：

1. 恢复 `vas/common/server_args.py` 中的端口设置
2. 恢复 `Dockerfile` 中的端口配置
3. 恢复 `docker-compose.yml` 中的端口映射
4. 重新构建和部署

```bash
# 快速回滚命令
git checkout HEAD~1 -- vas/common/server_args.py Dockerfile docker-compose.yml
./build.sh
docker-compose up -d
```

## 总结

端口变更已完成，新的推理服务将在 **9001** 端口提供服务。所有相关配置文件和文档都已更新，确保了服务的正常运行和访问。
