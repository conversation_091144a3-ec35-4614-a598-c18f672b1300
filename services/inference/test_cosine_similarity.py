#!/usr/bin/env python3
"""
测试余弦相似度计算的脚本
验证分类分数的计算是否正确
"""

import numpy as np
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

def test_cosine_similarity():
    """测试余弦相似度计算"""
    print("🧮 测试余弦相似度计算")
    print("=" * 50)
    
    # 模拟文本特征和图像特征
    np.random.seed(42)
    
    # 创建一些测试向量
    text_features = {
        "person": np.random.randn(1, 512),
        "helmet": np.random.randn(1, 512),
        "background": np.random.randn(1, 512),
        "no_helmet": np.random.randn(1, 512)
    }
    
    image_feature = np.random.randn(1, 512)
    
    print(f"文本特征维度: {list(text_features.values())[0].shape}")
    print(f"图像特征维度: {image_feature.shape}")
    print()
    
    # 计算余弦相似度
    similarities = {}
    max_pos_conf = -2.0
    max_neg_conf = -2.0
    
    positive_labels = ["person", "helmet"]
    negative_labels = ["background", "no_helmet"]
    
    print("📊 正例标签分类结果:")
    for label in positive_labels:
        text_feature = text_features[label]
        cos_sim = np.dot(text_feature, image_feature.T) / (np.linalg.norm(text_feature) * np.linalg.norm(image_feature))
        score = float(cos_sim[0][0])
        similarities[label] = score
        max_pos_conf = score if score > max_pos_conf else max_pos_conf
        
        print(f"  {label}: {score:.4f}")
        logger.info(f"[TEST] 正例标签 '{label}': {score:.4f}")
    
    print("\n📊 负例标签分类结果:")
    for label in negative_labels:
        text_feature = text_features[label]
        cos_sim = np.dot(text_feature, image_feature.T) / (np.linalg.norm(text_feature) * np.linalg.norm(image_feature))
        score = float(cos_sim[0][0])
        similarities[label] = score
        max_neg_conf = score if score > max_neg_conf else max_neg_conf
        
        print(f"  {label}: {score:.4f}")
        logger.info(f"[TEST] 负例标签 '{label}': {score:.4f}")
    
    print(f"\n📈 分类摘要:")
    print(f"  最高正例分数: {max_pos_conf:.4f}")
    print(f"  最高负例分数: {max_neg_conf:.4f}")
    
    # 测试告警逻辑
    confidence_threshold = 0.1
    print(f"  置信度阈值: {confidence_threshold:.4f}")
    
    alert_condition = max_pos_conf > max_neg_conf and max_pos_conf > confidence_threshold
    print(f"  告警状态: {'✅ 满足告警条件' if alert_condition else '❌ 不满足告警条件'}")
    
    return similarities

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("=" * 50)
    
    # 测试相同向量（应该得到1.0）
    vec1 = np.array([[1, 0, 0, 0]])
    vec2 = np.array([[1, 0, 0, 0]])
    cos_sim = np.dot(vec1, vec2.T) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    score = float(cos_sim[0][0])
    print(f"相同向量的余弦相似度: {score:.4f} (应该接近1.0)")
    
    # 测试正交向量（应该得到0.0）
    vec1 = np.array([[1, 0, 0, 0]])
    vec2 = np.array([[0, 1, 0, 0]])
    cos_sim = np.dot(vec1, vec2.T) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    score = float(cos_sim[0][0])
    print(f"正交向量的余弦相似度: {score:.4f} (应该接近0.0)")
    
    # 测试相反向量（应该得到-1.0）
    vec1 = np.array([[1, 0, 0, 0]])
    vec2 = np.array([[-1, 0, 0, 0]])
    cos_sim = np.dot(vec1, vec2.T) / (np.linalg.norm(vec1) * np.linalg.norm(vec2))
    score = float(cos_sim[0][0])
    print(f"相反向量的余弦相似度: {score:.4f} (应该接近-1.0)")

def test_initialization_values():
    """测试初始化值的合理性"""
    print("\n⚙️ 测试初始化值")
    print("=" * 50)
    
    # 模拟实际可能的余弦相似度值
    possible_scores = [-0.8, -0.3, 0.1, 0.5, 0.9]
    
    # 测试旧的初始化值 -100
    old_init = -100
    max_score_old = old_init
    for score in possible_scores:
        max_score_old = score if score > max_score_old else max_score_old
    
    # 测试新的初始化值 -2.0
    new_init = -2.0
    max_score_new = new_init
    for score in possible_scores:
        max_score_new = score if score > max_score_new else max_score_new
    
    print(f"可能的分数: {possible_scores}")
    print(f"旧初始化值 (-100): 最终最大值 = {max_score_old}")
    print(f"新初始化值 (-2.0): 最终最大值 = {max_score_new}")
    print(f"修复效果: {'✅ 正确' if max_score_new == max(possible_scores) else '❌ 错误'}")

def main():
    """主函数"""
    print("🚀 余弦相似度计算测试")
    print("验证分类分数计算的修复效果\n")
    
    # 测试余弦相似度计算
    similarities = test_cosine_similarity()
    
    # 测试边界情况
    test_edge_cases()
    
    # 测试初始化值
    test_initialization_values()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")
    print("=" * 50)
    
    print("\n📋 修复说明:")
    print("1. 修复了 zeroshot_classification_module.py 中的变量名错误")
    print("2. 将初始化值从 -100 改为 -2.0（余弦相似度范围是[-1,1]）")
    print("3. 确保分类分数能够正确显示和比较")
    
    print("\n🔧 余弦相似度说明:")
    print("- 取值范围: [-1, 1]")
    print("- 1.0: 完全相似")
    print("- 0.0: 无关联")
    print("- -1.0: 完全相反")
    print("- 在分类任务中，通常正值表示相似，负值表示不相似")

if __name__ == "__main__":
    main()
