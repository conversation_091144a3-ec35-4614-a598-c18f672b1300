#!/usr/bin/env python3
"""
OWL检测测试脚本 - 带可视化结果
使用resource目录下的图片进行检测，并生成带检测框的结果图片
"""

import os
import sys
import json
import time
import argparse
import requests
from typing import List, Dict, Any, Tuple
import cv2
import numpy as np
from pathlib import Path

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)


class OWLDetectionVisualizer:
    """OWL检测结果可视化器"""
    
    def __init__(self, api_url: str = "http://*************:10086"):
        """
        初始化可视化器
        
        Args:
            api_url: API服务地址
        """
        self.api_url = api_url.rstrip('/')
        self.session = requests.Session()
        self.session.timeout = 30
        
        # 预定义颜色 (BGR格式)
        self.colors = [
            (0, 255, 0),    # 绿色
            (255, 0, 0),    # 蓝色
            (0, 0, 255),    # 红色
            (255, 255, 0),  # 青色
            (255, 0, 255),  # 洋红色
            (0, 255, 255),  # 黄色
            (128, 0, 128),  # 紫色
            (255, 165, 0),  # 橙色
            (0, 128, 255),  # 橙蓝色
            (128, 255, 0),  # 春绿色
        ]
    
    def detect_objects(self, image_path: str, query_texts: List[str], 
                      threshold: float = None, nms_threshold: float = None) -> Dict[str, Any]:
        """
        调用API进行目标检测
        
        Args:
            image_path: 图片路径
            query_texts: 查询文本列表
            threshold: 检测阈值
            nms_threshold: NMS阈值
            
        Returns:
            检测结果字典
        """
        if not os.path.exists(image_path):
            raise FileNotFoundError(f"图片文件不存在: {image_path}")
        
        print(f"正在检测图片: {image_path}")
        print(f"查询文本: {query_texts}")
        
        # 准备请求数据
        files = {'image': open(image_path, 'rb')}
        data = {'query_texts': json.dumps(query_texts)}
        
        if threshold is not None:
            data['threshold'] = str(threshold)
        if nms_threshold is not None:
            data['nms_threshold'] = str(nms_threshold)
        
        try:
            # 发送请求
            start_time = time.time()
            response = self.session.post(
                f"{self.api_url}/api/v1/detect",
                files=files,
                data=data
            )
            request_time = time.time() - start_time
            
            files['image'].close()
            
            if response.status_code == 200:
                result = response.json()
                result['request_time'] = request_time
                return result
            else:
                raise RuntimeError(f"API请求失败: HTTP {response.status_code}, {response.text}")
                
        except requests.exceptions.RequestException as e:
            raise RuntimeError(f"网络请求失败: {e}")
    
    def draw_detection_results(self, image: np.ndarray, detections: List[Dict[str, Any]], 
                             query_texts: List[str]) -> np.ndarray:
        """
        在图片上绘制检测结果
        
        Args:
            image: 原始图片
            detections: 检测结果列表
            query_texts: 查询文本列表
            
        Returns:
            绘制了检测框的图片
        """
        result_image = image.copy()
        
        for i, detection in enumerate(detections):
            bbox = detection.get('bbox', [])
            score = detection.get('score', 0)
            query_text = detection.get('query_text', '')
            query_id = detection.get('query_id', 0)
            
            if len(bbox) != 4:
                continue
            
            x, y, w, h = bbox
            x1, y1 = int(x), int(y)
            x2, y2 = int(x + w), int(y + h)
            
            # 选择颜色
            color = self.colors[query_id % len(self.colors)]
            
            # 绘制检测框
            cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 2)
            
            # 准备标签文本
            label = f"{query_text}: {score:.3f}"
            
            # 计算文本大小
            font = cv2.FONT_HERSHEY_SIMPLEX
            font_scale = 0.6
            thickness = 2
            (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
            
            # 绘制文本背景
            cv2.rectangle(result_image, 
                         (x1, y1 - text_height - baseline - 5), 
                         (x1 + text_width, y1), 
                         color, -1)
            
            # 绘制文本
            cv2.putText(result_image, label, (x1, y1 - baseline - 2), 
                       font, font_scale, (255, 255, 255), thickness)
        
        return result_image
    
    def add_summary_info(self, image: np.ndarray, detections: List[Dict[str, Any]], 
                        processing_time: float, query_texts: List[str]) -> np.ndarray:
        """
        在图片上添加检测摘要信息
        
        Args:
            image: 图片
            detections: 检测结果
            processing_time: 处理时间
            query_texts: 查询文本
            
        Returns:
            添加了摘要信息的图片
        """
        result_image = image.copy()
        h, w = result_image.shape[:2]
        
        # 准备摘要信息
        summary_lines = [
            f"Total Detections: {len(detections)}",
            f"Processing Time: {processing_time:.3f}s",
            f"Query Texts: {', '.join(query_texts)}"
        ]
        
        # 统计每个类别的检测数量
        class_counts = {}
        for detection in detections:
            query_text = detection.get('query_text', 'unknown')
            class_counts[query_text] = class_counts.get(query_text, 0) + 1
        
        if class_counts:
            summary_lines.append("Class Counts:")
            for class_name, count in class_counts.items():
                summary_lines.append(f"  {class_name}: {count}")
        
        # 绘制摘要信息
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.5
        thickness = 1
        line_height = 20
        
        # 计算背景区域大小
        max_text_width = 0
        for line in summary_lines:
            (text_width, _), _ = cv2.getTextSize(line, font, font_scale, thickness)
            max_text_width = max(max_text_width, text_width)
        
        bg_height = len(summary_lines) * line_height + 10
        bg_width = max_text_width + 20
        
        # 绘制半透明背景
        overlay = result_image.copy()
        cv2.rectangle(overlay, (10, 10), (10 + bg_width, 10 + bg_height), (0, 0, 0), -1)
        cv2.addWeighted(overlay, 0.7, result_image, 0.3, 0, result_image)
        
        # 绘制文本
        for i, line in enumerate(summary_lines):
            y = 30 + i * line_height
            cv2.putText(result_image, line, (20, y), font, font_scale, (255, 255, 255), thickness)
        
        return result_image
    
    def process_image(self, image_path: str, query_texts: List[str], 
                     output_dir: str = "output", threshold: float = None, 
                     nms_threshold: float = None) -> Tuple[str, Dict[str, Any]]:
        """
        处理单张图片：检测 + 可视化
        
        Args:
            image_path: 输入图片路径
            query_texts: 查询文本列表
            output_dir: 输出目录
            threshold: 检测阈值
            nms_threshold: NMS阈值
            
        Returns:
            (输出图片路径, 检测结果)
        """
        # 读取原始图片
        image = cv2.imread(image_path)
        if image is None:
            raise ValueError(f"无法读取图片: {image_path}")
        
        # 执行检测
        result = self.detect_objects(image_path, query_texts, threshold, nms_threshold)
        
        if result.get('code') != 200:
            raise RuntimeError(f"检测失败: {result.get('message', 'Unknown error')}")
        
        data = result.get('data', {})
        detections = data.get('detections', [])
        processing_time = data.get('processing_time', 0)
        
        print(f"检测完成，发现 {len(detections)} 个目标，处理时间: {processing_time:.3f}s")
        
        # 绘制检测结果
        result_image = self.draw_detection_results(image, detections, query_texts)
        
        # 添加摘要信息
        result_image = self.add_summary_info(result_image, detections, processing_time, query_texts)
        
        # 保存结果图片
        os.makedirs(output_dir, exist_ok=True)
        input_name = Path(image_path).stem
        output_path = os.path.join(output_dir, f"{input_name}_detection_result.jpg")
        cv2.imwrite(output_path, result_image)
        
        print(f"结果图片已保存: {output_path}")
        
        return output_path, result
    
    def process_directory(self, input_dir: str, query_texts: List[str], 
                         output_dir: str = "output", threshold: float = None, 
                         nms_threshold: float = None) -> List[Tuple[str, str, Dict[str, Any]]]:
        """
        处理目录中的所有图片
        
        Args:
            input_dir: 输入目录
            query_texts: 查询文本列表
            output_dir: 输出目录
            threshold: 检测阈值
            nms_threshold: NMS阈值
            
        Returns:
            [(输入路径, 输出路径, 检测结果), ...]
        """
        # 支持的图片格式
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
        
        # 查找所有图片文件
        image_files = []
        for ext in image_extensions:
            image_files.extend(Path(input_dir).glob(f"*{ext}"))
            image_files.extend(Path(input_dir).glob(f"*{ext.upper()}"))
        
        if not image_files:
            print(f"在目录 {input_dir} 中未找到图片文件")
            return []
        
        print(f"找到 {len(image_files)} 张图片")
        
        results = []
        for i, image_file in enumerate(image_files, 1):
            print(f"\n处理第 {i}/{len(image_files)} 张图片...")
            try:
                output_path, result = self.process_image(
                    str(image_file), query_texts, output_dir, threshold, nms_threshold
                )
                results.append((str(image_file), output_path, result))
            except Exception as e:
                print(f"处理图片 {image_file} 失败: {e}")
                continue
        
        return results


def print_detection_summary(results: List[Tuple[str, str, Dict[str, Any]]]):
    """打印检测结果摘要"""
    if not results:
        print("没有处理任何图片")
        return

    print("\n" + "=" * 80)
    print("检测结果摘要")
    print("=" * 80)

    total_detections = 0
    total_time = 0
    class_stats = {}

    for input_path, output_path, result in results:
        data = result.get('data', {})
        detections = data.get('detections', [])
        processing_time = data.get('processing_time', 0)

        total_detections += len(detections)
        total_time += processing_time

        # 统计类别
        for detection in detections:
            query_text = detection.get('query_text', 'unknown')
            class_stats[query_text] = class_stats.get(query_text, 0) + 1

        print(f"图片: {Path(input_path).name}")
        print(f"  检测数量: {len(detections)}")
        print(f"  处理时间: {processing_time:.3f}s")
        print(f"  输出文件: {output_path}")

        if detections:
            print("  检测详情:")
            for detection in detections:
                bbox = detection.get('bbox', [])
                score = detection.get('score', 0)
                query_text = detection.get('query_text', '')
                print(f"    - {query_text}: {score:.3f} at [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
        print()

    print(f"总计:")
    print(f"  处理图片: {len(results)} 张")
    print(f"  检测目标: {total_detections} 个")
    print(f"  平均处理时间: {total_time/len(results):.3f}s")

    if class_stats:
        print(f"  类别统计:")
        for class_name, count in sorted(class_stats.items()):
            print(f"    {class_name}: {count} 个")

    print("=" * 80)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OWL检测测试脚本 - 带可视化结果")
    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8082",
        help="API服务URL (默认: http://localhost:8082)"
    )
    parser.add_argument(
        "--input",
        type=str,
        default="resource",
        help="输入图片路径或目录 (默认: resource)"
    )
    parser.add_argument(
        "--output",
        type=str,
        default="detection_output",
        help="输出目录 (默认: detection_output)"
    )
    parser.add_argument(
        "--query-texts",
        type=str,
        nargs="+",
        default=["person", "car", "bicycle", "cat", "dog"],
        help="查询文本列表 (默认: person car bicycle cat dog)"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        help="检测阈值 (0-1之间，默认使用服务器配置)"
    )
    parser.add_argument(
        "--nms-threshold",
        type=float,
        help="NMS阈值 (0-1之间，默认使用服务器配置)"
    )
    parser.add_argument(
        "--save-json",
        action="store_true",
        help="保存检测结果为JSON文件"
    )

    args = parser.parse_args()

    try:
        print("OWL检测可视化测试脚本")
        print(f"API服务: {args.url}")
        print(f"输入路径: {args.input}")
        print(f"输出目录: {args.output}")
        print(f"查询文本: {args.query_texts}")
        if args.threshold is not None:
            print(f"检测阈值: {args.threshold}")
        if args.nms_threshold is not None:
            print(f"NMS阈值: {args.nms_threshold}")
        print()

        # 创建可视化器
        visualizer = OWLDetectionVisualizer(args.url)

        # 检查输入路径
        if not os.path.exists(args.input):
            print(f"错误: 输入路径不存在: {args.input}")
            sys.exit(1)

        # 处理图片
        if os.path.isfile(args.input):
            # 单张图片
            print("处理单张图片...")
            output_path, result = visualizer.process_image(
                args.input, args.query_texts, args.output,
                args.threshold, args.nms_threshold
            )
            results = [(args.input, output_path, result)]
        else:
            # 目录
            print("处理目录中的图片...")
            results = visualizer.process_directory(
                args.input, args.query_texts, args.output,
                args.threshold, args.nms_threshold
            )

        # 打印摘要
        print_detection_summary(results)

        # 保存JSON结果
        if args.save_json and results:
            json_output = os.path.join(args.output, "detection_results.json")
            json_data = {
                "summary": {
                    "total_images": len(results),
                    "total_detections": sum(len(r[2].get('data', {}).get('detections', [])) for r in results),
                    "query_texts": args.query_texts,
                    "threshold": args.threshold,
                    "nms_threshold": args.nms_threshold
                },
                "results": [
                    {
                        "input_path": input_path,
                        "output_path": output_path,
                        "detection_result": result
                    }
                    for input_path, output_path, result in results
                ]
            }

            with open(json_output, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            print(f"检测结果已保存到: {json_output}")

        print(f"\n所有结果图片已保存到: {args.output}")

    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
