version: '3.8'

services:
  owl-detection-api:
    build:
      context: .
      dockerfile: Dockerfile.owl_api
    container_name: owl-detection-api
    ports:
      - "8082:8082"
    volumes:
      # 配置文件挂载
      - ./config/owl_detection_api_config.yaml:/workspace/owl_detection_api/config/owl_detection_api_config.yaml:ro
      # 模型文件挂载
      - ./models:/workspace/video_analysis_server/models:ro
      # 日志目录挂载
      - ./logs/owl_api:/workspace/owl_detection_api/logs
      # 测试图片挂载（可选）
      - ./data/images:/workspace/owl_detection_api/test_images:ro
    environment:
      - PYTHONPATH=/workspace/owl_detection_api
      - PYTHONUNBUFFERED=1
      # Triton服务器配置
      - TRITON_SERVER_IP=triton-server
      - TRITON_SERVER_PORT=9991
    networks:
      - owl-detection-network
    depends_on:
      - triton-server
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8082/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Triton推理服务器（如果需要独立部署）
  triton-server:
    image: nvcr.io/nvidia/tritonserver:23.10-py3
    container_name: triton-server
    ports:
      - "8000:8000"  # HTTP
      - "8001:8001"  # GRPC
      - "8002:8002"  # Metrics
    volumes:
      # 模型仓库挂载
      - ./models:/models:ro
    command: >
      tritonserver
      --model-repository=/models
      --allow-http=true
      --allow-grpc=true
      --allow-metrics=true
      --log-verbose=1
    networks:
      - owl-detection-network
    runtime: nvidia
    environment:
      - NVIDIA_VISIBLE_DEVICES=all
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/v2/health/ready"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 120s

networks:
  owl-detection-network:
    driver: bridge

volumes:
  owl-api-logs:
    driver: local
