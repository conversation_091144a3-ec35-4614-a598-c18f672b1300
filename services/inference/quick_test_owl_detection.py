#!/usr/bin/env python3
"""
OWL检测快速测试脚本
简化版本，快速测试resource目录下的图片
"""

import os
import sys
import json
import requests
import cv2
import numpy as np
from pathlib import Path

# 添加项目路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)


def detect_and_visualize(image_path: str, api_url: str = "http://175.168.10.52:10086",
                        query_text: str = "person", threshold: float = 0.15):
    """
    检测并可视化单张图片

    Args:
        image_path: 图片路径
        api_url: API服务地址
        query_text: 查询文本（单个）
        threshold: 检测阈值
    """
    
    print(f"正在处理图片: {image_path}")
    print(f"查询文本: {query_text}")
    print(f"检测阈值: {threshold}")

    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图片 {image_path}")
        return

    # 调用API检测
    try:
        files = {'image': open(image_path, 'rb')}
        data = {
            'query_texts': json.dumps([query_text]),  # 包装成数组但只有一个元素
            'threshold': str(threshold)
        }
        
        print("正在调用检测API...")
        response = requests.post(f"{api_url}/api/v1/detect", files=files, data=data, timeout=30)
        files['image'].close()
        
        if response.status_code != 200:
            print(f"API调用失败: HTTP {response.status_code}")
            print(response.text)
            return
        
        result = response.json()
        if result.get('code') != 200:
            print(f"检测失败: {result.get('message')}")
            return
        
        # 解析结果
        data = result.get('data', {})
        detections = data.get('detections', [])
        processing_time = data.get('processing_time', 0)
        
        print(f"检测完成! 发现 {len(detections)} 个目标，处理时间: {processing_time:.3f}s")

        # 可视化结果
        result_image = visualize_detections(image, detections, [query_text])
        
        # 保存结果
        output_dir = "quick_test_output"
        os.makedirs(output_dir, exist_ok=True)
        
        input_name = Path(image_path).stem
        output_path = os.path.join(output_dir, f"{input_name}_detected.jpg")
        cv2.imwrite(output_path, result_image)
        
        print(f"结果图片已保存: {output_path}")
        
        # 打印检测详情
        if detections:
            print("\n检测详情:")
            for i, detection in enumerate(detections, 1):
                bbox = detection.get('bbox', [])
                score = detection.get('score', 0)
                query_text = detection.get('query_text', '')
                print(f"  {i}. {query_text}: {score:.3f} at [{bbox[0]:.1f}, {bbox[1]:.1f}, {bbox[2]:.1f}, {bbox[3]:.1f}]")
        else:
            print("未检测到任何目标")
        
        # 保存JSON结果
        json_path = os.path.join(output_dir, f"{input_name}_result.json")
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(result, f, ensure_ascii=False, indent=2)
        print(f"检测结果已保存: {json_path}")
        
    except Exception as e:
        print(f"检测过程出错: {e}")
        import traceback
        traceback.print_exc()


def visualize_detections(image: np.ndarray, detections: list, query_texts: list) -> np.ndarray:
    """
    在图片上绘制检测结果
    
    Args:
        image: 原始图片
        detections: 检测结果列表
        query_texts: 查询文本列表
        
    Returns:
        绘制了检测框的图片
    """
    result_image = image.copy()
    
    # 预定义颜色 (BGR格式)
    colors = [
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 0, 255),    # 红色
        (255, 255, 0),  # 青色
        (255, 0, 255),  # 洋红色
        (0, 255, 255),  # 黄色
        (128, 0, 128),  # 紫色
        (255, 165, 0),  # 橙色
        (0, 128, 255),  # 橙蓝色
        (128, 255, 0),  # 春绿色
    ]
    
    for detection in detections:
        bbox = detection.get('bbox', [])
        score = detection.get('score', 0)
        query_text = detection.get('query_text', '')
        query_id = detection.get('query_id', 0)
        
        if len(bbox) != 4:
            continue
        
        x, y, w, h = bbox
        x1, y1 = int(x), int(y)
        x2, y2 = int(x + w), int(y + h)
        
        # 选择颜色
        color = colors[query_id % len(colors)]
        
        # 绘制检测框
        cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 3)
        
        # 准备标签文本
        label = f"{query_text}: {score:.3f}"
        
        # 计算文本大小
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.7
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
        
        # 绘制文本背景
        cv2.rectangle(result_image, 
                     (x1, y1 - text_height - baseline - 8), 
                     (x1 + text_width + 4, y1), 
                     color, -1)
        
        # 绘制文本
        cv2.putText(result_image, label, (x1 + 2, y1 - baseline - 4), 
                   font, font_scale, (255, 255, 255), thickness)
    
    # 添加摘要信息
    summary_text = f"Detections: {len(detections)}"
    cv2.putText(result_image, summary_text, (10, 30), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.0, (0, 255, 0), 2)
    
    return result_image


def test_resource_images():
    """测试resource目录下的所有图片"""
    resource_dir = "resource"

    if not os.path.exists(resource_dir):
        print(f"错误: resource目录不存在: {resource_dir}")
        return

    # 查找图片文件
    image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif'}
    image_files = []

    for ext in image_extensions:
        image_files.extend(Path(resource_dir).glob(f"*{ext}"))
        image_files.extend(Path(resource_dir).glob(f"*{ext.upper()}"))

    if not image_files:
        print(f"在 {resource_dir} 目录中未找到图片文件")
        return

    print(f"找到 {len(image_files)} 张图片")

    # 常用的查询文本（每次只测试一个）
    query_texts = [
        "person", "car", "bicycle", "cat", "dog", "bird"
    ]

    # 处理每张图片，对每个查询文本分别测试
    for i, image_file in enumerate(image_files, 1):
        print(f"\n{'='*60}")
        print(f"处理第 {i}/{len(image_files)} 张图片")
        print(f"{'='*60}")

        for j, query_text in enumerate(query_texts):
            print(f"\n--- 测试查询文本 {j+1}/{len(query_texts)}: {query_text} ---")
            try:
                detect_and_visualize(str(image_file), query_text=query_text, threshold=0.15)
            except Exception as e:
                print(f"处理图片 {image_file} 查询 {query_text} 失败: {e}")
                continue
    
    print(f"\n{'='*60}")
    print("所有图片处理完成!")
    print("结果保存在 quick_test_output 目录中")
    print(f"{'='*60}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="OWL检测快速测试脚本")
    parser.add_argument(
        "--image",
        type=str,
        help="指定单张图片路径（如果不指定则处理resource目录下所有图片）"
    )
    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8082",
        help="API服务URL (默认: http://localhost:8082)"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.15,
        help="检测阈值 (默认: 0.15)"
    )
    parser.add_argument(
        "--query-text",
        type=str,
        default="person",
        help="查询文本（单个）"
    )
    
    args = parser.parse_args()
    
    print("OWL检测快速测试脚本")
    print(f"API服务: {args.url}")
    
    try:
        if args.image:
            # 处理单张图片
            if not os.path.exists(args.image):
                print(f"错误: 图片文件不存在: {args.image}")
                sys.exit(1)
            
            detect_and_visualize(
                args.image,
                api_url=args.url,
                query_text=args.query_text,
                threshold=args.threshold
            )
        else:
            # 处理resource目录
            test_resource_images()
            
    except KeyboardInterrupt:
        print("\n用户中断")
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
