# video_analysis_server

## deepstream-service-lib 安装
```
apt update && apt-get install \
    libgstrtspserver-1.0-dev \
    gstreamer1.0-rtsp \
    libapr1 \
    libapr1-dev \
    libaprutil1 \
    libaprutil1-dev \
    libgeos-dev \
    libcurl4-openssl-dev

apt-get install \
    libjson-glib-dev \
    libsoup-gnome2.4-dev \
    libgstreamer-plugins-bad1.0-dev \
    libnice-dev \
    gstreamer1.0-nice

pkg-config --cflags json-glib-1.0
git clone https://github.com/prominenceai/deepstream-services-library.git

cd /workspace/dsl_server/deepstream-services-library
make -j8 && make install
cp libdsl.so /usr/local/lib/libdsl.so
```

## python包依赖安装
```
python3 -m pip install pybase64 requests pydantic tqdm setproctitle psutil lapx
python3 -m pip install numpy==1.26.1
python3 -m pip install opencv-python-headless==*********
python3 -m pip install pyarrow=10.0.0
python3 -m pip install flask=2.25
python3 -m pip install tritonclient[all]==2.59.0
python3 -m pip install transformers==4.40.0
wget https://github.com/NVIDIA-AI-IOT/deepstream_python_apps/releases/download/v1.1.11/pyds-1.1.11-py3-none-linux_x86_64.whl
python3 -m pip install pyds-1.1.11-py3-none-linux_x86_64.whl
```


## 启动tritonserver服务
```
docker run --gpus all --rm -itd --privileged --shm-size 32g -p 9991:9001 \
    -v /home/<USER>/gitee_codes/nanoowl2/triton_models:/workspace/triton_models \
    tritonserver:23.10-py3 tritonserver --model-repository=/workspace/triton_models \
    --grpc-port=9001 --model-control-mode=explicit
```


## 检测分类服务
```
<!-- 启动服务 -->
python start_owl_detection_api.py --config config/owl_detection_api_config.yaml --port 8090
or
python start_owl_detection_api.py --config config/owl_detection_api_config.yaml --port 8090 &

<!-- 测试检测接口 -->
python test_owl_detection_api.py --url http://localhost:8090 --image /home/<USER>/bxt-analysis/bxt-analysis/services/inference/data/images/cat_224_224.jpg --query-texts cat

<!-- 测试分类接口 -->
python test_classification_simple.py

```

## 视频分析服务
```
<!-- 构建镜像 -->
./service.sh build

<!-- 脚本启动镜像 -->
./service.sh start

<!-- 手动启动镜像 -->
docker run --rm -it  --privileged --gpus all -p 10001:10001 -p 19001:9001 -v $PWD:/workspace --entrypoint /bin/bash video-analysis-inference:latest

<!-- 启动code-server -->
export PASSWORD=1
./code-server --host "0.0.0.0" --port 10001 --auth password

<!-- 启动mediamtx流服务器 -->
docker run --rm -itd -v $PWD/data/videos:/workspace/test_videos -e MTX_RTSPTRANSPORTS=tcp -p 18554:8554 bluenviron/mediamtx:latest-ffmpeg

docker exec -it xxxxx /bin/sh 

ffmpeg -re -stream_loop -1 -i knight.mp4 -c copy -f rtsp rtsp://127.0.0.1:8554/mystream

<!-- 停止镜像 -->
./service.sh stop
```
