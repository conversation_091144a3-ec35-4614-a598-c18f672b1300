# 分类模块时间间隔控制和重复告警避免

## 功能概述

为分类模块添加了两个重要功能：
1. **trackId时间间隔控制**：每个trackId每隔1秒才分类一次
2. **重复告警避免**：某个trackId告警过后，不再重复告警

## 实现原理

### 1. 时间间隔控制

#### 数据结构
```python
self.track_last_classification_frame = {}  # 记录每个trackId上次分类的frame_id
self.classification_interval_frames = 25   # 分类间隔帧数（默认25帧=1秒@25fps）
self.frame_rate = 25                       # 视频帧率
self.decode_step = 1                       # DSL跳帧步长
```

#### 间隔计算逻辑
```python
# 实际间隔 = 帧率 * 跳帧步长
self.classification_interval_frames = frame_rate * decode_step
```

**示例**：
- 25fps视频，decodeStep=4：间隔 = 25 * 4 = 100帧
- 由于跳帧，实际frame_id序列：1, 5, 9, 13, 17, 21, 25, 29, 33, 37, 41, 45, 49, 53, 57, 61, 65, 69, 73, 77, 81, 85, 89, 93, 97, 101...
- trackId第一次分类：frame_id=1，下次分类：frame_id=101（间隔100帧）

#### 判断逻辑
```python
def should_classify_track(self, track_id: int, current_frame_id: int) -> bool:
    # 如果已告警，不再分类
    if track_id in self.track_alerted:
        return False
        
    # 第一次遇到，直接分类
    if track_id not in self.track_last_classification_frame:
        return True
        
    # 检查时间间隔
    last_frame = self.track_last_classification_frame[track_id]
    frame_diff = current_frame_id - last_frame
    return abs(frame_diff) >= self.classification_interval_frames
```

### 2. 重复告警避免

#### 数据结构
```python
self.track_alerted = {}  # 记录已经告警过的trackId
```

#### 避免逻辑
```python
def mark_track_alerted(self, track_id: int):
    """标记trackId已告警，避免重复告警"""
    self.track_alerted[track_id] = True
```

一旦某个trackId触发告警，就会被标记，后续该trackId的所有检测框都不会再进行分类。

## 处理流程

### 修改前的流程
```
每帧 -> 对所有检测框分类 -> 输出结果
```

### 修改后的流程
```
每帧 -> 遍历检测框 -> 检查trackId -> 判断是否应该分类 -> 分类 -> 更新时间/标记告警
                                    ↓
                              跳过（已告警或未到时间）
```

### 详细处理逻辑
```python
for bbox_idx, bbox in enumerate(bboxes):
    track_id = track_ids[bbox_idx]
    
    # 1. 检查是否应该分类
    if not self.should_classify_track(track_id, frame_id):
        continue  # 跳过分类
    
    # 2. 进行分类
    # ... 分类逻辑 ...
    
    # 3. 更新分类时间
    self.update_track_classification_time(track_id, frame_id)
    
    # 4. 如果告警，标记已告警
    if alert_flag:
        self.mark_track_alerted(track_id)
```

## 配置动态更新

### 配置获取
```python
def update_classification_interval(self, task_info):
    if task_info and hasattr(task_info, 'device'):
        frame_rate = task_info.device.streamConfig.frameRate
        decode_step = task_info.device.streamConfig.decoderConf.decodeStep
        self.classification_interval_frames = frame_rate * decode_step
```

### 配置示例
```json
{
  "device": {
    "streamConfig": {
      "frameRate": 25,
      "decoderConf": {
        "decodeStep": 4
      }
    }
  }
}
```

对应的分类间隔：25 * 4 = 100帧

## 日志输出

### 跳过分类的日志
```
[CLASSIFICATION] Frame 5: trackId 123 未到分类时间或已告警，跳过分类
```

### 分类执行的日志
```
[CLASSIFICATION] Frame 101: 开始对trackId 123 进行分类
```

### 告警日志
```
[CLASSIFICATION] Frame 101: trackId 123 框1 - 告警! 最高正例:0.856 最高负例:0.234
```

## 优势

1. **性能优化**：减少不必要的分类计算，提高处理效率
2. **告警去重**：避免同一目标重复告警，减少噪音
3. **时间控制**：确保分类频率合理，符合业务需求
4. **配置灵活**：自动适配不同的帧率和跳帧配置

## 注意事项

1. **frame_id跳跃**：由于DSL跳帧，frame_id不连续，使用绝对差值判断间隔
2. **trackId生命周期**：trackId可能会重复使用，但在当前实现中不清理历史记录
3. **内存管理**：长时间运行可能需要定期清理过期的trackId记录
4. **配置更新**：每次处理消息时都会更新配置，确保使用最新参数

## 后续优化建议

1. **内存清理**：定期清理长时间未出现的trackId记录
2. **配置缓存**：避免每次都重新读取配置
3. **告警重置**：提供机制重置已告警的trackId状态
4. **统计信息**：添加分类跳过/执行的统计信息
