# OWL检测HTTP API服务

基于OWL-ViT和CLIP的0样本目标检测HTTP API服务，支持通过文本描述检测图像中的任意目标。

## 🚀 功能特性

- **0样本检测**: 无需训练，通过文本描述即可检测任意目标
- **高性能**: 基于Triton推理服务器，支持GPU加速
- **易于使用**: 标准HTTP API接口，支持多种客户端
- **灵活配置**: 支持运行时参数调整（阈值、NMS等）
- **生产就绪**: 完整的日志、监控、健康检查

## 📋 系统要求

### 硬件要求
- GPU: NVIDIA GPU (推荐RTX 3080或更高)
- 内存: 16GB+ RAM
- 存储: 20GB+ 可用空间

### 软件要求
- CUDA 11.8+
- Docker (可选)
- Python 3.8+

## 🛠️ 安装部署

### 方式1: 本地部署

1. **安装依赖**
```bash
cd inference
pip install -r requirements_owl_api.txt
```

2. **配置服务**
编辑 `config/owl_detection_api_config.yaml`:
```yaml
triton_server:
  ip: "**********"  # 修改为你的Triton服务器地址
  port: 9991
```

3. **启动服务**
```bash
python3 start_owl_detection_api.py --config config/owl_detection_api_config.yaml
```

### 方式2: Docker部署

1. **构建镜像**
```bash
docker build -f Dockerfile.owl_api -t owl-detection-api:latest .
```

2. **运行容器**
```bash
docker run -p 8082:8082 \
  -v $(pwd)/config:/workspace/owl_detection_api/config:ro \
  -v $(pwd)/models:/workspace/video_analysis_server/models:ro \
  owl-detection-api:latest
```

### 方式3: Docker Compose部署

```bash
docker-compose -f docker-compose.owl_api.yml up
```

### 方式4: 使用启动脚本

```bash
# 本地启动
./start_owl_api_service.sh --mode local --debug

# Docker启动
./start_owl_api_service.sh --mode docker --build

# Compose启动
./start_owl_api_service.sh --mode compose
```

## 📖 API文档

### 基础信息

- **服务地址**: `http://localhost:8082`
- **API版本**: v1
- **数据格式**: JSON

### 接口列表

#### 1. 健康检查
```http
GET /health
```

**响应示例**:
```json
{
  "code": 200,
  "message": "服务正常",
  "data": {
    "status": "healthy",
    "timestamp": **********.789,
    "service_info": {...}
  }
}
```

#### 2. 服务信息
```http
GET /api/v1/info
```

#### 3. 目标检测 (主要接口)
```http
POST /api/v1/detect
Content-Type: multipart/form-data
```

**请求参数**:
- `image` (file): 图片文件 (支持jpg, png, bmp等格式)
- `query_texts` (string): JSON数组，查询文本列表，如 `["person", "car", "cat"]`
- `threshold` (float, 可选): 检测阈值 (0-1)，默认0.15
- `nms_threshold` (float, 可选): NMS阈值 (0-1)，默认0.3

**响应示例**:
```json
{
  "code": 200,
  "message": "检测成功",
  "data": {
    "detections": [
      {
        "bbox": [100.5, 200.3, 150.2, 180.7],
        "score": 0.85,
        "query_text": "person",
        "query_id": 0
      }
    ],
    "total_count": 1,
    "processing_time": 0.123,
    "parameters": {
      "query_texts": ["person", "car"],
      "threshold": 0.15,
      "nms_threshold": 0.3
    }
  }
}
```

## 💻 使用示例

### Python客户端
```python
import requests
import json

# 检测请求
files = {'image': open('test.jpg', 'rb')}
data = {'query_texts': json.dumps(['person', 'car', 'bicycle'])}

response = requests.post(
    'http://localhost:8082/api/v1/detect',
    files=files,
    data=data
)

result = response.json()
print(f"检测到 {result['data']['total_count']} 个目标")
```

### curl命令
```bash
curl -X POST http://localhost:8082/api/v1/detect \
  -F "image=@test.jpg" \
  -F "query_texts=[\"person\", \"car\"]" \
  -F "threshold=0.2"
```

### 使用示例客户端
```bash
python3 example_client.py \
  --image test.jpg \
  --query-texts person car bicycle \
  --threshold 0.2 \
  --output result.json
```

## 🧪 测试验证

### 运行测试套件
```bash
python3 test_owl_detection_api.py --url http://localhost:8082
```

### 带图片的完整测试
```bash
python3 test_owl_detection_api.py \
  --url http://localhost:8082 \
  --image data/images/test.jpg \
  --query-texts person car cat
```

## ⚙️ 配置说明

### 主要配置项

```yaml
# 服务器配置
server:
  host: "0.0.0.0"
  port: 8082
  debug: false

# OWL检测配置
owl_detector:
  default_policy:
    embeddings_query:
      threshold: 0.15    # 检测阈值
    nms:
      threshold: 0.3     # NMS阈值

# Triton服务器配置
triton_server:
  ip: "**********"
  port: 9991

# API限制配置
api:
  max_file_size: 10485760      # 最大文件大小 (10MB)
  max_query_texts: 10          # 最大查询文本数量
  request_timeout: 30          # 请求超时时间
```

## 🔧 故障排除

### 常见问题

1. **模型加载失败**
   - 检查模型文件路径是否正确
   - 确认Triton服务器正常运行

2. **内存不足**
   - 减少并发请求数量
   - 调整图片大小

3. **检测结果为空**
   - 降低检测阈值
   - 尝试更具体的查询文本

### 日志查看
```bash
# 查看服务日志
tail -f logs/owl_detection_api.log

# Docker日志
docker logs owl-detection-api
```

## 📊 性能优化

### 推荐配置
- **GPU**: RTX 3080或更高
- **批处理**: 单张图片处理
- **缓存**: 文本嵌入自动缓存
- **并发**: 建议限制在5个并发请求

### 性能指标
- **处理时间**: 通常0.1-0.5秒/张
- **内存使用**: 约4-8GB GPU内存
- **吞吐量**: 约2-10 QPS (取决于硬件)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

本项目采用Apache 2.0许可证。
