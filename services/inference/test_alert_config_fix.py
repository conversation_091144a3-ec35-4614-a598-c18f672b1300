#!/usr/bin/env python3
"""
测试AlertConfig修复的脚本
验证scheduler和inference之间的数据传递是否正确
"""

import json
import logging
from dataclasses import dataclass, field
from typing import List, Optional

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)

# 模拟scheduler的AlertConfig结构
@dataclass
class SchedulerAlertConfig:
    labels: List[str] = field(default_factory=list)
    confidence: float = 0.0
    positiveLabels: List[str] = field(default_factory=list)
    negativeLabels: List[str] = field(default_factory=list)

# 模拟inference的AlertConfig结构
@dataclass
class InferenceAlertConfig:
    labels: List[str] = field(default_factory=list)
    confidence: float = 0.0
    positiveLabels: List[str] = field(default_factory=list)
    negativeLabels: List[str] = field(default_factory=list)

def test_new_format():
    """测试新格式的AlertConfig（包含positiveLabels和negativeLabels）"""
    print("🆕 测试新格式AlertConfig")
    print("=" * 50)
    
    # 模拟scheduler发送的新格式配置
    scheduler_config = SchedulerAlertConfig(
        labels=["no_helmet"],
        confidence=0.8,
        positiveLabels=["person", "helmet"],
        negativeLabels=["background", "no_helmet"]
    )
    
    print("Scheduler发送的配置:")
    print(f"  labels: {scheduler_config.labels}")
    print(f"  confidence: {scheduler_config.confidence}")
    print(f"  positiveLabels: {scheduler_config.positiveLabels}")
    print(f"  negativeLabels: {scheduler_config.negativeLabels}")
    
    # 模拟inference接收和处理
    inference_config = InferenceAlertConfig(
        labels=scheduler_config.labels,
        confidence=scheduler_config.confidence,
        positiveLabels=scheduler_config.positiveLabels,
        negativeLabels=scheduler_config.negativeLabels
    )
    
    # 模拟inference的兼容性处理逻辑
    positive_labels = inference_config.positiveLabels if inference_config.positiveLabels else []
    negative_labels = inference_config.negativeLabels if inference_config.negativeLabels else []
    
    print("\nInference处理后的配置:")
    print(f"  正例标签: {positive_labels}")
    print(f"  负例标签: {negative_labels}")
    print(f"  置信度: {inference_config.confidence}")
    
    # 验证结果
    success = (
        len(positive_labels) > 0 and 
        len(negative_labels) > 0 and
        "person" in positive_labels and
        "helmet" in positive_labels and
        "background" in negative_labels and
        "no_helmet" in negative_labels
    )
    
    print(f"\n✅ 新格式测试: {'通过' if success else '失败'}")
    return success

def test_old_format_compatibility():
    """测试旧格式的兼容性（只有labels字段）"""
    print("\n🔄 测试旧格式兼容性")
    print("=" * 50)
    
    # 模拟scheduler发送的旧格式配置
    scheduler_config = SchedulerAlertConfig(
        labels=["no_helmet"],
        confidence=0.8,
        positiveLabels=[],  # 旧格式没有这些字段
        negativeLabels=[]
    )
    
    print("Scheduler发送的旧格式配置:")
    print(f"  labels: {scheduler_config.labels}")
    print(f"  confidence: {scheduler_config.confidence}")
    print(f"  positiveLabels: {scheduler_config.positiveLabels} (空)")
    print(f"  negativeLabels: {scheduler_config.negativeLabels} (空)")
    
    # 模拟inference接收和处理
    inference_config = InferenceAlertConfig(
        labels=scheduler_config.labels,
        confidence=scheduler_config.confidence,
        positiveLabels=scheduler_config.positiveLabels,
        negativeLabels=scheduler_config.negativeLabels
    )
    
    # 模拟inference的兼容性处理逻辑
    positive_labels = inference_config.positiveLabels if inference_config.positiveLabels else []
    negative_labels = inference_config.negativeLabels if inference_config.negativeLabels else []
    
    # 兼容性处理：如果正负例标签都为空，从labels推导
    if not positive_labels and not negative_labels and inference_config.labels:
        negative_labels = inference_config.labels
        positive_labels = ["person", "normal"]  # 默认正例标签
        print("\n⚠️ 使用兼容模式，从labels推导正负例标签")
    
    print("\nInference兼容性处理后的配置:")
    print(f"  正例标签: {positive_labels}")
    print(f"  负例标签: {negative_labels}")
    print(f"  置信度: {inference_config.confidence}")
    
    # 验证结果
    success = (
        len(positive_labels) > 0 and 
        len(negative_labels) > 0 and
        "no_helmet" in negative_labels
    )
    
    print(f"\n✅ 兼容性测试: {'通过' if success else '失败'}")
    return success

def test_json_serialization():
    """测试JSON序列化和反序列化"""
    print("\n📄 测试JSON序列化")
    print("=" * 50)
    
    # 创建新格式的配置
    original_config = {
        "algorithmId": "helmet_detection",
        "algorithmType": "CLASSIFICATION",
        "alertConfig": {
            "labels": ["no_helmet"],
            "confidence": 0.8,
            "positiveLabels": ["person", "helmet"],
            "negativeLabels": ["background", "no_helmet"]
        }
    }
    
    print("原始配置:")
    print(json.dumps(original_config, indent=2, ensure_ascii=False))
    
    # 序列化为JSON
    json_str = json.dumps(original_config)
    
    # 反序列化
    deserialized_config = json.loads(json_str)
    alert_config = deserialized_config["alertConfig"]
    
    print("\n反序列化后的AlertConfig:")
    print(f"  labels: {alert_config.get('labels', [])}")
    print(f"  confidence: {alert_config.get('confidence', 0.0)}")
    print(f"  positiveLabels: {alert_config.get('positiveLabels', [])}")
    print(f"  negativeLabels: {alert_config.get('negativeLabels', [])}")
    
    # 验证结果
    success = (
        alert_config.get("positiveLabels") == ["person", "helmet"] and
        alert_config.get("negativeLabels") == ["background", "no_helmet"] and
        alert_config.get("confidence") == 0.8
    )
    
    print(f"\n✅ JSON序列化测试: {'通过' if success else '失败'}")
    return success

def test_real_config_file():
    """测试实际的配置文件"""
    print("\n📁 测试实际配置文件")
    print("=" * 50)
    
    try:
        # 读取实际的配置文件
        config_file = "data/config/yolo_tracking_clip_task.json"
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 查找分类算法的alertConfig
        algorithm_chain = config["algorithmOrchestration"]["algorithmChain"]
        classification_algo = None
        
        for algo in algorithm_chain:
            if algo.get("algorithmType") == "CLASSIFICATION":
                classification_algo = algo
                break
        
        if classification_algo and "alertConfig" in classification_algo:
            alert_config = classification_algo["alertConfig"]
            
            print("配置文件中的AlertConfig:")
            print(f"  labels: {alert_config.get('labels', [])}")
            print(f"  confidence: {alert_config.get('confidence', 0.0)}")
            print(f"  positiveLabels: {alert_config.get('positiveLabels', [])}")
            print(f"  negativeLabels: {alert_config.get('negativeLabels', [])}")
            
            # 验证新字段是否存在
            has_positive = "positiveLabels" in alert_config and len(alert_config["positiveLabels"]) > 0
            has_negative = "negativeLabels" in alert_config and len(alert_config["negativeLabels"]) > 0
            
            print(f"\n✅ 配置文件测试: {'通过' if has_positive and has_negative else '失败'}")
            return has_positive and has_negative
        else:
            print("❌ 配置文件中未找到分类算法的alertConfig")
            return False
            
    except Exception as e:
        print(f"❌ 读取配置文件失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 AlertConfig修复验证测试")
    print("验证scheduler和inference之间的数据传递修复效果\n")
    
    # 运行所有测试
    test_results = []
    
    test_results.append(test_new_format())
    test_results.append(test_old_format_compatibility())
    test_results.append(test_json_serialization())
    test_results.append(test_real_config_file())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print("=" * 60)
    
    test_names = [
        "新格式AlertConfig测试",
        "旧格式兼容性测试", 
        "JSON序列化测试",
        "实际配置文件测试"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{i+1}. {name}: {status}")
    
    overall_success = all(test_results)
    print(f"\n🎯 总体结果: {'✅ 全部通过' if overall_success else '❌ 存在失败'}")
    
    if overall_success:
        print("\n🎉 AlertConfig修复成功！")
        print("- scheduler和inference的数据结构已对齐")
        print("- 支持新的positiveLabels和negativeLabels字段")
        print("- 保持对旧格式的兼容性")
        print("- JSON序列化和反序列化正常")
    else:
        print("\n⚠️ 还有问题需要解决，请检查失败的测试项")

if __name__ == "__main__":
    main()
