# 基于DeepStream 7.0的多架构镜像
FROM deepstream:7.0-triton-multiarch

# 设置环境变量
ENV DEBIAN_FRONTEND=noninteractive
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1

# 更新包管理器并安装系统依赖
RUN apt update && apt-get install -y \
    # GStreamer和RTSP相关
    libgstrtspserver-1.0-dev \
    gstreamer1.0-rtsp \
    gstreamer1.0-nice \
    libgstreamer-plugins-bad1.0-dev \
    # Apache相关
    libapr1 \
    libapr1-dev \
    libaprutil1 \
    libaprutil1-dev \
    # 地理空间库
    libgeos-dev \
    # 网络库
    libcurl4-openssl-dev \
    # JSON和HTTP库
    libjson-glib-dev \
    libsoup-gnome2.4-dev \
    libnice-dev \
    # 开发工具
    pkg-config \
    build-essential \
    # 图像处理库
    libjpeg-dev \
    libpng-dev \
    libtiff-dev \
    libwebp-dev \
    # 字体库（用于图像文本渲染）
    fonts-dejavu-core \
    # 时区数据
    tzdata \
    # 网络工具
    curl \
    wget \
    # 清理缓存
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 验证pkg-config配置
RUN pkg-config --cflags json-glib-1.0

# 设置工作目录
WORKDIR /workspace

# 复制依赖文件
COPY dependencies/libdsl.so /usr/local/lib/libdsl.so
COPY dependencies/pyds-1.1.11-py3-none-linux_x86_64.whl .

# 更新pip并安装Python依赖
COPY requirements.txt .
RUN python3 -m pip install --upgrade pip setuptools wheel && \
    python3 -m pip install -r requirements.txt && \
    python3 -m pip cache purge

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /workspace/logs \
    /workspace/temp \
    /workspace/screenshots \
    /workspace/events \
    /workspace/cache/huggingface

# 设置环境变量
ENV TRANSFORMERS_CACHE=/workspace/cache/huggingface
ENV HF_HOME=/workspace/cache/huggingface

# 设置权限
RUN chmod +x /workspace/vas/entrypoints/http_server.py

# 创建非root用户（安全最佳实践）
RUN groupadd -r inference && useradd -r -g inference inference

# 设置目录权限，确保inference用户可以读写
RUN chown -R inference:inference /workspace && \
    chmod -R 755 /workspace && \
    chmod -R 777 /workspace/logs \
    /workspace/temp \
    /workspace/screenshots \
    /workspace/events \
    /workspace/cache

USER inference

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:9001/health || exit 1

# 暴露端口
EXPOSE 9001

# 设置启动命令
CMD ["python3", "launch_server.py", "--config-path", "config/config.yaml", "--log-path", "/workspace/logs", "--port", "9001"]

