#!/usr/bin/env python3
"""
测试服务注册功能
"""

import os
import sys
import yaml
import requests
import json
from vas.services.service_registry import ServiceRegistry

def test_config_loading():
    """测试配置加载"""
    print("=== 测试配置加载 ===")
    
    config_path = "config/config.yaml"
    if not os.path.exists(config_path):
        print(f"✗ 配置文件不存在: {config_path}")
        return None
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        service_registry_config = config.get('service_registry')
        if not service_registry_config:
            print("✗ 配置文件中未找到service_registry配置")
            return None
        
        if not service_registry_config.get('enabled', False):
            print("✗ 服务注册功能已禁用")
            return None
        
        print("✓ 配置加载成功")
        print(f"配置内容: {json.dumps(service_registry_config, indent=2, ensure_ascii=False)}")
        return service_registry_config
        
    except Exception as e:
        print(f"✗ 配置加载失败: {e}")
        return None

def test_service_registration(config):
    """测试服务注册功能"""
    print("\n=== 测试服务注册功能 ===")
    
    # 创建服务注册器
    registry = ServiceRegistry(config, "localhost", 9001)
    
    # 测试构建注册数据
    print("\n1. 测试构建注册数据...")
    try:
        registration_data = registry._build_registration_data()
        print(f"✓ 注册数据构建成功")
        print(f"注册数据: {json.dumps(registration_data, indent=2, ensure_ascii=False)}")
    except Exception as e:
        print(f"✗ 注册数据构建失败: {e}")
        return
    
    # 测试注册服务
    print("\n2. 测试服务注册...")
    try:
        success = registry.register_service()
        if success:
            print("✓ 服务注册成功")
        else:
            print("✗ 服务注册失败")
    except Exception as e:
        print(f"✗ 服务注册异常: {e}")
    
    # 测试健康检查
    print("\n3. 测试健康检查...")
    try:
        health_ok = registry._health_check()
        if health_ok:
            print("✓ 健康检查通过")
        else:
            print("✗ 健康检查失败")
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")

def test_scheduler_connectivity():
    """测试调度器连接性"""
    print("\n=== 测试调度器连接性 ===")
    
    scheduler_url = os.getenv('SCHEDULER_URL', 'http://localhost:8080')
    print(f"调度器地址: {scheduler_url}")
    
    # 测试健康检查端点
    print("\n1. 测试健康检查端点...")
    try:
        response = requests.get(f"{scheduler_url}/actuator/health", timeout=5)
        if response.status_code == 200:
            print(f"✓ 调度器健康检查正常")
            try:
                health_data = response.json()
                print(f"健康状态: {json.dumps(health_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容: {response.text}")
        else:
            print(f"✗ 调度器健康检查失败: HTTP {response.status_code}")
    except Exception as e:
        print(f"✗ 调度器连接失败: {e}")
    
    # 测试注册端点
    print("\n2. 测试注册端点...")
    test_data = {
        "serviceName": "test-service",
        "baseUrl": "http://localhost:9001",
        "algorithmOrchestration": {
            "algorithm_chain": [
                {
                    "algorithm_id": "test_detection",
                    "algorithm_name": "测试检测",
                    "algorithm_type": "DETECTION",
                    "order": 1
                }
            ]
        },
        "maxQuota": 1,
        "region": "test",
        "gpuType": "test"
    }
    
    try:
        response = requests.post(
            f"{scheduler_url}/api/v1/services/register",
            json=test_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        if response.status_code in [200, 201]:
            print(f"✓ 注册端点正常")
            try:
                response_data = response.json()
                print(f"注册响应: {json.dumps(response_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"响应内容: {response.text}")
        else:
            print(f"✗ 注册端点失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
    except Exception as e:
        print(f"✗ 注册端点连接失败: {e}")

def main():
    """主函数"""
    print("推理服务注册功能测试")
    print("=" * 50)
    
    # 设置环境变量示例
    print("环境变量设置示例:")
    print("export SCHEDULER_URL=http://localhost:8080")
    print("export SERVICE_NAME=inference-service-test")
    print("export MAX_QUOTA=5")
    print("export REGION=test")
    print("export GPU_TYPE=A10")
    print()
    
    # 测试配置加载
    config = test_config_loading()
    if not config:
        print("配置加载失败，无法继续测试")
        return
    
    # 测试调度器连接
    test_scheduler_connectivity()
    
    # 测试服务注册
    test_service_registration(config)
    
    print("\n=== 测试完成 ===")
    print("如果调度器连接失败，请确保调度器服务正在运行")
    print("如果需要修改配置，请编辑 config/config.yaml 文件中的 service_registry 部分")

if __name__ == "__main__":
    main()
