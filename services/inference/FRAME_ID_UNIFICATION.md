# Frame ID 统一化修改说明

## 问题描述

之前的实现中，各个模块都维护自己的 frame_id 计数器，导致整个流水线中的 frame_id 不一致：

- **DSL Pipeline**: 使用 `frame_meta.frame_num`，在跳帧时是跳跃的（如：1, 5, 9, 13...）
- **ByteTracker**: 内部维护 `self.frame_id += 1`，是连续的（如：1, 2, 3, 4...）
- **其他模块**: 各自维护连续的计数器

## 解决方案

统一所有模块使用 DSL 传递的原始 frame_id，确保整个流水线中 frame_id 的一致性。

## 修改内容

### 1. ByteTracker 核心修改

**文件**: `inference/vas/bytetracker/byte_tracker.py`

```python
# 修改前
def update(self, dets, _ = None):
    self.frame_id += 1
    
# 修改后  
def update(self, dets, frame_id=None):
    # 使用传入的frame_id，如果没有传入则使用内部计数器
    if frame_id is not None:
        self.frame_id = frame_id
    else:
        self.frame_id += 1
```

### 2. ByteTrackerModule 修改

**文件**: `inference/vas/modules/byte_tracker_module.py`

```python
# 修改前
track_objs = self.tracker.update(bboxes)

# 修改后
# 传递DSL的原始frame_id给tracker
track_objs = self.tracker.update(bboxes, frame_id=frame_id)
```

同时移除了 debug 方法中的自维护 frame_id 计数器。

### 3. VideoCapModule 修改

**文件**: `inference/vas/modules/video_cap_module.py`

- 移除了 `self.frame_id = 0` 初始化
- 修改 `process_none_message` 方法使用传入消息的 frame_id

### 4. TripwireIntrusionModule 修改

**文件**: `inference/vas/modules/tripwire_intrusion_module.py`

- 修改 `process_none_message` 方法使用传入消息的 frame_id
- 移除了 `self.frame_id += 1` 自增逻辑

### 5. ZoneIntrusionModule 修改

**文件**: `inference/vas/modules/zone_intrusion_module.py`

- 修改 `process_none_message` 方法使用传入消息的 frame_id
- 移除了 `self.frame_id += 1` 自增逻辑

## 修改效果

### 修改前的 frame_id 流向：
```
DSL Pipeline (1,5,9,13...) -> YOLO (1,2,3,4...) -> Track (1,2,3,4...) -> Classification (1,2,3,4...)
```

### 修改后的 frame_id 流向：
```
DSL Pipeline (1,5,9,13...) -> YOLO (1,5,9,13...) -> Track (1,5,9,13...) -> Classification (1,5,9,13...)
```

## 验证方法

1. **查看 DSL Pipeline 日志**：
   ```
   frame: {frame_number} {frame_image.shape}, bbox:{bboxes}
   ```

2. **查看各模块日志**：
   ```
   [YOLO] Frame {frame_id}: 🔍 开始YOLO目标检测
   [TRACK] Frame {frame_id}: 🎯 开始目标跟踪  
   [CLASSIFICATION] Frame {frame_id}: 🏷️ 开始分类处理
   ```

3. **确认 frame_id 一致性**：
   所有模块的 frame_id 应该与 DSL Pipeline 的 frame_number 保持一致。

## 注意事项

1. **跳帧配置**: 当 `decodeStep: 4` 时，frame_id 会是 1, 5, 9, 13... 这是正常的
2. **时间间隔计算**: 分类模块的时间间隔控制需要考虑 frame_id 的跳跃性
3. **向后兼容**: 保留了 ByteTracker 的内部计数器作为备选方案

## 相关配置

在设备配置中，`decodeStep` 参数控制跳帧间隔：

```json
{
  "decoderConf": {
    "keyFrameOnly": false,
    "decodeStep": 4  // 每隔4帧解码一次
  }
}
```

## 后续工作

1. 验证所有模块的 frame_id 一致性
2. 更新分类模块的时间间隔控制逻辑，适配跳跃的 frame_id
3. 完善测试用例，确保修改的正确性
