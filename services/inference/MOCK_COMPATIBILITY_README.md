# Mock接口兼容性改造说明

## 概述

本次改造将真实的inference实现改造为完全兼容mock接口的版本，保持原有的推理逻辑不变，但数据模型和HTTP接口与mock服务完全一致。

## 改造内容

### 1. 新增文件

- `test_mock_compatibility.py` - 接口兼容性测试脚本
- `MOCK_COMPATIBILITY_README.md` - 本说明文档

### 2. 修改文件

- `vas/entrypoints/task_interface.py` - 数据模型完全对齐mock格式
- `vas/entrypoints/http_server.py` - HTTP接口完全兼容mock

### 3. 新增接口

#### 健康检查接口
```
GET /health
```
返回系统健康状态和统计信息。

#### 根路径接口
```
GET /
```
返回服务基本信息和可用端点列表。

#### 任务管理接口
```
POST /api/v1/tasks          # 创建任务（兼容mock格式）
GET /api/v1/tasks           # 获取任务列表
GET /api/v1/tasks/{id}      # 获取任务详情
DELETE /api/v1/tasks/{id}   # 删除任务
```

#### 任务事件接口
```
GET /api/v1/tasks/{id}/events  # 获取任务事件
```

#### 统计信息接口
```
GET /api/v1/stats           # 获取服务统计信息
```

### 4. 数据模型完全统一

#### 核心改造原则
- **无转换**: 真实实现的数据模型直接修改为与mock完全一致
- **无引用**: 两个工程之间没有任何代码引用或依赖
- **完全兼容**: 字段名、类型、结构完全一致

#### 主要模型变更
- `SimplifiedAtomicTask`: 字段改为snake_case + alias
- `TaskRequest`: 新增包装类
- `TaskResponse`: 直接返回任务信息，不再使用code/message/data结构
- `Device`, `StreamConfig`, `AlgorithmOrchestration`: 字段名完全对齐
- `TaskStatus`: 增加PENDING状态，保持CREATED兼容

#### 字段命名统一
所有模型使用snake_case内部字段名 + camelCase alias：
```python
class SimplifiedAtomicTask(BaseModel):
    task_id: str = Field(alias="taskId")
    task_name: str = Field(alias="taskName")
    # ...

    class Config:
        populate_by_name = True
```

### 5. 简化的实现方式

#### 核心设计原则
- **直接使用SimplifiedAtomicTask**: 不创建额外的Task对象
- **task_infos统一管理**: 在task_infos中存储所有任务信息和状态
- **移除mock特有字段**: 不包含screenshots、alert_interval等mock专用字段
- **保持推理核心**: 完全保持原有的多进程推理逻辑

#### 数据存储结构
```python
task_infos[task_id] = {
    "atomic_task": SimplifiedAtomicTask,  # 原始任务对象
    "task_id": str,
    "device_id": str,
    "rtsp_url": str,
    "status": TaskStatus,
    "start_time": datetime,
    "last_event_time": datetime,
    "event_count": int,
    "config": dict,
    "processes": [],  # 推理进程列表
    "queues": []      # 进程间通信队列
}
```

### 6. 动态算法链构建

#### 算法类型映射
```python
ALGORITHM_TYPE_TO_MODULE = {
    "CLASSIFICATION": CLASSIFICATION_MODULE,
    "TRACKING": BYTE_TRACKER_MODULE,
    "DETECTION": YOLOV8_DETECTOR_MODULE,  # 默认YOLO
    "OWL_DETECTION": OWL_DETECTOR_MODULE,
    "PIPELINE": DSL_PIPELINE_MODULE
}
```

#### 构建规则
1. **按order逆序排列**: 算法链按order字段逆序构建module_list
2. **智能检测器选择**:
   - 如果algorithm_type是"DETECTION"且包含"owl"关键字 → OWL_DETECTOR_MODULE
   - 否则 → YOLOV8_DETECTOR_MODULE
3. **严格类型校验**: 只支持预定义的5种模块类型
4. **管道模块保证**: DSL_PIPELINE_MODULE总是在最后执行
5. **去重处理**: 避免重复的模块

#### 支持的算法类型
- `CLASSIFICATION` → `CLASSIFICATION_MODULE`
- `TRACKING` → `BYTE_TRACKER_MODULE`
- `DETECTION` → `YOLOV8_DETECTOR_MODULE` 或 `OWL_DETECTOR_MODULE`
- `PIPELINE` → `DSL_PIPELINE_MODULE`

### 7. 保持的原有功能

- 多进程推理架构
- 算法编排支持（动态构建，不再硬编码）
- 进程生命周期管理
- 原有的日志系统
- 信号处理机制

## 使用方法

### 启动服务
```bash
cd inference
python -m vas.entrypoints.http_server
```

### 测试接口兼容性
```bash
cd inference
python test_mock_compatibility.py
```

### 创建任务示例
```bash
curl -X POST http://localhost:8080/api/v1/tasks \
  -H "Content-Type: application/json" \
  -d '{
    "taskRequest": {
      "taskId": "test-001",
      "taskName": "测试任务",
      "taskMeta": {
        "enabled": true,
        "taskLevel": "HIGH",
        "protocol": "VIDEO",
        "eventTypeId": "event-001",
        "eventAction": ["ALERT"]
      },
      "algorithmOrchestration": {
        "orchestrationId": "orch-001",
        "orchestrationType": "YOLO_TRACKING_CLIP",
        "algorithmChain": [
          {
            "algorithmId": "yolo-v8-detector",
            "algorithmName": "YOLO检测器",
            "algorithmType": "DETECTION",
            "order": 1,
            "required": true
          },
          {
            "algorithmId": "byte-tracker",
            "algorithmName": "目标跟踪",
            "algorithmType": "TRACKING",
            "order": 2,
            "required": true
          },
          {
            "algorithmId": "clip-classifier",
            "algorithmName": "CLIP分类",
            "algorithmType": "CLASSIFICATION",
            "order": 3,
            "required": true
          }
        ],
        "status": "CREATED"
      },
      "device": {
        "deviceId": "camera-001",
        "streamConfig": {
          "url": "rtsp://example.com/stream",
          "decoderConf": {
            "keyFrameOnly": false,
            "decodeStep": 1
          }
        }
      }
    }
  }'
```

## 兼容性说明

### 向后兼容
- 原有的SimplifiedAtomicTask格式仍然支持
- 原有的推理逻辑完全保持
- 原有的配置和启动方式不变

### Mock接口兼容
- 所有mock服务的接口都已实现
- 响应格式与mock服务保持一致
- 支持mock格式的请求数据

## 注意事项

1. **端口配置**: 确保服务端口与调用方配置一致
2. **算法支持**: 目前支持YOLO_TRACKING_CLIP和OVIT_CLIP两种编排类型
3. **资源管理**: 任务创建会启动多个推理进程，注意资源使用
4. **错误处理**: 所有接口都包含完整的错误处理和日志记录

## 测试建议

1. 使用提供的测试脚本验证基本功能
2. 测试任务的完整生命周期（创建→运行→删除）
3. 验证健康检查和统计信息的准确性
4. 测试异常情况的处理（如重复创建任务、删除不存在的任务等）

## 部署说明

改造后的服务可以直接替换原有的mock服务，提供相同的接口但具备真实的推理能力。
