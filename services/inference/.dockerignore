# Git相关
.git
.gitignore
.gitattributes

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE相关
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统相关
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 日志文件
*.log
logs/
*.out

# 临时文件
tmp/
temp/
*.tmp
*.temp

# 测试相关
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 文档
docs/
*.md
README*
CHANGELOG*
LICENSE*

# 配置文件（可能包含敏感信息）
.env
*.env
config/local_*
config/dev_*
config/test_*

# 数据文件
data/
datasets/
*.csv
*.json
*.xml
*.yaml
*.yml

# 模型文件（通常很大）
*.pth
*.pt
*.onnx
*.trt
*.engine
checkpoints/

# 图片和视频文件
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
*.mp4
*.avi
*.mov
*.mkv
*.webm

# 压缩文件
*.zip
*.tar
*.tar.gz
*.rar
*.7z

# 备份文件
*.bak
*.backup
*.old

# 本地开发文件
local/
dev/
debug/

# 测试脚本
test_*.py
*_test.py
tests/

# 示例文件
examples/
samples/

# 文档生成
_build/
.doctrees/

# Jupyter Notebook
.ipynb_checkpoints/
*.ipynb

# 性能分析
*.prof

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 缓存目录
cache/
.cache/

# 输出目录
output/
outputs/
results/

# 截图和事件图片目录（运行时生成）
screenshots/
events/

# Kafka和S3测试文件
test_kafka_push.py
test_s3_storage.py
test_upload_image.py
test_mock_compatibility.py

# 说明文档
*_README.md
KAFKA_PUSH_README.md
S3_STORAGE_README.md
MOCK_COMPATIBILITY_README.md
