video_cap_module:
    video_uri: "/workspace/video_analysis_server/data/videos/sample_1080p_h265.mp4"
    frame_rate: 25
    task_info: ""


classification_module:
    work_mode: "loop"
    preprocessor:
        vocab_file: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14/vocab.txt"
    text_model:
        model_name: "cnclip_text"
        model_path: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14"
    visual_model:
        model_name: "cnclip_visual"
        model_path: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14"
    tiny_model:
        model_name: ""
        threash: 0.5


dsl_pipeline_module:
    work_mode: "loop"
    task_config_file: "/workspace/data/config/module_test_task.json"


yolov8_detector_module:
    work_mode: "loop"
    model_name: "yolov8"
    class_ids: [0, 16]
    input_width: 640
    input_height: 640


byte_tracker_module:
    work_mode: "loop"
    track_thresh: 0.45
    track_buffer: 25
    match_thresh: 0.8 
    frame_rate: 30


owl_detector_module:
    work_mode: "loop"
    visual_model: 
        model_path: "/workspace/models/google/owlv2-large-patch14-ensemble"
        model_name: "owlv2_visual"
    text_model: 
        model_path: "/workspace/models/BAAI/AltCLIP"
        model_name: "altclip_text"
    query_texts: ["cat", ]
    policy:
        embeddings_query:
            threshold: 0.15
        nms:
            threshold: 0.3


tripwire_intrusion_module:
    direction: "both"
    max_age: 3


zone_intrusion_module:
    work_mode: "loop"
    alarm_thresh: 10
    alarm_frames: 200
    fps: 25
    re_alarm_thresh: 10
    re_alarm_frames: 200


triton_server:
    ip: *************
    port: 9991


plasma_server:
    path: "/tmp/plasma"