video_cap_module:
    video_uri: "/workspace/data/videos/sample_1080p_h265.mp4"
    frame_rate: 25
    task_info: ""


classification_module:
    work_mode: "trigger"
    preprocessor:
        vocab_file: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14/vocab.txt"
    text_model:
        model_name: "cnclip_text"
        model_path: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14"
    visual_model:
        model_name: "cnclip_visual"
        model_path: "/workspace/models/AI-ModelScope/chinese-clip-vit-large-patch14"
    tiny_model:
        model_name: ""
        threash: 0.5


dsl_pipeline_module:
    work_mode: "trigger"
    task_config_file: "/workspace/data/config/yolo_tracking_clip_task.json"


yolov8_detector_module:
    work_mode: "trigger"
    model_name: "yolov8"
    detection_type: "PERSON"  # 默认人员检测，支持: PERSON, VEHICLE, NON_MOTOR_VEHICLE
    # class_ids: [0, 16]  # 可选：直接指定class_ids，会覆盖detection_type
    input_width: 640
    input_height: 640


byte_tracker_module:
    work_mode: "trigger"
    track_thresh: 0.45
    track_buffer: 25
    match_thresh: 0.8
    frame_rate: 30


owl_detector_module:
    work_mode: "trigger"
    visual_model:
        model_path: "/workspace/models/google/owlv2-large-patch14-ensemble"
        model_name: "owlv2_visual"
    text_model:
        model_path: "/workspace/models/BAAI/AltCLIP"
        model_name: "altclip_text"
    query_texts: ["cat", ]
    policy:
        embeddings_query:
            threshold: 0.15
        nms:
            threshold: 0.3


tripwire_intrusion_module:
    work_mode: "trigger"
    direction: "both"
    max_age: 3


zone_intrusion_module:
    work_mode: "trigger"
    alarm_thresh: 10
    alarm_frames: 200
    fps: 25
    re_alarm_thresh: 10
    re_alarm_frames: 200


triton_server:
    ip: *************
    port: 9991


plasma_server:
    path: "/tmp/plasma"


s3_storage:
    access_key: "YEOU3E6XTI234M25Q4UR"
    secret_key: "4DBRP1MBeyjcy0Ln1D6OYT9Oc3FsyYvf0x1W7zXm"
    endpoint: "http://*************:38888"
    bucket_name: "mybucket"
    region_name: "us-east-1"
    # 存图相关配置
    screenshot_prefix: "screenshots/"  # 截图存储前缀
    event_prefix: "events/"           # 事件图片存储前缀
    auto_create_bucket: true          # 是否自动创建bucket


kafka:
    # Kafka服务器配置
    bootstrap_servers: ["*************:9092"]  # Kafka服务器地址列表
    # 主题配置
    topic: "video_analysis_events"         # 事件推送主题
    # 生产者配置
    producer:
        acks: 1                           # 确认级别: 0=不等待, 1=等待leader, all=等待所有副本
        retries: 3                        # 重试次数
        batch_size: 16384                 # 批处理大小(字节)
        linger_ms: 10                     # 等待时间(毫秒)
        buffer_memory: 33554432           # 缓冲区大小(字节)
        max_request_size: 1048576         # 最大请求大小(字节)
        compression_type: "gzip"          # 压缩类型: none, gzip, snappy, lz4
        request_timeout_ms: 30000         # 请求超时时间(毫秒)
        delivery_timeout_ms: 120000       # 交付超时时间(毫秒)
    # 序列化配置
    serialization:
        key_serializer: "string"          # 键序列化器: string, bytes
        value_serializer: "json"          # 值序列化器: json, string, bytes
    # 其他配置
    enable_kafka: true                    # 是否启用Kafka推送
    max_retries: 3                        # 最大重试次数
    retry_backoff_ms: 1000               # 重试间隔(毫秒)


# 服务注册配置
service_registry:
    # 是否启用服务注册
    enabled: true
    # 调度器配置
    scheduler:
        url: "http://*************:8080"      # 调度器地址，可通过环境变量SCHEDULER_URL覆盖
        registration_retry_interval: 30  # 注册重试间隔(秒)
    # 服务信息配置
    service:
        name: "inference-service-1"       # 服务名称，可通过环境变量SERVICE_NAME覆盖
        max_quota: 10                     # 最大配额，可通过环境变量MAX_QUOTA覆盖
        region: "default"                 # 区域，可通过环境变量REGION覆盖
        gpu_type: "A10"                   # GPU类型，可通过环境变量GPU_TYPE覆盖
        # 算法编排配置
        algorithm_orchestration:
            algorithm_chain:
                - algorithm_id: "yolov8_detection"
                  algorithm_name: "YOLOv8目标检测"
                  algorithm_type: "DETECTION"
                  order: 1
                - algorithm_id: "byte_tracking"
                  algorithm_name: "ByteTracker目标跟踪"
                  algorithm_type: "TRACKING"
                  order: 2
                - algorithm_id: "zone_intrusion"
                  algorithm_name: "区域入侵检测"
                  algorithm_type: "PIPELINE"
                  order: 3