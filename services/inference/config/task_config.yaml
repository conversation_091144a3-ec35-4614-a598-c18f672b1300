dsl_pipeline_module:
    work_mode: "loop"
    task_config_file: "/workspace/video_analysis_server/data/config/yolo_tracking_clip_task.json"


yolov8_detector_module:
    work_mode: "trigger"
    model_name: "yolov8"
    class_ids: [0, ]
    input_width: 640
    input_height: 640


byte_tracker_module:
    work_mode: "trigger"
    track_thresh: 0.45
    track_buffer: 25
    match_thresh: 0.8 
    frame_rate: 30


classification_module:
    work_mode: "trigger"


triton_server:
    ip: **********
    port: 9991


plasma_server:
    path: "/tmp/plasma"