# OWL检测API服务配置文件
# 简化版配置，专门用于HTTP API服务

# 服务器配置
server:
  host: "0.0.0.0"
  port: 8082
  debug: false

# OWL检测模块配置
owl_detector:
  visual_model: 
    model_path: "/home/<USER>/bxt-analysis/bxt-analysis/services/inference/models/google/owlv2-large-patch14-ensemble"
    model_name: "owlv2_visual"
  text_model: 
    model_path: "/home/<USER>/bxt-analysis/bxt-analysis/services/inference/models/BAAI/AltCLIP"
    model_name: "altclip_text"
  
  # 默认策略配置
  default_policy:
    embeddings_query:
      threshold: 0.15
    nms:
      threshold: 0.3

# Triton推理服务器配置
triton_server:
  ip: "*************"
  port: 9991
  use_http: false
  verbose: false
  network_timeout: 30

# 日志配置
logging:
  level: "info"
  log_path: "./logs"
  format: "[%(asctime)s] - [pid:%(process)d] - [tid:%(thread)d] - %(levelname)s - %(filename)s:%(lineno)d - %(funcName)s %(message)s"

# API配置
api:
  max_file_size: 10485760  # 10MB
  allowed_extensions: ["jpg", "jpeg", "png", "bmp", "tiff"]
  max_query_texts: 10
  default_threshold: 0.15
  default_nms_threshold: 0.3
  request_timeout: 30
  max_concurrent_requests: 5

  # 分类相关配置
  max_bboxes: 50  # 最大边界框数量
  max_positive_texts: 20  # 最大正例文本数量
  max_negative_texts: 20  # 最大负例文本数量
  default_classification_threshold: 0.15  # 默认分类阈值

# 缓存配置
cache:
  text_embedding_cache_size: 2048
  enable_model_warmup: true
  warmup_texts: ["person", "car", "cat", "dog", "bicycle"]

# 分类模块配置（用于零样本分类）
classification:
  preprocessor:
    vocab_file: "/home/<USER>/bxt-analysis/bxt-analysis/services/inference/models/AI-ModelScope/chinese-clip-vit-large-patch14/vocab.txt"
  text_model:
    model_name: "cnclip_text"
    model_path: "/home/<USER>/bxt-analysis/bxt-analysis/services/inference/models/AI-ModelScope/chinese-clip-vit-large-patch14"
  visual_model:
    model_name: "cnclip_visual"
    model_path: "/home/<USER>/bxt-analysis/bxt-analysis/services/inference/models/AI-ModelScope/chinese-clip-vit-large-patch14"
