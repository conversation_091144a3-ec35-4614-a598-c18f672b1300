# OWL检测API服务Docker镜像
FROM deepstream:7.0-triton-multiarch

# 设置工作目录
WORKDIR /workspace/owl_detection_api

# 安装系统依赖
RUN apt update && apt-get install -y \
    libgstrtspserver-1.0-dev \
    gstreamer1.0-rtsp \
    libapr1 \
    libapr1-dev \
    libaprutil1 \
    libaprutil1-dev \
    libgeos-dev \
    libcurl4-openssl-dev \
    libjson-glib-dev \
    libsoup-gnome2.4-dev \
    libgstreamer-plugins-bad1.0-dev \
    libnice-dev \
    gstreamer1.0-nice \
    && rm -rf /var/lib/apt/lists/*

# 复制依赖文件
COPY dependencies/pyds-1.1.11-py3-none-linux_x86_64.whl /tmp/
COPY requirements_owl_api.txt /tmp/

# 安装Python依赖
RUN python3 -m pip install --no-cache-dir -r /tmp/requirements_owl_api.txt && \
    python3 -m pip install --no-cache-dir /tmp/pyds-1.1.11-py3-none-linux_x86_64.whl

# 复制源代码
COPY vas/ ./vas/
COPY config/owl_detection_api_config.yaml ./config/
COPY owl_detection_api.py ./
COPY start_owl_detection_api.py ./

# 创建日志目录
RUN mkdir -p /workspace/owl_detection_api/logs

# 设置环境变量
ENV PYTHONPATH=/workspace/owl_detection_api
ENV PYTHONUNBUFFERED=1

# 暴露端口
EXPOSE 8082

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8082/health || exit 1

# 启动命令
CMD ["python3", "start_owl_detection_api.py", "--config", "config/owl_detection_api_config.yaml"]
