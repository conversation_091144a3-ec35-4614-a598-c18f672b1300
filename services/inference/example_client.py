#!/usr/bin/env python3
"""
OWL检测API客户端示例
演示如何使用API进行0样本检测
"""

import os
import sys
import json
import requests
import argparse
from typing import List, Dict, Any


def detect_objects(api_url: str, image_path: str, query_texts: List[str], 
                  threshold: float = None, nms_threshold: float = None) -> Dict[str, Any]:
    """
    调用OWL检测API进行目标检测
    
    Args:
        api_url: API服务URL
        image_path: 图片路径
        query_texts: 查询文本列表
        threshold: 检测阈值
        nms_threshold: NMS阈值
        
    Returns:
        检测结果字典
    """
    # 检查图片文件
    if not os.path.exists(image_path):
        raise FileNotFoundError(f"图片文件不存在: {image_path}")
    
    # 准备请求数据
    files = {'image': open(image_path, 'rb')}
    data = {'query_texts': json.dumps(query_texts)}
    
    if threshold is not None:
        data['threshold'] = str(threshold)
    if nms_threshold is not None:
        data['nms_threshold'] = str(nms_threshold)
    
    try:
        # 发送请求
        response = requests.post(
            f"{api_url.rstrip('/')}/api/v1/detect",
            files=files,
            data=data,
            timeout=30
        )
        
        files['image'].close()
        
        # 检查响应
        if response.status_code == 200:
            return response.json()
        else:
            raise RuntimeError(f"API请求失败: HTTP {response.status_code}, {response.text}")
            
    except requests.exceptions.RequestException as e:
        raise RuntimeError(f"网络请求失败: {e}")


def print_detection_results(result: Dict[str, Any]):
    """打印检测结果"""
    if result.get('code') != 200:
        print(f"检测失败: {result.get('message', 'Unknown error')}")
        return
    
    data = result.get('data', {})
    detections = data.get('detections', [])
    total_count = data.get('total_count', 0)
    processing_time = data.get('processing_time', 0)
    parameters = data.get('parameters', {})
    
    print("=" * 60)
    print("检测结果")
    print("=" * 60)
    print(f"检测参数:")
    print(f"  - 查询文本: {parameters.get('query_texts', [])}")
    print(f"  - 检测阈值: {parameters.get('threshold', 'N/A')}")
    print(f"  - NMS阈值: {parameters.get('nms_threshold', 'N/A')}")
    print(f"处理时间: {processing_time:.3f}秒")
    print(f"检测到目标数量: {total_count}")
    print()
    
    if detections:
        print("检测详情:")
        for i, detection in enumerate(detections, 1):
            bbox = detection.get('bbox', [])
            score = detection.get('score', 0)
            query_text = detection.get('query_text', '')
            query_id = detection.get('query_id', -1)
            
            print(f"  目标 {i}:")
            print(f"    - 类别: {query_text} (ID: {query_id})")
            print(f"    - 置信度: {score:.3f}")
            print(f"    - 位置: x={bbox[0]:.1f}, y={bbox[1]:.1f}, w={bbox[2]:.1f}, h={bbox[3]:.1f}")
            print()
    else:
        print("未检测到任何目标")
    
    print("=" * 60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="OWL检测API客户端示例")
    parser.add_argument(
        "--url",
        type=str,
        default="http://localhost:8082",
        help="API服务URL (默认: http://localhost:8082)"
    )
    parser.add_argument(
        "--image",
        type=str,
        required=True,
        help="输入图片路径"
    )
    parser.add_argument(
        "--query-texts",
        type=str,
        nargs="+",
        required=True,
        help="查询文本列表，例如: person car bicycle"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        help="检测阈值 (0-1之间，默认使用服务器配置)"
    )
    parser.add_argument(
        "--nms-threshold",
        type=float,
        help="NMS阈值 (0-1之间，默认使用服务器配置)"
    )
    parser.add_argument(
        "--output",
        type=str,
        help="输出结果到JSON文件"
    )
    
    args = parser.parse_args()
    
    try:
        print("OWL检测API客户端")
        print(f"API服务: {args.url}")
        print(f"输入图片: {args.image}")
        print(f"查询文本: {args.query_texts}")
        if args.threshold is not None:
            print(f"检测阈值: {args.threshold}")
        if args.nms_threshold is not None:
            print(f"NMS阈值: {args.nms_threshold}")
        print()
        
        # 执行检测
        print("正在检测...")
        result = detect_objects(
            args.url,
            args.image,
            args.query_texts,
            args.threshold,
            args.nms_threshold
        )
        
        # 显示结果
        print_detection_results(result)
        
        # 保存结果到文件
        if args.output:
            with open(args.output, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {args.output}")
        
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
