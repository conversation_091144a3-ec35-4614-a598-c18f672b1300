# S3存储功能说明

## 概述

本功能在 `base_module.py` 中实现了 `upload_image` 函数，用于在推理过程中自动将事件图片上传到S3兼容存储。该函数会在生成告警事件时被调用。

## 配置说明

### 1. 配置文件

S3相关配置已添加到 `config/config.yaml` 文件中：

```yaml
s3_storage:
  access_key: "YEOU3E6XTI234M25Q4UR"
  secret_key: "4DBRP1MBeyjcy0Ln1D6OYT9Oc3FsyYvf0x1W7zXm"
  endpoint: "http://*************:38888"
  bucket_name: "mybucket"
  region_name: "us-east-1"
  # 存图相关配置
  screenshot_prefix: "screenshots/"  # 截图存储前缀
  event_prefix: "events/"           # 事件图片存储前缀
  auto_create_bucket: true          # 是否自动创建bucket
```

### 2. 依赖安装

```bash
pip install boto3 PyYAML Pillow
```

## 功能特性

### 1. upload_image函数

在 `base_module.py` 中实现了两个版本的 `upload_image` 函数：

#### 全局函数
```python
def upload_image(image_bytes: bytes, task_id: str = None, event_id: str = None) -> str
```

#### BaseModule实例方法
```python
def upload_image(self, frame_bytes: bytes, task_id: str = None, event_id: str = None) -> str
```

### 2. 自动调用机制

在 `generate_alert_event` 方法中，当生成告警事件时会自动调用：
```python
# upload image
retval, encode_buf = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 95])
if retval:
    task_id = task_info.task_id if hasattr(task_info, 'task_id') else task_info.taskId
    url = upload_image(encode_buf.tobytes(), task_id=task_id, event_id=unique_id)
```

### 3. 存储特性

- **自动临时文件管理**: 自动创建和清理临时文件
- **错误处理**: 上传失败时返回空字符串，不影响主流程
- **路径规则**: `events/{task_id}_{event_id}_{timestamp}.jpg`
- **兼容性**: 支持新旧字段名的兼容处理

## 使用方法

### 1. 自动调用（推荐）

在推理过程中，当生成告警事件时会自动调用 `upload_image` 函数：
- 无需手动调用
- 自动获取task_id和event_id
- 自动处理图片编码和上传

### 2. 手动调用

#### 使用全局函数
```python
from vas.modules.base_module import upload_image
import cv2

# 准备图片数据
frame = cv2.imread("test.jpg")
retval, encode_buf = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 95])

if retval:
    s3_url = upload_image(
        image_bytes=encode_buf.tobytes(),
        task_id="task-001",
        event_id="event-001"
    )
    print(f"上传成功: {s3_url}")
```

#### 使用BaseModule实例方法
```python
# 在BaseModule子类中
class MyModule(BaseModule):
    def process_frame(self, frame):
        retval, encode_buf = cv2.imencode('.jpg', frame)
        if retval:
            s3_url = self.upload_image(
                frame_bytes=encode_buf.tobytes(),
                task_id="task-001",
                event_id="event-001"
            )
```

### 3. 直接使用S3管理器

```python
from vas.common.s3_storage import S3StorageManager

# 初始化管理器
s3_manager = S3StorageManager()

# 上传文件
s3_url = s3_manager.upload_event_image(
    local_file_path="/path/to/event.jpg",
    task_id="task-001",
    event_id="event-001"
)
```

## 测试

### 1. 运行S3存储测试
```bash
cd inference
python test_s3_storage.py
```

### 2. 运行完整兼容性测试
```bash
cd inference
python test_mock_compatibility.py
```

## 文件结构

```
inference/
├── config/
│   └── config.yaml              # S3配置
├── vas/
│   ├── common/
│   │   └── s3_storage.py        # S3存储管理器
│   └── entrypoints/
│       ├── task_interface.py    # 包含Screenshot模型
│       └── http_server.py       # HTTP接口实现
├── test_s3_storage.py           # S3功能测试
├── test_mock_compatibility.py   # 完整兼容性测试
└── S3_STORAGE_README.md         # 本文档
```

## 存储路径规则

### 截图路径
```
screenshots/{task_id}_{timestamp}.{ext}
screenshots/{custom_name}.{ext}  # 如果指定了custom_name
```

### 事件图片路径
```
events/{task_id}_{event_id}_{timestamp}.{ext}
```

### 示例
```
screenshots/task-001_20250101_120000.jpg
screenshots/my_custom_screenshot.jpg
events/task-001_event-001_20250101_120000.jpg
```

## 注意事项

1. **文件权限**: 确保应用有读取本地文件的权限
2. **网络连接**: 确保能访问S3存储服务
3. **存储空间**: 注意S3存储的空间使用情况
4. **配置安全**: 生产环境中应使用环境变量或加密配置
5. **错误处理**: 上传失败时会返回None，需要检查返回值

## 扩展功能

1. **批量上传**: 可以扩展支持批量文件上传
2. **图片压缩**: 可以在上传前进行图片压缩
3. **缓存机制**: 可以添加本地缓存机制
4. **权限控制**: 可以添加文件访问权限控制
5. **生命周期管理**: 可以配置文件自动删除策略
