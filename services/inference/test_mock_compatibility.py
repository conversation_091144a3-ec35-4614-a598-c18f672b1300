#!/usr/bin/env python3
"""
测试改造后的inference服务是否兼容mock接口
"""
import requests
import json
import time
from datetime import datetime


def test_health_endpoint(base_url):
    """测试健康检查接口"""
    print("Testing /health endpoint...")
    try:
        response = requests.get(f"{base_url}/health")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_root_endpoint(base_url):
    """测试根路径接口"""
    print("\nTesting / endpoint...")
    try:
        response = requests.get(f"{base_url}/")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_stats_endpoint(base_url):
    """测试统计信息接口"""
    print("\nTesting /api/v1/stats endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/stats")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_task_list_endpoint(base_url):
    """测试任务列表接口"""
    print("\nTesting /api/v1/tasks endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/tasks")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def create_test_task_request():
    """创建测试任务请求"""
    return {
        "taskRequest": {
            "taskId": "test-task-001",
            "taskName": "测试任务",
            "taskDescription": "用于测试的任务",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "HIGH",
                "protocol": "VIDEO",
                "eventTypeId": "test-event-type",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "test-orch-001",
                "orchestrationName": "测试编排",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8-detector",
                        "algorithmName": "YOLO检测器",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {}
                    },
                    {
                        "algorithmId": "byte-tracker",
                        "algorithmName": "目标跟踪",
                        "algorithmType": "TRACKING",
                        "order": 2,
                        "required": True,
                        "dependsOn": ["yolo-v8-detector"],
                        "config": {}
                    },
                    {
                        "algorithmId": "clip-classifier",
                        "algorithmName": "CLIP分类",
                        "algorithmType": "CLASSIFICATION",
                        "order": 3,
                        "required": True,
                        "dependsOn": ["byte-tracker"],
                        "config": {}
                    }
                ],
                "status": "CREATED"
            },
            "device": {
                "deviceId": "test-camera-001",
                "deviceName": "测试摄像头",
                "streamConfig": {
                    "url": "rtsp://test.example.com/stream",
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP",
                    "decoderConf": {
                        "keyFrameOnly": False,
                        "decodeStep": 1
                    }
                }
            }
        },
        "config": {
            "test": True
        },
        "priority": 1,
        "region": "default"
    }


def create_owl_test_task_request():
    """创建OWL检测测试任务请求"""
    return {
        "taskRequest": {
            "taskId": "test-owl-task-001",
            "taskName": "OWL检测测试任务",
            "taskDescription": "用于测试OWL检测的任务",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "HIGH",
                "protocol": "VIDEO",
                "eventTypeId": "test-event-type-owl",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "test-owl-orch-001",
                "orchestrationName": "OWL检测编排",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "owl-vit-detector",
                        "algorithmName": "OWL-ViT检测器",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {}
                    },
                    {
                        "algorithmId": "clip-classifier",
                        "algorithmName": "CLIP分类",
                        "algorithmType": "CLASSIFICATION",
                        "order": 2,
                        "required": True,
                        "dependsOn": ["owl-vit-detector"],
                        "config": {}
                    }
                ],
                "status": "CREATED"
            },
            "device": {
                "deviceId": "test-camera-owl-001",
                "deviceName": "OWL测试摄像头",
                "streamConfig": {
                    "url": "rtsp://test.example.com/owl-stream",
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP",
                    "decoderConf": {
                        "keyFrameOnly": False,
                        "decodeStep": 1
                    }
                }
            }
        },
        "config": {
            "test": True,
            "algorithm_type": "OWL"
        },
        "priority": 1,
        "region": "default"
    }


def test_create_task_endpoint(base_url):
    """测试任务创建接口"""
    print("\nTesting POST /api/v1/tasks endpoint...")
    try:
        task_request = create_test_task_request()
        response = requests.post(
            f"{base_url}/api/v1/tasks",
            json=task_request,
            headers={"Content-Type": "application/json"}
        )
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
        if response.status_code == 200:
            return task_request["taskRequest"]["taskId"]
        return None
    except Exception as e:
        print(f"Error: {e}")
        return None


def test_get_task_endpoint(base_url, task_id):
    """测试获取任务接口"""
    print(f"\nTesting GET /api/v1/tasks/{task_id} endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/tasks/{task_id}")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_get_task_events_endpoint(base_url, task_id):
    """测试获取任务事件接口"""
    print(f"\nTesting GET /api/v1/tasks/{task_id}/events endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/tasks/{task_id}/events")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def test_screenshot_upload(base_url, task_id):
    """测试截图上传接口"""
    print(f"\nTesting POST /api/v1/tasks/{task_id}/screenshot endpoint...")
    try:
        # 创建测试图片文件
        import tempfile
        import os
        from PIL import Image
        import numpy as np

        # 创建临时图片
        image_array = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        image = Image.fromarray(image_array)
        temp_file = os.path.join(tempfile.gettempdir(), f"test_screenshot_{task_id}.jpg")
        image.save(temp_file, 'JPEG')

        # 上传截图
        response = requests.post(
            f"{base_url}/api/v1/tasks/{task_id}/screenshot",
            json={
                "file_path": temp_file,
                "custom_name": f"test_screenshot_{task_id}"
            },
            headers={"Content-Type": "application/json"}
        )

        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")

        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return response.status_code == 200

    except Exception as e:
        print(f"Error: {e}")
        return False


def test_event_image_upload(base_url, task_id):
    """测试事件图片上传接口"""
    print(f"\nTesting POST /api/v1/tasks/{task_id}/event-image endpoint...")
    try:
        # 创建测试图片文件
        import tempfile
        import os
        from PIL import Image
        import numpy as np

        # 创建临时图片
        image_array = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        image = Image.fromarray(image_array)
        temp_file = os.path.join(tempfile.gettempdir(), f"test_event_{task_id}.jpg")
        image.save(temp_file, 'JPEG')

        # 上传事件图片
        response = requests.post(
            f"{base_url}/api/v1/tasks/{task_id}/event-image",
            json={
                "file_path": temp_file,
                "event_id": f"event_{task_id}_001"
            },
            headers={"Content-Type": "application/json"}
        )

        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")

        # 清理临时文件
        if os.path.exists(temp_file):
            os.remove(temp_file)

        return response.status_code == 200

    except Exception as e:
        print(f"Error: {e}")
        return False


def test_delete_task_endpoint(base_url, task_id):
    """测试删除任务接口"""
    print(f"\nTesting DELETE /api/v1/tasks/{task_id} endpoint...")
    try:
        response = requests.delete(f"{base_url}/api/v1/tasks/{task_id}")
        print(f"Status: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error: {e}")
        return False


def main():
    """主测试函数"""
    base_url = "http://localhost:8080"  # 根据实际部署地址修改

    print("=== Testing Mock Compatibility ===")
    print(f"Base URL: {base_url}")
    print(f"Test Time: {datetime.now().isoformat()}")

    # 测试基础接口
    print("\n--- Testing Basic Endpoints ---")
    test_health_endpoint(base_url)
    test_root_endpoint(base_url)
    test_stats_endpoint(base_url)
    test_task_list_endpoint(base_url)

    # 测试任务管理接口
    print("\n--- Testing Task Management ---")
    task_id = test_create_task_endpoint(base_url)
    if task_id:
        time.sleep(1)  # 等待任务创建完成
        test_get_task_endpoint(base_url, task_id)
        test_get_task_events_endpoint(base_url, task_id)
        test_task_list_endpoint(base_url)  # 再次检查任务列表

        # 测试S3存图功能
        print("\n--- Testing S3 Storage ---")
        test_screenshot_upload(base_url, task_id)
        test_event_image_upload(base_url, task_id)

        # 再次获取任务信息，查看截图是否已添加
        test_get_task_endpoint(base_url, task_id)

        test_delete_task_endpoint(base_url, task_id)
    else:
        print("❌ Task creation failed, skipping task management tests")

    print("\n=== Test Completed ===")


if __name__ == "__main__":
    main()
