{"add_prefix_space": false, "added_tokens_decoder": {"0": {"content": "!", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": true}, "49406": {"content": "<|startoftext|>", "lstrip": false, "normalized": true, "rstrip": false, "single_word": false, "special": false}, "49407": {"content": "<|endoftext|>", "lstrip": false, "normalized": false, "rstrip": false, "single_word": false, "special": false}}, "additional_special_tokens": [], "bos_token": "<|startoftext|>", "clean_up_tokenization_spaces": true, "do_lower_case": true, "eos_token": "<|endoftext|>", "errors": "replace", "model_max_length": 16, "pad_token": "!", "processor_class": "Owlv2Processor", "tokenizer_class": "CLIPTokenizer", "tokenizer_file": "/Users/<USER>/.cache/huggingface/hub/models--openai--clip-vit-base-patch32/snapshots/e6a30b603a447e251fdaca1c3056b2a16cdfebeb/tokenizer.json", "unk_token": "<|endoftext|>"}