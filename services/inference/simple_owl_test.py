#!/usr/bin/env python3
"""
简单的OWL检测测试脚本
专门用于单个查询文本的测试
"""

import os
import sys
import json
import requests
import cv2
import numpy as np
from pathlib import Path


def test_single_detection(image_path: str, query_text: str, 
                         api_url: str = "http://*************:10086", 
                         threshold: float = 0.15):
    """
    测试单个查询文本的检测
    
    Args:
        image_path: 图片路径
        query_text: 查询文本
        api_url: API服务地址
        threshold: 检测阈值
    """
    print(f"正在测试图片: {image_path}")
    print(f"查询文本: '{query_text}'")
    print(f"检测阈值: {threshold}")
    print(f"API地址: {api_url}")
    
    # 检查图片文件
    if not os.path.exists(image_path):
        print(f"错误: 图片文件不存在: {image_path}")
        return None
    
    # 读取图片
    image = cv2.imread(image_path)
    if image is None:
        print(f"错误: 无法读取图片 {image_path}")
        return None
    
    print(f"图片尺寸: {image.shape}")
    
    try:
        # 准备请求数据
        files = {'image': open(image_path, 'rb')}
        data = {
            'query_texts': json.dumps([query_text]),  # 单个文本包装成数组
            'threshold': str(threshold)
        }
        
        print("正在调用检测API...")
        
        # 发送请求
        response = requests.post(
            f"{api_url}/api/v1/detect",
            files=files,
            data=data,
            timeout=60
        )
        
        files['image'].close()
        
        print(f"API响应状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"API调用失败: HTTP {response.status_code}")
            print(f"响应内容: {response.text}")
            return None
        
        # 解析结果
        result = response.json()
        print(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
        
        if result.get('code') != 200:
            print(f"检测失败: {result.get('message')}")
            return None
        
        # 提取检测数据
        data = result.get('data', {})
        detections = data.get('detections', [])
        processing_time = data.get('processing_time', 0)
        total_count = data.get('total_count', 0)
        
        print(f"\n检测结果:")
        print(f"  - 检测到目标数量: {total_count}")
        print(f"  - 处理时间: {processing_time:.3f}秒")
        
        if detections:
            print(f"  - 检测详情:")
            for i, detection in enumerate(detections, 1):
                bbox = detection.get('bbox', [])
                score = detection.get('score', 0)
                detected_text = detection.get('query_text', '')
                query_id = detection.get('query_id', -1)
                
                print(f"    {i}. 类别: {detected_text}")
                print(f"       置信度: {score:.3f}")
                print(f"       位置: x={bbox[0]:.1f}, y={bbox[1]:.1f}, w={bbox[2]:.1f}, h={bbox[3]:.1f}")
                print(f"       查询ID: {query_id}")
        else:
            print(f"  - 未检测到任何'{query_text}'目标")
        
        # 可视化结果
        if detections:
            result_image = draw_detections(image, detections)
            
            # 保存结果图片
            output_dir = "simple_test_output"
            os.makedirs(output_dir, exist_ok=True)
            
            input_name = Path(image_path).stem
            safe_query = query_text.replace(" ", "_").replace("/", "_")
            output_path = os.path.join(output_dir, f"{input_name}_{safe_query}_result.jpg")
            
            cv2.imwrite(output_path, result_image)
            print(f"  - 结果图片已保存: {output_path}")
            
            # 保存JSON结果
            json_path = os.path.join(output_dir, f"{input_name}_{safe_query}_result.json")
            with open(json_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"  - JSON结果已保存: {json_path}")
        
        return result
        
    except requests.exceptions.Timeout:
        print("错误: 请求超时")
        return None
    except requests.exceptions.ConnectionError:
        print("错误: 无法连接到API服务器")
        return None
    except Exception as e:
        print(f"检测过程出错: {e}")
        import traceback
        traceback.print_exc()
        return None


def draw_detections(image: np.ndarray, detections: list) -> np.ndarray:
    """
    在图片上绘制检测结果
    
    Args:
        image: 原始图片
        detections: 检测结果列表
        
    Returns:
        绘制了检测框的图片
    """
    result_image = image.copy()
    
    # 预定义颜色 (BGR格式)
    colors = [
        (0, 255, 0),    # 绿色
        (255, 0, 0),    # 蓝色
        (0, 0, 255),    # 红色
        (255, 255, 0),  # 青色
        (255, 0, 255),  # 洋红色
        (0, 255, 255),  # 黄色
    ]
    
    for i, detection in enumerate(detections):
        bbox = detection.get('bbox', [])
        score = detection.get('score', 0)
        query_text = detection.get('query_text', '')
        
        if len(bbox) != 4:
            continue
        
        x, y, w, h = bbox
        x1, y1 = int(x), int(y)
        x2, y2 = int(x + w), int(y + h)
        
        # 选择颜色
        color = colors[i % len(colors)]
        
        # 绘制检测框
        cv2.rectangle(result_image, (x1, y1), (x2, y2), color, 3)
        
        # 准备标签文本
        label = f"{query_text}: {score:.3f}"
        
        # 计算文本大小
        font = cv2.FONT_HERSHEY_SIMPLEX
        font_scale = 0.8
        thickness = 2
        (text_width, text_height), baseline = cv2.getTextSize(label, font, font_scale, thickness)
        
        # 绘制文本背景
        cv2.rectangle(result_image, 
                     (x1, y1 - text_height - baseline - 10), 
                     (x1 + text_width + 6, y1), 
                     color, -1)
        
        # 绘制文本
        cv2.putText(result_image, label, (x1 + 3, y1 - baseline - 5), 
                   font, font_scale, (255, 255, 255), thickness)
    
    # 添加摘要信息
    summary_text = f"Found: {len(detections)} objects"
    cv2.putText(result_image, summary_text, (10, 40), 
               cv2.FONT_HERSHEY_SIMPLEX, 1.2, (0, 255, 0), 3)
    
    return result_image


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="简单OWL检测测试")
    parser.add_argument(
        "--image",
        type=str,
        default="resource/vlcsnap-2025-07-27-09h00m37s504.png",
        help="图片路径"
    )
    parser.add_argument(
        "--query",
        type=str,
        default="person",
        help="查询文本"
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.15,
        help="检测阈值"
    )
    parser.add_argument(
        "--url",
        type=str,
        default="http://*************:10086",
        help="API服务地址"
    )
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("简单OWL检测测试")
    print("=" * 60)
    
    # 执行测试
    result = test_single_detection(
        args.image,
        args.query,
        args.url,
        args.threshold
    )
    
    if result:
        print("\n✅ 测试成功完成!")
    else:
        print("\n❌ 测试失败!")
        sys.exit(1)


if __name__ == "__main__":
    main()
