#!/usr/bin/env python3
"""
测试跳过处理逻辑的脚本
验证各个模块的skip_processing标记位功能
"""

import sys
import os
from dataclasses import dataclass, field
from typing import Optional

# 简化的数据结构用于测试
@dataclass
class ShmDataInfo:
    shape: list = field(default_factory=list)
    type: str = "float64"
    id: bytes = b''

@dataclass
class FrameInfo:
    frame: ShmDataInfo = field(default_factory=ShmDataInfo)
    bboxes: ShmDataInfo = field(default_factory=ShmDataInfo)
    track_objs: ShmDataInfo = field(default_factory=ShmDataInfo)

@dataclass
class MetaInfo:
    frame_id: int = -1
    timestamp: str = ""

@dataclass
class MessageInfo:
    meta_info: MetaInfo = field(default_factory=MetaInfo)
    frame_info: FrameInfo = field(default_factory=FrameInfo)
    skip_processing: bool = False

def create_test_message(has_bboxes=True, skip_processing=False):
    """创建测试消息"""
    message = MessageInfo()
    message.meta_info = MetaInfo(frame_id=1)
    message.skip_processing = skip_processing
    
    # 模拟frame info
    frame_info = FrameInfo()
    
    # 模拟frame数据
    frame_info.frame = ShmDataInfo()
    frame_info.frame.shape = [480, 640, 3]
    frame_info.frame.type = "uint8"
    frame_info.frame.id = b'test_frame'
    
    # 模拟bboxes数据
    frame_info.bboxes = ShmDataInfo()
    if has_bboxes:
        frame_info.bboxes.shape = [2, 6]  # 2个检测框
        frame_info.bboxes.type = "float32"
        frame_info.bboxes.id = b'test_bboxes'
    else:
        frame_info.bboxes.shape = [0, 6]  # 无检测框
        frame_info.bboxes.type = "float32"
        frame_info.bboxes.id = b'empty_bboxes'
    
    # 模拟track_ids数据
    frame_info.track_objs = ShmDataInfo()
    frame_info.track_objs.shape = [2, 1] if has_bboxes else [0, 1]
    frame_info.track_objs.type = "int32"
    frame_info.track_objs.id = b'test_track_ids'
    
    message.frame_info = frame_info
    return message

def create_test_task():
    """创建测试任务配置"""
    # 简化的任务配置，仅用于测试
    return {
        "taskId": "test_task_001",
        "algorithmType": "CLASSIFICATION",
        "positiveLabels": ["person", "car"],
        "negativeLabels": ["background"],
        "confidence": 0.5
    }

def test_message_skip_logic():
    """测试消息跳过逻辑"""
    print("=== 测试消息跳过处理逻辑 ===")
    
    # 测试1: 正常消息（有检测结果）
    print("\n1. 测试正常消息（有检测结果）")
    message1 = create_test_message(has_bboxes=True, skip_processing=False)
    print(f"   初始状态: skip_processing = {message1.skip_processing}")
    print(f"   检测框数量: {message1.frame_info.bboxes.shape[0]}")
    
    # 测试2: 无检测结果的消息
    print("\n2. 测试无检测结果的消息")
    message2 = create_test_message(has_bboxes=False, skip_processing=False)
    print(f"   初始状态: skip_processing = {message2.skip_processing}")
    print(f"   检测框数量: {message2.frame_info.bboxes.shape[0]}")
    
    # 测试3: 已标记跳过的消息
    print("\n3. 测试已标记跳过的消息")
    message3 = create_test_message(has_bboxes=True, skip_processing=True)
    print(f"   初始状态: skip_processing = {message3.skip_processing}")
    print(f"   检测框数量: {message3.frame_info.bboxes.shape[0]}")

def test_module_logic():
    """测试各模块的处理逻辑"""
    print("\n=== 测试各模块处理逻辑 ===")
    
    # 模拟YOLO检测模块逻辑
    print("\n1. YOLO检测模块逻辑:")
    print("   - 有检测对象 -> 继续传递 (skip_processing = False)")
    print("   - 无检测对象 -> 标记跳过 (skip_processing = True)")
    
    # 模拟OWL检测模块逻辑
    print("\n2. OWL检测模块逻辑:")
    print("   - 有检测对象 -> 继续传递 (skip_processing = False)")
    print("   - 无检测对象 -> 标记跳过 (skip_processing = True)")
    
    # 模拟Track模块逻辑
    print("\n3. Track模块逻辑:")
    print("   - 默认往下传 (不修改skip_processing)")
    
    # 模拟Classification模块逻辑
    print("\n4. Classification模块逻辑:")
    print("   - 满足预警条件 -> 继续传递 (skip_processing = False)")
    print("   - 不满足预警条件 -> 标记跳过 (skip_processing = True)")

def test_final_node_logic():
    """测试最后节点的告警逻辑"""
    print("\n=== 测试最后节点告警逻辑 ===")
    
    print("\n最后节点处理逻辑:")
    print("1. 检查 next_module 是否为 None")
    print("2. 检查 message 是否不为空")
    print("3. 检查 skip_processing 是否为 False")
    print("4. 如果以上条件都满足，则推送告警")
    
    # 测试场景
    scenarios = [
        ("有检测结果，未跳过", True, False, "推送告警"),
        ("有检测结果，已跳过", True, True, "不推送告警"),
        ("无检测结果，未跳过", False, False, "不推送告警"),
        ("无检测结果，已跳过", False, True, "不推送告警"),
    ]
    
    print("\n测试场景:")
    for desc, has_bboxes, skip_processing, expected in scenarios:
        print(f"   {desc}: {expected}")

def main():
    """主函数"""
    print("跳过处理逻辑测试")
    print("=" * 50)
    
    # 测试消息跳过逻辑
    test_message_skip_logic()
    
    # 测试模块逻辑
    test_module_logic()
    
    # 测试最后节点逻辑
    test_final_node_logic()
    
    print("\n" + "=" * 50)
    print("测试完成")
    
    print("\n实现总结:")
    print("1. 在MessageInfo中添加了skip_processing标记位")
    print("2. YOLO/OWL模块：有检测对象时继续传递，无检测对象时标记跳过")
    print("3. Track模块：默认往下传，不修改标记位")
    print("4. Classification模块：满足预警条件时继续传递，否则标记跳过")
    print("5. 最后节点：检测到无下一个节点且消息未跳过时推送告警")

if __name__ == "__main__":
    main()
