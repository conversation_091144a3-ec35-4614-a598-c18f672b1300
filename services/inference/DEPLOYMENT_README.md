# 视频分析推理服务部署指南

## 概述

本文档介绍如何构建和部署视频分析推理服务的Docker容器。该服务集成了S3存储、Kafka消息队列、动态算法链构建等功能。

## 系统要求

### 硬件要求
- **CPU**: 8核心以上推荐
- **内存**: 16GB以上推荐
- **GPU**: NVIDIA GPU（支持CUDA 12.1）
- **存储**: 50GB以上可用空间

### 软件要求
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+
- **NVIDIA Docker**: 2.0+（如果使用GPU）

## 快速开始

### 1. 使用启停脚本（推荐）

```bash
# 快速启动
./start.sh

# 启用GPU支持
./start.sh --gpu

# 指定端口
./start.sh --port 8080

# 停止服务
./stop.sh
```

### 2. 使用完整管理脚本

```bash
# 查看帮助
./service.sh --help

# 启动服务
./service.sh start

# 查看状态
./service.sh status

# 查看日志
./service.sh logs -f

# 停止服务
./service.sh stop
```

### 3. 手动构建和启动

```bash
# 构建镜像
./build.sh

# 手动启动
docker run -p 9001:9001 video-analysis-inference:latest
```

### 4. 验证服务

```bash
# 检查健康状态
curl http://localhost:9001/health

# 查看服务信息
curl http://localhost:9001/
```

## 详细部署步骤

### 1. 准备环境

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 重新登录或执行
newgrp docker
```

#### 安装NVIDIA Docker（GPU支持）
```bash
# 添加NVIDIA Docker仓库
distribution=$(. /etc/os-release;echo $ID$VERSION_ID)
curl -s -L https://nvidia.github.io/nvidia-docker/gpgkey | sudo apt-key add -
curl -s -L https://nvidia.github.io/nvidia-docker/$distribution/nvidia-docker.list | sudo tee /etc/apt/sources.list.d/nvidia-docker.list

# 安装nvidia-docker2
sudo apt-get update
sudo apt-get install -y nvidia-docker2
sudo systemctl restart docker
```

### 2. 准备依赖文件

确保以下文件存在：
```
inference/
├── dependencies/
│   ├── libdsl.so                    # DSL库文件
│   └── pyds-1.1.11-py3-none-linux_x86_64.whl  # DeepStream Python绑定
├── config/
│   └── config.yaml                  # 配置文件
├── Dockerfile                       # Docker构建文件
├── requirements.txt                 # Python依赖
├── docker-compose.yml              # 服务编排文件
└── build.sh                        # 构建脚本
```

### 3. 配置文件

#### 修改config/config.yaml
```yaml
# S3存储配置
s3_storage:
  access_key: "your_access_key"
  secret_key: "your_secret_key"
  endpoint: "http://minio:9000"  # 使用docker-compose时
  bucket_name: "video-analysis"

# Kafka配置
kafka:
  bootstrap_servers: ["kafka:29092"]  # 使用docker-compose时
  topic: "video_analysis_events"
  enable_kafka: true

# 其他配置...
```

### 4. 构建选项

#### 基本构建
```bash
./build.sh
```

#### 高级构建选项
```bash
# 指定标签
./build.sh -t v1.0.0

# 无缓存构建
./build.sh --no-cache

# 详细输出
./build.sh -v

# 构建并推送到仓库
./build.sh -p -r registry.example.com

# 查看所有选项
./build.sh --help
```

### 5. 部署方式

#### 方式1: 启停脚本（推荐）

##### 快速启停脚本
```bash
# 快速启动（默认9001端口）
./start.sh

# 启用GPU支持
./start.sh --gpu

# 指定端口
./start.sh --port 8080

# 停止服务
./stop.sh
```

##### 完整管理脚本
```bash
# 启动服务
./service.sh start

# 启动服务并启用GPU
./service.sh start --gpu

# 指定端口启动
./service.sh start -p 8080

# 查看服务状态
./service.sh status

# 查看实时日志
./service.sh logs -f

# 健康检查
./service.sh health

# 重启服务
./service.sh restart

# 停止服务
./service.sh stop

# 构建镜像
./service.sh build

# 清理资源
./service.sh clean
```

#### 方式2: 单独容器
```bash
# 启动推理服务
docker run -d \
  --name inference-service \
  -p 9001:9001 \
  -v $(pwd)/config:/workspace/config:ro \
  -v $(pwd)/logs:/workspace/logs \
  --restart unless-stopped \
  video-analysis-inference:latest

# 查看日志
docker logs -f inference-service
```

#### 方式3: GPU支持
```bash
# 使用GPU运行
docker run -d \
  --name inference-service \
  --gpus all \
  -p 9001:9001 \
  -v $(pwd)/config:/workspace/config:ro \
  video-analysis-inference:latest
```

## 服务配置

### 环境变量
```bash
# Python环境
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# 时区
TZ=Asia/Shanghai

# GPU相关
NVIDIA_VISIBLE_DEVICES=all
NVIDIA_DRIVER_CAPABILITIES=compute,utility,video
```

### 端口映射
- **9001**: 推理服务HTTP端口
- **9092**: Kafka端口
- **9000**: MinIO S3端口
- **9002**: MinIO控制台端口
- **8080**: Kafka UI端口（可选）

### 数据卷
- **config**: 配置文件目录（只读）
- **logs**: 日志文件目录
- **temp**: 临时文件目录
- **models**: 模型文件目录（只读）

## 监控和维护

### 健康检查
```bash
# 检查服务健康状态
curl http://localhost:9001/health

# 检查服务统计信息
curl http://localhost:9001/api/v1/stats

# 检查任务列表
curl http://localhost:9001/api/v1/tasks
```

### 日志管理
```bash
# 查看实时日志
docker-compose logs -f inference

# 查看特定时间的日志
docker-compose logs --since="2h" inference

# 查看错误日志
docker-compose logs inference | grep ERROR
```

### 性能监控
```bash
# 查看容器资源使用
docker stats inference-service

# 查看容器详细信息
docker inspect inference-service
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查容器日志
docker logs inference-service

# 检查配置文件
docker exec -it inference-service cat /workspace/config/config.yaml
```

#### 2. GPU不可用
```bash
# 检查GPU状态
nvidia-smi

# 检查NVIDIA Docker
docker run --rm --gpus all nvidia/cuda:11.8-base-ubuntu20.04 nvidia-smi
```

#### 3. 网络连接问题
```bash
# 检查网络连接
docker network ls
docker network inspect inference_inference-network

# 测试服务连接
docker exec -it inference-service curl http://kafka:29092
```

#### 4. 存储问题
```bash
# 检查磁盘空间
df -h

# 检查数据卷
docker volume ls
docker volume inspect inference_minio-data
```

### 调试模式

#### 启用详细日志
```yaml
# 在config.yaml中设置
logging:
  level: DEBUG
```

#### 交互式调试
```bash
# 进入容器
docker exec -it inference-service bash

# 手动启动服务
python3 -m vas.entrypoints.http_server --config_path config/config.yaml
```

## 生产部署建议

### 1. 安全配置
- 使用非root用户运行容器
- 限制容器权限
- 使用secrets管理敏感信息
- 配置防火墙规则

### 2. 性能优化
- 根据负载调整资源限制
- 使用SSD存储
- 优化网络配置
- 启用GPU加速

### 3. 高可用性
- 使用多副本部署
- 配置负载均衡
- 实施健康检查
- 设置自动重启策略

### 4. 监控告警
- 集成Prometheus监控
- 配置Grafana仪表板
- 设置告警规则
- 实施日志聚合

## 更新和维护

### 更新服务
```bash
# 拉取最新代码
git pull

# 重新构建镜像
./build.sh -t v1.1.0

# 更新服务
docker-compose up -d --no-deps inference
```

### 备份和恢复
```bash
# 备份配置
tar -czf config-backup.tar.gz config/

# 备份数据卷
docker run --rm -v inference_minio-data:/data -v $(pwd):/backup alpine tar czf /backup/minio-backup.tar.gz /data
```

## 支持和联系

如有问题，请查看：
1. 应用日志：`docker-compose logs inference`
2. 系统日志：`journalctl -u docker`
3. 健康检查：`curl http://localhost:9001/health`

更多信息请参考相关文档：
- [S3存储功能说明](S3_STORAGE_README.md)
- [Kafka推送功能说明](KAFKA_PUSH_README.md)
- [Mock接口兼容性说明](MOCK_COMPATIBILITY_README.md)
