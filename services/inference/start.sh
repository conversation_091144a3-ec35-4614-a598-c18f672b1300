#!/bin/bash

# 快速启动脚本 - 视频分析推理服务
# 用法: ./start.sh [--gpu] [--port PORT]

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m'

# 默认配置
PORT=9001
USE_GPU=false

# 解析参数
while [[ $# -gt 0 ]]; do
    case $1 in
        --gpu)
            USE_GPU=true
            shift
            ;;
        --port)
            PORT="$2"
            shift 2
            ;;
        -h|--help)
            echo "快速启动脚本 - 视频分析推理服务"
            echo ""
            echo "用法: $0 [选项]"
            echo ""
            echo "选项:"
            echo "  --gpu         启用GPU支持"
            echo "  --port PORT   指定端口 (默认: 9001)"
            echo "  -h, --help    显示帮助信息"
            echo ""
            echo "示例:"
            echo "  $0                # 基本启动"
            echo "  $0 --gpu          # 启用GPU"
            echo "  $0 --port 8080    # 指定端口"
            exit 0
            ;;
        *)
            echo "未知参数: $1"
            echo "使用 $0 --help 查看帮助"
            exit 1
            ;;
    esac
done

echo -e "${BLUE}启动视频分析推理服务...${NC}"
echo "端口: $PORT"
if [[ "$USE_GPU" == "true" ]]; then
    echo "GPU: 启用"
else
    echo "GPU: 禁用"
fi
echo ""

# 构建Docker运行命令
docker_cmd="docker run -d --name inference-service --restart unless-stopped -p $PORT:9001"

# 添加目录映射
docker_cmd="$docker_cmd -v $(pwd)/config:/workspace/config:ro"
docker_cmd="$docker_cmd -v $(pwd)/logs:/workspace/logs"
docker_cmd="$docker_cmd -v $(pwd)/temp:/workspace/temp"

# 添加GPU支持
if [[ "$USE_GPU" == "true" ]]; then
    docker_cmd="$docker_cmd --gpus all"
fi

# 添加镜像名
docker_cmd="$docker_cmd video-analysis-inference:latest"

# 创建必要目录
mkdir -p logs temp

# 停止已存在的容器
docker stop inference-service 2>/dev/null || true
docker rm inference-service 2>/dev/null || true

# 启动容器
echo "执行命令: $docker_cmd"
if eval $docker_cmd; then
    echo -e "${GREEN}✅ 服务启动成功!${NC}"
    echo ""
    echo "访问地址: http://localhost:$PORT"
    echo "健康检查: curl http://localhost:$PORT/health"
    echo "查看日志: docker logs -f inference-service"
    echo "停止服务: docker stop inference-service"
else
    echo "❌ 服务启动失败"
    exit 1
fi
