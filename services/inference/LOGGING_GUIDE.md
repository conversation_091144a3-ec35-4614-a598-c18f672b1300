# 跳过处理逻辑日志说明

## 日志级别说明

### 🔍 INFO级别 - 关键业务流程
记录每个模块的关键判定环节和状态变化：

#### 基础模块 (BaseModule)
- `[模块名] Frame {frame_id}: 传递消息到下一个模块 {next_module}, skip_processing={status}`
- `[模块名] Frame {frame_id}: 当前是最后一个节点`
- `[模块名] Frame {frame_id}: 满足告警条件 - 消息不为空且未跳过处理，开始推送告警`
- `[模块名] Frame {frame_id}: 消息被标记跳过处理，不推送告警`

#### YOLO检测模块
- `[YOLO] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块`
- `[YOLO] Frame {frame_id}: 🔍 开始YOLO目标检测`
- `[YOLO] Frame {frame_id}: ✅ 检测到 {count} 个目标，设置skip_processing=False，继续传递`
- `[YOLO] Frame {frame_id}: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理`

#### OWL检测模块
- `[OWL] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块`
- `[OWL] Frame {frame_id}: 🦉 开始OWL目标检测，查询文本: {query_texts}`
- `[OWL] Frame {frame_id}: ✅ 检测到 {count} 个目标，设置skip_processing=False，继续传递`
- `[OWL] Frame {frame_id}: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理`

#### 跟踪模块
- `[TRACK] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块`
- `[TRACK] Frame {frame_id}: 🎯 开始目标跟踪`
- `[TRACK] Frame {frame_id}: ✅ 跟踪完成，输入检测框: {input_count}, 输出跟踪ID: {output_count}`
- `[TRACK] Frame {frame_id}: 🔄 跟踪模块默认传递消息，skip_processing保持: {status}`

#### 分类模块
- `[CLASSIFICATION] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块`
- `[CLASSIFICATION] Frame {frame_id}: 🏷️ 开始分类处理`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 正例标签 '{label}': {score:.4f}`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 负例标签 '{label}': {score:.4f}`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 分类摘要:`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 最高正例分数: {max_pos_score:.4f}`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 最高负例分数: {max_neg_score:.4f}`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 置信度阈值: {threshold:.4f}`
- `[CLASSIFICATION] Frame {frame_id}: 检测框{i} - 告警状态: {'✅ 满足告警条件' if alert else '❌ 不满足告警条件'}`
- `[CLASSIFICATION] Frame {frame_id}: ✅ 检测到预警条件 ({alert_count}/{total_count}个目标满足条件)，设置skip_processing=False，继续传递`
- `[CLASSIFICATION] Frame {frame_id}: ❌ 未检测到预警条件 (0/{total_count}个目标满足条件)，设置skip_processing=True，标记跳过后续处理`

#### 零样本分类模块
- `[ZEROSHOT] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块`
- `[ZEROSHOT] Frame {frame_id}: 🎯 开始零样本分类处理`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 正例标签 '{label}': {score:.4f}`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 负例标签 '{label}': {score:.4f}`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 分类摘要:`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 最高正例分数: {max_pos_score:.4f}`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 最高负例分数: {max_neg_score:.4f}`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 置信度阈值: {threshold:.4f}`
- `[ZEROSHOT] Frame {frame_id}: 检测框{i} - 告警状态: {'✅ 满足告警条件' if alert else '❌ 不满足告警条件'}`
- `[ZEROSHOT] Frame {frame_id}: ✅ 检测到预警条件 ({alert_count}/{total_count}个目标满足条件)，设置skip_processing=False，继续传递`
- `[ZEROSHOT] Frame {frame_id}: ❌ 未检测到预警条件 (0/{total_count}个目标满足条件)，设置skip_processing=True，标记跳过后续处理`

### 🔧 DEBUG级别 - 详细技术信息
记录详细的处理过程和数据信息：

#### 检测模块详情
- 帧信息提取：`提取帧信息完成，frame shape: {shape}`
- 预处理信息：`预处理完成，input shape: {shape}`
- 模型推理：`开始模型推理` / `模型推理完成，status: {status}`
- 检测结果：`目标{i} - 位置:({x},{y},{w},{h}), 置信度:{score}, 类别:{class_id}`

#### 分类模块详情
- 算法处理：`处理分类算法配置: {algorithm_id}`
- 分类结果：`分类结果数量: {count}`
- 预警判定：`检测框{i}满足预警条件` / `检测框{i}不满足预警条件`
- 分类详情：`分类结果 - 类型:{type}, 文本:'{text}', 得分:{score}`
- 标签配置：`正例标签: {positive_labels}` / `负例标签: {negative_labels}`
- 置信度配置：`置信度阈值: {confidence_threshold}`

### ⚠️ WARNING级别 - 异常情况
- `消息不包含任务信息`
- `告警事件生成失败`

### ❌ ERROR级别 - 错误处理
- `最后节点告警处理异常: {error}`

## 日志格式说明

### 统一格式
```
[模块标识] Frame {帧ID}: {状态图标} {描述信息}
```

### 状态图标含义
- 🔍 开始检测
- 🦉 OWL检测
- 🎯 开始跟踪/分类
- 🏷️ 开始分类
- 🔄 状态保持
- ⏭️ 跳过处理
- ✅ 成功/检测到
- ❌ 失败/未检测到
- ⚠️ 警告

## 日志使用建议

### 1. 生产环境
- 设置日志级别为 `INFO`
- 关注关键业务流程和状态变化
- 监控告警推送情况

### 2. 调试环境
- 设置日志级别为 `DEBUG`
- 查看详细的处理过程
- 分析性能瓶颈

### 3. 问题排查
- 搜索特定帧ID：`grep "Frame 123" log_file`
- 搜索跳过处理：`grep "skip_processing=True" log_file`
- 搜索告警推送：`grep "告警" log_file`

## 典型日志流程示例

### 正常检测流程
```
[YOLO] Frame 100: 🔍 开始YOLO目标检测
[YOLO] Frame 100: ✅ 检测到 2 个目标，设置skip_processing=False，继续传递
[YOLO] Frame 100: 传递消息到下一个模块 byte_tracker_module, skip_processing=False
[TRACK] Frame 100: 🎯 开始目标跟踪
[TRACK] Frame 100: ✅ 跟踪完成，输入检测框: 2, 输出跟踪ID: 2
[TRACK] Frame 100: 传递消息到下一个模块 classification_module, skip_processing=False
[CLASSIFICATION] Frame 100: 🏷️ 开始分类处理
[CLASSIFICATION] Frame 100: ✅ 检测到预警条件 (1/2个目标满足条件)，设置skip_processing=False，继续传递
[CLASSIFICATION] Frame 100: 当前是最后一个节点
[CLASSIFICATION] Frame 100: 满足告警条件 - 消息不为空且未跳过处理，开始推送告警
```

### 跳过处理流程
```
[YOLO] Frame 101: 🔍 开始YOLO目标检测
[YOLO] Frame 101: ❌ 未检测到目标，设置skip_processing=True，标记跳过后续处理
[YOLO] Frame 101: 传递消息到下一个模块 byte_tracker_module, skip_processing=True
[TRACK] Frame 101: ⏭️ 消息已标记跳过处理，直接传递到下一个模块
[TRACK] Frame 101: 传递消息到下一个模块 classification_module, skip_processing=True
[CLASSIFICATION] Frame 101: ⏭️ 消息已标记跳过处理，直接传递到下一个模块
[CLASSIFICATION] Frame 101: 当前是最后一个节点
[CLASSIFICATION] Frame 101: 消息被标记跳过处理，不推送告警
```

### 详细分类流程
```
[CLASSIFICATION] Frame 200: 🏷️ 开始分类处理
[CLASSIFICATION] Frame 200: 检测框1 - 正例标签 'person': 0.7279
[CLASSIFICATION] Frame 200: 检测框1 - 正例标签 'helmet': 0.8651
[CLASSIFICATION] Frame 200: 检测框1 - 负例标签 'background': 0.2631
[CLASSIFICATION] Frame 200: 检测框1 - 负例标签 'no_helmet': 0.2729
[CLASSIFICATION] Frame 200: 检测框1 - 分类摘要:
[CLASSIFICATION] Frame 200: 检测框1 - 最高正例分数: 0.8651
[CLASSIFICATION] Frame 200: 检测框1 - 最高负例分数: 0.2729
[CLASSIFICATION] Frame 200: 检测框1 - 置信度阈值: 0.6000
[CLASSIFICATION] Frame 200: 检测框1 - 告警状态: ✅ 满足告警条件
[CLASSIFICATION] Frame 200: 零样本分类完成，总计 1 个检测框，其中 1 个满足告警条件
```
