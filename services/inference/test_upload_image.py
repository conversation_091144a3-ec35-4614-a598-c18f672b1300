#!/usr/bin/env python3
"""
测试upload_image函数功能
"""
import os
import sys
import numpy as np
import cv2
from datetime import datetime

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from vas.modules.base_module import upload_image, BaseModule


def create_test_image(width=640, height=480):
    """创建测试图片"""
    # 创建随机图片
    image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # 添加一些文本
    cv2.putText(image_array, f"Test Image {datetime.now().strftime('%H:%M:%S')}", 
                (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 1, (255, 255, 255), 2)
    cv2.putText(image_array, "Upload Test", 
                (10, 70), cv2.FONT_HERSHEY_SIMPLEX, 1, (0, 255, 0), 2)
    
    return image_array


def test_global_upload_image():
    """测试全局upload_image函数"""
    print("=== 测试全局upload_image函数 ===")
    
    try:
        # 创建测试图片
        test_image = create_test_image()
        
        # 编码为JPEG
        retval, encode_buf = cv2.imencode('.jpg', test_image, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        if not retval:
            print("❌ 图片编码失败")
            return False
        
        print(f"✅ 图片编码成功，大小: {len(encode_buf)} bytes")
        
        # 测试上传
        task_id = "test-task-001"
        event_id = "test-event-001"
        
        s3_url = upload_image(
            image_bytes=encode_buf.tobytes(),
            task_id=task_id,
            event_id=event_id
        )
        
        if s3_url:
            print(f"✅ 图片上传成功: {s3_url}")
            return True
        else:
            print("❌ 图片上传失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_base_module_upload_image():
    """测试BaseModule的upload_image方法"""
    print("\n=== 测试BaseModule.upload_image方法 ===")
    
    try:
        # 创建模拟的命令行参数
        class MockArgs:
            def __init__(self):
                self.config_path = "config/config.yaml"
                self.mock_plasma = True
        
        # 创建模拟的队列
        mock_queues = {}
        
        # 创建模拟的logger
        import logging
        logger = logging.getLogger("test")
        logger.setLevel(logging.INFO)
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)
        
        # 创建BaseModule实例
        base_module = BaseModule(
            module_name="test_module",
            cmd_args=MockArgs(),
            queues=mock_queues,
            logger=logger
        )
        
        # 创建测试图片
        test_image = create_test_image()
        
        # 编码为JPEG
        retval, encode_buf = cv2.imencode('.jpg', test_image, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        if not retval:
            print("❌ 图片编码失败")
            return False
        
        print(f"✅ 图片编码成功，大小: {len(encode_buf)} bytes")
        
        # 测试上传
        task_id = "test-task-002"
        event_id = "test-event-002"
        
        s3_url = base_module.upload_image(
            frame_bytes=encode_buf.tobytes(),
            task_id=task_id,
            event_id=event_id
        )
        
        if s3_url:
            print(f"✅ BaseModule图片上传成功: {s3_url}")
            return True
        else:
            print("❌ BaseModule图片上传失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_upload_without_params():
    """测试不提供参数的上传"""
    print("\n=== 测试默认参数上传 ===")
    
    try:
        # 创建测试图片
        test_image = create_test_image()
        
        # 编码为JPEG
        retval, encode_buf = cv2.imencode('.jpg', test_image, [cv2.IMWRITE_JPEG_QUALITY, 95])
        
        if not retval:
            print("❌ 图片编码失败")
            return False
        
        # 测试不提供task_id和event_id的上传
        s3_url = upload_image(encode_buf.tobytes())
        
        if s3_url:
            print(f"✅ 默认参数上传成功: {s3_url}")
            return True
        else:
            print("❌ 默认参数上传失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n=== 测试错误处理 ===")
    
    try:
        # 测试空字节数据
        s3_url = upload_image(b"")
        print(f"空字节数据上传结果: {s3_url}")
        
        # 测试无效图片数据
        s3_url = upload_image(b"invalid image data")
        print(f"无效图片数据上传结果: {s3_url}")
        
        print("✅ 错误处理测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 错误处理测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("=== upload_image函数测试 ===")
    print(f"测试时间: {datetime.now().isoformat()}")
    
    # 检查依赖
    try:
        import cv2
        import boto3
        import yaml
        print("✅ 依赖检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖: {e}")
        print("请安装: pip install opencv-python boto3 PyYAML")
        return
    
    # 运行测试
    results = []
    
    results.append(test_global_upload_image())
    results.append(test_base_module_upload_image())
    results.append(test_upload_without_params())
    results.append(test_error_handling())
    
    # 统计结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 测试结果 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！")
    else:
        print("⚠️  部分测试失败")
    
    print("\n=== 测试完成 ===")


if __name__ == "__main__":
    main()
