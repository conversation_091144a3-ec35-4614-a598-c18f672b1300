import os
import sys
import time
import multiprocessing as mp
from vas.common.server_args import prepare_server_args
from vas.modules.base_module import OWL_DETECTOR_MODULE
from vas.modules.launch_modules import launch_sub_processes
from vas.common.utils import kill_process_tree


if __name__ == "__main__":
    # Set mp start method
    mp.set_start_method("spawn", force=True)
    ctx = mp.get_context("spawn")
    # mp.set_start_method("fork", force=True)
    # ctx = mp.get_context("fork")

    # parse cmd args
    server_args = prepare_server_args(sys.argv[1:])

    try:
        launch_modules = [OWL_DETECTOR_MODULE, ]
        processes, queues = launch_sub_processes(ctx, server_args, launch_modules)
        for proc in processes:
            proc.join()
    finally:
        kill_process_tree(os.getpid(), include_parent=False)
