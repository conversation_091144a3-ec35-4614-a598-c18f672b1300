#!/usr/bin/env python3
"""
测试分类模块日志功能的脚本
模拟分类过程中每个标签的分数打印
"""

import logging
import sys
import numpy as np
from dataclasses import dataclass, field
from typing import Optional, List

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# 简化的数据结构
@dataclass
class AlertConfig:
    positiveLabels: List[str] = field(default_factory=list)
    negativeLabels: List[str] = field(default_factory=list)
    confidence: float = 0.5

@dataclass
class Algorithm:
    algorithmId: str = ""
    algorithmType: str = ""
    alertConfig: Optional[AlertConfig] = None

@dataclass
class MetaInfo:
    frame_id: int = -1
    timestamp: str = ""

@dataclass
class MessageInfo:
    meta_info: MetaInfo = field(default_factory=MetaInfo)
    skip_processing: bool = False

def simulate_zeroshot_classify(label: str, frame: np.ndarray, bbox: list) -> float:
    """模拟零样本分类，返回随机分数"""
    # 模拟不同标签的分数
    scores = {
        "person": np.random.uniform(0.7, 0.9),
        "car": np.random.uniform(0.6, 0.8),
        "helmet": np.random.uniform(0.8, 0.95),
        "no_helmet": np.random.uniform(0.2, 0.4),
        "background": np.random.uniform(0.1, 0.3),
        "noise": np.random.uniform(0.05, 0.2),
    }
    return scores.get(label, np.random.uniform(0.1, 0.9))

def simulate_classification_process(frame_id: int, bbox_count: int = 2):
    """模拟分类过程的日志输出"""
    
    # 创建模拟的算法配置
    alert_config = AlertConfig()
    alert_config.positiveLabels = ["person", "helmet"]
    alert_config.negativeLabels = ["background", "no_helmet"]
    alert_config.confidence = 0.6
    
    algo_config = Algorithm()
    algo_config.algorithmId = "helmet_detection"
    algo_config.algorithmType = "CLASSIFICATION"
    algo_config.alertConfig = alert_config
    
    # 模拟检测框
    bboxes = []
    for i in range(bbox_count):
        bbox = [100 + i*50, 50 + i*30, 80, 120, 0.85, 0]  # x, y, w, h, score, class_id
        bboxes.append(bbox)
    
    logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 开始零样本分类，检测框数量: {len(bboxes)}")
    logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 正例标签: {alert_config.positiveLabels}")
    logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 负例标签: {alert_config.negativeLabels}")
    logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 置信度阈值: {alert_config.confidence}")
    
    alert_results = []
    
    for bbox_idx, bbox in enumerate(bboxes):
        alert_flag = False
        bbox_coords = bbox[0:4]
        classifications = []
        
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 处理检测框{bbox_idx+1} - 位置: {bbox_coords}")
        
        # 正例分类
        max_pos_conf = -100
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 开始正例分类")
        for label in alert_config.positiveLabels:
            pos_conf = simulate_zeroshot_classify(label, None, bbox_coords)
            max_pos_conf = pos_conf if pos_conf > max_pos_conf else max_pos_conf
            classifications.append({"score": pos_conf, "type": "positive", "label": label})
            
            # 打印每个正例标签的分数
            logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 正例标签 '{label}': {pos_conf:.4f}")

        # 负例分类
        max_neg_conf = -100
        logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 开始负例分类")
        for label in alert_config.negativeLabels:
            neg_conf = simulate_zeroshot_classify(label, None, bbox_coords)
            max_neg_conf = neg_conf if neg_conf > max_neg_conf else max_neg_conf
            classifications.append({"score": neg_conf, "type": "negative", "label": label})
            
            # 打印每个负例标签的分数
            logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 负例标签 '{label}': {neg_conf:.4f}")

        # 判断是否需要告警结果
        if max_pos_conf > max_neg_conf and max_pos_conf > alert_config.confidence:
            alert_flag = True

        # 打印分类结果摘要
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 分类摘要:")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 最高正例分数: {max_pos_conf:.4f}")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 最高负例分数: {max_neg_conf:.4f}")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 置信度阈值: {alert_config.confidence:.4f}")
        logger.info(f"[CLASSIFICATION] Frame {frame_id}: 检测框{bbox_idx+1} - 告警状态: {'✅ 满足告警条件' if alert_flag else '❌ 不满足告警条件'}")

        alert_results.append({
            "alert": alert_flag,
            "bbox": bbox_coords,
            "classifications": classifications
        })

    alert_count = sum(1 for res in alert_results if res["alert"])
    logger.info(f"[CLASSIFICATION] Frame {frame_id}: 零样本分类完成，总计 {len(alert_results)} 个检测框，其中 {alert_count} 个满足告警条件")
    
    return alert_results

def test_helmet_detection_scenario():
    """测试安全帽检测场景"""
    print("\n" + "="*80)
    print("🪖 测试场景1: 安全帽检测分类")
    print("="*80)
    
    frame_id = 200
    results = simulate_classification_process(frame_id, bbox_count=2)
    
    return results

def test_person_detection_scenario():
    """测试人员检测场景"""
    print("\n" + "="*80)
    print("👤 测试场景2: 人员检测分类")
    print("="*80)
    
    # 设置不同的随机种子以获得不同的结果
    np.random.seed(42)
    
    frame_id = 201
    results = simulate_classification_process(frame_id, bbox_count=1)
    
    return results

def test_multiple_objects_scenario():
    """测试多目标检测场景"""
    print("\n" + "="*80)
    print("🎯 测试场景3: 多目标检测分类")
    print("="*80)
    
    # 设置不同的随机种子
    np.random.seed(123)
    
    frame_id = 202
    results = simulate_classification_process(frame_id, bbox_count=3)
    
    return results

def analyze_results(results: list, scenario_name: str):
    """分析分类结果"""
    print(f"\n📊 {scenario_name} 结果分析:")
    print("-" * 50)
    
    total_boxes = len(results)
    alert_boxes = sum(1 for res in results if res["alert"])
    
    print(f"总检测框数量: {total_boxes}")
    print(f"满足告警条件: {alert_boxes}")
    print(f"告警率: {alert_boxes/total_boxes*100:.1f}%")
    
    for i, result in enumerate(results):
        print(f"\n检测框 {i+1}:")
        print(f"  位置: {result['bbox']}")
        print(f"  告警状态: {'✅ 告警' if result['alert'] else '❌ 无告警'}")
        print(f"  分类详情:")
        
        for cls in result["classifications"]:
            cls_type = cls["type"]
            label = cls["label"]
            score = cls["score"]
            icon = "➕" if cls_type == "positive" else "➖"
            print(f"    {icon} {label}: {score:.4f}")

def main():
    """主函数"""
    print("🚀 分类模块日志测试")
    print("本测试模拟分类过程中每个标签的分数打印")
    
    # 设置DEBUG级别以显示详细信息
    logger.setLevel(logging.DEBUG)
    
    # 测试各种场景
    results1 = test_helmet_detection_scenario()
    results2 = test_person_detection_scenario()
    results3 = test_multiple_objects_scenario()
    
    print("\n" + "="*80)
    print("📈 结果分析")
    print("="*80)
    
    # 分析结果
    analyze_results(results1, "安全帽检测")
    analyze_results(results2, "人员检测")
    analyze_results(results3, "多目标检测")
    
    print("\n" + "="*80)
    print("✅ 分类日志测试完成")
    print("="*80)
    print("\n📋 日志功能说明:")
    print("- INFO级别: 每个标签的分类分数和分类摘要")
    print("- DEBUG级别: 详细的处理过程和配置信息")
    print("- 分数格式: 保留4位小数，便于精确分析")
    print("- 摘要信息: 包含最高正例/负例分数、阈值和告警状态")
    print("\n🔧 实际使用时，这些日志将帮助你:")
    print("- 调试分类模型的性能")
    print("- 分析不同标签的置信度分布")
    print("- 优化告警阈值设置")
    print("- 排查分类错误的原因")

if __name__ == "__main__":
    main()
