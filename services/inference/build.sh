#!/bin/bash

# 视频分析推理服务构建脚本
# 用法: ./build.sh [选项]

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 默认配置
IMAGE_NAME="video-analysis-inference"
IMAGE_TAG="latest"
DOCKERFILE="Dockerfile"
BUILD_CONTEXT="."
PUSH_TO_REGISTRY=false
REGISTRY_URL=""
NO_CACHE=false
VERBOSE=false

# 显示帮助信息
show_help() {
    echo "视频分析推理服务构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help              显示此帮助信息"
    echo "  -t, --tag TAG           设置镜像标签 (默认: latest)"
    echo "  -n, --name NAME         设置镜像名称 (默认: video-analysis-inference)"
    echo "  -f, --file FILE         指定Dockerfile路径 (默认: Dockerfile)"
    echo "  -c, --context PATH      设置构建上下文路径 (默认: .)"
    echo "  -p, --push              构建后推送到镜像仓库"
    echo "  -r, --registry URL      设置镜像仓库URL"
    echo "  --no-cache              不使用缓存构建"
    echo "  -v, --verbose           显示详细输出"
    echo ""
    echo "示例:"
    echo "  $0                      # 使用默认配置构建"
    echo "  $0 -t v1.0.0            # 构建并标记为v1.0.0"
    echo "  $0 -p -r registry.com   # 构建并推送到指定仓库"
    echo "  $0 --no-cache -v        # 无缓存详细构建"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装或不在PATH中"
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker守护进程未运行或无权限访问"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 检查文件
check_files() {
    log_info "检查构建文件..."
    
    if [[ ! -f "$DOCKERFILE" ]]; then
        log_error "Dockerfile不存在: $DOCKERFILE"
        exit 1
    fi
    
    if [[ ! -f "requirements.txt" ]]; then
        log_error "requirements.txt不存在"
        exit 1
    fi
    
    if [[ ! -d "dependencies" ]]; then
        log_warning "dependencies目录不存在，某些依赖可能缺失"
    fi
    
    log_success "文件检查通过"
}

# 构建镜像
build_image() {
    log_info "开始构建Docker镜像..."
    
    # 构建参数
    BUILD_ARGS=""
    
    if [[ "$NO_CACHE" == "true" ]]; then
        BUILD_ARGS="$BUILD_ARGS --no-cache"
    fi
    
    if [[ "$VERBOSE" == "true" ]]; then
        BUILD_ARGS="$BUILD_ARGS --progress=plain"
    fi
    
    # 完整镜像名称
    FULL_IMAGE_NAME="$IMAGE_NAME:$IMAGE_TAG"
    if [[ -n "$REGISTRY_URL" ]]; then
        FULL_IMAGE_NAME="$REGISTRY_URL/$FULL_IMAGE_NAME"
    fi
    
    # 执行构建
    log_info "构建镜像: $FULL_IMAGE_NAME"
    
    if docker build $BUILD_ARGS -t "$FULL_IMAGE_NAME" -f "$DOCKERFILE" "$BUILD_CONTEXT"; then
        log_success "镜像构建成功: $FULL_IMAGE_NAME"
    else
        log_error "镜像构建失败"
        exit 1
    fi
    
    # 显示镜像信息
    log_info "镜像信息:"
    docker images "$FULL_IMAGE_NAME" --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
}

# 推送镜像
push_image() {
    if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then
        if [[ -z "$REGISTRY_URL" ]]; then
            log_error "推送镜像需要指定仓库URL (-r 选项)"
            exit 1
        fi
        
        FULL_IMAGE_NAME="$REGISTRY_URL/$IMAGE_NAME:$IMAGE_TAG"
        
        log_info "推送镜像到仓库: $FULL_IMAGE_NAME"
        
        if docker push "$FULL_IMAGE_NAME"; then
            log_success "镜像推送成功: $FULL_IMAGE_NAME"
        else
            log_error "镜像推送失败"
            exit 1
        fi
    fi
}

# 清理旧镜像
cleanup_old_images() {
    log_info "清理悬空镜像..."
    
    # 清理悬空镜像
    DANGLING_IMAGES=$(docker images -f "dangling=true" -q)
    if [[ -n "$DANGLING_IMAGES" ]]; then
        docker rmi $DANGLING_IMAGES
        log_success "已清理悬空镜像"
    else
        log_info "没有悬空镜像需要清理"
    fi
}

# 显示构建摘要
show_summary() {
    echo ""
    echo "=================================="
    echo "         构建摘要"
    echo "=================================="
    echo "镜像名称: $IMAGE_NAME"
    echo "镜像标签: $IMAGE_TAG"
    echo "Dockerfile: $DOCKERFILE"
    echo "构建上下文: $BUILD_CONTEXT"
    if [[ -n "$REGISTRY_URL" ]]; then
        echo "镜像仓库: $REGISTRY_URL"
    fi
    echo "推送到仓库: $(if [[ "$PUSH_TO_REGISTRY" == "true" ]]; then echo "是"; else echo "否"; fi)"
    echo "使用缓存: $(if [[ "$NO_CACHE" == "true" ]]; then echo "否"; else echo "是"; fi)"
    echo "=================================="
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -t|--tag)
            IMAGE_TAG="$2"
            shift 2
            ;;
        -n|--name)
            IMAGE_NAME="$2"
            shift 2
            ;;
        -f|--file)
            DOCKERFILE="$2"
            shift 2
            ;;
        -c|--context)
            BUILD_CONTEXT="$2"
            shift 2
            ;;
        -p|--push)
            PUSH_TO_REGISTRY=true
            shift
            ;;
        -r|--registry)
            REGISTRY_URL="$2"
            shift 2
            ;;
        --no-cache)
            NO_CACHE=true
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        *)
            log_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
done

# 主执行流程
main() {
    log_info "开始构建视频分析推理服务..."
    
    show_summary
    
    check_dependencies
    check_files
    build_image
    push_image
    cleanup_old_images
    
    log_success "构建完成！"
    
    echo ""
    echo "后续步骤:"
    echo "1. 运行容器: docker run -p 9001:9001 $IMAGE_NAME:$IMAGE_TAG"
    echo "2. 使用docker-compose: docker-compose up -d"
    echo "3. 检查健康状态: curl http://localhost:9001/health"
}

# 执行主函数
main
