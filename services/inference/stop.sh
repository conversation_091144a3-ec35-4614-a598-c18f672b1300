#!/bin/bash

# 快速停止脚本 - 视频分析推理服务

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "${BLUE}停止视频分析推理服务...${NC}"

# 检查容器是否存在
if docker ps --format "{{.Names}}" | grep -q "^inference-service$"; then
    echo "正在停止容器..."
    if docker stop inference-service; then
        echo -e "${GREEN}✅ 容器停止成功${NC}"
    else
        echo "❌ 容器停止失败"
        exit 1
    fi
else
    echo -e "${YELLOW}⚠️  容器未在运行${NC}"
fi

# 删除容器
if docker ps -a --format "{{.Names}}" | grep -q "^inference-service$"; then
    echo "正在删除容器..."
    if docker rm inference-service; then
        echo -e "${GREEN}✅ 容器删除成功${NC}"
    else
        echo "❌ 容器删除失败"
        exit 1
    fi
fi

echo -e "${GREEN}✅ 服务停止完成${NC}"
