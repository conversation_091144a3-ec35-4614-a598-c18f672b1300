#!/bin/bash

# 测试service.sh中的IP获取功能

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# 从service.sh中提取get_host_ip函数
get_host_ip() {
    local host_ip=""
    
    # 方法1: 检查环境变量
    if [ -n "$HOST_IP" ]; then
        host_ip="$HOST_IP"
        log_info "使用环境变量HOST_IP: $host_ip"
        echo "$host_ip"
        return 0
    fi
    
    # 方法2: 通过hostname -I获取
    if command -v hostname >/dev/null 2>&1; then
        host_ip=$(hostname -I | awk '{print $1}')
        if [ -n "$host_ip" ] && [ "$host_ip" != "127.0.0.1" ]; then
            log_info "通过hostname -I获取到IP: $host_ip"
            echo "$host_ip"
            return 0
        fi
    fi
    
    # 方法3: 通过ip route获取默认路由的IP
    if command -v ip >/dev/null 2>&1; then
        host_ip=$(ip route get ******* | awk '{print $7; exit}' 2>/dev/null)
        if [ -n "$host_ip" ] && [ "$host_ip" != "127.0.0.1" ]; then
            log_info "通过ip route获取到IP: $host_ip"
            echo "$host_ip"
            return 0
        fi
    fi
    
    # 方法4: 通过ifconfig获取（如果可用）
    if command -v ifconfig >/dev/null 2>&1; then
        host_ip=$(ifconfig | grep -Eo 'inet (addr:)?([0-9]*\.){3}[0-9]*' | grep -Eo '([0-9]*\.){3}[0-9]*' | grep -v '127.0.0.1' | head -n1)
        if [ -n "$host_ip" ]; then
            log_info "通过ifconfig获取到IP: $host_ip"
            echo "$host_ip"
            return 0
        fi
    fi
    
    log_warning "无法自动获取宿主机IP，将使用localhost"
    echo "localhost"
    return 1
}

echo "测试宿主机IP获取功能..."
echo ""

# 测试1: 未设置环境变量
echo "=== 测试1: 未设置HOST_IP环境变量 ==="
unset HOST_IP
result_ip=$(get_host_ip)
echo "获取到的IP: $result_ip"
echo ""

# 测试2: 设置环境变量
echo "=== 测试2: 设置HOST_IP环境变量 ==="
export HOST_IP="*************"
result_ip=$(get_host_ip)
echo "获取到的IP: $result_ip"
if [ "$result_ip" = "*************" ]; then
    log_success "正确使用了环境变量"
else
    log_warning "期望*************，但得到$result_ip"
fi
echo ""

# 测试3: 清除环境变量，测试自动获取
echo "=== 测试3: 清除环境变量，测试自动获取 ==="
unset HOST_IP
result_ip=$(get_host_ip)
echo "获取到的IP: $result_ip"
if [ "$result_ip" != "localhost" ] && [ -n "$result_ip" ]; then
    log_success "成功自动获取到IP地址"
else
    log_warning "自动获取失败，使用备选方案"
fi
echo ""

echo "测试完成！"
