# Kafka事件推送功能说明

## 概述

本功能在 `base_module.py` 中实现了 `push_kafka` 函数，用于在推理过程中自动将事件实例推送到Kafka消息队列。该函数会在生成告警事件时被自动调用。

## 配置说明

### 1. 配置文件

Kafka相关配置已添加到 `config/config.yaml` 文件中：

```yaml
kafka:
  # Kafka服务器配置
  bootstrap_servers: ["localhost:9092"]  # Kafka服务器地址列表
  # 主题配置
  topic: "video_analysis_events"         # 事件推送主题
  # 生产者配置
  producer:
    acks: 1                           # 确认级别: 0=不等待, 1=等待leader, all=等待所有副本
    retries: 3                        # 重试次数
    batch_size: 16384                 # 批处理大小(字节)
    linger_ms: 10                     # 等待时间(毫秒)
    buffer_memory: 33554432           # 缓冲区大小(字节)
    compression_type: "gzip"          # 压缩类型: none, gzip, snappy, lz4
    request_timeout_ms: 30000         # 请求超时时间(毫秒)
  # 序列化配置
  serialization:
    key_serializer: "string"          # 键序列化器: string, bytes
    value_serializer: "json"          # 值序列化器: json, string, bytes
  # 其他配置
  enable_kafka: true                  # 是否启用Kafka推送
  max_retries: 3                      # 最大重试次数
```

### 2. 依赖安装

```bash
pip install kafka-python
```

## 功能特性

### 1. push_kafka函数

在 `base_module.py` 中实现了两个版本的 `push_kafka` 函数：

#### 全局函数
```python
def push_kafka(event: AtomicEventInstance, key: str = None) -> bool
```

#### BaseModule实例方法
```python
def push_kafka(self, alert_event: AtomicEventInstance, key: str = None) -> bool
```

### 2. 自动推送机制

在 `generate_alert_event` 方法中，当生成告警事件时会自动推送到Kafka：
```python
# 创建事件实例
alert_event = AtomicEventInstance(...)

# 自动推送到Kafka
try:
    push_success = push_kafka(alert_event, key=task_id)
    if push_success:
        self.logger.info(f"事件已推送到Kafka: event_id={unique_id}")
except Exception as e:
    self.logger.error(f"推送事件到Kafka异常: {e}")
```

### 3. Kafka生产者特性

- **自动配置加载**: 从config.yaml自动加载Kafka配置
- **连接管理**: 自动管理Kafka连接和重连
- **序列化**: 支持JSON、字符串、字节等序列化方式
- **批处理**: 支持批量发送以提高性能
- **压缩**: 支持gzip、snappy、lz4等压缩算法
- **错误处理**: 完整的异常处理和重试机制

### 4. 消息格式

推送到Kafka的消息格式：
```json
{
  "atomicEventInstanceId": "event_12345678",
  "eventTypeId": "intrusion_detection",
  "taskId": "task-001",
  "deviceId": "camera-001",
  "timestamp": "2025-01-01T12:00:00.000Z",
  "imageUri": "http://s3.example.com/events/task-001_event-001_20250101_120000.jpg",
  "entities": [
    {
      "entityInstanceId": "entity_001",
      "entityType": "person",
      "algorithmId": "yolo-v8",
      "boundingBox": {
        "x": 100,
        "y": 100,
        "width": 200,
        "height": 150
      }
    }
  ],
  "taskInfo": {
    "taskId": "task-001",
    "deviceId": "camera-001",
    "taskLevel": "HIGH"
  },
  "_kafka_timestamp": "2025-01-01T12:00:00.123Z"
}
```

## 使用方法

### 1. 自动推送（推荐）

在推理过程中，当生成告警事件时会自动推送到Kafka：
- 无需手动调用
- 自动获取task_id作为消息键
- 自动处理序列化和错误

### 2. 手动推送

#### 使用全局函数
```python
from vas.modules.base_module import push_kafka
from vas.entrypoints.event_interface import AtomicEventInstance

# 创建事件实例
event = AtomicEventInstance(...)

# 推送到Kafka
success = push_kafka(event, key="custom-key")
if success:
    print("推送成功")
```

#### 使用BaseModule实例方法
```python
# 在BaseModule子类中
class MyModule(BaseModule):
    def process_event(self, event):
        success = self.push_kafka(event, key="module-key")
        if success:
            self.logger.info("事件推送成功")
```

### 3. 直接使用Kafka生产者

```python
from vas.common.kafka_client import KafkaEventProducer

# 创建生产者
producer = KafkaEventProducer()

# 同步发送
success = producer.send_event(event)

# 异步发送
def callback(record_metadata, exception):
    if exception:
        print(f"发送失败: {exception}")
    else:
        print(f"发送成功: {record_metadata.offset}")

producer.send_event_async(event, callback=callback)
```

## 配置选项详解

### 1. 生产者配置

- **acks**: 确认级别
  - `0`: 不等待确认（最快，可能丢失）
  - `1`: 等待leader确认（平衡）
  - `all`: 等待所有副本确认（最安全）

- **retries**: 重试次数，发送失败时的重试次数

- **batch_size**: 批处理大小，累积到此大小后发送

- **linger_ms**: 等待时间，即使批次未满也会在此时间后发送

- **compression_type**: 压缩类型
  - `none`: 不压缩
  - `gzip`: GZIP压缩（推荐）
  - `snappy`: Snappy压缩（快速）
  - `lz4`: LZ4压缩（平衡）

### 2. 序列化配置

- **key_serializer**: 键序列化器
  - `string`: 字符串序列化
  - `bytes`: 字节序列化

- **value_serializer**: 值序列化器
  - `json`: JSON序列化（推荐）
  - `string`: 字符串序列化
  - `bytes`: 字节序列化

### 3. 主题配置

- **topic**: Kafka主题名称，建议使用有意义的名称如 `video_analysis_events`

## 测试验证

### 1. 运行Kafka推送测试
```bash
cd inference
python test_kafka_push.py
```

### 2. 启动Kafka服务器（如果需要）
```bash
# 启动Zookeeper
bin/zookeeper-server-start.sh config/zookeeper.properties

# 启动Kafka
bin/kafka-server-start.sh config/server.properties

# 创建主题
bin/kafka-topics.sh --create --topic video_analysis_events --bootstrap-server localhost:9092
```

### 3. 消费消息验证
```bash
# 消费消息
bin/kafka-console-consumer.sh --topic video_analysis_events --from-beginning --bootstrap-server localhost:9092
```

## 性能优化

### 1. 批处理优化
- 增加 `batch_size` 提高吞吐量
- 调整 `linger_ms` 平衡延迟和吞吐量

### 2. 压缩优化
- 使用 `gzip` 或 `lz4` 压缩减少网络传输
- 根据CPU和网络情况选择合适的压缩算法

### 3. 异步发送
- 使用异步发送提高性能
- 合理设置缓冲区大小

## 监控和故障排除

### 1. 日志监控
- 查看应用日志中的Kafka推送状态
- 监控推送成功率和失败原因

### 2. Kafka监控
- 监控Kafka集群状态
- 查看主题的消息积压情况

### 3. 常见问题
- **连接失败**: 检查Kafka服务器地址和端口
- **推送超时**: 调整超时配置或检查网络
- **序列化错误**: 检查事件数据格式

## 注意事项

1. **网络依赖**: 确保能访问Kafka服务器
2. **配置正确**: 检查bootstrap_servers和topic配置
3. **资源使用**: 注意内存和网络使用情况
4. **错误处理**: 推送失败不会影响主推理流程
5. **消息顺序**: 使用相同的key保证消息顺序

## 扩展功能

1. **消息过滤**: 可以根据事件类型过滤推送
2. **多主题**: 可以根据不同条件推送到不同主题
3. **消息转换**: 可以在推送前转换消息格式
4. **批量推送**: 可以实现批量事件推送
5. **监控集成**: 可以集成监控和告警系统
