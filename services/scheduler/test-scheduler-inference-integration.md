# Scheduler-Inference 对接功能测试指南

## 概述

本文档描述了如何测试 Scheduler 接口 `POST /api/v1/scheduler/release/{taskId}` 与 Inference 接口 `DELETE /api/v1/tasks/{taskId}` 的对接功能。

## 实现的功能

### 1. 核心对接逻辑

当调用 Scheduler 的释放任务接口时，系统会：

1. **查找活跃任务分配**：根据 taskId 查找 Scheduler 中的活跃任务分配记录
2. **调用 Inference 删除接口**：向对应的推理服务发送 DELETE 请求删除任务
3. **更新 Scheduler 状态**：无论推理服务删除是否成功，都会更新 Scheduler 中的任务状态
4. **释放服务配额**：减少推理服务的当前配额计数

### 2. 错误处理机制

- **推理服务不存在**：跳过推理服务调用，直接释放 Scheduler 资源
- **推理服务不可用**：跳过推理服务调用，记录错误信息
- **推理服务返回 404**：视为删除成功（任务已不存在）
- **推理服务删除失败**：记录错误信息，但继续释放 Scheduler 资源
- **网络异常**：记录错误信息，但继续释放 Scheduler 资源

### 3. 关键代码实现

#### SchedulerService.releaseTaskByTaskId()
```java
@Transactional
public void releaseTaskByTaskId(String taskId) {
    // 1. 查找活跃的任务分配
    List<TaskAllocation> activeAllocations = allocationRepository.findActiveTasksByTaskId(taskId);
    
    for (TaskAllocation allocation : activeAllocations) {
        // 2. 调用推理服务删除任务
        boolean inferenceDeleted = false;
        if (serviceOpt.isPresent() && service.isAvailable()) {
            inferenceDeleted = deleteTaskFromInferenceService(taskId, service);
        }
        
        // 3. 更新 Scheduler 状态
        allocation.setTaskStatus(TaskStatus.STOPPED);
        allocation.setEndTime(LocalDateTime.now());
        allocationRepository.save(allocation);
        
        // 4. 释放服务配额
        updateServiceQuota(serviceId, -1);
    }
}
```

#### SchedulerService.deleteTaskFromInferenceService()
```java
private boolean deleteTaskFromInferenceService(String taskId, InferenceService service) {
    String deleteUrl = service.getBaseUrl() + "/api/v1/tasks/" + taskId;
    
    ResponseEntity<String> response = restTemplate.exchange(
        deleteUrl, HttpMethod.DELETE, null, String.class);
    
    if (response.getStatusCode().is2xxSuccessful()) {
        return true;
    } else if (response.getStatusCode().value() == 404) {
        return true; // 任务不存在，视为删除成功
    } else {
        return false;
    }
}
```

## 测试场景

### 1. 成功场景测试

**前置条件**：
- Scheduler 中存在活跃的任务分配
- 推理服务可用且任务存在

**测试步骤**：
```bash
# 1. 调用释放任务接口
curl -X POST http://localhost:8080/api/v1/scheduler/release/test-task-001

# 2. 验证响应
# 期望：200 OK

# 3. 验证 Scheduler 状态
curl -X GET http://localhost:8080/api/v1/scheduler/tasks/test-task-001
# 期望：404 Not Found 或任务状态为 STOPPED

# 4. 验证推理服务状态
curl -X GET http://localhost:9001/api/v1/tasks/test-task-001
# 期望：404 Not Found
```

### 2. 推理服务任务不存在场景

**前置条件**：
- Scheduler 中存在活跃的任务分配
- 推理服务可用但任务不存在（返回 404）

**期望结果**：
- Scheduler 返回 200 OK
- Scheduler 中任务状态更新为 STOPPED
- 服务配额正确释放

### 3. 推理服务不可用场景

**前置条件**：
- Scheduler 中存在活跃的任务分配
- 推理服务状态为 INACTIVE

**期望结果**：
- Scheduler 返回 200 OK
- Scheduler 中任务状态更新为 STOPPED，错误信息记录推理服务不可用
- 服务配额正确释放

### 4. 网络异常场景

**前置条件**：
- Scheduler 中存在活跃的任务分配
- 推理服务网络不可达

**期望结果**：
- Scheduler 返回 200 OK
- Scheduler 中任务状态更新为 STOPPED，错误信息记录网络异常
- 服务配额正确释放

### 5. 任务不存在场景

**前置条件**：
- Scheduler 中不存在指定 taskId 的活跃任务分配

**期望结果**：
- Scheduler 返回 404 Not Found

## 日志监控

### 关键日志信息

1. **开始释放任务**：
```
INFO - 释放任务资源: taskId=test-task-001
INFO - 开始释放任务分配: taskId=test-task-001, allocationId=alloc-001, serviceId=svc-001
```

2. **调用推理服务**：
```
INFO - 开始从推理服务删除任务: taskId=test-task-001, serviceId=svc-001, serviceUrl=http://localhost:9001
INFO - 向推理服务发送删除任务请求: url=http://localhost:9001/api/v1/tasks/test-task-001
```

3. **成功场景**：
```
INFO - 推理服务任务删除成功: taskId=test-task-001, serviceId=svc-001
INFO - 任务分配状态更新成功: taskId=test-task-001, newStatus=STOPPED
INFO - 服务配额释放成功: taskId=test-task-001, serviceId=svc-001
```

4. **错误场景**：
```
WARN - 推理服务任务删除失败，但继续释放Scheduler资源: taskId=test-task-001, serviceId=svc-001
ERROR - 推理服务任务删除失败: taskId=test-task-001, status=500
```

## 单元测试

已实现的测试用例：
- `testReleaseTaskByTaskId_Success`：成功删除场景
- `testReleaseTaskByTaskId_InferenceServiceNotFound`：推理服务不存在
- `testReleaseTaskByTaskId_InferenceServiceUnavailable`：推理服务不可用
- `testReleaseTaskByTaskId_InferenceDeleteFailed`：推理服务删除失败
- `testReleaseTaskByTaskId_InferenceTaskNotFound`：推理服务中任务不存在
- `testReleaseTaskByTaskId_TaskNotFound`：Scheduler 中任务不存在
- `testReleaseTaskByTaskId_NetworkException`：网络异常

## 注意事项

1. **事务一致性**：Scheduler 的状态更新使用了 `@Transactional` 注解，确保数据一致性
2. **容错设计**：即使推理服务调用失败，Scheduler 也会继续释放资源，避免资源泄露
3. **幂等性**：重复调用释放接口是安全的，不会产生副作用
4. **日志记录**：详细的日志记录便于问题排查和监控
5. **无重试机制**：按要求移除了重试逻辑，简化了实现
