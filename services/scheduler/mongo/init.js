// mongo/init.js
db = db.getSiblingDB('cv_scheduler');

// 创建用户
db.createUser({
  user: 'bxt_user',
  pwd: 'bxt_password',
  roles: [
    {
      role: 'readWrite',
      db: 'cv_scheduler'
    }
  ]
});

// 创建索引
db.inference_service.createIndex({ "status": 1 });
db.inference_service.createIndex({ "region": 1 });
db.inference_service.createIndex({ "gpu_type": 1 });
db.inference_service.createIndex({ "service_name": 1 });

db.task_allocation.createIndex({ "service_id": 1 });
db.task_allocation.createIndex({ "task_status": 1 });
db.task_allocation.createIndex({ "device_id": 1 });
db.task_allocation.createIndex({ "allocate_time": 1 });
db.task_allocation.createIndex({ "task_id": 1 });

db.schedule_strategy.createIndex({ "schedule_mode": 1 });

// 插入默认调度策略
db.schedule_strategy.insertOne({
  _id: "default_strategy",
  schedule_mode: "FILL_FIRST",
  enable_dynamic_schedule: true,
  rebalance_threshold: 80,
  health_check_interval: 30000,
  create_time: new Date(),
  config: {
    "max_retry_count": 3,
    "retry_delay_ms": 1000
  }
});

print("MongoDB initialization completed successfully!");
