# queryTasks 方法优化文档

## 概述

本文档描述了对 `SchedulerService.queryTasks()` 方法的性能优化，主要解决了循环查询数据库导致的 N+1 查询问题。

## 问题分析

### 优化前的问题

原始实现在查询指定任务ID列表时存在性能问题：

```java
// 优化前的代码 - 存在 N+1 查询问题
public List<TaskInfo> queryTasks(List<String> taskIds) {
    if (taskIds == null || taskIds.isEmpty()) {
        // 查询所有活跃任务 - 这部分没问题
        allocations = allocationRepository.findByTaskStatusIn(
            Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));
    } else {
        // 问题代码：循环查询数据库
        allocations = new ArrayList<>();
        for (String taskId : taskIds) {
            allocations.addAll(allocationRepository.findActiveTasksByTaskId(taskId));
        }
    }
}
```

**问题分析：**
1. **N+1 查询问题**：对于 N 个任务ID，会执行 N 次数据库查询
2. **性能影响**：每次查询都需要建立数据库连接、网络往返
3. **资源浪费**：数据库连接池压力增大，响应时间延长
4. **扩展性差**：任务ID数量增加时，性能线性下降

### 性能影响示例

假设查询 10 个任务ID：

```sql
-- 优化前：执行 10 次查询
SELECT * FROM task_allocation WHERE taskId = 'task-001' AND taskStatus IN ('ALLOCATED', 'RUNNING');
SELECT * FROM task_allocation WHERE taskId = 'task-002' AND taskStatus IN ('ALLOCATED', 'RUNNING');
SELECT * FROM task_allocation WHERE taskId = 'task-003' AND taskStatus IN ('ALLOCATED', 'RUNNING');
-- ... 继续 7 次
```

## 优化方案

### 1. 添加批量查询方法

在 `TaskAllocationRepository` 中添加批量查询方法：

```java
/**
 * 根据任务ID列表批量查询活跃任务（已分配或运行中）
 */
@Query("{'taskId': {$in: ?0}, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
List<TaskAllocation> findActiveTasksByTaskIdIn(List<String> taskIds);
```

**MongoDB 查询语法说明：**
- `{$in: ?0}`：MongoDB 的 IN 操作符，匹配数组中的任何值
- `?0`：Spring Data MongoDB 的参数占位符，对应第一个方法参数

### 2. 优化 queryTasks 方法

```java
/**
 * 批量查询任务信息 - 优化版本
 */
public List<TaskInfo> queryTasks(List<String> taskIds) {
    List<TaskAllocation> allocations;

    if (taskIds == null || taskIds.isEmpty()) {
        // 查询所有活跃任务
        log.info("查询所有活跃任务");
        allocations = allocationRepository.findByTaskStatusIn(
            Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));
    } else {
        // 批量查询指定任务ID的活跃任务 - 优化：使用单次查询替代循环查询
        log.info("批量查询指定任务的活跃状态: taskIds={}, count={}", taskIds, taskIds.size());
        allocations = allocationRepository.findActiveTasksByTaskIdIn(taskIds);
    }

    log.info("查询到活跃任务分配数量: {}", allocations.size());
    
    return allocations.stream()
        .map(this::convertToTaskInfo)
        .collect(Collectors.toList());
}
```

## 性能对比

### 查询次数对比

| 任务ID数量 | 优化前查询次数 | 优化后查询次数 | 性能提升 |
|-----------|---------------|---------------|----------|
| 1         | 1             | 1             | 无变化    |
| 10        | 10            | 1             | 90%      |
| 50        | 50            | 1             | 98%      |
| 100       | 100           | 1             | 99%      |

### 实际 SQL 对比

**优化前（10个任务ID）：**
```sql
-- 执行 10 次独立查询
db.task_allocation.find({"taskId": "task-001", "taskStatus": {$in: ["ALLOCATED", "RUNNING"]}})
db.task_allocation.find({"taskId": "task-002", "taskStatus": {$in: ["ALLOCATED", "RUNNING"]}})
-- ... 8 次类似查询
```

**优化后（10个任务ID）：**
```sql
-- 执行 1 次批量查询
db.task_allocation.find({
    "taskId": {$in: ["task-001", "task-002", "task-003", "task-004", "task-005", 
                     "task-006", "task-007", "task-008", "task-009", "task-010"]}, 
    "taskStatus": {$in: ["ALLOCATED", "RUNNING"]}
})
```

## 测试验证

### 单元测试覆盖

创建了专门的测试类 `SchedulerServiceQueryTasksTest`，覆盖以下场景：

1. **批量查询优化验证**：确保使用批量查询方法而非循环查询
2. **空任务ID列表**：验证查询所有活跃任务的逻辑
3. **null任务ID列表**：验证边界条件处理
4. **单个任务ID**：验证单个ID也使用批量查询
5. **无活跃任务**：验证空结果处理
6. **大量任务ID**：验证大数据量场景的性能

### 关键测试验证点

```java
@Test
public void testQueryTasks_BatchQueryOptimization() {
    // 验证使用了批量查询方法，而不是循环查询
    verify(allocationRepository, times(1)).findActiveTasksByTaskIdIn(taskIds);
    verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
}
```

## 优化效果

### 1. 性能提升
- **数据库查询次数**：从 N 次减少到 1 次
- **网络往返**：减少 N-1 次网络往返
- **连接池压力**：显著降低数据库连接池压力

### 2. 扩展性改善
- **线性复杂度变为常数复杂度**：O(N) → O(1)
- **支持更大规模查询**：可以高效处理数百个任务ID的查询

### 3. 资源利用率
- **CPU使用率**：减少循环处理开销
- **内存使用**：减少临时对象创建
- **数据库负载**：减少查询压力

## 注意事项

### 1. MongoDB IN 查询限制
- MongoDB 的 `$in` 操作符对数组大小有限制（通常为 16MB）
- 对于极大的任务ID列表，可能需要分批处理

### 2. 索引优化建议
确保 `task_allocation` 集合在相关字段上有适当的索引：

```javascript
// 建议的复合索引
db.task_allocation.createIndex({"taskId": 1, "taskStatus": 1})

// 或者分别创建单字段索引
db.task_allocation.createIndex({"taskId": 1})
db.task_allocation.createIndex({"taskStatus": 1})
```

### 3. 日志监控
添加了详细的日志记录，便于监控查询性能：

```java
log.info("批量查询指定任务的活跃状态: taskIds={}, count={}", taskIds, taskIds.size());
log.info("查询到活跃任务分配数量: {}", allocations.size());
```

## 后续优化建议

### 1. 缓存机制
对于频繁查询的任务信息，可以考虑添加缓存：

```java
@Cacheable(value = "taskInfo", key = "#taskIds")
public List<TaskInfo> queryTasks(List<String> taskIds) {
    // 现有实现
}
```

### 2. 分页支持
对于大量结果的查询，可以添加分页支持：

```java
public Page<TaskInfo> queryTasksWithPaging(List<String> taskIds, Pageable pageable) {
    // 分页查询实现
}
```

### 3. 异步查询
对于非实时要求的查询，可以考虑异步处理：

```java
@Async
public CompletableFuture<List<TaskInfo>> queryTasksAsync(List<String> taskIds) {
    // 异步查询实现
}
```

## 总结

通过将循环查询优化为批量查询，`queryTasks` 方法的性能得到了显著提升：

1. **解决了 N+1 查询问题**
2. **提高了查询效率**
3. **改善了系统扩展性**
4. **减少了数据库负载**

这种优化模式可以应用到其他类似的批量查询场景中，是一个通用的性能优化最佳实践。
