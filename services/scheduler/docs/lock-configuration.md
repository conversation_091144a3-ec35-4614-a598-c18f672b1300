# 锁配置说明

## 概述

调度器支持两种锁实现方式：
- **本地锁（Local Lock）**：适用于单实例部署
- **分布式锁（Redis Lock）**：适用于多实例部署

## 配置方式

### 1. 本地锁配置（默认）

```yaml
scheduler:
  lock:
    type: local  # 使用本地内存锁
```

**特点：**
- ✅ 无需外部依赖
- ✅ 性能最佳
- ✅ 适合单实例部署
- ❌ 不支持多实例部署

### 2. 分布式锁配置

```yaml
scheduler:
  lock:
    type: redis  # 使用Redis分布式锁

spring:
  redis:
    host: localhost
    port: 6379
    password: your-password
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
```

**特点：**
- ✅ 支持多实例部署
- ✅ 真正的分布式锁
- ❌ 需要Redis依赖
- ❌ 网络延迟影响性能

## 依赖管理

### 启用Redis锁时

需要在 `pom.xml` 中取消注释Redis依赖：

```xml
<dependency>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-data-redis</artifactId>
</dependency>
```

### 使用本地锁时

无需额外依赖，Redis相关配置可以注释掉。

## 使用场景

### 单实例部署（推荐本地锁）
- 开发环境
- 测试环境
- 小规模生产环境

### 多实例部署（必须使用分布式锁）
- 高可用生产环境
- 负载均衡部署
- 容器化集群部署

## 性能对比

| 锁类型 | 延迟 | 吞吐量 | 可靠性 | 部署复杂度 |
|--------|------|--------|--------|------------|
| 本地锁 | 极低 | 极高   | 高     | 低         |
| Redis锁| 低   | 高     | 极高   | 中         |

## 切换方式

只需修改配置文件中的 `scheduler.lock.type` 值：
- `local` → 本地锁
- `redis` → Redis锁

应用会自动选择对应的实现，无需修改代码。
