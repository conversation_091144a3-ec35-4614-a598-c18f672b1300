# CV推理服务调度器业务逻辑文档

## 1. 项目概述

### 1.1 项目简介
CV推理服务调度器是一个基于Spring Boot的微服务，负责管理多个CV推理服务实例，根据设备资源和调度策略，将视频流任务合理分配到不同的推理服务上，确保系统资源的高效利用。

### 1.2 核心功能
- **服务管理**：推理服务的注册、注销和状态管理
- **任务调度**：根据调度策略分配任务到最优服务
- **负载均衡**：支持优先沾满和优先平铺两种调度模式
- **健康检查**：定时监控推理服务健康状态
- **资源管理**：实时跟踪和管理服务资源配额
- **任务恢复**：检测异常任务并自动重新调度

### 1.3 技术栈
- **Java 17** + **Spring Boot 3.2**
- **MongoDB 6.0**：持久化存储
- **Redis 7**：分布式锁（可选）
- **Maven 3.9**：项目构建
- **Docker**：容器化部署

## 2. 系统架构

### 2.1 分层架构
```
┌─────────────────────────────────────────┐
│              Controller Layer           │  ← REST API接口层
├─────────────────────────────────────────┤
│              Service Layer              │  ← 业务逻辑层
├─────────────────────────────────────────┤
│             Repository Layer            │  ← 数据访问层
├─────────────────────────────────────────┤
│              Database Layer             │  ← MongoDB存储层
└─────────────────────────────────────────┘
```

### 2.2 核心组件
- **SchedulerService**：核心调度服务
- **ServiceManager**：服务管理器
- **HealthCheckService**：健康检查服务
- **LockService**：锁服务（本地锁/分布式锁）

## 3. 数据模型

### 3.1 推理服务模型 (InferenceService)
```java
@Document(collection = "inference_service")
public class InferenceService {
    private String serviceId;           // 服务ID
    private String serviceName;         // 服务名称
    private String baseUrl;             // 服务基础URL
    private Integer maxQuota;           // 最大配额
    private Integer currentQuota;       // 当前使用配额
    private ServiceStatus status;       // 服务状态
    private GpuType gpuType;           // GPU类型
    private String region;             // 区域
    private LocalDateTime lastHeartbeat; // 最后心跳时间
    private Map<String, String> tags;   // 标签
    private Map<String, Object> metadata; // 元数据
}
```

### 3.2 任务分配记录 (TaskAllocation)
```java
@Document(collection = "task_allocation")
public class TaskAllocation {
    private String allocationId;        // 分配ID
    private String taskId;             // 任务ID
    private String deviceId;           // 设备ID
    private String serviceId;          // 服务ID
    private TaskStatus taskStatus;     // 任务状态
    private LocalDateTime allocateTime; // 分配时间
    private LocalDateTime startTime;   // 开始时间
    private LocalDateTime endTime;     // 结束时间
    private SimplifiedAtomicTask taskRequest; // 任务请求
    private ExecutionMetrics executionMetrics; // 执行指标
    private String errorMessage;       // 错误信息
}
```

### 3.3 调度策略 (ScheduleStrategy)
```java
@Document(collection = "schedule_strategy")
public class ScheduleStrategy {
    private String strategyId;         // 策略ID
    private ScheduleMode scheduleMode; // 调度模式
    private Boolean enableDynamicSchedule; // 是否启用动态调度
    private Integer rebalanceThreshold; // 重平衡阈值
    private Integer healthCheckInterval; // 健康检查间隔
    private Map<String, Object> config; // 配置信息
}
```

### 3.4 枚举类型定义

#### 服务状态 (ServiceStatus)
- **ACTIVE**：服务正常
- **INACTIVE**：服务不可用
- **MAINTENANCE**：维护中

#### 任务状态 (TaskStatus)
- **ALLOCATED**：已分配
- **RUNNING**：运行中
- **STOPPED**：已停止
- **ERROR**：错误
- **COMPLETED**：已完成
- **FAILED**：失败
- **CANCELLED**：已取消

#### 调度模式 (ScheduleMode)
- **FILL_FIRST**：优先沾满（优先选择已有任务较多的服务）
- **SPREAD_FIRST**：优先平铺（优先选择负载最轻的服务）

#### GPU类型 (GpuType)
- **A10**：默认配额30路
- **A100**：默认配额50路
- **V100**：默认配额20路
- **T4**：默认配额10路

## 4. 核心业务流程

### 4.1 任务调度流程
```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Scheduler as 调度器
    participant Lock as 锁服务
    participant DB as 数据库
    participant Service as 推理服务

    Client->>Scheduler: 提交调度请求
    Scheduler->>DB: 获取调度策略
    Scheduler->>DB: 查询可用服务列表
    Scheduler->>Scheduler: 根据策略选择最优服务
    Scheduler->>Lock: 获取服务配额锁
    Lock-->>Scheduler: 锁获取成功
    Scheduler->>DB: 检查服务可用性
    Scheduler->>DB: 创建任务分配记录
    Scheduler->>DB: 更新服务配额
    Scheduler->>Service: 发送任务到推理服务
    Service-->>Scheduler: 任务创建成功
    Scheduler->>Lock: 释放锁
    Scheduler-->>Client: 返回调度结果
```

### 4.2 服务注册流程
```mermaid
sequenceDiagram
    participant Service as 推理服务
    participant Scheduler as 调度器
    participant DB as 数据库

    Service->>Scheduler: 发送注册请求
    Scheduler->>DB: 检查服务是否已存在
    alt 服务已存在
        Scheduler->>DB: 更新服务状态为ACTIVE
    else 服务不存在
        Scheduler->>DB: 创建新服务记录
    end
    Scheduler->>DB: 更新心跳时间
    Scheduler-->>Service: 返回注册结果
```

### 4.3 健康检查流程
```mermaid
sequenceDiagram
    participant Timer as 定时器
    participant HealthCheck as 健康检查服务
    participant DB as 数据库
    participant Service as 推理服务

    Timer->>HealthCheck: 触发健康检查
    HealthCheck->>DB: 查询ACTIVE状态服务
    loop 遍历每个服务
        HealthCheck->>Service: 发送健康检查请求
        alt 健康检查成功
            HealthCheck->>DB: 更新心跳时间
        else 健康检查失败
            HealthCheck->>DB: 标记服务为INACTIVE
        end
    end
```

## 5. 数据访问层设计

### 5.1 InferenceServiceRepository
```java
public interface InferenceServiceRepository extends MongoRepository<InferenceService, String> {
    // 根据状态查询服务
    List<InferenceService> findByStatus(ServiceStatus status);
    
    // 根据区域查询服务
    List<InferenceService> findByRegion(String region);
    
    // 根据GPU类型查询服务
    List<InferenceService> findByGpuType(GpuType gpuType);
    
    // 根据服务名和URL查询（防重复注册）
    Optional<InferenceService> findByServiceNameAndBaseUrl(String serviceName, String baseUrl);
    
    // 根据区域和状态查询服务
    @Query("{'region': ?0, 'status': ?1}")
    List<InferenceService> findByRegionAndStatus(String region, ServiceStatus status);
    
    // 查询心跳超时的服务
    @Query("{'lastHeartbeat': {$lt: ?0}, 'status': {$in: ['ACTIVE', 'MAINTENANCE']}}")
    List<InferenceService> findServicesWithExpiredHeartbeat(LocalDateTime expiredTime);
}
```

### 5.2 TaskAllocationRepository
```java
public interface TaskAllocationRepository extends MongoRepository<TaskAllocation, String> {
    // 根据服务ID查询任务分配
    List<TaskAllocation> findByServiceId(String serviceId);
    
    // 根据任务状态查询
    List<TaskAllocation> findByTaskStatus(TaskStatus taskStatus);
    
    // 根据任务状态列表查询
    List<TaskAllocation> findByTaskStatusIn(List<TaskStatus> taskStatuses);
    
    // 根据任务ID查询活跃任务
    @Query("{'taskId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
    List<TaskAllocation> findActiveTasksByTaskId(String taskId);
    
    // 查询指定服务的活跃任务
    @Query("{'serviceId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}")
    List<TaskAllocation> findActiveTasksByServiceId(String serviceId);
    
    // 统计指定服务的活跃任务数量
    @Query(value = "{'serviceId': ?0, 'taskStatus': {$in: ['ALLOCATED', 'RUNNING']}}", count = true)
    long countActiveTasksByServiceId(String serviceId);
}
```

### 5.3 ScheduleStrategyRepository
```java
public interface ScheduleStrategyRepository extends MongoRepository<ScheduleStrategy, String> {
    // 根据调度模式查询策略
    Optional<ScheduleStrategy> findByScheduleMode(ScheduleMode scheduleMode);
    
    // 查询启用动态调度的策略
    @Query("{'enableDynamicSchedule': true}")
    List<ScheduleStrategy> findDynamicStrategies();
    
    // 查询默认策略
    @Query("{'strategyId': 'default_strategy'}")
    Optional<ScheduleStrategy> findDefaultStrategy();
}
```

## 6. 核心服务实现

### 6.1 调度服务 (SchedulerService)

#### 6.1.1 任务调度核心逻辑
```java
@Transactional
public ScheduleResult scheduleTask(ScheduleRequest request) {
    // 1. 获取调度策略
    ScheduleStrategy strategy = getActiveStrategy();

    // 2. 获取可用服务列表
    List<InferenceService> availableServices = getAvailableServices(request.getRegion());

    // 3. 根据策略选择服务
    InferenceService selectedService = selectService(availableServices, request, strategy);

    // 4. 使用锁分配任务（确保并发安全）
    String lockKey = QUOTA_LOCK_PREFIX + selectedService.getServiceId();
    return lockService.executeWithLock(lockKey, LOCK_TIMEOUT, () -> {
        // 5. 再次检查服务可用性
        InferenceService service = serviceRepository.findById(selectedService.getServiceId())
            .orElseThrow(() -> new NotFoundException("服务不存在"));

        // 6. 分配任务（幂等操作）
        TaskAllocationResult allocationResult = allocateTask(request, service);
        TaskAllocation allocation = allocationResult.getAllocation();

        // 7. 只有新创建的任务才需要更新quota和发送任务
        if (allocationResult.isNewlyCreated()) {
            // 更新服务quota
            updateServiceQuota(service.getServiceId(), 1);

            // 向推理服务发送任务
            boolean taskSent = sendTaskToInferenceService(request, service);
            if (!taskSent) {
                // 任务发送失败，回滚操作
                updateServiceQuota(service.getServiceId(), -1);
                allocation.setTaskStatus(TaskStatus.ERROR);
                allocation.setErrorMessage("向推理服务发送任务失败");
                allocation.setEndTime(LocalDateTime.now());
                allocationRepository.save(allocation);
                throw new RuntimeException("向推理服务发送任务失败");
            }
        }

        return ScheduleResult.success(allocation);
    });
}
```

#### 6.1.2 调度策略实现
```java
// 优先沾满策略：优先选择已有任务较多的服务
private InferenceService selectByFillFirst(List<InferenceService> services) {
    return services.stream()
        .filter(InferenceService::isAvailable)
        .max(Comparator.comparing(InferenceService::getCurrentQuota))
        .orElseThrow(() -> new NoAvailableServiceException("没有可用的推理服务"));
}

// 优先平铺策略：优先选择负载最轻的服务
private InferenceService selectBySpreadFirst(List<InferenceService> services) {
    return services.stream()
        .filter(InferenceService::isAvailable)
        .min(Comparator.comparing(InferenceService::getCurrentQuota))
        .orElseThrow(() -> new NoAvailableServiceException("没有可用的推理服务"));
}
```

#### 6.1.3 任务释放逻辑
```java
@Transactional
public void releaseTask(String allocationId) {
    TaskAllocation allocation = allocationRepository.findById(allocationId)
        .orElseThrow(() -> new NotFoundException("任务分配记录不存在"));

    // 更新任务状态
    allocation.setTaskStatus(TaskStatus.STOPPED);
    allocation.setEndTime(LocalDateTime.now());
    allocationRepository.save(allocation);

    // 释放服务quota
    updateServiceQuota(allocation.getServiceId(), -1);
}
```

### 6.2 服务管理器 (ServiceManager)

#### 6.2.1 服务注册逻辑
```java
public InferenceService registerService(ServiceRegistration registration) {
    // 检查是否已存在相同的服务
    Optional<InferenceService> existingService = serviceRepository
        .findByServiceNameAndBaseUrl(registration.getServiceName(), registration.getBaseUrl());

    if (existingService.isPresent()) {
        InferenceService service = existingService.get();
        // 更新已存在服务的状态
        service.setStatus(ServiceStatus.ACTIVE);
        service.setLastHeartbeat(LocalDateTime.now());
        return serviceRepository.save(service);
    }

    // 创建新服务
    InferenceService service = InferenceService.builder()
        .serviceId(generateServiceId())
        .serviceName(registration.getServiceName())
        .baseUrl(registration.getBaseUrl())
        .maxQuota(registration.getCustomQuota() != null ?
                 registration.getCustomQuota() :
                 registration.getGpuType().getDefaultQuota())
        .currentQuota(0)
        .status(ServiceStatus.ACTIVE)
        .gpuType(registration.getGpuType())
        .region(registration.getRegion())
        .tags(registration.getTags())
        .metadata(registration.getMetadata())
        .lastHeartbeat(LocalDateTime.now())
        .build();

    return serviceRepository.save(service);
}
```

#### 6.2.2 服务注销逻辑
```java
public void unregisterService(String serviceId) {
    InferenceService service = serviceRepository.findById(serviceId)
        .orElseThrow(() -> new NotFoundException("服务不存在"));

    // 检查是否有活跃任务
    if (service.getCurrentQuota() > 0) {
        // 服务仍有活跃任务，设置为维护状态
        updateServiceStatus(serviceId, ServiceStatus.MAINTENANCE);
    } else {
        // 直接删除服务
        serviceRepository.delete(service);
    }
}
```

### 6.3 健康检查服务 (HealthCheckService)

#### 6.3.1 定时健康检查
```java
@Scheduled(fixedDelayString = "${scheduler.health-check.interval:30000}")
public void performHealthCheck() {
    if (!healthCheckEnabled) {
        return;
    }

    List<InferenceService> services = serviceRepository.findByStatus(ServiceStatus.ACTIVE);

    for (InferenceService service : services) {
        try {
            boolean isHealthy = checkServiceHealth(service);
            if (!isHealthy) {
                serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
            }
        } catch (Exception e) {
            serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
        }
    }
}
```

#### 6.3.2 服务恢复检查
```java
@Scheduled(fixedDelayString = "${scheduler.health-check.recovery-interval:60000}")
public void recoverUnhealthyServices() {
    if (!healthCheckEnabled) {
        return;
    }

    List<InferenceService> inactiveServices = serviceRepository.findByStatus(ServiceStatus.INACTIVE);

    for (InferenceService service : inactiveServices) {
        try {
            boolean isHealthy = checkServiceHealth(service);
            if (isHealthy) {
                serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.ACTIVE);
            }
        } catch (Exception e) {
            // 服务仍未恢复，继续保持INACTIVE状态
        }
    }
}
```

#### 6.3.3 孤儿任务清理
```java
@Scheduled(fixedRate = 30000) // 30秒
public void scheduledTaskMaintenance() {
    try {
        // 重试失败的任务
        retryFailedTasks();

        // 清理孤儿任务
        cleanupOrphanTasks();
    } catch (Exception e) {
        log.error("定时任务维护异常: {}", e.getMessage(), e);
    }
}

private void cleanupOrphanTasks() {
    // 查找可能的孤儿任务（分配时间超过阈值但仍为ALLOCATED状态）
    LocalDateTime threshold = LocalDateTime.now().minusMinutes(5);
    List<TaskAllocation> orphanTasks = allocationRepository
        .findByTaskStatusAndAllocateTimeBefore(TaskStatus.ALLOCATED, threshold);

    for (TaskAllocation orphanTask : orphanTasks) {
        processOrphanTask(orphanTask);
    }
}
```

## 7. 锁机制设计

### 7.1 锁服务接口
```java
public interface LockService {
    boolean tryLock(String lockKey, long timeoutSeconds);
    void unlock(String lockKey);
    <T> T executeWithLock(String lockKey, long timeoutSeconds, LockAction<T> action) throws Exception;

    @FunctionalInterface
    interface LockAction<T> {
        T execute() throws Exception;
    }
}
```

### 7.2 本地锁实现
```java
@Service
@ConditionalOnProperty(name = "scheduler.lock.type", havingValue = "local", matchIfMissing = true)
public class LocalLockService implements LockService {
    private final ConcurrentHashMap<String, ReentrantLock> locks = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> lockExpireTime = new ConcurrentHashMap<>();

    @Override
    public boolean tryLock(String lockKey, long timeoutSeconds) {
        try {
            ReentrantLock lock = locks.computeIfAbsent(lockKey, k -> new ReentrantLock());
            boolean acquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);

            if (acquired) {
                long expireTime = System.currentTimeMillis() + (timeoutSeconds * 1000);
                lockExpireTime.put(lockKey, expireTime);
            }
            return acquired;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    @Override
    public void unlock(String lockKey) {
        ReentrantLock lock = locks.get(lockKey);
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
            lockExpireTime.remove(lockKey);

            if (!lock.hasQueuedThreads()) {
                locks.remove(lockKey);
            }
        }
    }
}
```

### 7.3 锁配置说明
- **本地锁**：适用于单实例部署，性能最佳，无需外部依赖
- **分布式锁**：适用于多实例部署，基于Redis实现，支持真正的分布式锁
- **配置切换**：通过 `scheduler.lock.type` 配置项控制（local/redis）

## 8. REST API接口设计

### 8.1 调度控制器 (SchedulerController)

#### 8.1.1 任务调度接口
```java
@PostMapping("/schedule")
public ResponseEntity<ScheduleResult> scheduleTask(@Valid @RequestBody ScheduleRequest request) {
    ScheduleResult result = schedulerService.scheduleTask(request);

    if (result.isSuccess()) {
        return ResponseEntity.ok(result);
    } else {
        HttpStatus status = "NO_AVAILABLE_SERVICE".equals(result.getErrorCode()) ?
            HttpStatus.SERVICE_UNAVAILABLE : HttpStatus.INTERNAL_SERVER_ERROR;
        return ResponseEntity.status(status).body(result);
    }
}
```

#### 8.1.2 任务释放接口
```java
@PostMapping("/release/{allocationId}")
public ResponseEntity<Void> releaseTask(@PathVariable String allocationId) {
    try {
        schedulerService.releaseTask(allocationId);
        return ResponseEntity.ok().build();
    } catch (NotFoundException e) {
        return ResponseEntity.notFound().build();
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

#### 8.1.3 服务状态查询接口
```java
@GetMapping("/services")
public ResponseEntity<List<ServiceInfo>> getAllServices() {
    List<ServiceInfo> services = schedulerService.getAllServices();
    return ResponseEntity.ok(services);
}
```

### 8.2 服务管理控制器 (ServiceController)

#### 8.2.1 服务注册接口
```java
@PostMapping("/register")
public ResponseEntity<InferenceService> registerService(@Valid @RequestBody ServiceRegistration registration) {
    try {
        InferenceService service = serviceManager.registerService(registration);
        return ResponseEntity.status(HttpStatus.CREATED).body(service);
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

#### 8.2.2 服务状态更新接口
```java
@PutMapping("/{serviceId}/status")
public ResponseEntity<Void> updateServiceStatus(@PathVariable String serviceId,
                                               @RequestParam ServiceStatus status) {
    try {
        serviceManager.updateServiceStatus(serviceId, status);
        return ResponseEntity.ok().build();
    } catch (NotFoundException e) {
        return ResponseEntity.notFound().build();
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

#### 8.2.3 服务配额更新接口
```java
@PutMapping("/{serviceId}/quota")
public ResponseEntity<Void> updateServiceQuota(@PathVariable String serviceId,
                                              @RequestParam Integer maxQuota) {
    try {
        serviceManager.updateServiceQuota(serviceId, maxQuota);
        return ResponseEntity.ok().build();
    } catch (NotFoundException e) {
        return ResponseEntity.notFound().build();
    } catch (IllegalArgumentException e) {
        return ResponseEntity.badRequest().build();
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

#### 8.2.4 服务心跳接口
```java
@PostMapping("/{serviceId}/heartbeat")
public ResponseEntity<Void> serviceHeartbeat(@PathVariable String serviceId) {
    try {
        serviceManager.updateServiceHeartbeat(serviceId);
        return ResponseEntity.ok().build();
    } catch (NotFoundException e) {
        return ResponseEntity.notFound().build();
    } catch (Exception e) {
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
    }
}
```

## 9. 配置管理

### 9.1 应用配置 (application.yml)
```yaml
server:
  port: ${SERVER_PORT:8080}

spring:
  application:
    name: cv-scheduler

  data:
    mongodb:
      uri: ${MONGODB_URI:*************************************************************************}
      database: ${MONGODB_DATABASE:cv_scheduler}
      auto-index-creation: true

scheduler:
  # 锁类型配置: local(本地内存锁) 或 redis(分布式锁)
  lock:
    type: local  # 默认使用本地锁，适用于单例部署

  health-check:
    interval: 30000  # 30秒
    timeout: 5000    # 5秒
    enabled: true
    recovery-interval: 60000  # 60秒

  strategy:
    default-mode: FILL_FIRST
    enable-dynamic-schedule: true
    rebalance-threshold: 80  # 负载超过80%时触发重平衡

  service:
    discovery:
      enabled: true
      interval: 60000  # 60秒

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always

logging:
  level:
    com.bohua.scheduler: DEBUG
    org.springframework.web: INFO
    org.springframework.data.mongodb: INFO
```

### 9.2 MongoDB配置类
```java
@Configuration
@EnableMongoRepositories(basePackages = "com.bohua.scheduler.repository")
@EnableMongoAuditing
public class MongoConfig {

    @Value("${spring.data.mongodb.uri}")
    private String mongoUri;

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), "cv_scheduler");
    }

    @Bean
    public MongoClient mongoClient() {
        return MongoClients.create(mongoUri);
    }

    @Bean
    public MongoCustomConversions customConversions() {
        List<Converter<?, ?>> converters = new ArrayList<>();
        converters.add(new LocalDateTimeToDateConverter());
        converters.add(new DateToLocalDateTimeConverter());
        return new MongoCustomConversions(converters);
    }
}
```

### 9.3 Web配置类
```java
@Configuration
public class WebConfig {

    @Value("${scheduler.health-check.timeout:5000}")
    private int healthCheckTimeout;

    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplateBuilder()
            .setConnectTimeout(Duration.ofMillis(healthCheckTimeout))
            .setReadTimeout(Duration.ofMillis(healthCheckTimeout))
            .build();
    }
}
```

## 10. 异常处理机制

### 10.1 异常类层次结构
```java
// 基础异常类
public class SchedulerException extends RuntimeException {
    private final String errorCode;

    public SchedulerException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}

// 无可用服务异常
public class NoAvailableServiceException extends SchedulerException {
    public NoAvailableServiceException(String message) {
        super(message, "NO_AVAILABLE_SERVICE");
    }
}

// 资源未找到异常
public class NotFoundException extends SchedulerException {
    public NotFoundException(String message) {
        super(message, "NOT_FOUND");
    }
}
```

### 10.2 全局异常处理器
```java
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(NoAvailableServiceException.class)
    public ResponseEntity<ErrorResponse> handleNoAvailableService(NoAvailableServiceException e) {
        ErrorResponse error = ErrorResponse.builder()
            .errorCode(e.getErrorCode())
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .build();
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(error);
    }

    @ExceptionHandler(NotFoundException.class)
    public ResponseEntity<ErrorResponse> handleNotFound(NotFoundException e) {
        ErrorResponse error = ErrorResponse.builder()
            .errorCode(e.getErrorCode())
            .message(e.getMessage())
            .timestamp(LocalDateTime.now())
            .build();
        return ResponseEntity.status(HttpStatus.NOT_FOUND).body(error);
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGeneral(Exception e) {
        ErrorResponse error = ErrorResponse.builder()
            .errorCode("INTERNAL_ERROR")
            .message("内部服务器错误")
            .timestamp(LocalDateTime.now())
            .build();
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(error);
    }
}
```

## 11. 监控指标

### 11.1 健康检查端点
- `/actuator/health`：应用健康状态
- `/actuator/info`：应用信息
- `/actuator/metrics`：应用指标
- `/actuator/prometheus`：Prometheus格式指标

### 11.2 业务指标
- **服务数量**：活跃服务、不可用服务、维护中服务数量
- **任务数量**：已分配、运行中、已完成、失败任务数量
- **资源利用率**：各服务的配额使用率
- **调度性能**：任务调度成功率、平均调度时间
- **健康检查**：健康检查成功率、服务恢复次数

## 12. 部署配置

### 12.1 Docker配置
```dockerfile
FROM openjdk:17-jre-slim

WORKDIR /app
COPY target/scheduler-*.jar app.jar

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

### 12.2 Docker Compose配置
```yaml
version: '3.8'

services:
  cv-scheduler:
    image: cv-scheduler:latest
    container_name: cv-scheduler
    ports:
      - "${SCHEDULER_PORT:-8080}:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - MONGODB_URI=${MONGODB_URI:-mongodb://admin:<EMAIL>:27017/cv_scheduler?authSource=admin}
      - MONGODB_DATABASE=${MONGODB_DATABASE:-cv_scheduler}
      - SCHEDULER_LOCK_TYPE=${SCHEDULER_LOCK_TYPE:-local}
      - SCHEDULER_HEALTH_CHECK_INTERVAL=${SCHEDULER_HEALTH_CHECK_INTERVAL:-30000}
      - SCHEDULER_HEALTH_CHECK_TIMEOUT=${SCHEDULER_HEALTH_CHECK_TIMEOUT:-5000}
    depends_on:
      - mongodb
    restart: unless-stopped

  mongodb:
    image: mongo:6.0
    container_name: cv-scheduler-mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=password123
      - MONGO_INITDB_DATABASE=cv_scheduler
    volumes:
      - mongodb_data:/data/db
      - ./mongo/init.js:/docker-entrypoint-initdb.d/init.js:ro
    restart: unless-stopped

volumes:
  mongodb_data:
```

## 13. 关键业务特性

### 13.1 幂等性保证
- **任务分配幂等**：相同taskId的重复调度请求返回相同结果
- **服务注册幂等**：相同服务的重复注册更新状态而非创建新记录
- **状态更新幂等**：重复的状态更新操作不会产生副作用

### 13.2 并发安全
- **配额锁机制**：使用锁确保服务配额更新的原子性
- **数据库事务**：关键操作使用事务保证数据一致性
- **乐观锁**：MongoDB的版本控制防止并发修改冲突

### 13.3 容错机制
- **健康检查**：定时检测服务健康状态，自动标记不可用服务
- **任务恢复**：检测孤儿任务并自动重新调度
- **服务恢复**：定时尝试恢复不可用服务
- **重试机制**：失败任务自动重试，支持指数退避策略

### 13.4 扩展性设计
- **策略模式**：调度策略可插拔，支持自定义调度算法
- **锁抽象**：支持本地锁和分布式锁，可根据部署模式选择
- **配置驱动**：核心参数通过配置文件控制，无需修改代码
- **微服务架构**：独立部署，可水平扩展

## 14. 性能优化

### 14.1 数据库优化
- **索引设计**：为常用查询字段创建索引
- **连接池**：配置合适的数据库连接池大小
- **查询优化**：使用聚合查询减少数据传输

### 14.2 缓存策略
- **服务列表缓存**：缓存活跃服务列表，减少数据库查询
- **策略缓存**：缓存调度策略配置
- **本地缓存**：使用本地缓存减少网络开销

### 14.3 异步处理
- **健康检查异步**：健康检查操作异步执行，不阻塞主流程
- **任务恢复异步**：孤儿任务清理和恢复异步执行
- **事件驱动**：使用事件机制解耦业务逻辑

## 15. 总结

CV推理服务调度器是一个功能完整、设计合理的微服务系统，具有以下核心特点：

### 15.1 架构优势
- **分层清晰**：Controller-Service-Repository三层架构，职责分明
- **模块化设计**：各功能模块独立，便于维护和扩展
- **配置灵活**：支持多种部署模式和配置选项

### 15.2 业务能力
- **智能调度**：支持多种调度策略，根据负载情况智能分配任务
- **服务管理**：完整的服务生命周期管理，包括注册、注销、状态监控
- **故障恢复**：自动检测和恢复异常任务，保证系统稳定性

### 15.3 技术特色
- **并发安全**：使用锁机制和事务保证数据一致性
- **容错能力**：多层次的容错机制，提高系统可靠性
- **监控完善**：丰富的监控指标和健康检查端点

### 15.4 扩展性
- **水平扩展**：支持多实例部署，可根据负载动态扩缩容
- **策略扩展**：调度策略可插拔，支持自定义算法
- **存储扩展**：支持MongoDB集群，可处理大规模数据

该调度器为CV推理服务提供了稳定、高效、可扩展的任务调度能力，是整个CV分析系统的核心组件。
```
