package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 告警配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlertConfig {

    /** 告警标签列表（兼容旧版本） */
    private List<String> labels;

    /** 置信度阈值 */
    private Double confidence;

    /** 零样本分类正例标签 */
    private List<String> positiveLabels;

    /** 零样本分类负例标签 */
    private List<String> negativeLabels;
}
