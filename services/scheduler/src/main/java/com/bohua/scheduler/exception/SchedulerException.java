package com.bohua.scheduler.exception;

/**
 * 调度器基础异常
 */
public class SchedulerException extends RuntimeException {
    
    private final String errorCode;
    
    public SchedulerException(String message) {
        super(message);
        this.errorCode = "SCHEDULER_ERROR";
    }
    
    public SchedulerException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }
    
    public SchedulerException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = "SCHEDULER_ERROR";
    }
    
    public SchedulerException(String message, String errorCode, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
    }
    
    public String getErrorCode() {
        return errorCode;
    }
}
