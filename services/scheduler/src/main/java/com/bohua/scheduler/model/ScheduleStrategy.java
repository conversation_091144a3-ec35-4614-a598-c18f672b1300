package com.bohua.scheduler.model;

import com.bohua.scheduler.model.enums.ScheduleMode;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 调度策略配置
 */
@Document(collection = "schedule_strategy")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleStrategy {
    
    @Id
    private String strategyId;
    
    @Field("schedule_mode")
    private ScheduleMode scheduleMode;
    
    @Field("enable_dynamic_schedule")
    private Boolean enableDynamicSchedule;
    
    @Field("rebalance_threshold")
    private Integer rebalanceThreshold;
    
    @Field("health_check_interval")
    private Integer healthCheckInterval;
    
    @Field("create_time")
    @CreatedDate
    private LocalDateTime createTime;
    
    @Field("config")
    private Map<String, Object> config;
}
