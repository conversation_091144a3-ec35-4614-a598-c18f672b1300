package com.bohua.scheduler.service;

import com.bohua.scheduler.dto.HealthResponse;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.List;

/**
 * 健康检查服务
 */
@Service
@Slf4j
public class HealthCheckService {
    
    @Autowired
    private InferenceServiceRepository serviceRepository;
    
    @Autowired
    private ServiceManager serviceManager;
    
    @Value("${scheduler.health-check.timeout:5000}")
    private int healthCheckTimeout;
    
    @Value("${scheduler.health-check.enabled:true}")
    private boolean healthCheckEnabled;

    private RestTemplate restTemplate;

    @PostConstruct
    public void init() {
        // 配置RestTemplate
        restTemplate = new RestTemplateBuilder()
            .setConnectTimeout(Duration.ofMillis(healthCheckTimeout))
            .setReadTimeout(Duration.ofMillis(healthCheckTimeout))
            .build();

        log.info("健康检查服务初始化完成: timeout={}ms, enabled={}", healthCheckTimeout, healthCheckEnabled);
    }
    
    /**
     * 定时健康检查
     */
    @Scheduled(fixedDelayString = "${scheduler.health-check.interval:30000}")
    public void performHealthCheck() {
        if (!healthCheckEnabled) {
            return;
        }
        
        log.debug("开始执行健康检查");
        
        List<InferenceService> services = serviceRepository.findByStatus(ServiceStatus.ACTIVE);
        
        for (InferenceService service : services) {
            try {
                boolean isHealthy = checkServiceHealth(service);
                if (!isHealthy) {
                    log.warn("服务健康检查失败，标记为不可用: serviceId={}, serviceName={}", 
                           service.getServiceId(), service.getServiceName());
                    serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
                }
            } catch (Exception e) {
                log.error("健康检查异常: serviceId={}, error={}", 
                         service.getServiceId(), e.getMessage());
                serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.INACTIVE);
            }
        }
        
        log.debug("健康检查完成，检查了{}个服务", services.size());
    }
    
    /**
     * 检查单个服务健康状态
     */
    public boolean checkServiceHealth(InferenceService service) {
        try {
            String healthUrl = buildHealthUrl(service.getBaseUrl());
            
            log.debug("检查服务健康状态: serviceId={}, url={}", service.getServiceId(), healthUrl);
            
            ResponseEntity<HealthResponse> response = restTemplate.getForEntity(
                healthUrl, HealthResponse.class);
            
            boolean isHealthy = response.getStatusCode().is2xxSuccessful() && 
                               response.getBody() != null && 
                               "UP".equalsIgnoreCase(response.getBody().getStatus());
            
            if (isHealthy) {
                log.debug("服务健康检查通过: serviceId={}", service.getServiceId());
                // 更新服务心跳时间
                serviceManager.updateServiceHeartbeat(service.getServiceId());
            } else {
                log.warn("服务健康检查失败: serviceId={}, status={}",
                        service.getServiceId(),
                        response.getBody() != null ? response.getBody().getStatus() : "null");
            }

            return isHealthy;
            
        } catch (Exception e) {
            log.debug("服务健康检查异常: serviceId={}, error={}", 
                     service.getServiceId(), e.getMessage());
            return false;
        }
    }
    
    /**
     * 手动检查服务健康状态
     */
    public HealthResponse manualHealthCheck(String serviceId) {
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new RuntimeException("服务不存在"));
        
        try {
            String healthUrl = buildHealthUrl(service.getBaseUrl());
            ResponseEntity<HealthResponse> response = restTemplate.getForEntity(
                healthUrl, HealthResponse.class);
            
            return response.getBody();
            
        } catch (Exception e) {
            return HealthResponse.builder()
                .status("DOWN")
                .timestamp(System.currentTimeMillis())
                .build();
        }
    }
    
    /**
     * 恢复不健康的服务
     */
    @Scheduled(fixedDelayString = "${scheduler.health-check.recovery-interval:60000}")
    public void recoverUnhealthyServices() {
        if (!healthCheckEnabled) {
            return;
        }
        
        List<InferenceService> inactiveServices = serviceRepository.findByStatus(ServiceStatus.INACTIVE);
        
        for (InferenceService service : inactiveServices) {
            try {
                boolean isHealthy = checkServiceHealth(service);
                if (isHealthy) {
                    log.info("服务恢复健康，重新激活: serviceId={}, serviceName={}", 
                           service.getServiceId(), service.getServiceName());
                    serviceManager.updateServiceStatus(service.getServiceId(), ServiceStatus.ACTIVE);
                }
            } catch (Exception e) {
                log.debug("服务恢复检查失败: serviceId={}, error={}", 
                         service.getServiceId(), e.getMessage());
            }
        }
    }
    
    /**
     * 构建健康检查URL
     */
    private String buildHealthUrl(String baseUrl) {
        String url = baseUrl.endsWith("/") ? baseUrl : baseUrl + "/";
        return url + "health";
    }
}
