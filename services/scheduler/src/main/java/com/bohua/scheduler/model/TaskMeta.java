package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务元信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskMeta {
    
    /** 是否启用 */
    private Boolean enabled;
    
    /** 任务级别 */
    private String taskLevel;
    
    /** 协议类型 */
    private String protocol;
    
    /** 事件类型ID */
    private String eventTypeId;
    
    /** 事件动作 */
    private List<String> eventAction;
}
