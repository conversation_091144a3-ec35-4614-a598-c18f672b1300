package com.bohua.scheduler.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;

/**
 * Web配置类
 */
@Configuration
public class WebConfig {
    
    @Value("${scheduler.health-check.timeout:5000}")
    private int healthCheckTimeout;
    
    /**
     * 配置RestTemplate Bean
     */
    @Bean
    public RestTemplate restTemplate() {
        return new RestTemplateBuilder()
            .setConnectTimeout(Duration.ofMillis(healthCheckTimeout))
            .setReadTimeout(Duration.ofMillis(healthCheckTimeout))
            .build();
    }
}
