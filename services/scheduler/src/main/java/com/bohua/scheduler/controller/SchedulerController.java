package com.bohua.scheduler.controller;

import com.bohua.scheduler.dto.ScheduleRequest;
import com.bohua.scheduler.dto.ScheduleResult;
import com.bohua.scheduler.dto.ServiceInfo;
import com.bohua.scheduler.dto.TaskInfo;
import com.bohua.scheduler.dto.TaskQueryRequest;
import com.bohua.scheduler.exception.NoAvailableServiceException;
import com.bohua.scheduler.exception.NotFoundException;
import com.bohua.scheduler.service.SchedulerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 调度控制器
 */
@RestController
@RequestMapping("/api/v1/scheduler")
@Slf4j
@Validated
public class SchedulerController {
    
    @Autowired
    private SchedulerService schedulerService;
    
    /**
     * 调度任务
     */
    @PostMapping("/schedule")
    public ResponseEntity<ScheduleResult> scheduleTask(@Valid @RequestBody ScheduleRequest request) {
        log.info("收到任务调度请求: taskId={}, deviceId={}",
                request.getTaskRequest().getTaskId(),
                request.getTaskRequest().getDevice().getDeviceId());
        
        try {
            ScheduleResult result = schedulerService.scheduleTask(request);
            
            if (result.isSuccess()) {
                return ResponseEntity.ok(result);
            } else {
                HttpStatus status = "NO_AVAILABLE_SERVICE".equals(result.getErrorCode()) ? 
                    HttpStatus.SERVICE_UNAVAILABLE : HttpStatus.INTERNAL_SERVER_ERROR;
                return ResponseEntity.status(status).body(result);
            }
            
        } catch (NoAvailableServiceException e) {
            log.warn("无可用服务: {}", e.getMessage());
            return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ScheduleResult.builder()
                    .success(false)
                    .errorMessage("无可用的推理服务")
                    .errorCode("NO_AVAILABLE_SERVICE")
                    .build());
                    
        } catch (Exception e) {
            log.error("任务调度失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ScheduleResult.builder()
                    .success(false)
                    .errorMessage("内部服务错误")
                    .errorCode("INTERNAL_ERROR")
                    .build());
        }
    }
    
    /**
     * 释放任务
     */
    @PostMapping("/release/{taskId}")
    public ResponseEntity<Void> releaseTask(@PathVariable String taskId) {
        log.info("收到任务释放请求: taskId={}", taskId);

        try {
            schedulerService.releaseTaskByTaskId(taskId);
            return ResponseEntity.ok().build();

        } catch (NotFoundException e) {
            log.warn("任务不存在: taskId={}", taskId);
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("任务释放失败: taskId={}", taskId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 查询服务状态
     */
    @GetMapping("/services")
    public ResponseEntity<List<ServiceInfo>> getServices(
            @RequestParam(required = false) String region,
            @RequestParam(required = false) String status) {
        
        try {
            List<ServiceInfo> services = schedulerService.getAllServices();
            
            // 简单过滤（实际应该在Service层实现）
            if (region != null && !region.isEmpty()) {
                services = services.stream()
                    .filter(s -> region.equals(s.getRegion()))
                    .collect(Collectors.toList());
            }
            
            return ResponseEntity.ok(services);
            
        } catch (Exception e) {
            log.error("查询服务列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 查询单个服务信息
     */
    @GetMapping("/services/{serviceId}")
    public ResponseEntity<ServiceInfo> getService(@PathVariable String serviceId) {
        try {
            List<ServiceInfo> services = schedulerService.getAllServices();
            ServiceInfo service = services.stream()
                .filter(s -> serviceId.equals(s.getServiceId()))
                .findFirst()
                .orElse(null);
            
            if (service == null) {
                return ResponseEntity.notFound().build();
            }
            
            return ResponseEntity.ok(service);
            
        } catch (Exception e) {
            log.error("查询服务信息失败: serviceId={}", serviceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 批量查询任务
     */
    @PostMapping("/tasks")
    public ResponseEntity<List<TaskInfo>> queryTasks(@RequestBody TaskQueryRequest request) {
        try {
            List<TaskInfo> tasks = schedulerService.queryTasks(request.getTaskIds());
            return ResponseEntity.ok(tasks);

        } catch (Exception e) {
            log.error("查询任务列表失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 查询特定任务
     */
    @GetMapping("/tasks/{taskId}")
    public ResponseEntity<TaskInfo> getTask(@PathVariable String taskId) {
        try {
            TaskInfo task = schedulerService.getTaskInfo(taskId);
            return ResponseEntity.ok(task);

        } catch (NotFoundException e) {
            log.warn("任务不存在: taskId={}", taskId);
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("查询任务信息失败: taskId={}", taskId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("OK");
    }
}
