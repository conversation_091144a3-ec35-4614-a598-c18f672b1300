package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 线段
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Line {
    
    /** 线段ID */
    private String lineId;
    
    /** 线段名称 */
    private String lineName;
    
    /** 起点坐标 */
    private Point startPoint;
    
    /** 终点坐标 */
    private Point endPoint;
    
    /** 检测方向 */
    private Direction direction;
}
