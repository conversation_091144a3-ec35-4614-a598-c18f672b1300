package com.bohua.scheduler.dto;

import com.bohua.scheduler.model.enums.GpuType;
import com.bohua.scheduler.model.enums.ServiceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 服务信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceInfo {
    
    private String serviceId;
    private String serviceName;
    private String baseUrl;
    private Integer maxQuota;
    private Integer currentQuota;
    private ServiceStatus status;
    private GpuType gpuType;
    private String region;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private Map<String, String> tags;
    private Double loadRate;
    private Integer availableQuota;
}
