package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 多边形区域
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Polygon {
    
    /** 多边形ID */
    private String polygonId;
    
    /** 多边形名称 */
    private String polygonName;
    
    /** 顶点坐标列表 */
    private List<Point> points;
}
