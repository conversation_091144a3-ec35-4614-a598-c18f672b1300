package com.bohua.scheduler.model;

import com.bohua.scheduler.model.enums.GpuType;
import com.bohua.scheduler.model.enums.ServiceStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.*;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 推理服务配置
 */
@Document(collection = "inference_service")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InferenceService {
    
    @Id
    private String serviceId;
    
    @Field("service_name")
    @Indexed
    private String serviceName;
    
    @Field("base_url")
    private String baseUrl;
    
    @Field("max_quota")
    private Integer maxQuota;
    
    @Field("current_quota")
    private Integer currentQuota;
    
    @Field("status")
    @Indexed
    private ServiceStatus status;
    
    @Field("gpu_type")
    private GpuType gpuType;
    
    @Field("region")
    @Indexed
    private String region;
    
    @Field("create_time")
    @CreatedDate
    private LocalDateTime createTime;
    
    @Field("update_time")
    @LastModifiedDate
    private LocalDateTime updateTime;

    @Field("last_heartbeat")
    private LocalDateTime lastHeartbeat;

    @Field("tags")
    private Map<String, String> tags;

    @Field("metadata")
    private Map<String, Object> metadata;
    
    /**
     * 检查服务是否可用
     */
    public boolean isAvailable() {
        return status == ServiceStatus.ACTIVE && currentQuota < maxQuota;
    }
    
    /**
     * 获取剩余quota
     */
    public int getAvailableQuota() {
        return maxQuota - currentQuota;
    }
    
    /**
     * 获取负载率
     */
    public double getLoadRate() {
        return maxQuota > 0 ? (double) currentQuota / maxQuota : 0.0;
    }
}
