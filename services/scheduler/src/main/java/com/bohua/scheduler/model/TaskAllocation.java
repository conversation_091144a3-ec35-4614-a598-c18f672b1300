package com.bohua.scheduler.model;

import com.bohua.scheduler.model.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.CreatedDate;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.time.LocalDateTime;

/**
 * 任务分配记录
 */
@Document(collection = "task_allocation")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAllocation {
    
    @Id
    private String allocationId;
    
    @Field("task_id")
    @Indexed
    private String taskId;
    
    @Field("device_id")
    @Indexed
    private String deviceId;
    
    @Field("service_id")
    @Indexed
    private String serviceId;
    
    @Field("task_status")
    @Indexed
    private TaskStatus taskStatus;
    
    @Field("allocate_time")
    @CreatedDate
    private LocalDateTime allocateTime;
    
    @Field("start_time")
    private LocalDateTime startTime;
    
    @Field("end_time")
    private LocalDateTime endTime;
    
    @Field("task_request")
    private SimplifiedAtomicTask taskRequest;
    
    @Field("execution_metrics")
    private ExecutionMetrics executionMetrics;

    @Field("error_message")
    private String errorMessage;
    
    /**
     * 检查任务是否活跃
     */
    public boolean isActive() {
        return taskStatus == TaskStatus.ALLOCATED || taskStatus == TaskStatus.RUNNING;
    }
    
    /**
     * 获取执行时长（毫秒）
     */
    public Long getExecutionDuration() {
        if (startTime == null) return null;
        LocalDateTime endTimeToUse = endTime != null ? endTime : LocalDateTime.now();
        return java.time.Duration.between(startTime, endTimeToUse).toMillis();
    }
}
