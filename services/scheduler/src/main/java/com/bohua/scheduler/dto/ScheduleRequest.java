package com.bohua.scheduler.dto;

import com.bohua.scheduler.model.SimplifiedAtomicTask;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 调度请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ScheduleRequest {

    @NotNull(message = "任务请求不能为空")
    private SimplifiedAtomicTask taskRequest;

    private Map<String, Object> config;

    private String region;

    private Integer priority;
}
