package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * 算法模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Algorithm {
    
    /** 算法ID */
    private String algorithmId;
    
    /** 算法名称 */
    private String algorithmName;
    
    /** 算法类型 */
    private String algorithmType;
    
    /** 执行顺序 */
    private Integer order;
    
    /** 是否必需 */
    private Boolean required;
    
    /** 依赖的算法ID列表 */
    private List<String> dependsOn;
    
    /** 算法配置 */
    private Map<String, Object> config;
    
    /** 告警配置（仅分类算法需要） */
    private AlertConfig alertConfig;
    
    /** 训练配置（仅分类算法需要） */
    private TrainingConfig trainingConfig;
    
    /** 规则配置（仅规则类算法需要） */
    private RuleConfig ruleConfig;
}
