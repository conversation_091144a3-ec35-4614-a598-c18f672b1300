package com.bohua.scheduler.service.impl;

import com.bohua.scheduler.service.LockService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.TimeUnit;

/**
 * 本地内存锁实现
 * 适用于单例部署模式
 */
@Service
@Slf4j
@ConditionalOnProperty(name = "scheduler.lock.type", havingValue = "local", matchIfMissing = true)
public class LocalLockService implements LockService {
    
    private final ConcurrentHashMap<String, ReentrantLock> locks = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> lockExpireTime = new ConcurrentHashMap<>();
    
    @Override
    public boolean tryLock(String lockKey, long timeoutSeconds) {
        try {
            ReentrantLock lock = locks.computeIfAbsent(lockKey, k -> new ReentrantLock());
            
            // 尝试获取锁
            boolean acquired = lock.tryLock(timeoutSeconds, TimeUnit.SECONDS);
            
            if (acquired) {
                // 设置锁的过期时间
                long expireTime = System.currentTimeMillis() + (timeoutSeconds * 1000);
                lockExpireTime.put(lockKey, expireTime);
                log.debug("成功获取本地锁: lockKey={}, timeout={}s", lockKey, timeoutSeconds);
                return true;
            } else {
                log.debug("获取本地锁失败: lockKey={}, timeout={}s", lockKey, timeoutSeconds);
                return false;
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.warn("获取本地锁被中断: lockKey={}", lockKey, e);
            return false;
        }
    }
    
    @Override
    public void unlock(String lockKey) {
        ReentrantLock lock = locks.get(lockKey);
        if (lock != null && lock.isHeldByCurrentThread()) {
            lock.unlock();
            lockExpireTime.remove(lockKey);
            
            // 如果锁没有等待的线程，可以移除它以节省内存
            if (!lock.hasQueuedThreads()) {
                locks.remove(lockKey);
            }
            
            log.debug("释放本地锁: lockKey={}", lockKey);
        } else {
            log.warn("尝试释放未持有的锁: lockKey={}", lockKey);
        }
    }
    
    @Override
    public <T> T executeWithLock(String lockKey, long timeoutSeconds, LockAction<T> action) throws Exception {
        if (!tryLock(lockKey, timeoutSeconds)) {
            throw new RuntimeException("获取锁失败: " + lockKey);
        }
        
        try {
            return action.execute();
        } finally {
            unlock(lockKey);
        }
    }
    
    /**
     * 清理过期的锁（可以通过定时任务调用）
     */
    public void cleanupExpiredLocks() {
        long currentTime = System.currentTimeMillis();
        lockExpireTime.entrySet().removeIf(entry -> {
            String lockKey = entry.getKey();
            Long expireTime = entry.getValue();
            
            if (currentTime > expireTime) {
                ReentrantLock lock = locks.get(lockKey);
                if (lock != null && !lock.isLocked()) {
                    locks.remove(lockKey);
                    log.debug("清理过期锁: lockKey={}", lockKey);
                    return true;
                }
            }
            return false;
        });
    }
}
