package com.bohua.scheduler.controller;

import com.bohua.scheduler.dto.HealthResponse;
import com.bohua.scheduler.dto.ServiceRegistration;
import com.bohua.scheduler.exception.NotFoundException;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.service.HealthCheckService;
import com.bohua.scheduler.service.ServiceManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 服务管理控制器
 */
@RestController
@RequestMapping("/api/v1/services")
@Slf4j
@Validated
public class ServiceController {
    
    @Autowired
    private ServiceManager serviceManager;
    
    @Autowired
    private HealthCheckService healthCheckService;
    
    /**
     * 注册推理服务
     */
    @PostMapping("/register")
    public ResponseEntity<InferenceService> registerService(@Valid @RequestBody ServiceRegistration registration) {
        log.info("收到服务注册请求: serviceName={}, baseUrl={}", 
                registration.getServiceName(), registration.getBaseUrl());
        
        try {
            InferenceService service = serviceManager.registerService(registration);
            return ResponseEntity.status(HttpStatus.CREATED).body(service);
            
        } catch (Exception e) {
            log.error("服务注册失败", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 注销推理服务
     */
    @DeleteMapping("/{serviceId}")
    public ResponseEntity<Void> unregisterService(@PathVariable String serviceId) {
        log.info("收到服务注销请求: serviceId={}", serviceId);
        
        try {
            serviceManager.unregisterService(serviceId);
            return ResponseEntity.ok().build();
            
        } catch (NotFoundException e) {
            log.warn("服务不存在: serviceId={}", serviceId);
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("服务注销失败: serviceId={}", serviceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 更新服务状态
     */
    @PutMapping("/{serviceId}/status")
    public ResponseEntity<Void> updateServiceStatus(
            @PathVariable String serviceId,
            @RequestParam ServiceStatus status) {
        
        log.info("收到服务状态更新请求: serviceId={}, status={}", serviceId, status);
        
        try {
            serviceManager.updateServiceStatus(serviceId, status);
            return ResponseEntity.ok().build();
            
        } catch (NotFoundException e) {
            log.warn("服务不存在: serviceId={}", serviceId);
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("服务状态更新失败: serviceId={}, status={}", serviceId, status, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 更新服务配额
     */
    @PutMapping("/{serviceId}/quota")
    public ResponseEntity<Void> updateServiceQuota(
            @PathVariable String serviceId,
            @RequestParam Integer maxQuota) {
        
        log.info("收到服务配额更新请求: serviceId={}, maxQuota={}", serviceId, maxQuota);
        
        try {
            serviceManager.updateServiceQuota(serviceId, maxQuota);
            return ResponseEntity.ok().build();
            
        } catch (NotFoundException e) {
            log.warn("服务不存在: serviceId={}", serviceId);
            return ResponseEntity.notFound().build();
            
        } catch (IllegalArgumentException e) {
            log.warn("配额更新参数错误: serviceId={}, maxQuota={}, error={}", 
                    serviceId, maxQuota, e.getMessage());
            return ResponseEntity.badRequest().build();
            
        } catch (Exception e) {
            log.error("服务配额更新失败: serviceId={}, maxQuota={}", serviceId, maxQuota, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 获取服务详细信息
     */
    @GetMapping("/{serviceId}")
    public ResponseEntity<InferenceService> getService(@PathVariable String serviceId) {
        try {
            InferenceService service = serviceManager.getService(serviceId);
            return ResponseEntity.ok(service);
            
        } catch (NotFoundException e) {
            log.warn("服务不存在: serviceId={}", serviceId);
            return ResponseEntity.notFound().build();
            
        } catch (Exception e) {
            log.error("获取服务信息失败: serviceId={}", serviceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
    
    /**
     * 服务心跳接口
     */
    @PostMapping("/{serviceId}/heartbeat")
    public ResponseEntity<Void> serviceHeartbeat(@PathVariable String serviceId) {
        log.debug("收到服务心跳: serviceId={}", serviceId);

        try {
            serviceManager.updateServiceHeartbeat(serviceId);
            return ResponseEntity.ok().build();

        } catch (NotFoundException e) {
            log.warn("服务不存在: serviceId={}", serviceId);
            return ResponseEntity.notFound().build();

        } catch (Exception e) {
            log.error("更新服务心跳失败: serviceId={}", serviceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 手动健康检查
     */
    @PostMapping("/{serviceId}/health-check")
    public ResponseEntity<HealthResponse> manualHealthCheck(@PathVariable String serviceId) {
        log.info("收到手动健康检查请求: serviceId={}", serviceId);

        try {
            HealthResponse response = healthCheckService.manualHealthCheck(serviceId);
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("手动健康检查失败: serviceId={}", serviceId, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(HealthResponse.builder()
                    .status("ERROR")
                    .timestamp(System.currentTimeMillis())
                    .build());
        }
    }
}
