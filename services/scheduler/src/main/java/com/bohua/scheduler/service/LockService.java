package com.bohua.scheduler.service;

/**
 * 锁服务接口
 * 支持单例模式（内存锁）和分布式模式（Redis锁）
 */
public interface LockService {
    
    /**
     * 尝试获取锁
     * 
     * @param lockKey 锁的键
     * @param timeoutSeconds 锁超时时间（秒）
     * @return 是否成功获取锁
     */
    boolean tryLock(String lockKey, long timeoutSeconds);
    
    /**
     * 释放锁
     * 
     * @param lockKey 锁的键
     */
    void unlock(String lockKey);
    
    /**
     * 执行带锁的操作
     * 
     * @param lockKey 锁的键
     * @param timeoutSeconds 锁超时时间（秒）
     * @param action 要执行的操作
     * @param <T> 返回值类型
     * @return 操作结果
     * @throws Exception 如果获取锁失败或操作执行失败
     */
    <T> T executeWithLock(String lockKey, long timeoutSeconds, LockAction<T> action) throws Exception;
    
    /**
     * 锁操作接口
     */
    @FunctionalInterface
    interface LockAction<T> {
        T execute() throws Exception;
    }
}
