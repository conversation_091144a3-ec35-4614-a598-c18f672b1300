package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 训练配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TrainingConfig {
    
    /** 标签列表 */
    private List<String> labels;
    
    /** 数据收集配置 */
    private DataCollectionConfig dataCollection;
    
    /** 模型版本 */
    private String modelVersion;
}
