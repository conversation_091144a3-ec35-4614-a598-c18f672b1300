package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 执行指标
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExecutionMetrics {
    
    private Long processingTime;
    private Integer frameCount;
    private Double avgFps;
    private Map<String, Object> algorithmMetrics;
    private Long memoryUsage;
    private Double cpuUsage;
    private Double gpuUsage;
}
