package com.bohua.scheduler.service;

import com.bohua.scheduler.dto.ServiceRegistration;
import com.bohua.scheduler.exception.NotFoundException;
import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * 服务管理器
 */
@Service
@Slf4j
public class ServiceManager {
    
    @Autowired
    private InferenceServiceRepository serviceRepository;
    
    /**
     * 注册推理服务
     * 如果相同serviceName和baseUrl的服务已存在，则更新其状态为ACTIVE
     */
    public InferenceService registerService(ServiceRegistration registration) {
        log.info("注册推理服务: serviceName={}, baseUrl={}, gpuType={}",
                registration.getServiceName(), registration.getBaseUrl(), registration.getGpuType());

        // 检查是否已存在相同的服务
        Optional<InferenceService> existingService = serviceRepository
            .findByServiceNameAndBaseUrl(registration.getServiceName(), registration.getBaseUrl());

        if (existingService.isPresent()) {
            InferenceService service = existingService.get();
            log.info("发现已存在的服务，更新状态: serviceId={}, oldStatus={}",
                    service.getServiceId(), service.getStatus());

            // 更新服务状态为ACTIVE，并更新其他可能变化的字段
            service.setStatus(ServiceStatus.ACTIVE);
            service.setGpuType(registration.getGpuType());
            service.setMaxQuota(registration.getCustomQuota() != null ?
                               registration.getCustomQuota() :
                               registration.getGpuType().getDefaultQuota());
            service.setRegion(registration.getRegion());
            service.setTags(registration.getTags());
            service.setMetadata(registration.getMetadata());
            service.setLastHeartbeat(LocalDateTime.now());

            InferenceService savedService = serviceRepository.save(service);
            log.info("推理服务状态更新成功: serviceId={}", savedService.getServiceId());
            return savedService;
        }

        // 创建新服务
        InferenceService service = InferenceService.builder()
            .serviceId(generateServiceId())
            .serviceName(registration.getServiceName())
            .baseUrl(registration.getBaseUrl())
            .maxQuota(registration.getCustomQuota() != null ?
                     registration.getCustomQuota() :
                     registration.getGpuType().getDefaultQuota())
            .currentQuota(0)
            .status(ServiceStatus.ACTIVE)
            .gpuType(registration.getGpuType())
            .region(registration.getRegion())
            .tags(registration.getTags())
            .metadata(registration.getMetadata())
            .lastHeartbeat(LocalDateTime.now())
            .build();

        InferenceService savedService = serviceRepository.save(service);
        log.info("推理服务注册成功: serviceId={}", savedService.getServiceId());

        return savedService;
    }
    
    /**
     * 注销推理服务
     */
    public void unregisterService(String serviceId) {
        log.info("注销推理服务: serviceId={}", serviceId);
        
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new NotFoundException("服务不存在"));
        
        // 检查是否有活跃任务
        if (service.getCurrentQuota() > 0) {
            log.warn("服务仍有活跃任务，设置为维护状态: serviceId={}, currentQuota={}", 
                    serviceId, service.getCurrentQuota());
            updateServiceStatus(serviceId, ServiceStatus.MAINTENANCE);
        } else {
            serviceRepository.delete(service);
            log.info("推理服务注销成功: serviceId={}", serviceId);
        }
    }
    

    
    /**
     * 更新服务配额
     */
    public void updateServiceQuota(String serviceId, Integer maxQuota) {
        log.info("更新服务配额: serviceId={}, maxQuota={}", serviceId, maxQuota);
        
        InferenceService service = serviceRepository.findById(serviceId)
            .orElseThrow(() -> new NotFoundException("服务不存在"));
        
        if (maxQuota < service.getCurrentQuota()) {
            throw new IllegalArgumentException("新配额不能小于当前使用量");
        }
        
        service.setMaxQuota(maxQuota);
        serviceRepository.save(service);
        
        log.info("服务配额更新成功: serviceId={}, maxQuota={}", serviceId, maxQuota);
    }
    
    /**
     * 获取服务信息
     */
    public InferenceService getService(String serviceId) {
        return serviceRepository.findById(serviceId)
            .orElseThrow(() -> new NotFoundException("服务不存在"));
    }
    
    /**
     * 更新服务状态
     */
    public void updateServiceStatus(String serviceId, ServiceStatus status) {
        log.info("更新服务状态: serviceId={}, newStatus={}", serviceId, status);

        Optional<InferenceService> serviceOpt = serviceRepository.findById(serviceId);
        if (serviceOpt.isPresent()) {
            InferenceService service = serviceOpt.get();
            ServiceStatus oldStatus = service.getStatus();
            service.setStatus(status);
            serviceRepository.save(service);

            log.info("服务状态更新成功: serviceId={}, oldStatus={}, newStatus={}",
                    serviceId, oldStatus, status);
        } else {
            log.warn("尝试更新不存在的服务状态: serviceId={}", serviceId);
        }
    }

    /**
     * 更新服务心跳时间
     */
    public void updateServiceHeartbeat(String serviceId) {
        Optional<InferenceService> serviceOpt = serviceRepository.findById(serviceId);
        if (serviceOpt.isPresent()) {
            InferenceService service = serviceOpt.get();
            service.setLastHeartbeat(LocalDateTime.now());
            serviceRepository.save(service);
            log.debug("更新服务心跳: serviceId={}", serviceId);
        }
    }

    /**
     * 生成服务ID
     */
    private String generateServiceId() {
        return "service_" + UUID.randomUUID().toString().replace("-", "").substring(0, 8);
    }
}
