package com.bohua.scheduler.repository;

import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.enums.GpuType;
import com.bohua.scheduler.model.enums.ServiceStatus;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 推理服务Repository
 */
@Repository
public interface InferenceServiceRepository extends MongoRepository<InferenceService, String> {
    
    /**
     * 根据状态查询服务
     */
    List<InferenceService> findByStatus(ServiceStatus status);
    
    /**
     * 根据区域查询服务
     */
    List<InferenceService> findByRegion(String region);
    
    /**
     * 根据GPU类型查询服务
     */
    List<InferenceService> findByGpuType(GpuType gpuType);
    


    /**
     * 根据服务名和基础URL查询服务（用于检查重复注册）
     */
    Optional<InferenceService> findByServiceNameAndBaseUrl(String serviceName, String baseUrl);

    /**
     * 根据区域和状态查询服务
     */
    @Query("{'region': ?0, 'status': ?1}")
    List<InferenceService> findByRegionAndStatus(String region, ServiceStatus status);

    /**
     * 查询心跳超时的服务
     */
    @Query("{'lastHeartbeat': {$lt: ?0}, 'status': {$in: ['ACTIVE', 'MAINTENANCE']}}")
    List<InferenceService> findServicesWithExpiredHeartbeat(LocalDateTime expiredTime);
}
