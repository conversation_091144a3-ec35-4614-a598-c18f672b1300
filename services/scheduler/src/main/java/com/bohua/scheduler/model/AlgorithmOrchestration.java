package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 算法编排配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AlgorithmOrchestration {

    /** 编排ID */
    private String orchestrationId;

    /** 编排类型 */
    private String orchestrationType;

    /** 算法链 */
    private List<Algorithm> algorithmChain;
}
