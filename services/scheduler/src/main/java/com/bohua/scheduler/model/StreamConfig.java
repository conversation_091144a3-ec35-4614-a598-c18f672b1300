package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 流配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StreamConfig {
    
    /** 分辨率 */
    private String resolution;
    
    /** 帧率 */
    private Integer frameRate;
    
    /** 协议类型 */
    private String protocol;
    
    /** 流地址 */
    private String url;
    
    /** 解码器配置 */
    private DecoderConf decoderConf;
}
