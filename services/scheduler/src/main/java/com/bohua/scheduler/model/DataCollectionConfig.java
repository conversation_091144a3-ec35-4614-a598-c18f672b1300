package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 数据收集配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DataCollectionConfig {
    
    /** 是否启用数据收集 */
    private Boolean enabled;
    
    /** 置信度阈值配置 */
    private ConfidenceThreshold thresholds;
    
    /** 采样率 */
    private Double samplingRate;
    
    /** 每日最大样本数 */
    private Integer maxSamplesPerDay;
}
