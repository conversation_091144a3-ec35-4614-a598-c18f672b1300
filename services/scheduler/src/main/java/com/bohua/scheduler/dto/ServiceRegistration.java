package com.bohua.scheduler.dto;

import com.bohua.scheduler.model.enums.GpuType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 服务注册请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ServiceRegistration {
    
    @NotBlank(message = "服务名称不能为空")
    private String serviceName;
    
    @NotBlank(message = "服务地址不能为空")
    private String baseUrl;
    
    @NotNull(message = "GPU类型不能为空")
    private GpuType gpuType;
    
    private String region;
    
    private Integer customQuota;
    
    private Map<String, String> tags;
    
    private Map<String, Object> metadata;
}
