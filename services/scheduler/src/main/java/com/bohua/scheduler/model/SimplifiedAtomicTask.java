package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 简化原子任务模型
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SimplifiedAtomicTask {
    
    /** 任务ID */
    private String taskId;
    
    /** 任务名称 */
    private String taskName;
    
    /** 任务描述 */
    private String taskDescription;
    
    /** 任务元信息 */
    private TaskMeta taskMeta;
    
    /** 算法编排 */
    private AlgorithmOrchestration algorithmOrchestration;
    
    /** 设备信息 */
    private Device device;
}
