package com.bohua.scheduler.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 规则配置
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RuleConfig {
    
    /** 规则类型 */
    private String ruleType;
    
    /** 多边形区域配置 */
    private List<Polygon> polygons;
    
    /** 线段配置 */
    private List<Line> lines;
}
