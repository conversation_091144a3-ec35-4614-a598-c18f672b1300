package com.bohua.scheduler.dto;

import com.bohua.scheduler.model.AlgorithmOrchestration;
import com.bohua.scheduler.model.Device;
import com.bohua.scheduler.model.TaskMeta;
import com.bohua.scheduler.model.enums.TaskStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务信息DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfo {
    
    private String taskId;
    private String taskName;
    private String taskDescription;
    private TaskStatus status;
    private String serviceId;
    private String serviceUrl;
    private String region;
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    private String deviceId;
    private TaskMeta taskMeta;
    private AlgorithmOrchestration algorithmOrchestration;
    private Device device;
}
