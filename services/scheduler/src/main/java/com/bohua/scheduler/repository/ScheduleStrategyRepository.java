package com.bohua.scheduler.repository;

import com.bohua.scheduler.model.ScheduleStrategy;
import com.bohua.scheduler.model.enums.ScheduleMode;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 调度策略Repository
 */
@Repository
public interface ScheduleStrategyRepository extends MongoRepository<ScheduleStrategy, String> {
    
    /**
     * 根据调度模式查询策略
     */
    Optional<ScheduleStrategy> findByScheduleMode(ScheduleMode scheduleMode);
    
    /**
     * 查询启用动态调度的策略
     */
    @Query("{'enableDynamicSchedule': true}")
    List<ScheduleStrategy> findDynamicStrategies();
    
    /**
     * 查询默认策略
     */
    @Query("{'strategyId': 'default_strategy'}")
    Optional<ScheduleStrategy> findDefaultStrategy();
}
