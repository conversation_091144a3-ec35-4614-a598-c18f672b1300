package com.bohua.scheduler.service;

import com.bohua.scheduler.model.InferenceService;
import com.bohua.scheduler.model.SimplifiedAtomicTask;
import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.ServiceStatus;
import com.bohua.scheduler.model.enums.TaskStatus;
import com.bohua.scheduler.repository.InferenceServiceRepository;
import com.bohua.scheduler.repository.TaskAllocationRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * 失败任务重试功能测试
 */
@ExtendWith(MockitoExtension.class)
class RetryFailedTasksTest {

    @Mock
    private TaskAllocationRepository allocationRepository;

    @Mock
    private InferenceServiceRepository serviceRepository;

    @Mock
    private RestTemplate restTemplate;

    @InjectMocks
    private SchedulerService schedulerService;

    private TaskAllocation failedTask;
    private InferenceService availableService;

    @BeforeEach
    void setUp() {
        // 创建测试用的失败任务
        SimplifiedAtomicTask taskRequest = SimplifiedAtomicTask.builder()
                .taskId("test-task-001")
                .taskName("测试任务")
                .build();

        failedTask = TaskAllocation.builder()
                .allocationId("alloc-001")
                .taskId("test-task-001")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ERROR)
                .errorMessage("初始失败")
                .retryCount(0)
                .taskRequest(taskRequest)
                .build();

        // 创建可用的推理服务
        availableService = InferenceService.builder()
                .serviceId("service-001")
                .serviceName("测试服务")
                .baseUrl("http://localhost:8081")
                .status(ServiceStatus.ACTIVE)
                .currentQuota(0)
                .maxQuota(10)
                .build();
    }

    @Test
    void testRetryFailedTasks_Success() {
        // 准备测试数据
        when(allocationRepository.findByTaskStatus(TaskStatus.ERROR))
                .thenReturn(Arrays.asList(failedTask));
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));

        // 模拟任务发送成功
        when(restTemplate.exchange(any(), any(), any(), eq(String.class)))
                .thenReturn(null); // 简化测试，实际应该返回成功响应

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证任务状态更新
        verify(allocationRepository, times(1)).save(argThat(task -> 
                task.getTaskStatus() == TaskStatus.RUNNING &&
                task.getRetryCount() == 1 &&
                task.getErrorMessage() == null
        ));
    }

    @Test
    void testRetryFailedTasks_MaxRetriesReached() {
        // 设置任务已达到最大重试次数
        failedTask.setRetryCount(3);

        when(allocationRepository.findByTaskStatus(TaskStatus.ERROR))
                .thenReturn(Arrays.asList(failedTask));

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证不会尝试重试
        verify(serviceRepository, never()).findById(any());
        verify(restTemplate, never()).exchange(any(), any(), any(), any());
        verify(allocationRepository, never()).save(any());
    }

    @Test
    void testRetryFailedTasks_ServiceUnavailable() {
        // 设置服务不可用
        availableService.setStatus(ServiceStatus.INACTIVE);

        when(allocationRepository.findByTaskStatus(TaskStatus.ERROR))
                .thenReturn(Arrays.asList(failedTask));
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证不会尝试发送任务
        verify(restTemplate, never()).exchange(any(), any(), any(), any());
        verify(allocationRepository, never()).save(any());
    }

    @Test
    void testRetryFailedTasks_RetryFailed() {
        when(allocationRepository.findByTaskStatus(TaskStatus.ERROR))
                .thenReturn(Arrays.asList(failedTask));
        when(serviceRepository.findById("service-001"))
                .thenReturn(Optional.of(availableService));

        // 模拟任务发送失败
        when(restTemplate.exchange(any(), any(), any(), eq(String.class)))
                .thenThrow(new RuntimeException("网络错误"));

        // 执行重试
        schedulerService.retryFailedTasks();

        // 验证重试次数增加，错误信息更新
        verify(allocationRepository, times(1)).save(argThat(task -> 
                task.getTaskStatus() == TaskStatus.ERROR &&
                task.getRetryCount() == 1 &&
                task.getErrorMessage().contains("重试失败")
        ));
    }

    @Test
    void testTaskAllocation_CanRetry() {
        TaskAllocation task = new TaskAllocation();
        
        // 测试初始状态
        task.setRetryCount(0);
        assert task.canRetry(3) == true;
        
        // 测试未达到最大重试次数
        task.setRetryCount(2);
        assert task.canRetry(3) == true;
        
        // 测试达到最大重试次数
        task.setRetryCount(3);
        assert task.canRetry(3) == false;
        
        // 测试超过最大重试次数
        task.setRetryCount(5);
        assert task.canRetry(3) == false;
    }

    @Test
    void testTaskAllocation_IncrementRetryCount() {
        TaskAllocation task = new TaskAllocation();
        LocalDateTime beforeIncrement = LocalDateTime.now();
        
        // 测试从null开始
        task.incrementRetryCount();
        assert task.getRetryCount() == 1;
        assert task.getLastRetryTime() != null;
        assert task.getLastRetryTime().isAfter(beforeIncrement) || 
               task.getLastRetryTime().isEqual(beforeIncrement);
        
        // 测试递增
        task.incrementRetryCount();
        assert task.getRetryCount() == 2;
        
        task.incrementRetryCount();
        assert task.getRetryCount() == 3;
    }
}
