package com.bohua.scheduler.model;

import com.fasterxml.jackson.databind.ObjectMapper;

import java.util.Arrays;

/**
 * 简单的测试运行器
 */
public class TestRunner {

    public static void main(String[] args) {
        System.out.println("开始测试简化原子任务模型...");
        
        try {
            testSimplifiedAtomicTaskSerialization();
            System.out.println("✅ 所有测试通过！");
        } catch (Exception e) {
            System.err.println("❌ 测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public static void testSimplifiedAtomicTaskSerialization() throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();

        // 创建告警配置
        AlertConfig alertConfig = AlertConfig.builder()
                .labels(Arrays.asList("no_helmet"))
                .confidence(0.8)
                .positiveLabels(Arrays.asList("person", "helmet"))
                .negativeLabels(Arrays.asList("background", "no_helmet"))
                .build();

        // 创建数据收集配置
        ConfidenceThreshold thresholds = ConfidenceThreshold.builder()
                .minConfidence(0.1)
                .maxConfidence(0.9)
                .build();

        DataCollectionConfig dataCollection = DataCollectionConfig.builder()
                .enabled(true)
                .thresholds(thresholds)
                .samplingRate(0.1)
                .maxSamplesPerDay(1000)
                .build();

        // 创建训练配置
        TrainingConfig trainingConfig = TrainingConfig.builder()
                .labels(Arrays.asList("helmet", "no_helmet"))
                .dataCollection(dataCollection)
                .modelVersion("v1.0")
                .build();

        // 创建算法
        Algorithm algorithm = Algorithm.builder()
                .algorithmId("helmet_detection")
                .algorithmName("安全帽检测")
                .algorithmType("CLASSIFICATION")
                .order(3)
                .required(true)
                .dependsOn(Arrays.asList("person_tracking"))
                .alertConfig(alertConfig)
                .trainingConfig(trainingConfig)
                .build();

        // 创建算法编排
        AlgorithmOrchestration orchestration = AlgorithmOrchestration.builder()
                .orchestrationId("orch_001")
                .orchestrationType("YOLO_TRACKING_CLIP")
                .algorithmChain(Arrays.asList(algorithm))
                .build();

        // 创建解码器配置
        DecoderConf decoderConf = DecoderConf.builder()
                .keyFrameOnly(false)
                .decodeStep(4)
                .build();

        // 创建流配置
        StreamConfig streamConfig = StreamConfig.builder()
                .resolution("1920x1080")
                .frameRate(25)
                .protocol("RTSP")
                .url("rtsp://*************:554/stream")
                .decoderConf(decoderConf)
                .build();

        // 创建设备
        Device device = Device.builder()
                .deviceId("camera_001")
                .deviceName("工地入口摄像头")
                .streamConfig(streamConfig)
                .build();

        // 创建任务元信息
        TaskMeta taskMeta = TaskMeta.builder()
                .enabled(true)
                .taskLevel("HIGH")
                .protocol("VIDEO")
                .eventTypeId("event_type_uuid_001")
                .eventAction(Arrays.asList("ALERT"))
                .build();

        // 创建简化原子任务
        SimplifiedAtomicTask task = SimplifiedAtomicTask.builder()
                .taskId("task_001")
                .taskName("人员检测任务")
                .taskDescription("检测未戴安全帽的人员")
                .taskMeta(taskMeta)
                .algorithmOrchestration(orchestration)
                .device(device)
                .build();

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(task);
        System.out.println("序列化的JSON:");
        System.out.println(json);

        // 反序列化
        SimplifiedAtomicTask deserializedTask = objectMapper.readValue(json, SimplifiedAtomicTask.class);

        // 验证
        assert "task_001".equals(deserializedTask.getTaskId()) : "任务ID不匹配";
        assert "人员检测任务".equals(deserializedTask.getTaskName()) : "任务名称不匹配";
        assert "检测未戴安全帽的人员".equals(deserializedTask.getTaskDescription()) : "任务描述不匹配";
        assert "HIGH".equals(deserializedTask.getTaskMeta().getTaskLevel()) : "任务级别不匹配";
        assert "event_type_uuid_001".equals(deserializedTask.getTaskMeta().getEventTypeId()) : "事件类型ID不匹配";
        assert "camera_001".equals(deserializedTask.getDevice().getDeviceId()) : "设备ID不匹配";
        assert "orch_001".equals(deserializedTask.getAlgorithmOrchestration().getOrchestrationId()) : "编排ID不匹配";

        // 验证算法配置
        Algorithm deserializedAlgorithm = deserializedTask.getAlgorithmOrchestration().getAlgorithmChain().get(0);
        assert "helmet_detection".equals(deserializedAlgorithm.getAlgorithmId()) : "算法ID不匹配";
        assert "安全帽检测".equals(deserializedAlgorithm.getAlgorithmName()) : "算法名称不匹配";
        assert "CLASSIFICATION".equals(deserializedAlgorithm.getAlgorithmType()) : "算法类型不匹配";
        assert deserializedAlgorithm.getAlertConfig() != null : "告警配置为空";
        assert deserializedAlgorithm.getTrainingConfig() != null : "训练配置为空";

        // 验证告警配置
        AlertConfig deserializedAlertConfig = deserializedAlgorithm.getAlertConfig();
        assert deserializedAlertConfig.getLabels().size() == 1 : "告警标签数量不匹配";
        assert "no_helmet".equals(deserializedAlertConfig.getLabels().get(0)) : "告警标签不匹配";
        assert Math.abs(deserializedAlertConfig.getConfidence() - 0.8) < 0.001 : "告警置信度不匹配";

        // 验证正例和负例标签
        assert deserializedAlertConfig.getPositiveLabels() != null : "正例标签为空";
        assert deserializedAlertConfig.getPositiveLabels().size() == 2 : "正例标签数量不匹配";
        assert deserializedAlertConfig.getPositiveLabels().contains("person") : "正例标签不包含person";
        assert deserializedAlertConfig.getPositiveLabels().contains("helmet") : "正例标签不包含helmet";

        assert deserializedAlertConfig.getNegativeLabels() != null : "负例标签为空";
        assert deserializedAlertConfig.getNegativeLabels().size() == 2 : "负例标签数量不匹配";
        assert deserializedAlertConfig.getNegativeLabels().contains("background") : "负例标签不包含background";
        assert deserializedAlertConfig.getNegativeLabels().contains("no_helmet") : "负例标签不包含no_helmet";

        // 验证训练配置
        TrainingConfig deserializedTrainingConfig = deserializedAlgorithm.getTrainingConfig();
        assert deserializedTrainingConfig.getLabels().size() == 2 : "训练标签数量不匹配";
        assert deserializedTrainingConfig.getLabels().contains("helmet") : "训练标签不包含helmet";
        assert deserializedTrainingConfig.getLabels().contains("no_helmet") : "训练标签不包含no_helmet";
        assert "v1.0".equals(deserializedTrainingConfig.getModelVersion()) : "模型版本不匹配";

        System.out.println("✅ SimplifiedAtomicTask 序列化/反序列化测试通过");
    }
}
