package com.bohua.scheduler.service;

import com.bohua.scheduler.dto.TaskInfo;
import com.bohua.scheduler.model.TaskAllocation;
import com.bohua.scheduler.model.enums.TaskStatus;
import com.bohua.scheduler.repository.TaskAllocationRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * SchedulerService.queryTasks 方法优化测试
 * 重点测试批量查询优化，避免循环查询数据库
 */
@RunWith(MockitoJUnitRunner.class)
public class SchedulerServiceQueryTasksTest {

    @Mock
    private TaskAllocationRepository allocationRepository;

    @InjectMocks
    private SchedulerService schedulerService;

    private TaskAllocation testAllocation1;
    private TaskAllocation testAllocation2;
    private TaskAllocation testAllocation3;

    @Before
    public void setUp() {
        // 创建测试用的任务分配
        testAllocation1 = TaskAllocation.builder()
                .allocationId("alloc-001")
                .taskId("task-001")
                .deviceId("device-001")
                .serviceId("service-001")
                .taskStatus(TaskStatus.RUNNING)
                .allocateTime(LocalDateTime.now().minusMinutes(10))
                .startTime(LocalDateTime.now().minusMinutes(9))
                .build();

        testAllocation2 = TaskAllocation.builder()
                .allocationId("alloc-002")
                .taskId("task-002")
                .deviceId("device-002")
                .serviceId("service-001")
                .taskStatus(TaskStatus.ALLOCATED)
                .allocateTime(LocalDateTime.now().minusMinutes(5))
                .build();

        testAllocation3 = TaskAllocation.builder()
                .allocationId("alloc-003")
                .taskId("task-003")
                .deviceId("device-003")
                .serviceId("service-002")
                .taskStatus(TaskStatus.RUNNING)
                .allocateTime(LocalDateTime.now().minusMinutes(3))
                .startTime(LocalDateTime.now().minusMinutes(2))
                .build();
    }

    @Test
    public void testQueryTasks_BatchQueryOptimization() {
        // 准备测试数据 - 查询多个指定任务ID
        List<String> taskIds = Arrays.asList("task-001", "task-002", "task-003");
        List<TaskAllocation> expectedAllocations = Arrays.asList(testAllocation1, testAllocation2);

        // 模拟批量查询返回结果（task-003 不在活跃状态，所以不返回）
        when(allocationRepository.findActiveTasksByTaskIdIn(taskIds))
                .thenReturn(expectedAllocations);

        // 执行测试
        List<TaskInfo> result = schedulerService.queryTasks(taskIds);

        // 验证结果
        assertEquals("应该返回2个任务信息", 2, result.size());
        
        // 验证使用了批量查询方法，而不是循环查询 - 这是关键的优化点
        verify(allocationRepository, times(1)).findActiveTasksByTaskIdIn(taskIds);
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());

        // 验证返回的任务信息正确
        assertEquals("task-001", result.get(0).getTaskId());
        assertEquals("task-002", result.get(1).getTaskId());
        assertEquals(TaskStatus.RUNNING.toString(), result.get(0).getStatus());
        assertEquals(TaskStatus.ALLOCATED.toString(), result.get(1).getStatus());
    }

    @Test
    public void testQueryTasks_EmptyTaskIdsList() {
        // 准备测试数据 - 查询所有活跃任务
        List<TaskAllocation> allActiveAllocations = Arrays.asList(testAllocation1, testAllocation2, testAllocation3);
        
        when(allocationRepository.findByTaskStatusIn(
                Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING)))
                .thenReturn(allActiveAllocations);

        // 执行测试 - 传入空列表
        List<TaskInfo> result = schedulerService.queryTasks(Collections.emptyList());

        // 验证结果
        assertEquals("应该返回所有活跃任务", 3, result.size());
        
        // 验证使用了查询所有活跃任务的方法
        verify(allocationRepository, times(1)).findByTaskStatusIn(
                Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));
        verify(allocationRepository, never()).findActiveTasksByTaskIdIn(anyList());
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
    }

    @Test
    public void testQueryTasks_NullTaskIdsList() {
        // 准备测试数据 - 查询所有活跃任务
        List<TaskAllocation> allActiveAllocations = Arrays.asList(testAllocation1, testAllocation2, testAllocation3);
        
        when(allocationRepository.findByTaskStatusIn(
                Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING)))
                .thenReturn(allActiveAllocations);

        // 执行测试 - 传入null
        List<TaskInfo> result = schedulerService.queryTasks(null);

        // 验证结果
        assertEquals("应该返回所有活跃任务", 3, result.size());
        
        // 验证使用了查询所有活跃任务的方法
        verify(allocationRepository, times(1)).findByTaskStatusIn(
                Arrays.asList(TaskStatus.ALLOCATED, TaskStatus.RUNNING));
        verify(allocationRepository, never()).findActiveTasksByTaskIdIn(anyList());
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
    }

    @Test
    public void testQueryTasks_SingleTaskId() {
        // 准备测试数据 - 查询单个任务ID
        List<String> taskIds = Arrays.asList("task-001");
        List<TaskAllocation> expectedAllocations = Arrays.asList(testAllocation1);

        when(allocationRepository.findActiveTasksByTaskIdIn(taskIds))
                .thenReturn(expectedAllocations);

        // 执行测试
        List<TaskInfo> result = schedulerService.queryTasks(taskIds);

        // 验证结果
        assertEquals("应该返回1个任务信息", 1, result.size());
        assertEquals("task-001", result.get(0).getTaskId());
        
        // 验证仍然使用批量查询方法（即使只有一个ID）
        verify(allocationRepository, times(1)).findActiveTasksByTaskIdIn(taskIds);
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
    }

    @Test
    public void testQueryTasks_NoActiveTasksFound() {
        // 准备测试数据 - 指定的任务ID都没有活跃状态
        List<String> taskIds = Arrays.asList("task-004", "task-005");
        
        when(allocationRepository.findActiveTasksByTaskIdIn(taskIds))
                .thenReturn(Collections.emptyList());

        // 执行测试
        List<TaskInfo> result = schedulerService.queryTasks(taskIds);

        // 验证结果
        assertEquals("应该返回空列表", 0, result.size());
        
        // 验证使用了批量查询方法
        verify(allocationRepository, times(1)).findActiveTasksByTaskIdIn(taskIds);
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
    }

    @Test
    public void testQueryTasks_LargeTaskIdsList() {
        // 准备测试数据 - 大量任务ID列表，模拟实际场景
        List<String> taskIds = Arrays.asList(
                "task-001", "task-002", "task-003", "task-004", "task-005",
                "task-006", "task-007", "task-008", "task-009", "task-010"
        );
        List<TaskAllocation> expectedAllocations = Arrays.asList(testAllocation1, testAllocation2);

        when(allocationRepository.findActiveTasksByTaskIdIn(taskIds))
                .thenReturn(expectedAllocations);

        // 执行测试
        List<TaskInfo> result = schedulerService.queryTasks(taskIds);

        // 验证结果
        assertEquals("应该返回2个任务信息", 2, result.size());
        
        // 关键验证：即使有10个任务ID，也只调用一次数据库查询
        verify(allocationRepository, times(1)).findActiveTasksByTaskIdIn(taskIds);
        verify(allocationRepository, never()).findActiveTasksByTaskId(anyString());
        
        // 如果没有优化，这里会调用10次 findActiveTasksByTaskId
        // 优化后只调用1次 findActiveTasksByTaskIdIn
    }

    /**
     * 性能对比测试说明：
     * 
     * 优化前（循环查询）：
     * - 对于N个任务ID，会执行N次数据库查询
     * - 每次查询: SELECT * FROM task_allocation WHERE taskId = ? AND taskStatus IN ('ALLOCATED', 'RUNNING')
     * - 总查询次数: N
     * 
     * 优化后（批量查询）：
     * - 对于N个任务ID，只执行1次数据库查询
     * - 查询: SELECT * FROM task_allocation WHERE taskId IN (?, ?, ..., ?) AND taskStatus IN ('ALLOCATED', 'RUNNING')
     * - 总查询次数: 1
     * 
     * 性能提升：
     * - 减少了数据库连接开销
     * - 减少了网络往返次数
     * - 提高了查询效率，特别是在任务ID数量较多时
     */
}
