package com.bohua.scheduler.model;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * 简化原子任务模型测试
 */
public class SimplifiedAtomicTaskTest {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void testSimplifiedAtomicTaskSerialization() throws Exception {
        // 创建告警配置
        AlertConfig alertConfig = AlertConfig.builder()
                .labels(Arrays.asList("no_helmet"))
                .confidence(0.8)
                .positiveLabels(Arrays.asList("person", "helmet"))
                .negativeLabels(Arrays.asList("background", "no_helmet"))
                .build();

        // 创建数据收集配置
        ConfidenceThreshold thresholds = ConfidenceThreshold.builder()
                .minConfidence(0.1)
                .maxConfidence(0.9)
                .build();

        DataCollectionConfig dataCollection = DataCollectionConfig.builder()
                .enabled(true)
                .thresholds(thresholds)
                .samplingRate(0.1)
                .maxSamplesPerDay(1000)
                .build();

        // 创建训练配置
        TrainingConfig trainingConfig = TrainingConfig.builder()
                .labels(Arrays.asList("helmet", "no_helmet"))
                .dataCollection(dataCollection)
                .modelVersion("v1.0")
                .build();

        // 创建算法
        Algorithm algorithm = Algorithm.builder()
                .algorithmId("helmet_detection")
                .algorithmName("安全帽检测")
                .algorithmType("CLASSIFICATION")
                .order(3)
                .required(true)
                .dependsOn(Arrays.asList("person_tracking"))
                .alertConfig(alertConfig)
                .trainingConfig(trainingConfig)
                .build();

        // 创建算法编排
        AlgorithmOrchestration orchestration = AlgorithmOrchestration.builder()
                .orchestrationId("orch_001")
                .orchestrationType("YOLO_TRACKING_CLIP")
                .algorithmChain(Arrays.asList(algorithm))
                .build();

        // 创建解码器配置
        DecoderConf decoderConf = DecoderConf.builder()
                .keyFrameOnly(false)
                .decodeStep(4)
                .build();

        // 创建流配置
        StreamConfig streamConfig = StreamConfig.builder()
                .resolution("1920x1080")
                .frameRate(25)
                .protocol("RTSP")
                .url("rtsp://*************:554/stream")
                .decoderConf(decoderConf)
                .build();

        // 创建设备
        Device device = Device.builder()
                .deviceId("camera_001")
                .deviceName("工地入口摄像头")
                .streamConfig(streamConfig)
                .build();

        // 创建任务元信息
        TaskMeta taskMeta = TaskMeta.builder()
                .enabled(true)
                .taskLevel("HIGH")
                .protocol("VIDEO")
                .eventTypeId("event_type_uuid_001")
                .eventAction(Arrays.asList("ALERT"))
                .build();

        // 创建简化原子任务
        SimplifiedAtomicTask task = SimplifiedAtomicTask.builder()
                .taskId("task_001")
                .taskName("人员检测任务")
                .taskDescription("检测未戴安全帽的人员")
                .taskMeta(taskMeta)
                .algorithmOrchestration(orchestration)
                .device(device)
                .build();

        // 序列化为JSON
        String json = objectMapper.writeValueAsString(task);
        System.out.println("Serialized JSON:");
        System.out.println(json);

        // 反序列化
        SimplifiedAtomicTask deserializedTask = objectMapper.readValue(json, SimplifiedAtomicTask.class);

        // 验证
        assertEquals("task_001", deserializedTask.getTaskId());
        assertEquals("人员检测任务", deserializedTask.getTaskName());
        assertEquals("检测未戴安全帽的人员", deserializedTask.getTaskDescription());
        assertEquals("HIGH", deserializedTask.getTaskMeta().getTaskLevel());
        assertEquals("event_type_uuid_001", deserializedTask.getTaskMeta().getEventTypeId());
        assertEquals("camera_001", deserializedTask.getDevice().getDeviceId());
        assertEquals("orch_001", deserializedTask.getAlgorithmOrchestration().getOrchestrationId());

        // 验证算法配置
        Algorithm deserializedAlgorithm = deserializedTask.getAlgorithmOrchestration().getAlgorithmChain().get(0);
        assertEquals("helmet_detection", deserializedAlgorithm.getAlgorithmId());
        assertEquals("安全帽检测", deserializedAlgorithm.getAlgorithmName());
        assertEquals("CLASSIFICATION", deserializedAlgorithm.getAlgorithmType());
        assertNotNull(deserializedAlgorithm.getAlertConfig());
        assertNotNull(deserializedAlgorithm.getTrainingConfig());

        // 验证告警配置
        AlertConfig deserializedAlertConfig = deserializedAlgorithm.getAlertConfig();
        assertEquals(1, deserializedAlertConfig.getLabels().size());
        assertEquals("no_helmet", deserializedAlertConfig.getLabels().get(0));
        assertEquals(0.8, deserializedAlertConfig.getConfidence(), 0.001);

        // 验证正例和负例标签
        assertNotNull(deserializedAlertConfig.getPositiveLabels());
        assertEquals(2, deserializedAlertConfig.getPositiveLabels().size());
        assertTrue(deserializedAlertConfig.getPositiveLabels().contains("person"));
        assertTrue(deserializedAlertConfig.getPositiveLabels().contains("helmet"));

        assertNotNull(deserializedAlertConfig.getNegativeLabels());
        assertEquals(2, deserializedAlertConfig.getNegativeLabels().size());
        assertTrue(deserializedAlertConfig.getNegativeLabels().contains("background"));
        assertTrue(deserializedAlertConfig.getNegativeLabels().contains("no_helmet"));

        // 验证训练配置
        TrainingConfig deserializedTrainingConfig = deserializedAlgorithm.getTrainingConfig();
        assertEquals(2, deserializedTrainingConfig.getLabels().size());
        assertTrue(deserializedTrainingConfig.getLabels().contains("helmet"));
        assertTrue(deserializedTrainingConfig.getLabels().contains("no_helmet"));
        assertEquals("v1.0", deserializedTrainingConfig.getModelVersion());

        System.out.println("✅ SimplifiedAtomicTask 序列化/反序列化测试通过");
    }

    @Test
    public void testTaskAllocationWithSimplifiedTask() throws Exception {
        // 创建简化任务（简化版本）
        TaskMeta taskMeta = TaskMeta.builder()
                .enabled(true)
                .taskLevel("MEDIUM")
                .protocol("VIDEO")
                .eventTypeId("event_type_uuid_002")
                .eventAction(Arrays.asList("ALERT"))
                .build();

        Device device = Device.builder()
                .deviceId("camera_002")
                .deviceName("测试摄像头")
                .build();

        AlgorithmOrchestration orchestration = AlgorithmOrchestration.builder()
                .orchestrationId("orch_002")
                .orchestrationType("SIMPLE_DETECTION")
                .build();

        SimplifiedAtomicTask task = SimplifiedAtomicTask.builder()
                .taskId("task_002")
                .taskName("测试任务")
                .taskMeta(taskMeta)
                .algorithmOrchestration(orchestration)
                .device(device)
                .build();

        // 创建任务分配
        TaskAllocation allocation = TaskAllocation.builder()
                .allocationId("alloc_001")
                .taskId(task.getTaskId())
                .deviceId(task.getDevice().getDeviceId())
                .serviceId("service_001")
                .taskStatus(com.bohua.scheduler.model.enums.TaskStatus.ALLOCATED)
                .taskRequest(task)
                .build();

        // 序列化
        String json = objectMapper.writeValueAsString(allocation);
        System.out.println("TaskAllocation JSON:");
        System.out.println(json);

        // 反序列化
        TaskAllocation deserializedAllocation = objectMapper.readValue(json, TaskAllocation.class);

        // 验证
        assertEquals("alloc_001", deserializedAllocation.getAllocationId());
        assertEquals("task_002", deserializedAllocation.getTaskId());
        assertEquals("camera_002", deserializedAllocation.getDeviceId());
        assertEquals("service_001", deserializedAllocation.getServiceId());
        assertEquals(com.bohua.scheduler.model.enums.TaskStatus.ALLOCATED, deserializedAllocation.getTaskStatus());
        assertNotNull(deserializedAllocation.getTaskRequest());
        assertEquals("测试任务", deserializedAllocation.getTaskRequest().getTaskName());

        System.out.println("✅ TaskAllocation 与 SimplifiedAtomicTask 集成测试通过");
    }
}
