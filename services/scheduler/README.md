# CV推理服务调度器

## 项目简介

CV推理服务调度器是一个基于Spring Boot的微服务，负责管理多个CV推理服务实例，根据设备资源和调度策略，将视频流任务合理分配到不同的推理服务上。

## 核心功能

- **服务管理**：推理服务的注册、注销和状态管理
- **任务调度**：根据调度策略分配任务到最优服务
- **负载均衡**：支持优先沾满和优先平铺两种调度模式
- **健康检查**：定时监控推理服务健康状态
- **资源管理**：实时跟踪和管理服务资源配额

## 技术栈

- **Java 17**
- **Spring Boot 3.2**
- **MongoDB 6.0**
- **Redis 7**
- **Maven 3.9**
- **Docker & Docker Compose**

## 快速开始

### 环境要求

- JDK 17+
- Maven 3.6+
- Docker & Docker Compose
- MongoDB 6.0+
- Redis 7+

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd scheduler
```

2. **启动依赖服务**
```bash
docker-compose up -d mongodb redis
```

3. **编译项目**
```bash
mvn clean compile
```

4. **运行应用**
```bash
mvn spring-boot:run
```

### Docker部署

1. **构建镜像**
```bash
mvn clean package
docker build -t cv-scheduler:1.0.0 .
```

2. **启动所有服务**
```bash
docker-compose up -d
```

## API文档

### 任务调度

#### 调度任务
```http
POST /api/v1/scheduler/schedule
Content-Type: application/json

{
  "taskId": "task-001",
  "deviceId": "camera-001",
  "algorithmOrchestration": {
    "orchestrationId": "orch-001",
    "orchestrationType": "OVIT_YOLO",
    "algorithmChain": [...]
  }
}
```

#### 释放任务
```http
POST /api/v1/scheduler/release/{allocationId}
```

#### 查询服务状态
```http
GET /api/v1/scheduler/services
```

### 服务管理

#### 注册服务
```http
POST /api/v1/services/register
Content-Type: application/json

{
  "serviceName": "inference-service-1",
  "baseUrl": "http://192.168.1.100:8080",
  "gpuType": "A10",
  "region": "beijing"
}
```

#### 更新服务状态
```http
PUT /api/v1/services/{serviceId}/status?status=ACTIVE
```

#### 更新服务配额
```http
PUT /api/v1/services/{serviceId}/quota?maxQuota=50
```

## 配置说明

### 应用配置 (application.yml)

```yaml
scheduler:
  health-check:
    interval: 30000      # 健康检查间隔(ms)
    timeout: 5000        # 健康检查超时(ms)
    enabled: true        # 是否启用健康检查
  
  strategy:
    default-mode: FILL_FIRST  # 默认调度策略
    enable-dynamic-schedule: true
    rebalance-threshold: 80   # 重平衡阈值
```

### 环境变量

- `MONGODB_URI`: MongoDB连接字符串
- `MONGODB_USERNAME`: MongoDB用户名
- `MONGODB_PASSWORD`: MongoDB密码
- `REDIS_HOST`: Redis主机地址
- `REDIS_PORT`: Redis端口
- `REDIS_PASSWORD`: Redis密码

## 调度策略

### 优先沾满 (FILL_FIRST)
优先选择已有任务较多的服务，充分利用单张GPU卡的资源。

### 优先平铺 (SPREAD_FIRST)
优先选择负载最轻的服务，将任务均匀分布到各个服务上。

## 监控指标

应用提供以下监控端点：

- `/actuator/health` - 健康检查
- `/actuator/metrics` - 应用指标
- `/actuator/prometheus` - Prometheus格式指标

## 开发指南

### 项目结构

```
src/main/java/com/bohua/scheduler/
├── config/          # 配置类
├── controller/      # REST控制器
├── service/         # 业务服务
├── repository/      # 数据访问层
├── model/          # 数据模型
├── dto/            # 数据传输对象
├── exception/      # 异常类
└── util/           # 工具类
```

### 测试

```bash
# 运行单元测试
mvn test

# 运行集成测试
mvn verify
```

## 故障排查

### 常见问题

1. **MongoDB连接失败**
   - 检查MongoDB服务是否启动
   - 验证连接字符串和认证信息

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证网络连接和端口

3. **服务注册失败**
   - 检查服务地址是否可达
   - 验证GPU类型配置

### 日志查看

```bash
# Docker环境
docker logs cv-scheduler

# 本地开发
tail -f logs/scheduler.log
```

## 贡献指南

1. Fork项目
2. 创建特性分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

本项目采用MIT许可证。
