{"taskRequest": {"taskId": "task_001", "taskName": "人员检测任务", "taskDescription": "检测未戴安全帽的人员", "taskMeta": {"enabled": true, "taskLevel": "HIGH", "protocol": "VIDEO", "eventTypeId": "event_type_uuid_001", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch_001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "person_detection", "algorithmName": "人员检测", "algorithmType": "DETECTION", "order": 1, "required": true, "config": {"confidence": 0.7, "nms_threshold": 0.5}}, {"algorithmId": "person_tracking", "algorithmName": "人员跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["person_detection"], "config": {}}, {"algorithmId": "helmet_detection", "algorithmName": "安全帽检测", "algorithmType": "CLASSIFICATION", "order": 3, "required": true, "dependsOn": ["person_tracking"], "alertConfig": {"labels": ["no_helmet"], "confidence": 0.8, "positiveLabels": ["person", "helmet"], "negativeLabels": ["background", "no_helmet"]}, "trainingConfig": {"labels": ["helmet", "no_helmet"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.1, "maxConfidence": 0.9}, "samplingRate": 0.1, "maxSamplesPerDay": 1000}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera_001", "deviceName": "工地入口摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://192.168.1.100:554/stream", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "config": {}, "region": "default", "priority": 1}