"""
数据模型定义
"""
from datetime import datetime
from enum import Enum
from typing import List, Dict, Any, Optional
from pydantic import BaseModel, Field


class TaskStatus(str, Enum):
    """任务状态枚举"""
    PENDING = "PENDING"
    RUNNING = "RUNNING"
    STOPPED = "STOPPED"
    ERROR = "ERROR"


class AlertConfig(BaseModel):
    """告警配置"""
    labels: List[str] = []
    confidence: Optional[float] = None

    class Config:
        populate_by_name = True


class DataCollectionConfig(BaseModel):
    """数据收集配置"""
    enabled: bool = True
    thresholds: Optional[Dict[str, float]] = None
    sampling_rate: float = Field(default=0.1, alias="samplingRate")
    max_samples_per_day: int = Field(default=1000, alias="maxSamplesPerDay")

    class Config:
        populate_by_name = True


class TrainingConfig(BaseModel):
    """训练配置"""
    labels: List[str] = []
    data_collection: Optional[DataCollectionConfig] = Field(default=None, alias="dataCollection")
    model_version: Optional[str] = Field(default=None, alias="modelVersion")

    class Config:
        populate_by_name = True
        protected_namespaces = ()


class RuleConfig(BaseModel):
    """规则配置"""
    rule_type: str = Field(alias="ruleType")
    polygons: Optional[List[Dict[str, Any]]] = None
    lines: Optional[List[Dict[str, Any]]] = None

    class Config:
        populate_by_name = True


class Algorithm(BaseModel):
    """算法配置"""
    algorithm_id: str = Field(alias="algorithmId")
    algorithm_name: str = Field(alias="algorithmName")
    algorithm_type: str = Field(alias="algorithmType")
    order: int = 1
    required: bool = True
    depends_on: List[str] = Field(default=[], alias="dependsOn")
    config: Optional[Dict[str, Any]] = None
    alert_config: Optional[AlertConfig] = Field(default=None, alias="alertConfig")
    training_config: Optional[TrainingConfig] = Field(default=None, alias="trainingConfig")
    rule_config: Optional[RuleConfig] = Field(default=None, alias="ruleConfig")

    class Config:
        populate_by_name = True


class DecoderConfig(BaseModel):
    """解码器配置"""
    key_frame_only: bool = Field(default=False, alias="keyFrameOnly")
    decode_step: int = Field(default=4, alias="decodeStep")

    class Config:
        populate_by_name = True


class AlgorithmOrchestration(BaseModel):
    """算法编排配置"""
    orchestration_id: str = Field(alias="orchestrationId")
    orchestration_type: str = Field(alias="orchestrationType")
    algorithm_chain: List[Algorithm] = Field(alias="algorithmChain")
    decoder_config: Optional[DecoderConfig] = Field(default=None, alias="decoderConfig")

    class Config:
        populate_by_name = True


class TaskMeta(BaseModel):
    """任务元信息"""
    enabled: bool = True
    task_level: str = Field(default="MEDIUM", alias="taskLevel")
    protocol: str = "VIDEO"
    event_type_id: str = Field(alias="eventTypeId")
    event_action: List[str] = Field(default=["ALERT"], alias="eventAction")

    class Config:
        populate_by_name = True


class StreamConfig(BaseModel):
    """流配置"""
    resolution: str = "1920x1080"
    frame_rate: int = Field(default=25, alias="frameRate")
    protocol: str = "RTSP"
    url: str
    decoder_conf: Optional[DecoderConfig] = Field(default=None, alias="decoderConf")

    class Config:
        populate_by_name = True


class Device(BaseModel):
    """设备配置"""
    device_id: str = Field(alias="deviceId")
    device_name: Optional[str] = Field(default=None, alias="deviceName")
    stream_config: Optional[StreamConfig] = Field(default=None, alias="streamConfig")

    class Config:
        populate_by_name = True


class SimplifiedAtomicTask(BaseModel):
    """简化原子任务模型"""
    task_id: str = Field(alias="taskId")
    task_name: str = Field(alias="taskName")
    task_description: Optional[str] = Field(default=None, alias="taskDescription")
    task_meta: TaskMeta = Field(alias="taskMeta")
    algorithm_orchestration: AlgorithmOrchestration = Field(alias="algorithmOrchestration")
    device: Device

    class Config:
        populate_by_name = True


class TaskRequest(BaseModel):
    """任务请求模型"""
    task_request: SimplifiedAtomicTask = Field(alias="taskRequest")
    config: Optional[Dict[str, Any]] = None
    priority: Optional[int] = 1
    region: Optional[str] = "default"

    class Config:
        populate_by_name = True


class BoundingBox(BaseModel):
    """边界框模型"""
    x: int
    y: int
    width: int
    height: int


class EntityAttribute(BaseModel):
    """实体属性模型"""
    attributeName: str
    attributeValue: Any
    confidence: float  # 改为confidence
    algorithmId: str


class PartOfRelation(BaseModel):
    """从属关系模型"""
    algorithmId: str
    confidence: float
    entities: List[str]


class RelationNameValue(BaseModel):
    """关系名值模型"""
    confidence: float
    entities: List[str] = []
    algorithmId: str
    attributes: Optional[Dict[str, Any]] = None


class EntityInstance(BaseModel):
    """实体实例模型"""
    entityInstanceId: str
    entityType: str
    algorithmId: str  # 新增字段
    boundingBox: Optional[BoundingBox] = None
    polygon: Optional[Dict[str, Any]] = None
    line: Optional[Dict[str, Any]] = None
    partOf: Dict[str, PartOfRelation] = {}
    entityAttributes: Dict[str, EntityAttribute] = {}
    entityRelationship: Dict[str, RelationNameValue] = {}
    externalInfo: Dict[str, Any] = {}


class TaskInfo(BaseModel):
    """任务信息模型"""
    taskId: str
    eventTypeId: str
    orchestrationId: str
    orchestrationType: str
    taskLevel: str
    deviceName: str
    frameId: str
    externalInfo: Dict[str, Any] = {}


class AtomicEventInstance(BaseModel):
    """原子事件实例模型 - 符合Vision Flow协议"""
    atomicEventInstanceId: str
    eventTypeId: str  # 改为eventTypeId
    taskId: str  # 新增字段
    deviceId: str
    timestamp: int  # Unix timestamp in milliseconds
    imageUri: str  # 改为必填
    entities: List[EntityInstance]
    relationEntities: List[EntityInstance] = []
    taskInfo: TaskInfo  # 新增字段


class Screenshot(BaseModel):
    """截图模型"""
    timestamp: datetime
    filename: str
    file_path: str
    events: List[AtomicEventInstance] = []


class Task(BaseModel):
    """任务模型"""
    task_id: str
    device_id: str
    rtsp_url: str
    algorithm_config: AlgorithmOrchestration
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    last_event_time: Optional[datetime] = None
    event_count: int = 0
    screenshots: List[Screenshot] = []
    config: Optional[Dict[str, Any]] = None
    alert_interval: Optional[int] = None  # 告警间隔（秒）
    last_alert_time: Optional[datetime] = None  # 最后告警时间


class HealthResponse(BaseModel):
    """健康检查响应"""
    status: str
    details: Dict[str, Any] = {}
    timestamp: int


class ServiceRegistration(BaseModel):
    """服务注册请求"""
    service_name: str = Field(alias="serviceName")
    base_url: str = Field(alias="baseUrl")
    algorithm_orchestration: AlgorithmOrchestration = Field(alias="algorithmOrchestration")
    max_quota: int = Field(alias="maxQuota", default=10)
    region: str = "default"
    gpu_type: str = Field(alias="gpuType", default="A10")

    class Config:
        populate_by_name = True


class TaskResponse(BaseModel):
    """任务响应模型"""
    task_id: str
    status: TaskStatus
    device_id: str
    rtsp_url: str
    start_time: Optional[datetime] = None
    last_event_time: Optional[datetime] = None
    event_count: int = 0
    screenshots: List[Screenshot] = []


class EventsResponse(BaseModel):
    """事件查询响应"""
    task_id: str
    events: List[AtomicEventInstance]
    total: int
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
