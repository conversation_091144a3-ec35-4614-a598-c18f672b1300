"""
服务注册模块
"""
import asyncio
import requests
from typing import Optional
from loguru import logger
from .config import config
from .models import ServiceRegistration, AlgorithmOrchestration


class ServiceRegistry:
    """服务注册管理器"""
    
    def __init__(self):
        self.scheduler_config = config.get_scheduler_config()
        self.service_config = config.get_service_config()
        self.server_config = config.get_server_config()
        self.registered = False
        self.registration_task: Optional[asyncio.Task] = None
    
    def _build_registration_data(self) -> ServiceRegistration:
        """构建服务注册数据"""
        base_url = f"http://localhost:{self.server_config.get('port', 8081)}"
        
        # 从环境变量或配置获取服务URL
        import os
        if os.getenv('SERVICE_HOST'):
            base_url = f"http://{os.getenv('SERVICE_HOST')}:{self.server_config.get('port', 8081)}"
        
        algorithm_config = self.service_config.get('algorithm_orchestration', {})
        
        return ServiceRegistration(
            service_name=self.service_config.get('name', 'inference-mock-1'),
            base_url=base_url,
            algorithm_orchestration=AlgorithmOrchestration(**algorithm_config),
            max_quota=self.service_config.get('max_quota', 10),
            region=self.service_config.get('region', 'default'),
            gpu_type=self.service_config.get('gpu_type', 'A10')
        )
    
    async def register_service(self) -> bool:
        """注册服务到调度器"""
        try:
            scheduler_url = self.scheduler_config.get('url', 'http://localhost:8080')
            registration_url = f"{scheduler_url}/api/v1/services/register"
            
            registration_data = self._build_registration_data()
            
            logger.info(f"正在注册服务到调度器: {registration_url}")
            logger.debug(f"注册数据: {registration_data.dict()}")
            
            response = requests.post(
                registration_url,
                json=registration_data.dict(by_alias=True),
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            if response.status_code in [200, 201]:
                self.registered = True
                logger.info(f"服务注册成功: {registration_data.service_name}")
                logger.debug(f"注册响应: {response.text}")
                return True
            else:
                logger.error(f"服务注册失败: HTTP {response.status_code}, {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            logger.error(f"服务注册网络错误: {e}")
            return False
        except Exception as e:
            logger.error(f"服务注册异常: {e}")
            return False
    
    async def start_registration_loop(self):
        """启动服务注册循环"""
        retry_interval = self.scheduler_config.get('registration_retry_interval', 30)
        
        while True:
            if not self.registered:
                logger.info("尝试注册服务...")
                success = await self.register_service()
                
                if not success:
                    logger.warning(f"服务注册失败，{retry_interval}秒后重试...")
                    await asyncio.sleep(retry_interval)
                else:
                    # 注册成功后，定期检查连接状态
                    await asyncio.sleep(retry_interval * 2)
            else:
                # 已注册，定期心跳检查
                await asyncio.sleep(retry_interval)
                await self._health_check()
    
    async def _health_check(self):
        """检查与调度器的连接状态"""
        try:
            scheduler_url = self.scheduler_config.get('url', 'http://localhost:8080')
            health_url = f"{scheduler_url}/actuator/health"
            
            response = requests.get(health_url, timeout=5)
            if response.status_code != 200:
                logger.warning("调度器健康检查失败，标记为未注册状态")
                self.registered = False
                
        except Exception as e:
            logger.warning(f"调度器连接检查失败: {e}")
            self.registered = False
    
    async def start(self):
        """启动服务注册"""
        if self.registration_task is None:
            self.registration_task = asyncio.create_task(self.start_registration_loop())
            logger.info("服务注册任务已启动")
    
    async def stop(self):
        """停止服务注册"""
        if self.registration_task:
            self.registration_task.cancel()
            try:
                await self.registration_task
            except asyncio.CancelledError:
                pass
            self.registration_task = None
            logger.info("服务注册任务已停止")


# 全局服务注册实例
service_registry = ServiceRegistry()
