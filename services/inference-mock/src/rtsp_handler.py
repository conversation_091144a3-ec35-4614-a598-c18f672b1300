"""
RTSP流处理模块
"""
import cv2
import asyncio
import threading
import time
from typing import Optional, Dict, Any
from datetime import datetime
from loguru import logger
from .config import config


class RTSPHandler:
    """RTSP流处理器"""

    @staticmethod
    def resolve_rtsp_url(device_id: str, original_url: str) -> str:
        """解析RTSP URL，支持设备ID映射和模拟模式"""
        rtsp_config = config.get("rtsp", {})

        # 如果已经是完整的RTSP URL，检查是否启用模拟模式
        if original_url.startswith('rtsp://'):
            # 检查是否启用RTSP模拟模式
            if rtsp_config.get("simulation_mode", True):
                # 模拟模式：将RTSP URL映射到本地文件
                local_file = rtsp_config.get("simulation_file", "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov")
                logger.info(f"RTSP模拟模式: {original_url} -> {local_file}")
                return local_file
            else:
                logger.info(f"使用真实RTSP URL: {original_url}")
                return original_url

        # 如果RTSP功能未启用，返回原始URL（本地文件）
        if not rtsp_config.get("enabled", True):
            logger.info(f"RTSP功能未启用，使用本地文件: {original_url}")
            return original_url

        # 尝试从设备映射中获取RTSP URL
        stream_mapping = rtsp_config.get("stream_mapping", {})

        if device_id in stream_mapping:
            rtsp_url = stream_mapping[device_id]
            # 检查模拟模式
            if rtsp_config.get("simulation_mode", True):
                local_file = rtsp_config.get("simulation_file", "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov")
                logger.info(f"设备 {device_id} RTSP模拟: {rtsp_url} -> {local_file}")
                return local_file
            else:
                logger.info(f"设备 {device_id} 映射到RTSP流: {rtsp_url}")
                return rtsp_url

        # 使用默认RTSP流
        default_url = stream_mapping.get("default")
        if default_url:
            if rtsp_config.get("simulation_mode", True):
                local_file = rtsp_config.get("simulation_file", "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov")
                logger.info(f"设备 {device_id} 默认RTSP模拟: {default_url} -> {local_file}")
                return local_file
            else:
                logger.info(f"设备 {device_id} 使用默认RTSP流: {default_url}")
                return default_url

        # 如果没有配置映射，返回本地文件
        local_file = rtsp_config.get("simulation_file", "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov")
        logger.info(f"设备 {device_id} 使用默认本地文件: {local_file}")
        return local_file

    def __init__(self, rtsp_url: str, task_id: str):
        self.rtsp_url = rtsp_url
        self.task_id = task_id
        self.cap: Optional[cv2.VideoCapture] = None
        self.is_running = False
        self.connected = False  # 连接状态
        self.last_frame = None
        self.last_frame_time = None
        self.connection_thread: Optional[threading.Thread] = None
        self.rtsp_config = config.get_rtsp_config()

        # 连接参数 - 从配置文件读取
        rtsp_config = config.get('rtsp', {})
        self.connection_timeout = rtsp_config.get('connection_timeout', 10)
        self.retry_interval = rtsp_config.get('retry_interval', 5)
        self.max_retries = rtsp_config.get('max_retries', 5)
        self.read_timeout = rtsp_config.get('read_timeout', 5)
        self.max_consecutive_failures = rtsp_config.get('max_consecutive_failures', 10)
        self.health_check_interval = rtsp_config.get('health_check_interval', 30)
        self.frame_buffer_size = self.rtsp_config.get('frame_buffer_size', 1)
        
        # 统计信息
        self.stats = {
            'connection_attempts': 0,
            'successful_connections': 0,
            'frames_captured': 0,
            'last_error': None,
            'connected': False,
            'start_time': None
        }
    
    def connect(self) -> bool:
        """连接RTSP流"""
        try:
            self.stats['connection_attempts'] += 1
            logger.info(f"正在连接RTSP流: {self.rtsp_url} (任务: {self.task_id})")

            # 检查是否是真实RTSP流
            is_rtsp_stream = self.rtsp_url.startswith('rtsp://')

            # 创建VideoCapture对象
            self.cap = cv2.VideoCapture(self.rtsp_url)

            if is_rtsp_stream:
                # RTSP流特殊配置
                logger.info(f"配置RTSP流参数: {self.rtsp_url}")

                # 设置RTSP传输协议 (TCP更稳定)
                self.cap.set(cv2.CAP_PROP_FOURCC, cv2.VideoWriter_fourcc('H', '2', '6', '4'))

                # 设置缓冲区大小（RTSP流建议较小的缓冲区）
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                # 设置连接超时
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self.connection_timeout * 1000)

                # 设置读取超时
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 10000)  # RTSP流需要更长的超时

                # 设置FPS（如果支持）
                self.cap.set(cv2.CAP_PROP_FPS, 25)

            else:
                # 本地文件配置
                logger.info(f"配置本地文件参数: {self.rtsp_url}")

                # 设置缓冲区大小
                self.cap.set(cv2.CAP_PROP_BUFFERSIZE, self.frame_buffer_size)

                # 设置连接超时
                self.cap.set(cv2.CAP_PROP_OPEN_TIMEOUT_MSEC, self.connection_timeout * 1000)

                # 设置读取超时
                self.cap.set(cv2.CAP_PROP_READ_TIMEOUT_MSEC, 5000)

            # 测试连接
            if self.cap.isOpened():
                ret, frame = self.cap.read()
                if ret and frame is not None:
                    self.stats['successful_connections'] += 1
                    self.stats['connected'] = True
                    self.stats['start_time'] = datetime.now()
                    self.connected = True  # 设置连接状态

                    # 获取视频信息
                    fps = self.cap.get(cv2.CAP_PROP_FPS)
                    width = int(self.cap.get(cv2.CAP_PROP_FRAME_WIDTH))
                    height = int(self.cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
                    logger.info(f"RTSP流连接成功: {self.rtsp_url}")
                    logger.info(f"视频信息: {width}x{height}, FPS: {fps:.2f}")

                    # 保存第一帧作为连接测试
                    self._save_test_frame(frame)

                    return True
                else:
                    logger.warning(f"RTSP流连接失败，无法读取帧: {self.rtsp_url}")
                    self.cap.release()
                    self.cap = None
                    return False
            else:
                logger.warning(f"RTSP流连接失败: {self.rtsp_url}")
                self.cap = None
                return False
                
        except Exception as e:
            self.stats['last_error'] = str(e)
            logger.error(f"RTSP连接异常: {e}")
            if self.cap:
                self.cap.release()
                self.cap = None
            return False
    
    def disconnect(self):
        """断开RTSP连接"""
        self.is_running = False
        self.reset_connection()
        logger.info(f"RTSP流已断开: {self.rtsp_url}")

    def _save_test_frame(self, frame):
        """保存测试帧"""
        try:
            import os
            os.makedirs("./screenshots", exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"./screenshots/rtsp_test_{self.task_id}_{timestamp}.jpg"

            cv2.imwrite(filename, frame)
            logger.info(f"保存RTSP测试截图: {filename}")

        except Exception as e:
            logger.error(f"保存测试帧失败: {e}")

    def capture_frame(self) -> Optional[Any]:
        """捕获一帧图像"""
        if not self.cap or not self.cap.isOpened():
            logger.debug(f"RTSP连接未打开: {self.task_id}")
            return None

        try:
            ret, frame = self.cap.read()
            if ret and frame is not None:
                self.last_frame = frame
                self.last_frame_time = datetime.now()
                self.stats['frames_captured'] += 1
                return frame
            else:
                # 读取失败可能表示连接断开
                logger.debug(f"无法读取RTSP帧: {self.rtsp_url}")
                # 检查连接状态
                if not self.cap.isOpened():
                    logger.warning(f"RTSP连接已断开: {self.task_id}")
                    self.connected = False
                return None

        except Exception as e:
            logger.error(f"捕获帧异常: {self.task_id}, {e}")
            self.stats['last_error'] = str(e)
            # 异常时也可能表示连接问题
            self.connected = False
            return None
    
    def start_capture_loop(self):
        """启动帧捕获循环"""
        self.is_running = True
        retry_count = 0
        
        while self.is_running:
            # 尝试连接
            if not self.stats['connected']:
                if retry_count < self.max_retries:
                    if self.connect():
                        retry_count = 0
                    else:
                        retry_count += 1
                        logger.warning(f"RTSP连接失败，{self.retry_interval}秒后重试 ({retry_count}/{self.max_retries})")
                        threading.Event().wait(self.retry_interval)
                        continue
                else:
                    logger.error(f"RTSP连接重试次数超限，停止尝试: {self.rtsp_url}")
                    break
            
            # 捕获帧
            frame = self.capture_frame()
            if frame is None:
                # 连接可能断开，重新连接
                self.stats['connected'] = False
                self.disconnect()
                continue
            
            # 短暂休眠，避免过度占用CPU
            threading.Event().wait(0.1)
        
        self.disconnect()
        logger.info(f"RTSP捕获循环已停止: {self.task_id}")

    def start_capture_loop_with_alerts(self):
        """启动RTSP捕获循环（包含告警逻辑）"""
        logger.info(f"开始RTSP捕获循环（包含告警逻辑）: {self.task_id}")

        # 设置运行状态
        self.is_running = True
        frame_count = 0
        retry_count = 0
        consecutive_failures = 0

        while self.is_running:
            try:
                # 检查连接状态，如果未连接则尝试重连
                if not self.connected or not self.is_connected():
                    if retry_count < self.max_retries:
                        logger.info(f"尝试连接RTSP流: {self.task_id} (重试: {retry_count + 1}/{self.max_retries})")
                        if self.connect():
                            retry_count = 0
                            consecutive_failures = 0
                            logger.info(f"RTSP流重连成功: {self.task_id}")
                        else:
                            retry_count += 1
                            logger.warning(f"RTSP连接失败，{self.retry_interval}秒后重试: {self.task_id} ({retry_count}/{self.max_retries})")
                            time.sleep(self.retry_interval)
                            continue
                    else:
                        logger.error(f"RTSP连接重试次数超限，停止任务: {self.task_id}")
                        break

                # 获取帧
                frame = self.capture_frame()
                if frame is None:
                    consecutive_failures += 1
                    logger.warning(f"无法获取帧: {self.task_id} (连续失败: {consecutive_failures})")

                    # 如果连续失败次数过多，标记为断连
                    if consecutive_failures >= self.max_consecutive_failures:
                        logger.warning(f"连续获取帧失败次数过多，标记为断连: {self.task_id}")
                        self.connected = False
                        self.disconnect()
                        consecutive_failures = 0
                        continue

                    time.sleep(0.1)
                    continue

                # 成功获取帧，重置失败计数
                consecutive_failures = 0
                frame_count += 1
                current_time = datetime.now()

                # 检查是否需要生成告警
                should_generate_alert = False
                if self.last_alert_time is None:
                    # 第一次，立即生成告警
                    should_generate_alert = True
                else:
                    # 检查时间间隔
                    time_since_last_alert = (current_time - self.last_alert_time).total_seconds()
                    if time_since_last_alert >= self.alert_interval:
                        should_generate_alert = True

                if should_generate_alert:
                    logger.info(f"🔔 触发告警生成: {self.task_id}, 帧数: {frame_count}")
                    self._generate_alert_from_frame(frame, current_time)
                    self.last_alert_time = current_time

                # 控制帧率，避免过度消耗CPU
                time.sleep(0.04)  # 约25fps

            except Exception as e:
                logger.error(f"RTSP捕获循环异常: {self.task_id}, {e}")
                # 异常时也标记为断连，触发重连
                self.connected = False
                self.disconnect()
                time.sleep(1)

        self.disconnect()
        logger.info(f"RTSP捕获循环已停止（包含告警逻辑）: {self.task_id}")

    def _generate_alert_from_frame(self, frame, timestamp):
        """从当前帧生成告警"""
        try:
            # 保存截图
            screenshot_filename = self.event_generator.save_screenshot(frame, timestamp)
            if screenshot_filename:
                logger.info(f"告警截图已保存: {screenshot_filename}")

            # 生成告警事件
            event = self.event_generator._generate_event_sync(frame, timestamp, screenshot_filename)
            if event:
                # 发送到Kafka
                self.event_generator.kafka_producer.send_event(event)
                logger.info(f"告警已生成并发送: {event.atomicEventInstanceId}")

                # 更新任务统计
                from .task_manager import task_manager
                task = task_manager.get_task(self.task_id)
                if task:
                    task.event_count += 1
                    task.last_event_time = timestamp

        except Exception as e:
            logger.error(f"告警生成失败: {self.task_id}, {e}")

    def start(self):
        """启动RTSP处理（不包含告警逻辑）"""
        if self.connection_thread is None or not self.connection_thread.is_alive():
            self.connection_thread = threading.Thread(
                target=self.start_capture_loop,
                name=f"rtsp-{self.task_id}",
                daemon=True
            )
            self.connection_thread.start()
            logger.info(f"RTSP处理线程已启动: {self.task_id}")

    def start_with_alert_logic(self, alert_interval: int, event_generator):
        """启动RTSP处理（包含告警逻辑）"""
        if self.connection_thread is None or not self.connection_thread.is_alive():
            self.alert_interval = alert_interval
            self.event_generator = event_generator
            self.last_alert_time = None

            self.connection_thread = threading.Thread(
                target=self.start_capture_loop_with_alerts,
                name=f"rtsp-alert-{self.task_id}",
                daemon=True
            )
            self.connection_thread.start()
            logger.info(f"RTSP处理线程已启动（包含告警逻辑）: {self.task_id}, 告警间隔: {alert_interval}秒")
    
    def stop(self):
        """停止RTSP处理"""
        self.is_running = False
        if self.connection_thread and self.connection_thread.is_alive():
            self.connection_thread.join(timeout=5)
        self.disconnect()
        logger.info(f"RTSP处理已停止: {self.task_id}")
    
    def get_latest_frame(self) -> Optional[Any]:
        """获取最新的帧"""
        return self.last_frame
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        stats = self.stats.copy()
        stats['last_frame_time'] = self.last_frame_time.isoformat() if self.last_frame_time else None
        stats['is_running'] = self.is_running
        return stats
    
    def is_connected(self) -> bool:
        """检查是否已连接"""
        if not self.connected or not self.cap:
            return False

        try:
            # 检查OpenCV capture对象状态
            is_opened = self.cap.isOpened()
            if not is_opened:
                logger.debug(f"OpenCV capture已关闭: {self.task_id}")
                self.connected = False
                return False

            return self.stats['connected'] and is_opened
        except Exception as e:
            logger.warning(f"检查连接状态异常: {self.task_id}, {e}")
            self.connected = False
            return False

    def reset_connection(self):
        """重置连接状态，准备重连"""
        logger.info(f"重置RTSP连接状态: {self.task_id}")
        self.connected = False
        self.stats['connected'] = False
        if self.cap:
            try:
                self.cap.release()
            except Exception as e:
                logger.warning(f"释放capture对象异常: {e}")
            self.cap = None

    def perform_health_check(self) -> bool:
        """执行健康检查"""
        try:
            if not self.is_connected():
                logger.debug(f"健康检查失败: 连接未建立 - {self.task_id}")
                return False

            # 尝试读取一帧来测试连接
            test_frame = self.capture_frame()
            if test_frame is not None:
                logger.debug(f"健康检查通过: {self.task_id}")
                return True
            else:
                logger.warning(f"健康检查失败: 无法读取帧 - {self.task_id}")
                return False

        except Exception as e:
            logger.warning(f"健康检查异常: {self.task_id}, {e}")
            return False
