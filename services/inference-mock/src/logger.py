"""
日志配置模块
"""
import os
import sys
from loguru import logger
from .config import config


def setup_logger():
    """设置日志配置"""
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.get_logging_config()
    
    # 确保日志目录存在
    log_file = log_config.get("file", "./logs/inference-mock.log")
    log_dir = os.path.dirname(log_file)
    if log_dir and not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)
    
    # 控制台日志
    logger.add(
        sys.stdout,
        level=log_config.get("level", "INFO"),
        format=log_config.get("format", "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"),
        colorize=True
    )
    
    # 文件日志
    logger.add(
        log_file,
        level=log_config.get("level", "INFO"),
        format=log_config.get("format", "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"),
        rotation=log_config.get("rotation", "1 day"),
        retention=log_config.get("retention", "7 days"),
        compression="zip"
    )
    
    logger.info("日志系统初始化完成")


# 初始化日志
setup_logger()
