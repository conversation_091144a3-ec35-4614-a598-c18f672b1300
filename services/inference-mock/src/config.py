"""
配置管理模块
"""
import os
import yaml
from typing import Dict, Any
from loguru import logger


class Config:
    """配置管理类"""
    
    def __init__(self, config_file: str = "config.yaml"):
        self.config_file = config_file
        self._config = {}
        self.load_config()
    
    def load_config(self):
        """加载配置文件并应用环境变量覆盖"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    self._config = yaml.safe_load(f)
                logger.info(f"配置文件加载成功: {self.config_file}")
            else:
                logger.warning(f"配置文件不存在: {self.config_file}，使用默认配置")
                self._config = self._get_default_config()
        except Exception as e:
            logger.error(f"配置文件加载失败: {e}")
            self._config = self._get_default_config()

        # 应用环境变量覆盖
        self._apply_env_overrides()

    def _apply_env_overrides(self):
        """应用环境变量覆盖配置"""
        # 服务器配置
        if os.getenv('SERVICE_HOST'):
            self._config.setdefault('server', {})['host'] = os.getenv('SERVICE_HOST')
        if os.getenv('SERVICE_PORT'):
            self._config.setdefault('server', {})['port'] = int(os.getenv('SERVICE_PORT'))

        # 调度器配置
        if os.getenv('SCHEDULER_URL'):
            self._config.setdefault('scheduler', {})['url'] = os.getenv('SCHEDULER_URL')

        # 服务配置
        service_config = self._config.setdefault('service', {})
        if os.getenv('SERVICE_NAME'):
            service_config['name'] = os.getenv('SERVICE_NAME')
        if os.getenv('SERVICE_REGION'):
            service_config['region'] = os.getenv('SERVICE_REGION')
        if os.getenv('SERVICE_GPU_TYPE'):
            service_config['gpu_type'] = os.getenv('SERVICE_GPU_TYPE')
        if os.getenv('SERVICE_MAX_QUOTA'):
            service_config['max_quota'] = int(os.getenv('SERVICE_MAX_QUOTA'))
        if os.getenv('CURRENT_ORCHESTRATION'):
            service_config['current_orchestration'] = os.getenv('CURRENT_ORCHESTRATION')

        # Kafka配置
        kafka_config = self._config.setdefault('kafka', {})
        if os.getenv('KAFKA_BOOTSTRAP_SERVERS'):
            kafka_config['bootstrap_servers'] = os.getenv('KAFKA_BOOTSTRAP_SERVERS').split(',')
        if os.getenv('KAFKA_TOPIC'):
            kafka_config['topic'] = os.getenv('KAFKA_TOPIC')
        if os.getenv('KAFKA_CLIENT_ID'):
            kafka_config['client_id'] = os.getenv('KAFKA_CLIENT_ID')

        # RTSP配置
        rtsp_config = self._config.setdefault('rtsp', {})
        if os.getenv('RTSP_DEFAULT_SERVER'):
            rtsp_config['default_server'] = os.getenv('RTSP_DEFAULT_SERVER')
        if os.getenv('RTSP_SIMULATION_MODE'):
            rtsp_config['simulation_mode'] = os.getenv('RTSP_SIMULATION_MODE').lower() == 'true'

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "server": {
                "host": "0.0.0.0",
                "port": 8081
            },
            "scheduler": {
                "url": "http://localhost:8080",
                "registration_retry_interval": 30,
                "health_check_interval": 60
            },
            "service": {
                "name": "inference-mock-1",
                "region": "default",
                "gpu_type": "A10",
                "max_quota": 10,
                "algorithm_orchestration": {
                    "algorithm_type": "OBJECT_DETECTION",
                    "model_name": "yolo-v8",
                    "model_version": "1.0.0",
                    "input_format": "IMAGE",
                    "output_format": "JSON"
                }
            },
            "rtsp": {
                "connection_timeout": 10,
                "retry_interval": 5,
                "max_retries": 3,
                "frame_buffer_size": 1
            },
            "screenshot": {
                "interval": 30,
                "output_dir": "./screenshots",
                "format": "jpg",
                "quality": 85,
                "max_files": 100
            },
            "kafka": {
                "bootstrap_servers": ["localhost:9092"],
                "topic": "vision-events",
                "client_id": "inference-mock-1",
                "acks": 1,
                "retries": 3,
                "batch_size": 16384,
                "linger_ms": 10,
                "buffer_memory": 33554432
            },
            "mock_events": {
                "enabled": True,
                "detection_probability": 0.7,
                "classes": ["person", "car", "bicycle", "dog", "cat"],
                "confidence_range": [0.6, 0.95],
                "max_objects_per_frame": 5
            },
            "logging": {
                "level": "INFO",
                "format": "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
                "file": "./logs/inference-mock.log",
                "rotation": "1 day",
                "retention": "7 days"
            }
        }
    
    def get(self, key: str, default=None):
        """获取配置值，支持点号分隔的嵌套键"""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return self.get("server", {})
    
    def get_scheduler_config(self) -> Dict[str, Any]:
        """获取调度器配置"""
        return self.get("scheduler", {})
    
    def get_service_config(self) -> Dict[str, Any]:
        """获取服务配置"""
        return self.get("service", {})
    
    def get_rtsp_config(self) -> Dict[str, Any]:
        """获取RTSP配置"""
        return self.get("rtsp", {})
    
    def get_screenshot_config(self) -> Dict[str, Any]:
        """获取截图配置"""
        return self.get("screenshot", {})
    
    def get_mock_events_config(self) -> Dict[str, Any]:
        """获取模拟事件配置"""
        return self.get("mock_events", {})
    
    def get_kafka_config(self) -> Dict[str, Any]:
        """获取Kafka配置"""
        return self.get("kafka", {})

    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        return self.get("logging", {})

    def get_current_orchestration(self) -> str:
        """获取当前编排类型"""
        return self.get("service.current_orchestration", "YOLO_TRACKING_CLIP")

    def get_orchestration_config(self, orchestration_type: str = None) -> Dict[str, Any]:
        """获取编排特定配置"""
        if orchestration_type is None:
            orchestration_type = self.get_current_orchestration()

        orchestration_configs = self.get("orchestration_configs", {})
        return orchestration_configs.get(orchestration_type, {})

    def get_alert_interval(self, orchestration_type: str = None) -> int:
        """获取编排特定的告警间隔"""
        orchestration_config = self.get_orchestration_config(orchestration_type)
        return orchestration_config.get("alert_interval", 30)  # 默认30秒


# 全局配置实例
config = Config()
