"""
任务管理模块
"""
import asyncio
import threading
from datetime import datetime
from typing import Dict, List, Optional, Any
from loguru import logger
from apscheduler.schedulers.asyncio import AsyncIOScheduler

from .config import config
from .models import Task, TaskRequest, TaskStatus
from .rtsp_handler import RTSPHandler
from .event_generator import EventGenerator


class TaskManager:
    """任务管理器"""
    
    def __init__(self):
        self.tasks: Dict[str, Task] = {}
        self.rtsp_handlers: Dict[str, RTSPHandler] = {}
        self.event_generators: Dict[str, EventGenerator] = {}
        self.scheduler = AsyncIOScheduler()
        self.screenshot_config = config.get_screenshot_config()
        self.screenshot_interval = self.screenshot_config.get("interval", 30)
        
        # 统计信息
        self.stats = {
            "total_tasks": 0,
            "active_tasks": 0,
            "completed_tasks": 0,
            "failed_tasks": 0,
            "last_screenshot_time": None,
            "last_event_time": None
        }
        
        # 启动调度器
        self.scheduler.start()
        logger.info("任务管理器初始化完成")
    
    async def create_task(self, task_request: TaskRequest) -> bool:
        """创建任务"""
        try:
            # 从新的协议结构中提取信息
            simplified_task = task_request.task_request
            task_id = simplified_task.task_id
            device_id = simplified_task.device.device_id
            original_url = simplified_task.device.stream_config.url if simplified_task.device.stream_config else ""

            # 解析RTSP URL，支持设备映射
            from .rtsp_handler import RTSPHandler
            rtsp_url = RTSPHandler.resolve_rtsp_url(device_id, original_url)

            # 检查任务是否已存在
            if task_id in self.tasks:
                logger.warning(f"任务已存在: {task_id}")
                return False
            
            # 创建任务对象
            task = Task(
                task_id=task_id,
                device_id=device_id,
                rtsp_url=rtsp_url,
                algorithm_config=simplified_task.algorithm_orchestration,
                status=TaskStatus.PENDING,
                config=task_request.config
            )

            # 创建RTSP处理器
            rtsp_handler = RTSPHandler(rtsp_url, task_id)

            # 创建事件生成器
            orchestration_type = simplified_task.algorithm_orchestration.orchestration_type
            event_generator = EventGenerator(
                task_id=task_id,
                device_id=device_id,
                event_type_id=simplified_task.task_meta.event_type_id,
                orchestration_id=simplified_task.algorithm_orchestration.orchestration_id,
                orchestration_type=orchestration_type
            )

            # 设置Kafka生产者
            from .kafka_producer import kafka_producer
            event_generator.kafka_producer = kafka_producer

            # 根据编排类型获取截图间隔
            orchestration_config = config.get_orchestration_config(orchestration_type)
            screenshot_interval = orchestration_config.get("screenshot_interval", self.screenshot_interval)
            
            # 保存到管理器
            self.tasks[task_id] = task
            self.rtsp_handlers[task_id] = rtsp_handler
            self.event_generators[task_id] = event_generator
            
            # 启动任务
            await self._start_task(task_id)
            
            self.stats["total_tasks"] += 1
            self.stats["active_tasks"] += 1
            
            logger.info(f"任务创建成功: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"创建任务失败: {e}")
            return False
    
    async def _start_task(self, task_id: str):
        """启动任务"""
        try:
            task = self.tasks[task_id]
            rtsp_handler = self.rtsp_handlers[task_id]
            event_generator = self.event_generators[task_id]

            # 更新任务状态
            task.status = TaskStatus.RUNNING
            task.start_time = datetime.now()

            # 获取编排特定的告警间隔
            orchestration_type = "default"
            alert_interval = self.screenshot_interval  # 默认使用截图间隔

            if task and hasattr(task, 'algorithm_config'):
                orchestration_type = task.algorithm_config.orchestration_type
                alert_interval = config.get_alert_interval(orchestration_type)

            # 设置告警间隔到任务对象
            task.alert_interval = alert_interval
            task.last_alert_time = None

            # 启动RTSP流处理（包含告警逻辑）
            rtsp_handler.start_with_alert_logic(alert_interval, event_generator)

            logger.info(f"任务已启动: {task_id}, 告警间隔: {alert_interval}秒, 编排类型: {orchestration_type}")

        except Exception as e:
            logger.error(f"启动任务失败: {e}")
            if task_id in self.tasks:
                self.tasks[task_id].status = TaskStatus.ERROR
    

    async def delete_task(self, task_id: str) -> bool:
        """删除任务"""
        try:
            if task_id not in self.tasks:
                logger.warning(f"任务不存在: {task_id}")
                return False
            
            # 停止定时任务
            job_id = f"screenshot_{task_id}"
            try:
                self.scheduler.remove_job(job_id)
            except Exception:
                pass
            
            # 停止RTSP处理
            if task_id in self.rtsp_handlers:
                self.rtsp_handlers[task_id].stop()
                del self.rtsp_handlers[task_id]
            
            # 删除事件生成器
            if task_id in self.event_generators:
                del self.event_generators[task_id]
            
            # 更新任务状态
            task = self.tasks[task_id]
            if task.status == TaskStatus.RUNNING:
                self.stats["active_tasks"] -= 1
                self.stats["completed_tasks"] += 1
            
            task.status = TaskStatus.STOPPED
            
            # 删除任务
            del self.tasks[task_id]
            
            logger.info(f"任务已删除: {task_id}")
            return True
            
        except Exception as e:
            logger.error(f"删除任务失败: {e}")
            return False
    
    def get_task(self, task_id: str) -> Optional[Task]:
        """获取任务"""
        return self.tasks.get(task_id)
    
    def list_tasks(self) -> List[Task]:
        """获取任务列表"""
        return list(self.tasks.values())
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        # 更新活跃任务数
        active_count = sum(1 for task in self.tasks.values() if task.status == TaskStatus.RUNNING)
        self.stats["active_tasks"] = active_count

        return self.stats.copy()

    def get_all_tasks(self) -> Dict[str, Task]:
        """获取所有任务字典"""
        return self.tasks.copy()

    def is_rtsp_connected(self, task_id: str) -> bool:
        """检查RTSP连接状态"""
        rtsp_handler = self.rtsp_handlers.get(task_id)
        if rtsp_handler:
            return rtsp_handler.connected
        return False

    def get_alert_interval(self, task_id: str) -> Optional[int]:
        """获取任务的告警间隔"""
        task = self.tasks.get(task_id)
        if task and hasattr(task, 'algorithm_config'):
            orchestration_type = task.algorithm_config.orchestration_type
            return config.get_alert_interval(orchestration_type)
        return None

    def get_task_health(self, task_id: str) -> Dict[str, Any]:
        """获取任务健康状态"""
        task = self.tasks.get(task_id)
        if not task:
            return {"exists": False}

        rtsp_connected = self.is_rtsp_connected(task_id)
        alert_interval = self.get_alert_interval(task_id)

        # 检查最后事件时间，判断是否正常产生告警
        last_event_time = task.last_event_time
        is_generating_alerts = False
        if last_event_time and alert_interval:
            from datetime import datetime, timedelta
            time_since_last_event = (datetime.now() - last_event_time).total_seconds()
            # 如果超过告警间隔的2倍时间没有产生事件，认为异常
            is_generating_alerts = time_since_last_event <= (alert_interval * 2)

        return {
            "exists": True,
            "status": task.status.value,
            "rtspConnected": rtsp_connected,
            "alertInterval": alert_interval,
            "isGeneratingAlerts": is_generating_alerts,
            "lastEventTime": last_event_time.isoformat() if last_event_time else None,
            "eventCount": task.event_count
        }
    
    async def stop_all_tasks(self):
        """停止所有任务"""
        logger.info("正在停止所有任务...")
        
        task_ids = list(self.tasks.keys())
        for task_id in task_ids:
            await self.delete_task(task_id)
        
        # 停止调度器
        if self.scheduler.running:
            self.scheduler.shutdown()
        
        logger.info("所有任务已停止")


# 全局任务管理器实例
task_manager = TaskManager()
