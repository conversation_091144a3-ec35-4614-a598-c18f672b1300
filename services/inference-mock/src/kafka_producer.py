"""
Kafka事件推送模块
"""
import json
import asyncio
from typing import Optional, Dict, Any
from kafka import KafkaProducer
from kafka.errors import KafkaError
from loguru import logger
from .config import config
from .models import AtomicEventInstance


class KafkaEventProducer:
    """Kafka事件生产者"""
    
    def __init__(self):
        self.kafka_config = config.get("kafka", {})
        self.producer: Optional[KafkaProducer] = None
        self.connected = False
        
        # Kafka配置
        self.bootstrap_servers = self.kafka_config.get("bootstrap_servers", ["localhost:9092"])
        self.topic = self.kafka_config.get("topic", "vision-events")
        self.client_id = self.kafka_config.get("client_id", "inference-mock-1")
        self.acks = self.kafka_config.get("acks", 1)
        self.retries = self.kafka_config.get("retries", 3)
        self.batch_size = self.kafka_config.get("batch_size", 16384)
        self.linger_ms = self.kafka_config.get("linger_ms", 10)
        self.buffer_memory = self.kafka_config.get("buffer_memory", 33554432)
        
        # 统计信息
        self.stats = {
            "messages_sent": 0,
            "messages_failed": 0,
            "last_error": None,
            "connected": False,
            "connection_attempts": 0
        }
    
    def connect(self) -> bool:
        """连接到Kafka"""
        try:
            self.stats["connection_attempts"] += 1
            logger.info(f"正在连接Kafka: {self.bootstrap_servers}")
            
            self.producer = KafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                client_id=self.client_id,
                acks=self.acks,
                retries=self.retries,
                batch_size=self.batch_size,
                linger_ms=self.linger_ms,
                buffer_memory=self.buffer_memory,
                value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
                key_serializer=lambda k: k.encode('utf-8') if k else None,
                # 添加连接和超时配置
                request_timeout_ms=30000,  # 30秒请求超时
                connections_max_idle_ms=300000,  # 5分钟连接空闲超时
                max_block_ms=10000,  # 10秒最大阻塞时间
                metadata_max_age_ms=300000,  # 5分钟元数据最大存活时间
                # 安全配置，避免连接到不可达的broker
                security_protocol='PLAINTEXT'
            )
            
            # 测试连接
            metadata = self.producer.bootstrap_connected()
            if metadata:
                self.connected = True
                self.stats["connected"] = True
                logger.info(f"Kafka连接成功: {self.bootstrap_servers}")
                return True
            else:
                logger.warning("Kafka连接失败")
                return False
                
        except Exception as e:
            self.stats["last_error"] = str(e)
            logger.error(f"Kafka连接异常: {e}")
            return False
    
    def disconnect(self):
        """断开Kafka连接"""
        if self.producer:
            try:
                self.producer.flush()
                self.producer.close()
                logger.info("Kafka连接已断开")
            except Exception as e:
                logger.error(f"断开Kafka连接异常: {e}")
            finally:
                self.producer = None
                self.connected = False
                self.stats["connected"] = False
    
    def send_event(self, event: AtomicEventInstance, key: Optional[str] = None) -> bool:
        """发送事件到Kafka"""
        if not self.connected or not self.producer:
            logger.warning("Kafka未连接，尝试重新连接...")
            if not self.connect():
                return False
        
        try:
            # 使用deviceId作为key，确保同一设备的事件有序
            if key is None:
                key = event.deviceId
            
            # 转换为字典
            event_dict = event.dict()
            
            logger.debug(f"发送事件到Kafka: topic={self.topic}, key={key}")
            
            # 异步发送
            future = self.producer.send(
                topic=self.topic,
                key=key,
                value=event_dict
            )
            
            # 添加回调
            future.add_callback(self._on_send_success)
            future.add_errback(self._on_send_error)
            
            # 立即刷新（可选，用于测试）
            # self.producer.flush()
            
            self.stats["messages_sent"] += 1
            logger.info(f"事件已发送: eventId={event.atomicEventInstanceId}, deviceId={event.deviceId}")
            return True
            
        except KafkaError as e:
            self.stats["messages_failed"] += 1
            self.stats["last_error"] = str(e)
            logger.error(f"Kafka发送失败: {e}")
            return False
        except Exception as e:
            self.stats["messages_failed"] += 1
            self.stats["last_error"] = str(e)
            logger.error(f"发送事件异常: {e}")
            return False
    
    def _on_send_success(self, record_metadata):
        """发送成功回调"""
        logger.debug(f"消息发送成功: topic={record_metadata.topic}, "
                    f"partition={record_metadata.partition}, "
                    f"offset={record_metadata.offset}")
    
    def _on_send_error(self, exception):
        """发送失败回调"""
        self.stats["messages_failed"] += 1
        self.stats["last_error"] = str(exception)
        logger.error(f"消息发送失败: {exception}")
    
    def flush(self):
        """刷新缓冲区"""
        if self.producer:
            try:
                self.producer.flush()
                logger.debug("Kafka缓冲区已刷新")
            except Exception as e:
                logger.error(f"刷新Kafka缓冲区异常: {e}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
    
    def is_connected(self) -> bool:
        """检查连接状态"""
        return self.connected and self.producer is not None
    
    async def start(self):
        """启动Kafka生产者"""
        logger.info("启动Kafka事件生产者...")
        success = self.connect()
        if success:
            logger.info("Kafka事件生产者启动成功")
        else:
            logger.warning("Kafka事件生产者启动失败")
        return success
    
    async def stop(self):
        """停止Kafka生产者"""
        logger.info("停止Kafka事件生产者...")
        self.disconnect()
        logger.info("Kafka事件生产者已停止")


# 全局Kafka生产者实例
kafka_producer = KafkaEventProducer()
