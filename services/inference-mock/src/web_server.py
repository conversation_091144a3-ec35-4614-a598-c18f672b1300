"""
Web服务器模块
"""
import time
from typing import Dict, Any, Optional
from fastapi import FastAPI, HTTPException, status
from fastapi.responses import JSONResponse
from loguru import logger

from .models import TaskRequest, HealthResponse, TaskResponse, TaskStatus
from .task_manager import task_manager
from .kafka_producer import kafka_producer


def create_app(lifespan=None) -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title="Inference Mock Service",
        description="模拟推理服务，用于测试CV推理服务调度器",
        version="1.0.0",
        lifespan=lifespan
    )
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查接口"""
        try:
            # 获取各组件状态
            task_stats = task_manager.get_stats()
            kafka_stats = kafka_producer.get_stats()
            
            # 判断整体状态
            status_value = "UP"
            details = {
                "active_tasks": task_stats.get("active_tasks", 0),
                "total_tasks": task_stats.get("total_tasks", 0),
                "kafka_connected": kafka_stats.get("connected", False),
                "kafka_messages_sent": kafka_stats.get("messages_sent", 0),
                "kafka_messages_failed": kafka_stats.get("messages_failed", 0),
                "last_screenshot": task_stats.get("last_screenshot_time"),
                "last_event": task_stats.get("last_event_time")
            }
            
            # 如果Kafka连接失败，标记为DOWN
            if not kafka_stats.get("connected", False):
                status_value = "DOWN"
                details["kafka_error"] = kafka_stats.get("last_error")
            
            return HealthResponse(
                status=status_value,
                details=details,
                timestamp=int(time.time() * 1000)
            )
            
        except Exception as e:
            logger.error(f"健康检查异常: {e}")
            return HealthResponse(
                status="DOWN",
                details={"error": str(e)},
                timestamp=int(time.time() * 1000)
            )
    
    @app.post("/api/v1/tasks", response_model=Dict[str, Any])
    async def create_task(task_request: TaskRequest):
        """创建任务接口"""
        try:
            task_id = task_request.task_request.task_id
            device_id = task_request.task_request.device.device_id
            logger.info(f"收到任务创建请求: taskId={task_id}, deviceId={device_id}")

            # 创建任务
            success = await task_manager.create_task(task_request)

            if success:
                return {
                    "success": True,
                    "message": "任务创建成功",
                    "taskId": task_id
                }
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="任务创建失败"
                )
                
        except Exception as e:
            logger.error(f"创建任务异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"创建任务异常: {str(e)}"
            )
    
    @app.get("/api/v1/tasks/{task_id}", response_model=TaskResponse)
    async def get_task(task_id: str):
        """获取任务状态接口"""
        try:
            task = task_manager.get_task(task_id)
            
            if not task:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"任务不存在: {task_id}"
                )
            
            return TaskResponse(
                task_id=task.task_id,
                status=task.status,
                device_id=task.device_id,
                rtsp_url=task.rtsp_url,
                start_time=task.start_time,
                last_event_time=task.last_event_time,
                event_count=task.event_count,
                screenshots=task.screenshots
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取任务异常: {str(e)}"
            )

    @app.get("/api/v1/tasks/{task_id}/events", response_model=Dict[str, Any])
    async def get_task_events(task_id: str):
        """获取任务事件接口"""
        try:
            task = task_manager.get_task(task_id)
            if not task:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="任务不存在"
                )

            # 模拟返回一些事件数据
            events = []
            if task.event_count > 0:
                # 这里可以从实际的事件存储中获取事件
                # 目前返回模拟数据
                events = [
                    {
                        "eventId": f"event_{task_id}_{i}",
                        "timestamp": task.start_time.isoformat() if task.start_time else None,
                        "eventType": "ALERT",
                        "confidence": 0.85
                    }
                    for i in range(min(task.event_count, 10))  # 最多返回10个事件
                ]

            return {
                "task_id": task_id,
                "total_events": task.event_count,
                "events": events
            }

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"获取任务事件异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取任务事件异常: {e}"
            )

    @app.delete("/api/v1/tasks/{task_id}")
    async def delete_task(task_id: str):
        """删除任务接口"""
        try:
            success = await task_manager.delete_task(task_id)
            
            if success:
                return {"success": True, "message": "任务删除成功"}
            else:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"任务不存在: {task_id}"
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"删除任务异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"删除任务异常: {str(e)}"
            )
    
    @app.get("/api/v1/tasks")
    async def list_tasks():
        """获取任务列表接口"""
        try:
            tasks = task_manager.list_tasks()
            
            task_list = []
            for task in tasks:
                task_list.append({
                    "task_id": task.task_id,
                    "device_id": task.device_id,
                    "status": task.status,
                    "start_time": task.start_time.isoformat() if task.start_time else None,
                    "event_count": task.event_count
                })
            
            return {
                "tasks": task_list,
                "total": len(task_list)
            }
            
        except Exception as e:
            logger.error(f"获取任务列表异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取任务列表异常: {str(e)}"
            )
    
    @app.get("/api/v1/stats")
    async def get_stats():
        """获取服务统计信息"""
        try:
            task_stats = task_manager.get_stats()
            kafka_stats = kafka_producer.get_stats()
            
            return {
                "task_manager": task_stats,
                "kafka_producer": kafka_stats,
                "timestamp": int(time.time() * 1000)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息异常: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"获取统计信息异常: {str(e)}"
            )
    
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "service": "Inference Mock Service",
            "version": "1.0.0",
            "status": "running",
            "endpoints": {
                "health": "/health",
                "tasks": "/api/v1/tasks",
                "stats": "/api/v1/stats",
                "screenshots": "/screenshots"
            }
        }
    
    return app
