"""
事件生成模块
"""
import os
import cv2
import uuid
import random
import time
import glob
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from loguru import logger
from .config import config
from .models import (
    AtomicEventInstance, EntityInstance, BoundingBox,
    EntityAttribute, RelationNameValue, TaskInfo, PartOfRelation
)
from .kafka_producer import kafka_producer


class EventGenerator:
    """事件生成器"""

    def __init__(self, task_id: str, device_id: str, event_type_id: str = None, orchestration_id: str = None, orchestration_type: str = None):
        self.task_id = task_id
        self.device_id = device_id
        self.event_type_id = event_type_id or f"event_type_{task_id}"
        self.orchestration_id = orchestration_id or f"orch_{task_id}"
        self.orchestration_type = orchestration_type or config.get_current_orchestration()

        # 获取编排特定配置
        self.orchestration_config = config.get_orchestration_config(self.orchestration_type)
        self.screenshot_config = config.get_screenshot_config()
        self.mock_config = config.get_mock_events_config()
        self.server_config = config.get_server_config()

        # 截图配置
        self.output_dir = self.screenshot_config.get("output_dir", "./screenshots")
        self.format = self.screenshot_config.get("format", "jpg")
        self.quality = self.screenshot_config.get("quality", 85)
        self.max_files = self.screenshot_config.get("max_files", 100)

        # 编排特定的模拟事件配置
        self.detection_probability = self.orchestration_config.get("detection_probability", self.mock_config.get("detection_probability", 0.7))
        self.event_types = self.orchestration_config.get("event_types", ["HELMET_MISSING"])
        self.entity_types = self.orchestration_config.get("entity_types", ["Person"])
        self.tracking_enabled = self.orchestration_config.get("tracking_enabled", True)
        self.confidence_range = self.orchestration_config.get("confidence_range", [0.6, 0.95])
        self.max_objects_per_frame = self.orchestration_config.get("max_objects_per_frame", 3)

        # 跟踪相关状态（仅YOLO_TRACKING_CLIP使用）
        self._track_counter = 1000
        self._active_tracks = {}

        # 兼容旧配置
        self.classes = self.mock_config.get("classes", ["person", "car", "bicycle"])
        self.confidence_range = self.mock_config.get("confidence_range", [0.6, 0.95])
        self.max_objects = self.mock_config.get("max_objects_per_frame", 5)
        self.violation_types = self.mock_config.get("violation_types", ["OBJECT_DETECTION"])
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 统计信息
        self.stats = {
            "screenshots_taken": 0,
            "events_generated": 0,
            "events_sent": 0,
            "last_screenshot_time": None,
            "last_event_time": None
        }

    def capture_rtsp_screenshot(self, rtsp_handler, timestamp: datetime) -> Optional[str]:
        """从RTSP流捕获真实截图"""
        try:
            # 从RTSP流捕获帧
            frame = rtsp_handler.capture_frame()
            if frame is None:
                logger.warning(f"无法从RTSP流捕获帧: {rtsp_handler.rtsp_url}")
                return None

            # 保存截图
            return self.save_screenshot(frame, timestamp)

        except Exception as e:
            logger.error(f"RTSP截图捕获失败: {e}")
            return None

    def save_screenshot(self, frame, timestamp: datetime) -> Optional[str]:
        """保存截图"""
        try:
            # 生成文件名
            timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
            filename = f"{self.task_id}_{timestamp_str}.{self.format}"
            filepath = os.path.join(self.output_dir, filename)
            
            # 保存图片
            if self.format.lower() == 'jpg':
                cv2.imwrite(filepath, frame, [cv2.IMWRITE_JPEG_QUALITY, self.quality])
            else:
                cv2.imwrite(filepath, frame)
            
            self.stats["screenshots_taken"] += 1
            self.stats["last_screenshot_time"] = timestamp.isoformat()
            
            logger.info(f"截图已保存: {filepath}")
            
            # 清理旧文件
            self._cleanup_old_files()
            
            return filename
            
        except Exception as e:
            logger.error(f"保存截图失败: {e}")
            return None
    
    def _cleanup_old_files(self):
        """清理旧的截图文件"""
        try:
            files = []
            for f in os.listdir(self.output_dir):
                if f.startswith(self.task_id) and f.endswith(f".{self.format}"):
                    filepath = os.path.join(self.output_dir, f)
                    files.append((filepath, os.path.getctime(filepath)))
            
            # 按创建时间排序
            files.sort(key=lambda x: x[1])
            
            # 删除超出限制的文件
            while len(files) > self.max_files:
                old_file = files.pop(0)
                try:
                    os.remove(old_file[0])
                    logger.debug(f"删除旧截图: {old_file[0]}")
                except Exception as e:
                    logger.warning(f"删除文件失败: {e}")
                    
        except Exception as e:
            logger.error(f"清理文件异常: {e}")
    
    def generate_mock_event(self, screenshot_filename: str, timestamp: datetime) -> Optional[AtomicEventInstance]:
        """生成模拟事件"""
        if not self.mock_config.get("enabled", True):
            return None

        # 随机决定是否生成事件
        if random.random() > self.detection_probability:
            return None

        try:
            # 根据编排类型生成不同的事件
            if self.orchestration_type == "YOLO_TRACKING_CLIP":
                return self._generate_yolo_tracking_event(screenshot_filename, timestamp)
            elif self.orchestration_type == "OVIT_CLIP":
                return self._generate_ovit_event(screenshot_filename, timestamp)
            else:
                return self._generate_default_event(screenshot_filename, timestamp)

        except Exception as e:
            logger.error(f"生成模拟事件失败: {e}")
            return None

    def _generate_yolo_tracking_event(self, screenshot_filename: str, timestamp: datetime) -> AtomicEventInstance:
        """生成YOLO+跟踪+CLIP编排的事件"""
        # 生成事件ID
        event_instance_id = f"aei_yolo_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        frame_id = f"frame_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 生成带跟踪的实体
        entities = self._generate_tracked_entities()

        # 构建图片URI
        port = self.server_config.get("port", 8081)
        image_uri = f"http://localhost:{port}/screenshots/{screenshot_filename}"

        # 选择事件类型
        event_type = random.choice(self.event_types) if self.event_types else "HELMET_MISSING"

        # 创建任务信息
        task_info = TaskInfo(
            taskId=self.task_id,
            eventTypeId=event_type,
            orchestrationId=self.orchestration_id,
            orchestrationType="YOLO_TRACKING_CLIP",
            taskLevel=random.choice(["LOW", "MEDIUM", "HIGH"]),
            deviceName=f"Mock Camera {self.device_id}",
            frameId=frame_id,
            externalInfo={
                "taskName": f"YOLO跟踪任务 {self.task_id}",
                "algorithmChain": ["yolo_detection", "bytetrack_tracking", "clip_classification"],
                "mockGenerated": True,
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "customFields": {
                    "taskId": self.task_id,
                    "screenshotFile": screenshot_filename,
                    "trackingEnabled": True
                }
            }
        )

        # 创建事件
        event = AtomicEventInstance(
            atomicEventInstanceId=event_instance_id,
            eventTypeId=event_type,
            taskId=self.task_id,
            deviceId=self.device_id,
            timestamp=int(timestamp.timestamp() * 1000),  # 毫秒时间戳
            imageUri=image_uri,
            entities=entities,
            relationEntities=[],
            taskInfo=task_info
        )

        self.stats["events_generated"] += 1
        self.stats["last_event_time"] = timestamp.isoformat()

        return event

    def _generate_ovit_event(self, screenshot_filename: str, timestamp: datetime) -> AtomicEventInstance:
        """生成O-VIT+CLIP编排的事件"""
        # 生成事件ID
        event_instance_id = f"aei_ovit_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        frame_id = f"frame_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 生成多样化实体（万物检测）
        entities = self._generate_diverse_entities()

        # 构建图片URI
        port = self.server_config.get("port", 8081)
        image_uri = f"http://localhost:{port}/screenshots/{screenshot_filename}"

        # 选择事件类型
        event_type = random.choice(self.event_types)

        # 创建任务信息
        task_info = TaskInfo(
            taskId=self.task_id,
            eventTypeId=event_type,
            orchestrationId=self.orchestration_id,
            orchestrationType="OVIT_CLIP",
            taskLevel=random.choice(["LOW", "MEDIUM", "HIGH"]),
            deviceName=f"Mock Camera {self.device_id}",
            frameId=frame_id,
            externalInfo={
                "taskName": f"O-VIT万物检测任务 {self.task_id}",
                "algorithmChain": ["ovit_detection", "clip_classification"],
                "mockGenerated": True,
                "orchestrationType": "OVIT_CLIP",
                "customFields": {
                    "taskId": self.task_id,
                    "screenshotFile": screenshot_filename,
                    "trackingEnabled": False,
                    "universalDetection": True
                }
            }
        )

        # 创建事件
        event = AtomicEventInstance(
            atomicEventInstanceId=event_instance_id,
            eventTypeId=event_type,
            taskId=self.task_id,
            deviceId=self.device_id,
            timestamp=int(timestamp.timestamp() * 1000),  # 毫秒时间戳
            imageUri=image_uri,
            entities=entities,
            relationEntities=[],
            taskInfo=task_info
        )

        self.stats["events_generated"] += 1
        self.stats["last_event_time"] = timestamp.isoformat()

        return event

    def _generate_default_event(self, screenshot_filename: str, timestamp: datetime) -> AtomicEventInstance:
        """生成默认事件（兼容旧版本）"""
        # 生成事件ID
        event_instance_id = f"aei_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"
        frame_id = f"frame_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 生成实体
        entities = self._generate_entities()

        # 构建图片URI
        port = self.server_config.get("port", 8081)
        image_uri = f"http://localhost:{port}/screenshots/{screenshot_filename}"

        # 选择事件类型
        event_type = random.choice(self.event_types) if self.event_types else "DEFAULT_EVENT"

        # 创建任务信息
        task_info = TaskInfo(
            taskId=self.task_id,
            eventTypeId=event_type,
            orchestrationId=self.orchestration_id,
            orchestrationType=self.orchestration_type,
            taskLevel=random.choice(["LOW", "MEDIUM", "HIGH"]),
            deviceName=f"Mock Camera {self.device_id}",
            frameId=frame_id,
            externalInfo={
                "taskName": f"默认任务 {self.task_id}",
                "algorithmChain": ["default_detection"],
                "mockGenerated": True,
                "orchestrationType": self.orchestration_type,
                "customFields": {
                    "taskId": self.task_id,
                    "screenshotFile": screenshot_filename
                }
            }
        )

        # 创建事件
        event = AtomicEventInstance(
            atomicEventInstanceId=event_instance_id,
            eventTypeId=event_type,
            taskId=self.task_id,
            deviceId=self.device_id,
            timestamp=int(timestamp.timestamp() * 1000),  # 毫秒时间戳
            imageUri=image_uri,
            entities=entities,
            relationEntities=[],
            taskInfo=task_info
        )

        self.stats["events_generated"] += 1
        self.stats["last_event_time"] = timestamp.isoformat()

        logger.info(f"生成模拟事件: {event_instance_id}, 实体数量: {len(entities)}")
        return event

    def _generate_tracked_entities(self) -> List[EntityInstance]:
        """生成带跟踪信息的实体（YOLO_TRACKING_CLIP）"""
        entities = []
        num_objects = random.randint(1, self.max_objects_per_frame)

        for i in range(num_objects):
            # 主要检测人员
            entity_type = "Person"

            # 获取或创建跟踪ID
            track_id = self._get_or_create_track_id()

            # 生成随机边界框
            bbox = self._generate_bbox()

            # 生成实体
            entity = EntityInstance(
                entityInstanceId=track_id,
                entityType=entity_type,
                algorithmId="yolo_detection",
                boundingBox=bbox,
                entityAttributes={
                    "helmet": EntityAttribute(
                        attributeName="helmet",
                        attributeValue=random.choice([True, False]),
                        confidence=random.uniform(*self.confidence_range),
                        algorithmId="clip_classification"
                    ),
                    "age": EntityAttribute(
                        attributeName="age",
                        attributeValue=random.randint(20, 60),
                        confidence=random.uniform(0.7, 0.9),
                        algorithmId="age_estimation"
                    ),
                    "gender": EntityAttribute(
                        attributeName="gender",
                        attributeValue=random.choice(["MALE", "FEMALE"]),
                        confidence=random.uniform(0.8, 0.95),
                        algorithmId="gender_classification"
                    ),
                    "trackingQuality": EntityAttribute(
                        attributeName="trackingQuality",
                        attributeValue=random.uniform(0.8, 0.98),
                        confidence=1.0,
                        algorithmId="bytetrack_tracking"
                    )
                },
                entityRelationship={},
                externalInfo={
                    "trackId": track_id,
                    "trackingDuration": random.randint(5, 30),
                    "trackingStable": True,
                    "detectionMethod": "yolo_tracking"
                }
            )
            entities.append(entity)

        return entities

    def _generate_diverse_entities(self) -> List[EntityInstance]:
        """生成多样化实体（OVIT_CLIP万物检测）"""
        entities = []
        num_objects = random.randint(1, self.max_objects_per_frame)

        for i in range(num_objects):
            # 随机选择实体类型
            entity_type = random.choice(self.entity_types)

            # 生成随机边界框
            bbox = self._generate_bbox()

            # 根据实体类型生成不同属性
            attributes = self._generate_ovit_attributes(entity_type)

            # 生成实体
            entity = EntityInstance(
                entityInstanceId=f"ovit_{uuid.uuid4().hex[:8]}",
                entityType=entity_type,
                algorithmId="ovit_detection",
                boundingBox=bbox,
                entityAttributes=attributes,
                entityRelationship={},
                externalInfo={
                    "detectionMethod": "ovit_universal",
                    "sceneContext": random.choice(["indoor", "outdoor", "industrial"]),
                    "temporalStability": random.uniform(0.7, 0.95),
                    "universalDetection": True
                }
            )
            entities.append(entity)

        return entities

    def _get_or_create_track_id(self) -> str:
        """获取或创建跟踪ID"""
        # 简单的跟踪模拟：70%概率使用现有轨迹，30%创建新轨迹
        if self._active_tracks and random.random() < 0.7:
            return random.choice(list(self._active_tracks.keys()))
        else:
            track_id = f"track_{self._track_counter}"
            self._track_counter += 1
            self._active_tracks[track_id] = time.time()

            # 清理过期轨迹（超过60秒）
            current_time = time.time()
            expired_tracks = [tid for tid, t in self._active_tracks.items() if current_time - t > 60]
            for tid in expired_tracks:
                del self._active_tracks[tid]

            return track_id

    def _generate_bbox(self) -> BoundingBox:
        """生成随机边界框"""
        x = random.randint(50, 500)
        y = random.randint(50, 300)
        width = random.randint(50, 200)
        height = random.randint(80, 250)

        return BoundingBox(x=x, y=y, width=width, height=height)

    def _generate_ovit_attributes(self, entity_type: str) -> Dict[str, EntityAttribute]:
        """根据实体类型生成O-VIT特定属性"""
        attributes = {}

        if entity_type == "Person":
            attributes.update({
                "age": EntityAttribute(
                    attributeName="age",
                    attributeValue=random.randint(18, 70),
                    confidence=random.uniform(0.6, 0.9),
                    algorithmId="clip_classification"
                ),
                "clothing": EntityAttribute(
                    attributeName="clothing",
                    attributeValue=random.choice(["uniform", "casual", "formal"]),
                    confidence=random.uniform(0.7, 0.9),
                    algorithmId="clip_classification"
                )
            })
        elif entity_type == "Vehicle":
            attributes.update({
                "vehicleType": EntityAttribute(
                    attributeName="vehicleType",
                    attributeValue=random.choice(["car", "truck", "bus", "motorcycle"]),
                    confidence=random.uniform(0.8, 0.95),
                    algorithmId="clip_classification"
                ),
                "color": EntityAttribute(
                    attributeName="color",
                    attributeValue=random.choice(["red", "blue", "white", "black", "gray"]),
                    confidence=random.uniform(0.7, 0.9),
                    algorithmId="clip_classification"
                )
            })
        elif entity_type == "Object":
            attributes.update({
                "objectType": EntityAttribute(
                    attributeName="objectType",
                    attributeValue=random.choice(["bag", "box", "tool", "equipment"]),
                    confidence=random.uniform(0.6, 0.85),
                    algorithmId="clip_classification"
                ),
                "abandoned": EntityAttribute(
                    attributeName="abandoned",
                    attributeValue=random.choice([True, False]),
                    confidence=random.uniform(0.7, 0.9),
                    algorithmId="clip_classification"
                )
            })
        elif entity_type == "Animal":
            attributes.update({
                "animalType": EntityAttribute(
                    attributeName="animalType",
                    attributeValue=random.choice(["dog", "cat", "bird"]),
                    confidence=random.uniform(0.8, 0.95),
                    algorithmId="clip_classification"
                )
            })
        elif entity_type == "Tool":
            attributes.update({
                "toolType": EntityAttribute(
                    attributeName="toolType",
                    attributeValue=random.choice(["hammer", "wrench", "drill", "saw"]),
                    confidence=random.uniform(0.7, 0.9),
                    algorithmId="clip_classification"
                )
            })

        return attributes

    def _generate_entities(self) -> List[EntityInstance]:
        """生成模拟实体"""
        entities = []
        num_objects = random.randint(1, self.max_objects)
        
        for i in range(num_objects):
            # 随机选择类别
            entity_type = random.choice(self.classes).title()
            if entity_type.lower() == "person":
                entity_type = "Person"
            elif entity_type.lower() in ["car", "truck", "bus"]:
                entity_type = "MotorVehicle"
            elif entity_type.lower() in ["bicycle"]:
                entity_type = "NonMotorVehicle"
            
            # 生成随机边界框
            x = random.randint(50, 500)
            y = random.randint(50, 300)
            width = random.randint(50, 200)
            height = random.randint(80, 250)
            
            # 生成置信度
            confidence = random.uniform(self.confidence_range[0], self.confidence_range[1])
            
            # 创建实体
            entity = EntityInstance(
                entityInstanceId=f"track_{uuid.uuid4().hex[:8]}",
                entityType=entity_type,
                algorithmId=self._get_algorithm_id_for_entity_type(entity_type),
                boundingBox=BoundingBox(x=x, y=y, width=width, height=height),
                entityAttributes=self._generate_entity_attributes(entity_type, confidence),
                externalInfo={
                    "trackId": f"track_{uuid.uuid4().hex[:8]}",
                    "quality": round(confidence, 2),
                    "mockGenerated": True
                }
            )
            
            entities.append(entity)
        
        return entities

    def _get_algorithm_id_for_entity_type(self, entity_type: str) -> str:
        """根据实体类型获取算法ID"""
        # v2.0: 统一使用"detection"算法ID，具体类型由detection_type配置决定
        if entity_type in ["Person", "MotorVehicle", "NonMotorVehicle"]:
            return "detection"
        else:
            return "detection"  # 默认也使用统一的detection

    def _generate_entity_attributes(self, entity_type: str, base_confidence: float) -> Dict[str, EntityAttribute]:
        """生成实体属性"""
        attributes = {}
        
        if entity_type == "Person":
            # 人员属性
            attributes["age"] = EntityAttribute(
                attributeName="age",
                attributeValue=random.randint(18, 65),
                confidence=round(random.uniform(0.7, 0.95), 2),
                algorithmId="age_estimation"
            )

            attributes["gender"] = EntityAttribute(
                attributeName="gender",
                attributeValue=random.choice(["MALE", "FEMALE"]),
                confidence=round(random.uniform(0.8, 0.95), 2),
                algorithmId="gender_classification"
            )

            # 随机添加安全帽属性
            if random.random() > 0.5:
                attributes["helmet"] = EntityAttribute(
                    attributeName="helmet",
                    attributeValue=random.choice([True, False]),
                    confidence=round(base_confidence, 2),
                    algorithmId="helmet_detection"
                )
        
        elif entity_type == "MotorVehicle":
            # 车辆属性
            attributes["vehicleType"] = EntityAttribute(
                attributeName="vehicleType",
                attributeValue=random.choice(["car", "truck", "bus"]),
                confidence=round(base_confidence, 2),
                algorithmId="vehicle_type_classification"
            )

            attributes["color"] = EntityAttribute(
                attributeName="color",
                attributeValue=random.choice(["red", "blue", "white", "black", "gray"]),
                confidence=round(random.uniform(0.7, 0.9), 2),
                algorithmId="vehicle_color_classification"
            )
        
        return attributes
    
    async def process_frame_and_generate_event(self, frame, timestamp: datetime):
        """处理帧并生成事件"""
        if frame is None:
            return
        
        # 保存截图
        screenshot_filename = self.save_screenshot(frame, timestamp)
        if not screenshot_filename:
            return
        
        # 生成模拟事件
        event = self.generate_mock_event(screenshot_filename, timestamp)
        if not event:
            return
        
        # 发送到Kafka
        success = kafka_producer.send_event(event)
        if success:
            self.stats["events_sent"] += 1
            logger.info(f"事件已发送到Kafka: {event.atomicEventInstanceId}")
        else:
            logger.error(f"事件发送失败: {event.atomicEventInstanceId}")
    
    def _generate_event_sync(self, frame, timestamp: datetime, screenshot_filename: str = None) -> Optional[dict]:
        """同步版本的事件生成方法"""
        try:
            # 如果没有提供截图文件名，生成一个默认的
            if not screenshot_filename:
                timestamp_str = timestamp.strftime("%Y%m%d_%H%M%S")
                screenshot_filename = f"{self.task_id}_{timestamp_str}.jpg"

            # 根据编排类型生成不同的事件
            if self.orchestration_type == "YOLO_TRACKING_CLIP":
                event = self._generate_yolo_tracking_event(screenshot_filename, timestamp)
            elif self.orchestration_type == "OVIT_CLIP":
                event = self._generate_ovit_event(screenshot_filename, timestamp)
            else:
                # 默认事件
                event = self._generate_default_event(screenshot_filename, timestamp)

            logger.info(f"同步事件生成成功: {event.atomicEventInstanceId}")
            return event

        except Exception as e:
            logger.error(f"同步事件生成失败: {e}")
            return None

    def _generate_default_event_dict(self, timestamp: datetime) -> dict:
        """生成默认事件"""
        event_id = f"aei_default_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:6]}"
        frame_id = f"frame_{timestamp.strftime('%Y%m%d_%H%M%S')}_{uuid.uuid4().hex[:8]}"

        # 生成默认实体
        entities = self._generate_entities()

        return AtomicEventInstance(
            atomicEventInstanceId=event_id,
            eventTypeId=random.choice(["PERSON_INTRUSION", "OBJECT_DETECTION", "MOTION_DETECTED"]),
            eventLevel="MEDIUM",
            eventTime=timestamp.isoformat(),
            entities=entities,
            deviceId=self.device_id,
            deviceName=f"Mock Camera {self.device_id}",
            frameId=frame_id,
            externalInfo={
                "taskName": f"默认检测任务 {self.task_id}",
                "algorithmChain": ["default_detection"],
                "mockGenerated": True,
                "orchestrationType": "DEFAULT",
                "customFields": {
                    "taskId": self.task_id,
                    "screenshotFile": None,
                    "trackingEnabled": False,
                    "detectionMethod": "default"
                }
            }
        )

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return self.stats.copy()
