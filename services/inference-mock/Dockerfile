FROM python:3.9-slim

# 设置工作目录
WORKDIR /app

# 安装系统依赖 - 移除 python3-opencv 避免冲突
RUN apt-get update && apt-get install -y \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgtk-3-0 \
    libgl1-mesa-glx \
    && rm -rf /var/lib/apt/lists/*

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/* 

# 复制requirements文件
COPY requirements.txt .

# 升级pip并安装Python依赖
RUN pip install --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# 复制源码
COPY . .

# 复制Docker配置文件作为默认配置
COPY config.docker.yaml config.yaml

# 创建必要的目录
RUN mkdir -p screenshots logs

# 暴露端口
EXPOSE 8081

# 设置环境变量
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# 启动命令
CMD ["python", "main.py"]
