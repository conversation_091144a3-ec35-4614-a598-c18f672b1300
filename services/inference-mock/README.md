# Inference Mock Service

一个模拟推理服务，用于测试和验证CV推理服务调度器的功能。该服务模拟真实的推理服务行为，包括服务注册、健康检查、RTSP拉流、定时截图和事件生成等功能。

## 功能特性

- ✅ **服务注册**: 自动向调度器注册服务
- ✅ **健康检查**: 提供健康检查接口
- ✅ **RTSP拉流**: 从RTSP地址拉取视频流
- ✅ **定时截图**: 每30秒截取一张图片
- ✅ **事件生成**: 基于截图生成模拟的推理事件
- ✅ **Kafka推送**: 将事件推送到Kafka消息队列
- ✅ **任务管理**: 接收和处理调度器分配的任务

## 技术栈

- **Python 3.9+**
- **FastAPI**: Web框架
- **OpenCV**: 视频处理和截图
- **Kafka**: 事件消息推送
- **APScheduler**: 定时任务调度
- **Docker**: 容器化部署

## 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd inference-mock

# 安装依赖
pip install -r requirements.txt
```

### 2. 配置文件

编辑 `config.yaml` 文件：

```yaml
server:
  host: "0.0.0.0"
  port: 8081

scheduler:
  url: "http://localhost:8080"

kafka:
  bootstrap_servers: ["localhost:9092"]
  topic: "vision-events"

service:
  name: "inference-mock-1"
  region: "default"
  gpu_type: "A10"
  max_quota: 10
```

### 3. 启动服务

```bash
# 直接运行
python main.py

# 或使用Docker
docker build -t inference-mock .
docker run -p 8081:8081 inference-mock
```

### 4. 使用Docker Compose

```bash
# 启动完整环境（包含Kafka）
docker-compose up -d

# 查看日志
docker-compose logs -f inference-mock-1
```

## API接口

### 健康检查

```http
GET /health
```

### 创建任务

```http
POST /api/v1/tasks
Content-Type: application/json

{
  "task_id": "task-001",
  "device_id": "camera-001",
  "rtsp_url": "rtsp://example.com/stream1",
  "algorithm_orchestration": {
    "algorithm_type": "OBJECT_DETECTION",
    "model_name": "yolo-v8",
    "model_version": "1.0.0",
    "input_format": "IMAGE",
    "output_format": "JSON"
  }
}
```

### 查询任务

```http
GET /api/v1/tasks/{task_id}
```

### 获取统计信息

```http
GET /api/v1/stats
```

## 事件输出协议

事件按照Vision Flow原子事件输出协议推送到Kafka：

```json
{
  "atomicEventInstanceId": "aei_20250719_143000_001",
  "atomicEventId": "object_detection_rule_001",
  "deviceId": "camera-001",
  "timestamp": 1642608600000,
  "imageUri": "http://inference-mock:8081/screenshots/task-001_20250719_143000.jpg",
  "entities": [
    {
      "entityInstanceId": "track_001",
      "entityType": "Person",
      "boundingBox": {
        "x": 100,
        "y": 100,
        "width": 150,
        "height": 200
      },
      "entityAttributes": {
        "helmet": {
          "attributeName": "helmet",
          "attributeValue": false,
          "score": 0.85,
          "algorithmId": "yolo-v8"
        }
      }
    }
  ],
  "externalInfo": {
    "ruleId": "mock_rule_001",
    "violationType": "OBJECT_DETECTION",
    "severity": "LOW"
  }
}
```

## 配置说明

### 服务配置

- `server.host`: 服务监听地址
- `server.port`: 服务监听端口
- `scheduler.url`: 调度器地址
- `service.name`: 服务名称

### RTSP配置

- `rtsp.connection_timeout`: 连接超时时间
- `rtsp.retry_interval`: 重试间隔
- `rtsp.max_retries`: 最大重试次数

### 截图配置

- `screenshot.interval`: 截图间隔（秒）
- `screenshot.output_dir`: 截图保存目录
- `screenshot.format`: 图片格式
- `screenshot.quality`: 图片质量

### Kafka配置

- `kafka.bootstrap_servers`: Kafka服务器地址
- `kafka.topic`: 事件推送主题
- `kafka.client_id`: 客户端ID

### 模拟事件配置

- `mock_events.enabled`: 是否启用事件生成
- `mock_events.detection_probability`: 检测概率
- `mock_events.classes`: 检测类别
- `mock_events.confidence_range`: 置信度范围

## 开发指南

### 项目结构

```
inference-mock/
├── src/
│   ├── __init__.py
│   ├── config.py          # 配置管理
│   ├── logger.py          # 日志配置
│   ├── models.py          # 数据模型
│   ├── web_server.py      # Web服务器
│   ├── service_registry.py # 服务注册
│   ├── kafka_producer.py  # Kafka生产者
│   ├── rtsp_handler.py    # RTSP处理
│   ├── event_generator.py # 事件生成
│   └── task_manager.py    # 任务管理
├── docs/
│   └── design.md          # 设计文档
├── config.yaml            # 配置文件
├── requirements.txt       # Python依赖
├── Dockerfile             # Docker镜像
├── docker-compose.yml     # Docker编排
└── main.py               # 主程序
```

### 扩展开发

1. **添加新的事件类型**: 修改 `event_generator.py`
2. **支持新的视频源**: 扩展 `rtsp_handler.py`
3. **自定义推理算法**: 实现新的事件生成逻辑
4. **添加新的API**: 扩展 `web_server.py`

## 故障排除

### 常见问题

1. **RTSP连接失败**
   - 检查RTSP URL是否正确
   - 确认网络连接正常
   - 查看日志中的错误信息

2. **Kafka连接失败**
   - 确认Kafka服务正在运行
   - 检查bootstrap_servers配置
   - 验证网络连接

3. **服务注册失败**
   - 确认调度器服务正在运行
   - 检查scheduler.url配置
   - 查看调度器日志

### 日志查看

```bash
# 查看应用日志
tail -f logs/inference-mock.log

# 查看Docker日志
docker logs -f inference-mock-1
```

## 快速启动

```bash
# 进入inference-mock目录
cd inference-mock

# 创建虚拟环境（推荐）
python3 -m venv venv
source venv/bin/activate  # Linux/Mac
# 或 venv\Scripts\activate  # Windows

# 安装依赖
pip install -r requirements.txt

# 修改配置文件（如果需要）
# 编辑 config.yaml，确保scheduler.url指向正确的地址
# scheduler:
#   url: "http://localhost:8080"  # 如果scheduler在不同端口，需要修改

# 启动服务
python main.py
```

## 许可证

MIT License
