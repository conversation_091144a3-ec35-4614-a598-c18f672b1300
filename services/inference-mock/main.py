#!/usr/bin/env python3
"""
Inference Mock Service 主程序
"""
import asyncio
import signal
import sys
from contextlib import asynccontextmanager
from fastapi import FastAPI
from fastapi.staticfiles import StaticFiles
import uvicorn

# 导入模块
from src.logger import logger
from src.config import config
from src.service_registry import service_registry
from src.kafka_producer import kafka_producer
from src.web_server import create_app


class InferenceMockService:
    """推理模拟服务主类"""
    
    def __init__(self):
        self.app = None
        self.server_config = config.get_server_config()
        self.running = False
        
    async def startup(self):
        """启动服务"""
        logger.info("正在启动 Inference Mock Service...")
        
        try:
            # 启动Kafka生产者
            await kafka_producer.start()
            
            # 启动服务注册
            await service_registry.start()
            
            self.running = True
            logger.info("Inference Mock Service 启动成功")
            
        except Exception as e:
            logger.error(f"服务启动失败: {e}")
            raise
    
    async def shutdown(self):
        """关闭服务"""
        logger.info("正在关闭 Inference Mock Service...")
        
        try:
            self.running = False
            
            # 停止服务注册
            await service_registry.stop()
            
            # 停止Kafka生产者
            await kafka_producer.stop()
            
            logger.info("Inference Mock Service 已关闭")
            
        except Exception as e:
            logger.error(f"服务关闭异常: {e}")


# 全局服务实例
mock_service = InferenceMockService()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动
    await mock_service.startup()
    yield
    # 关闭
    await mock_service.shutdown()


def create_application() -> FastAPI:
    """创建FastAPI应用"""
    app = create_app(lifespan=lifespan)
    
    # 挂载静态文件服务（用于提供截图访问）
    screenshot_dir = config.get("screenshot.output_dir", "./screenshots")
    app.mount("/screenshots", StaticFiles(directory=screenshot_dir), name="screenshots")
    
    return app


def signal_handler(signum, frame):
    """信号处理器"""
    logger.info(f"收到信号 {signum}，正在关闭服务...")
    sys.exit(0)


async def main():
    """主函数"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 创建应用
    app = create_application()
    
    # 获取服务器配置
    host = mock_service.server_config.get("host", "0.0.0.0")
    port = mock_service.server_config.get("port", 8081)
    
    # 启动服务器
    logger.info(f"启动HTTP服务器: http://{host}:{port}")
    
    config_uvicorn = uvicorn.Config(
        app=app,
        host=host,
        port=port,
        log_level="info",
        access_log=True
    )
    
    server = uvicorn.Server(config_uvicorn)
    
    try:
        await server.serve()
    except KeyboardInterrupt:
        logger.info("收到中断信号，正在关闭...")
    except Exception as e:
        logger.error(f"服务器运行异常: {e}")
    finally:
        logger.info("服务已停止")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("程序被用户中断")
    except Exception as e:
        logger.error(f"程序异常退出: {e}")
        sys.exit(1)
