server:
  host: "0.0.0.0"
  port: 8081

scheduler:
  url: "http://localhost:8080"
  registration_retry_interval: 30
  health_check_interval: 60
  
service:
  name: "inference-mock-1"
  region: "default"
  gpu_type: "A10"
  max_quota: 10
  # 当前编排类型 (可选: YOLO_TRACKING_CLIP, OVIT_CLIP)
  current_orchestration: "YOLO_TRACKING_CLIP"
  algorithm_orchestration:
    orchestrationId: "orch-001"
    orchestrationType: "YOLO_TRACKING_CLIP"
    algorithmChain:
      - algorithmId: "yolo-v8"
        algorithmName: "YOLO目标检测"
        algorithmType: "DETECTION"
        order: 1
        required: true
        dependsOn: []
        config:
          confidence_threshold: 0.5
          nms_threshold: 0.4
          modelName: "yolo-v8"
          modelVersion: "1.0.0"
          inputFormat: "IMAGE"
          outputFormat: "JSON"
  
rtsp:
  connection_timeout: 10
  retry_interval: 5
  max_retries: 3
  frame_buffer_size: 1
  
screenshot:
  interval: 30  # seconds
  output_dir: "./screenshots"
  format: "jpg"
  quality: 85
  max_files: 100  # 最大保留文件数

# RTSP连接配置
rtsp:
  max_retries: 5  # 最大重试次数
  retry_interval: 5  # 重试间隔(秒)
  connection_timeout: 10  # 连接超时(秒)
  read_timeout: 5  # 读取超时(秒)
  max_consecutive_failures: 10  # 最大连续失败次数
  health_check_interval: 30  # 健康检查间隔(秒)

kafka:
  bootstrap_servers: ["*************:9092"]  # 使用远端Kafka服务器
  topic: "vision-events"
  client_id: "inference-mock-1"
  acks: 1
  retries: 3
  batch_size: 16384
  linger_ms: 10
  buffer_memory: 33554432

# 编排特定配置
orchestration_configs:
  YOLO_TRACKING_CLIP:
    alert_interval: 30  # 30秒产生一条告警
    event_types: ["HELMET_MISSING", "PERSON_INTRUSION", "SAFETY_VIOLATION"]
    entity_types: ["Person"]
    tracking_enabled: true
    detection_probability: 0.7
    confidence_range: [0.7, 0.95]
    max_objects_per_frame: 3

  OVIT_CLIP:
    alert_interval: 300  # 5分钟（300秒）产生一条告警
    event_types: ["OBJECT_ABANDONED", "ABNORMAL_BEHAVIOR", "UNUSUAL_OBJECT"]
    entity_types: ["Person", "Vehicle", "Object", "Animal", "Tool"]
    tracking_enabled: false
    detection_probability: 0.5
    confidence_range: [0.6, 0.9]
    max_objects_per_frame: 5

# RTSP流配置
rtsp:
  # 默认RTSP服务器配置
  default_server: "localhost:9554"
  # 连接超时（秒）
  connection_timeout: 10
  # 读取超时（秒）
  read_timeout: 30
  # 重连间隔（秒）
  reconnect_interval: 5
  # 最大重连次数
  max_reconnect_attempts: 3
  # 缓冲区大小
  buffer_size: 1024
  # 是否启用RTSP流处理
  enabled: true
  # RTSP模拟模式（true=使用本地文件模拟，false=真实RTSP连接）
  simulation_mode: true
  # 模拟模式使用的本地文件
  simulation_file: "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
  # 默认流映射
  stream_mapping:
    "camera-001": "rtsp://localhost:9554/knight"
    "camera-002": "rtsp://localhost:9554/test"
    "camera-kafka-test-001": "rtsp://localhost:9554/stream1"
    "default": "rtsp://localhost:9554/knight"

mock_events:
  enabled: true
  # 以下配置将被orchestration_configs覆盖
  detection_probability: 0.7
  classes: ["person", "car", "bicycle", "dog", "cat", "truck", "bus"]
  confidence_range: [0.6, 0.95]
  max_objects_per_frame: 5
  violation_types: ["HELMET_MISSING", "AREA_INTRUSION", "OBJECT_DETECTION"]
  
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  file: "./logs/inference-mock.log"
  rotation: "1 day"
  retention: "7 days"
