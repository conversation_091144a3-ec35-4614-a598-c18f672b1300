# Inference Mock 服务设计文档

## 1. 概述

### 1.1 项目背景
Inference Mock 是一个模拟推理服务，用于测试和验证 CV 推理服务调度器的功能。该服务模拟真实的推理服务行为，包括服务注册、健康检查、任务处理等功能。

### 1.2 核心功能
- **服务注册**：向调度器注册自身服务
- **健康检查**：提供健康检查接口
- **RTSP 拉流**：从 RTSP 地址拉取视频流
- **定时截图**：每30秒截取一张图片
- **事件生成**：基于截图生成模拟的推理事件
- **Kafka推送**：将事件推送到Kafka消息队列
- **任务管理**：接收和处理调度器分配的任务

### 1.3 技术栈
- **Python 3.8+**
- **FastAPI**：Web 框架
- **OpenCV**：视频处理和截图
- **Uvicorn**：ASGI 服务器
- **Requests**：HTTP 客户端
- **APScheduler**：定时任务调度
- **Kafka-Python**：Kafka 客户端
- **Pillow**：图像处理

## 2. 系统架构

### 2.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐
│   Scheduler     │    │ Inference Mock  │
│                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │
│  │ Service   │  │◄──►│  │ Health    │  │
│  │ Registry  │  │    │  │ Check     │  │
│  └───────────┘  │    │  └───────────┘  │
│                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │
│  │ Task      │  │◄──►│  │ Task      │  │
│  │ Scheduler │  │    │  │ Handler   │  │
│  └───────────┘  │    │  └───────────┘  │
│                 │    │                 │
│  ┌───────────┐  │    │  ┌───────────┐  │
│  │ Health    │  │◄──►│  │ RTSP      │  │
│  │ Monitor   │  │    │  │ Stream    │  │
│  └───────────┘  │    │  └───────────┘  │
└─────────────────┘    └─────────────────┘
```

### 2.2 模块设计

#### 2.2.1 Web 服务模块 (web_server.py)
- 基于 FastAPI 的 HTTP 服务
- 提供健康检查接口
- 提供任务接收接口
- 提供服务状态查询接口

#### 2.2.2 服务注册模块 (service_registry.py)
- 向调度器注册服务
- 定期心跳保持
- 服务状态管理

#### 2.2.3 RTSP 流处理模块 (rtsp_handler.py)
- RTSP 流连接和管理
- 视频帧捕获
- 连接重试机制

#### 2.2.4 截图和事件模块 (event_generator.py)
- 定时截图功能
- 事件数据生成
- 图片存储管理

#### 2.2.5 Kafka推送模块 (kafka_producer.py)
- Kafka连接管理
- 事件消息推送
- 推送状态监控

#### 2.2.6 任务管理模块 (task_manager.py)
- 任务接收和处理
- 任务状态跟踪
- 任务结果生成

## 3. 接口规范

### 3.1 与调度器的交互接口

#### 3.1.1 服务注册
```http
POST http://scheduler:8080/api/v1/services/register
Content-Type: application/json

{
  "serviceName": "inference-mock-1",
  "baseUrl": "http://inference-mock:8081",
  "algorithmOrchestration": {
    "algorithmType": "OBJECT_DETECTION",
    "modelName": "yolo-v8",
    "modelVersion": "1.0.0",
    "inputFormat": "IMAGE",
    "outputFormat": "JSON"
  },
  "maxQuota": 10,
  "region": "default",
  "gpuType": "A10"
}
```

#### 3.1.2 健康检查接口
```http
GET http://inference-mock:8081/health

Response:
{
  "status": "UP",
  "details": {
    "rtsp_connections": 2,
    "active_tasks": 1,
    "last_screenshot": "2025-07-19T14:30:00Z"
  },
  "timestamp": 1642608600000
}
```

### 3.2 Mock 服务提供的接口

#### 3.2.1 任务接收接口
```http
POST http://inference-mock:8081/api/v1/tasks
Content-Type: application/json

{
  "taskId": "task-001",
  "taskName": "人员检测任务",
  "taskDescription": "检测未戴安全帽的人员",
  "taskMeta": {
    "enabled": true,
    "taskLevel": "HIGH",
    "protocol": "VIDEO",
    "eventTypeId": "event_type_uuid_001",
    "eventAction": ["ALERT"],
    "severity": "HIGH"
  },
  "algorithmOrchestration": {
    "orchestrationId": "orch_001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "algorithmChain": [
      {
        "algorithmId": "person_detection",
        "algorithmName": "人员检测",
        "algorithmType": "DETECTION",
        "order": 1,
        "required": true,
        "config": {
          "confidence": 0.7,
          "nms_threshold": 0.5
        }
      },
      {
        "algorithmId": "helmet_detection",
        "algorithmName": "安全帽检测",
        "algorithmType": "CLASSIFICATION",
        "order": 2,
        "required": true,
        "dependsOn": ["person_detection"],
        "config": {
          "confidence": 0.8
        }
      }
    ],
    "decoderConfig": {
      "keyFrameOnly": false,
      "decodeStep": 4
    }
  },
  "device": {
    "deviceId": "camera-001",
    "deviceName": "工地入口摄像头",
    "streamConfig": {
      "resolution": "1920x1080",
      "frameRate": 25,
      "protocol": "RTSP",
      "url": "rtsp://example.com/stream1",
      "decoderConf": {
        "keyFrameOnly": false,
        "decodeStep": 4
      }
    }
  }
}
```

#### 3.2.2 任务状态查询
```http
GET http://inference-mock:8081/api/v1/tasks/{taskId}

Response:
{
  "taskId": "task-001",
  "taskName": "人员检测任务",
  "status": "RUNNING",
  "device": {
    "deviceId": "camera-001",
    "deviceName": "工地入口摄像头"
  },
  "algorithmOrchestration": {
    "orchestrationId": "orch_001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "status": "RUNNING"
  },
  "taskMeta": {
    "enabled": true,
    "taskLevel": "HIGH",
    "eventTypeId": "event_type_uuid_001",
    "severity": "HIGH"
  },
  "startTime": "2025-07-19T14:00:00Z",
  "lastEventTime": "2025-07-19T14:30:00Z",
  "eventCount": 5,
  "screenshots": [
    {
      "timestamp": "2025-07-19T14:30:00Z",
      "filename": "task-001_20250719_143000.jpg",
      "imageUri": "http://inference-mock:8081/screenshots/task-001_20250719_143000.jpg"
    }
  ]
}
```

#### 3.2.3 Kafka事件推送
事件将按照 Vision Flow 原子事件输出协议推送到Kafka：

**Topic**: `vision-events`

**消息格式**: JSON (AtomicEventInstance)

```json
{
  "atomicEventInstanceId": "aei_20250719_143000_001",
  "eventTypeId": "event_type_uuid_001",
  "taskId": "task-001",
  "deviceId": "camera-001",
  "timestamp": 1642608600000,
  "imageUri": "http://inference-mock:8081/screenshots/task-001_20250719_143000.jpg",
  "entities": [
    {
      "entityInstanceId": "track_001",
      "entityType": "Person",
      "algorithmId": "person_detection",
      "boundingBox": {
        "x": 100,
        "y": 100,
        "width": 150,
        "height": 200
      },
      "partOf": {
        "head": {
          "algorithmId": "person_detection",
          "confidence": 0.95,
          "entities": ["track_001_head"]
        }
      },
      "entityAttributes": {
        "helmet": {
          "attributeName": "helmet",
          "attributeValue": false,
          "confidence": 0.85,
          "algorithmId": "helmet_detection"
        }
      },
      "entityRelationship": {},
      "externalInfo": {
        "trackId": "track_001",
        "quality": 0.92
      }
    }
  ],
  "relationEntities": [],
  "taskInfo": {
    "taskId": "task-001",
    "eventTypeId": "event_type_uuid_001",
    "orchestrationId": "orch_001",
    "orchestrationType": "YOLO_TRACKING_CLIP",
    "taskLevel": "HIGH",
    "deviceName": "工地入口摄像头",
    "frameId": "frame_20250719_143000_001",
    "externalInfo": {
      "taskName": "人员检测任务",
      "algorithmChain": ["person_detection", "helmet_detection"],
      "mockGenerated": true
    }
  }
}
```

## 4. 数据模型

### 4.1 任务模型
```python
class Task:
    task_id: str
    task_name: str
    task_description: str
    task_meta: TaskMeta
    algorithm_orchestration: AlgorithmOrchestration
    device: Device
    status: TaskStatus  # PENDING, RUNNING, STOPPED, ERROR
    start_time: datetime
    last_event_time: datetime
    event_count: int
    screenshots: List[Screenshot]

class TaskMeta:
    enabled: bool
    task_level: str  # HIGH, MEDIUM, LOW
    protocol: str    # VIDEO
    event_type_id: str  # 事件类型ID
    event_action: List[str]  # ["ALERT"]

class AlgorithmOrchestration:
    orchestration_id: str
    orchestration_type: str  # YOLO_TRACKING_CLIP, OVIT_CLIP
    algorithm_chain: List[Algorithm]
    decoder_config: DecoderConfig

class Algorithm:
    algorithm_id: str
    algorithm_name: str
    algorithm_type: str  # DETECTION, CLASSIFICATION, TRACKING, RULE
    order: int
    required: bool
    depends_on: List[str]
    config: dict
    alert_config: dict  # 告警配置（仅分类算法需要）
    training_config: dict  # 训练配置（仅分类算法需要）
    rule_config: dict  # 规则配置（仅规则类算法需要）

class Device:
    device_id: str
    device_name: str
    stream_config: StreamConfig

class StreamConfig:
    resolution: str
    frame_rate: int
    protocol: str
    url: str
    decoder_conf: DecoderConfig
```

### 4.2 截图模型
```python
class Screenshot:
    timestamp: datetime
    filename: str
    file_path: str
    events: List[Event]
```

### 4.3 事件模型
```python
class Event:
    event_id: str
    timestamp: datetime
    type: str  # object_detection, face_recognition, etc.
    screenshot_id: str
    metadata: dict
    confidence: float
```

## 5. 配置管理

### 5.1 配置文件 (config.yaml)
```yaml
server:
  host: "0.0.0.0"
  port: 8081
  
scheduler:
  url: "http://localhost:8080"
  registration_retry_interval: 30
  
service:
  name: "inference-mock-1"
  region: "default"
  gpu_type: "A10"
  max_quota: 10
  
rtsp:
  connection_timeout: 10
  retry_interval: 5
  max_retries: 3
  
screenshot:
  interval: 30  # seconds
  output_dir: "./screenshots"
  format: "jpg"
  quality: 85
  
mock_events:
  enabled: true
  detection_probability: 0.7
  classes: ["person", "car", "bicycle", "dog"]
  confidence_range: [0.6, 0.95]
```

## 6. 部署方案

### 6.1 Docker 部署
```dockerfile
FROM python:3.9-slim

WORKDIR /app

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    libopencv-dev \
    python3-opencv \
    && rm -rf /var/lib/apt/lists/*

# 安装 Python 依赖
COPY requirements.txt .
RUN pip install -r requirements.txt

# 复制源码
COPY . .

# 暴露端口
EXPOSE 8081

# 启动命令
CMD ["python", "main.py"]
```

### 6.2 Docker Compose
```yaml
version: '3.8'

services:
  inference-mock-1:
    build: .
    container_name: inference-mock-1
    ports:
      - "8081:8081"
    environment:
      - SCHEDULER_URL=http://scheduler:8080
      - SERVICE_NAME=inference-mock-1
      - SERVICE_PORT=8081
    volumes:
      - ./screenshots:/app/screenshots
      - ./config.yaml:/app/config.yaml
    depends_on:
      - scheduler
    networks:
      - cv-network

networks:
  cv-network:
    external: true
```

## 7. 开发计划

### 7.1 第一阶段：基础框架
- [x] 项目结构设计
- [ ] FastAPI Web 服务框架
- [ ] 配置管理模块
- [ ] 日志系统

### 7.2 第二阶段：核心功能
- [ ] 服务注册功能
- [ ] 健康检查接口
- [ ] RTSP 流处理
- [ ] 定时截图功能

### 7.3 第三阶段：高级功能
- [ ] 事件生成和管理
- [ ] 任务管理接口
- [ ] 错误处理和重试
- [ ] 性能监控

### 7.4 第四阶段：测试和优化
- [ ] 单元测试
- [ ] 集成测试
- [ ] 性能优化
- [ ] 文档完善
