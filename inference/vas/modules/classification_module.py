import cv2
import json
import numpy as np
from vas.entrypoints.task_interface import *
from vas.modules.base_module import *
from vas.modules.cnclip.model import CnCLIPModel


class ClassificationModule(BaseModule):

    def __init__(self, module_name, config, queue, logger, task_info: None | SimplifiedAtomicTask = None):

        super().__init__(module_name, config, queue, logger, task_info)
        self.preprocessor = self.config[module_name]["preprocessor"]
        self.text_model = self.config[module_name]["text_model"]
        self.visual_model = self.config[module_name]["visual_model"]

        # preprocessor
        self.model = CnCLIPModel(self.preprocessor["vocab_file"])

        # check text model ready
        model_name = self.text_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")

        # check visual model ready
        model_name = self.visual_model["model_name"]
        model_ready = self.check_model_ready(model_name)
        self.logger.info(f"infer server model:{model_name} status is {model_ready}")

        # tiny model ready
        self.tiny_model = self.config[module_name].get("tiny_model", None)

        # trackId时间间隔控制和重复告警避免
        self.track_last_classification_frame = {}  # 记录每个trackId上次分类的frame_id
        self.track_alerted = {}  # 记录已经告警过的trackId，避免重复告警
        self.classification_interval_frames = 25  # 默认25帧间隔（1秒@25fps），可配置
        self.frame_rate = 25  # 默认帧率
        self.decode_step = 1  # 默认跳帧步长

    def should_classify_track(self, track_id: int, current_frame_id: int) -> bool:
        """
        检查是否应该对指定trackId进行分类

        Args:
            track_id: 跟踪ID
            current_frame_id: 当前帧ID

        Returns:
            bool: True表示应该分类，False表示跳过
        """
        # 如果该trackId已经告警过，不再分类
        if track_id in self.track_alerted:
            return False

        # 如果是第一次遇到这个trackId，直接分类
        if track_id not in self.track_last_classification_frame:
            return True

        # 检查时间间隔
        last_frame = self.track_last_classification_frame[track_id]
        frame_diff = current_frame_id - last_frame

        # 考虑frame_id可能跳跃的情况，使用绝对差值
        return abs(frame_diff) >= self.classification_interval_frames

    def update_track_classification_time(self, track_id: int, frame_id: int):
        """更新trackId的上次分类时间"""
        self.track_last_classification_frame[track_id] = frame_id

    def mark_track_alerted(self, track_id: int):
        """标记trackId已告警，避免重复告警"""
        self.track_alerted[track_id] = True

    def update_classification_interval(self, task_info):
        """根据任务信息更新分类间隔配置"""
        if task_info and hasattr(task_info, 'device') and hasattr(task_info.device, 'streamConfig'):
            frame_rate = getattr(task_info.device.streamConfig, 'frameRate', 25)
            decode_step = getattr(task_info.device.streamConfig.decoderConf, 'decodeStep', 1)

            # 更新配置
            self.frame_rate = frame_rate
            self.decode_step = decode_step
            # 考虑跳帧的情况，实际间隔 = 帧率 * 跳帧步长
            self.classification_interval_frames = frame_rate * decode_step

            self.logger.info(f"[CLASSIFICATION] 更新分类间隔: {self.classification_interval_frames} 帧 (帧率:{frame_rate}, 跳帧:{decode_step})")
        else:
            self.logger.debug(f"[CLASSIFICATION] 无法获取帧率配置，保持当前间隔: {self.classification_interval_frames} 帧")

    def image_clip(self, img: np.ndarray, x: int, y: int) -> tuple:
        if x < 0:
            x = 0
        if x >= img.shape[1]:
            x = img.shape[1]
        if y < 0:
            y = 0
        if y >= img.shape[0]:
            y = img.shape[0]
        return (x, y)


    def image_feature_extract(self,
                              image: np.ndarray,
                              rect: list | None = None,
                              need_norm: bool = True) -> np.ndarray:

        # crop
        if rect is None:
            rect = [0, 0, image.shape[1], image.shape[0]]

        x1, y1 = self.image_clip(image, rect[0], rect[1])
        x2, y2 = self.image_clip(image, rect[0] + rect[2], rect[1] + rect[3])

        # 检查裁剪区域是否有效
        if x1 >= x2 or y1 >= y2:
            # 如果裁剪区域无效，使用整个图像
            crop_img = image
        else:
            crop_img = image[y1:y2, x1:x2, ...]

        # 检查裁剪后的图像是否有效
        if crop_img.size == 0 or crop_img.shape[0] == 0 or crop_img.shape[1] == 0:
            self.logger.warning(f"裁剪后的图像尺寸无效: {crop_img.shape}, 使用整个图像")
            crop_img = image

        # 再次检查图像是否有效
        if crop_img.size == 0 or crop_img.shape[0] == 0 or crop_img.shape[1] == 0:
            self.logger.error(f"输入图像尺寸无效: {crop_img.shape}, 无法进行特征提取")
            # 返回一个默认的特征向量
            return np.zeros((512,), dtype=np.float32)

        preprocess_image = self.model.image_preprocess(crop_img)
        model_name = self.visual_model["model_name"]
        model_inputs = {
            "image": preprocess_image
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        embed = model_outputs["unnorm_image_features"]

        if need_norm:
            embed = embed / np.linalg.norm(embed)
        return embed


    def text_feature_extract(self, text: str, need_norm: bool = True) -> np.ndarray:
        preprocess_text = self.model.text_preprocess(text)
        preprocess_text = preprocess_text.astype(np.int64)
        model_name = self.text_model["model_name"]
        model_inputs = {
            "text": preprocess_text
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        embed = model_outputs["unnorm_text_features"]
        if need_norm:
            embed = embed / np.linalg.norm(embed)
        return embed


    def get_texts_features(self, texts: list[str]) -> list[np.ndarray]:
        text_features = []
        for text in texts:
            text_feature = self.text_feature_extract(text)
            text_features.append(text_feature)
        return text_features


    def get_images_freatures(self, images: list[np.ndarray]) -> list[np.ndarray]:
        image_features = []
        for image in images:
            image_feature = self.image_feature_extract(image)
            image_features.append(image_feature)
        return image_features


    def classify(self, image: np.ndarray, bbox: np.ndarray):
        rect = bbox[0:4].astype(np.int32).tolist()
        image_feature = self.image_feature_extract(image, rect)
        model_name = self.tiny_model["model_name"]
        model_inputs = {
            "input": image_feature
        }
        status, model_outputs = self.infer_client.infer(model_name, model_inputs)
        classify_res = model_outputs["output"][0]
        return classify_res[0], classify_res[1]


    def process_classification_algo(self, algo_config: Algorithm, message: MessageInfo):
        meta_info = message.meta_info
        frame, bboxes, track_objs = self.extract_frame_info(message=message)
        # 只在debug模式下输出详细信息
        self.logger.debug(f"receive frame {meta_info.frame_id}, frame: {frame.shape}, " \
            f"bboxes: {bboxes.shape}, track_objs: {track_objs.shape}")

        for bbox in bboxes:
            true_conf, false_conf = self.classify(frame, bbox)


    def zeroshot_classify(self, text: str, image: np.ndarray, bbox):
        text_feature = self.text_feature_extract(text)
        # 处理bbox参数，支持list和numpy array两种类型
        if isinstance(bbox, list):
            rect = [int(x) for x in bbox[0:4]]
        else:
            rect = bbox[0:4].astype(np.int32).tolist()
        image_feature = self.image_feature_extract(image, rect)
        cos_sim = np.dot(text_feature, image_feature.T) / (np.linalg.norm(text_feature) * np.linalg.norm(image_feature))
        return float(cos_sim[0][0])


    def process_zeroshot_classification_algo(self, algo_config: Algorithm,
                                                        message:MessageInfo) -> list[DetectionClassificationAlert]:
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"
        frame, bboxes, track_objs = self.extract_frame_info(message=message)
        alert_config = algo_config.alertConfig
        alert_res = []

        # 兼容性处理：如果positiveLabels和negativeLabels为空，尝试从labels推导
        positive_labels = alert_config.positiveLabels if alert_config.positiveLabels else []
        negative_labels = alert_config.negativeLabels if alert_config.negativeLabels else []

        # 如果正负例标签都为空，从labels字段推导（兼容旧版本）
        if not positive_labels and not negative_labels and alert_config.labels:
            # 默认将labels作为负例标签（表示要检测的异常情况）
            negative_labels = alert_config.labels
            # 可以根据业务需求设置默认的正例标签
            positive_labels = ["person", "normal"]  # 默认正例标签
            self.logger.warning(f"[CLASSIFICATION] Frame {frame_id}: ⚠️ 使用兼容模式，从labels推导正负例标签")

        # 只在有检测框时输出分类开始信息
        if len(bboxes) > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 开始分类，检测框:{len(bboxes)}, "
                              f"track_objs shape:{track_objs.shape if track_objs is not None else 'None'}, "
                              f"正例:{positive_labels}, 负例:{negative_labels}, 阈值:{alert_config.confidence}")
            # 调试trackIds的内容
            if track_objs is not None and len(track_objs) > 0:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: track_ids内容: {track_objs[:min(5, len(track_objs))]}")  # 只显示前5个

        # 验证标签配置
        if not positive_labels and not negative_labels:
            self.logger.error(f"[CLASSIFICATION] Frame {frame_id}: ❌ 正例和负例标签都为空，无法进行分类")
            return []

        # 获取所有文本标签的特征
        all_labels = positive_labels + negative_labels
        text_features = self.get_texts_features(texts=all_labels)
        text_features = np.concatenate(text_features, axis=0)

        for track_idx, track_obj in enumerate(track_objs):
            # 获取当前检测框对应的trackId
            # track_ids的结构是 [x, y, w, h, score, cls, track_id]，track_id在索引6
            if track_idx < len(track_objs) and len(track_objs[track_idx]) > 6:
                track_result = track_objs[track_idx]
                track_id = track_result[6]  # 提取track_id（第7个元素，索引6）
            else:
                track_id = None

            # 如果没有trackId或不应该分类，跳过
            if track_id is None:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 目标框 {track_idx + 1} 无trackId，跳过分类")
                continue

            # 将numpy类型转换为Python基本类型，确保可以用作字典键
            if isinstance(track_id, np.ndarray):
                if track_id.size == 1:
                    track_id = track_id.item()  # 单元素数组转换为标量
                else:
                    track_id = int(track_id.flatten()[0])  # 多元素数组取第一个元素
            elif hasattr(track_id, 'item') and hasattr(track_id, 'size'):
                # numpy标量类型
                track_id = track_id.item()
            else:
                # 其他类型，直接转换为int
                track_id = int(track_id)

            if not self.should_classify_track(track_id, frame_id):
                # 检查具体的跳过原因
                if track_id in self.track_alerted:
                    self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} 已告警过，跳过分类")
                else:
                    last_frame = self.track_last_classification_frame.get(track_id, -1)
                    frame_diff = abs(frame_id - last_frame) if last_frame != -1 else 0
                    self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} 间隔不足 (当前:{frame_id}, 上次:{last_frame}, 差值:{frame_diff}, 需要:{self.classification_interval_frames})")
                continue

            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 开始对trackId {track_id} 进行分类")

            # 添加trackId调试信息
            if not hasattr(self, '_seen_track_ids'):
                self._seen_track_ids = set()
            if track_id not in self._seen_track_ids:
                self.logger.info(f"🆔 [TRACK_DEBUG] Frame {frame_id}: 发现新trackId {track_id}")
                self._seen_track_ids.add(track_id)
            else:
                self.logger.debug(f"[TRACK_DEBUG] Frame {frame_id}: 已知trackId {track_id}")

            alert_flag = False
            bbox_coords = track_obj[0:4].astype(np.int32).tolist()
            classifications = []

            # 更新该trackId的分类时间
            self.update_track_classification_time(track_id, frame_id)

            # 获取当前框的图像特征
            image_feature = self.image_feature_extract(frame, bbox_coords)

            # 计算文本和图像特征的相似度
            # scores shape is [1, label_num]
            scores = np.dot(image_feature, text_features.T)

            # 正例分类
            label_index = 0
            max_pos_conf = -2.0  # 余弦相似度范围是[-1,1]，初始化为-2确保能被正确更新
            pos_scores = []
            for label in positive_labels:
                pos_conf = scores[0, label_index]
                class_res = ClassificationDetectionAttr(pos_conf, "positive", label, obj_id=track_id)
                max_pos_conf = pos_conf if pos_conf > max_pos_conf else max_pos_conf
                classifications.append(class_res)
                pos_scores.append(f"{label}:{pos_conf:.3f}")
                label_index += 1

            # 负例分类
            max_neg_conf = -2.0  # 余弦相似度范围是[-1,1]，初始化为-2确保能被正确更新
            neg_scores = []
            for label in negative_labels:
                neg_conf = scores[0, label_index]
                class_res = ClassificationDetectionAttr(neg_conf, "negative", label, obj_id=track_id)  # 修复bug: 使用neg_conf而不是pos_conf
                max_neg_conf = neg_conf if neg_conf > max_neg_conf else max_neg_conf
                classifications.append(class_res)
                neg_scores.append(f"{label}:{neg_conf:.3f}")
                label_index += 1


            # 判断是否需要告警结果
            if max_pos_conf > max_neg_conf and max_pos_conf > alert_config.confidence:
                alert_flag = True

            # 只在有告警时输出详细信息，否则只输出debug级别
            if alert_flag:
                self.logger.info(f"🔥 [CLASSIFICATION] Frame {frame_id}: TrackID {track_id} 触发告警 | 正例:{max_pos_conf:.3f} 负例:{max_neg_conf:.3f}")
                # 标记该trackId已告警，避免重复告警
                self.mark_track_alerted(track_id)
            else:
                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: trackId {track_id} 框{bbox_idx+1} - 正例[{','.join(pos_scores)}] 负例[{','.join(neg_scores)}] 最高正例:{max_pos_conf:.3f} 最高负例:{max_neg_conf:.3f} 无告警")

            bbox_classify = DetectionClassificationAlert(alert=alert_flag,
                                                        bbox=bbox_coords,
                                                        classifications=classifications)
            alert_res.append(bbox_classify)

        alert_count = sum(1 for res in alert_res if res.alert)
        # 只在有告警或有检测框时输出信息
        if alert_count > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 分类完成，总计{len(alert_res)}框，告警{alert_count}框")
        elif len(alert_res) > 0:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 分类完成，总计{len(alert_res)}框，无告警")
        return alert_res


    def process_execute_task_message(self, message: MessageInfo):
        frame_id = message.meta_info.frame_id if message and message.meta_info else "unknown"

        # 如果消息标记为跳过处理，直接返回
        if message.skip_processing:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: ⏭️ 消息已标记跳过处理，直接传递到下一个模块")
            return message

        self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 🏷️ 开始分类处理")

        task_info = message.task_info
        has_alert = False  # 标记是否有预警条件满足
        total_classifications = 0
        alert_count = 0

        # 分类间隔配置只在首次处理时更新
        if task_info is not None and not hasattr(self, '_interval_updated'):
            self.update_classification_interval(task_info)
            self._interval_updated = True

        if task_info is not None:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 任务信息存在，开始处理算法链")

            for algo_config in task_info.algorithmOrchestration.algorithmChain:
                if algo_config.algorithmType != AlgorithmType.CLASSIFICATION:
                    continue

                self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 处理分类算法配置: {algo_config.algorithmId}")

                # self.process_classification_algo(algo_config, message)
                alert_res = self.process_zeroshot_classification_algo(algo_config, message)

                if alert_res:
                    total_classifications = len(alert_res)
                    self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 分类结果数量: {total_classifications}")

                    # 检查是否有满足预警条件的结果
                    for i, bbox_result in enumerate(alert_res):
                        if bbox_result.alert:
                            alert_count += 1
                            has_alert = True
                            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框{i+1}满足预警条件")
                        else:
                            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 检测框{i+1}不满足预警条件")

                    if has_alert:
                        break
                else:
                    self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: 无分类结果")
        else:
            self.logger.warning(f"[CLASSIFICATION] Frame {frame_id}: ⚠️ 消息不包含任务信息")

        # Classification模块逻辑：如果满足预警条件往下传，否则标记跳过
        if has_alert:
            self.logger.debug(f"✅ [CLASSIFICATION] Frame {frame_id}: 检测到预警条件 ({alert_count}/{total_classifications}个目标满足条件)")
            message.skip_processing = False
        else:
            self.logger.debug(f"[CLASSIFICATION] Frame {frame_id}: ❌ 未检测到预警条件 (0/{total_classifications}个目标满足条件)，标记跳过后续处理")
            message.skip_processing = True

        return message


    def process_none_message(self, message: MessageInfo):
        task_json_file = "/workspace/video_analysis_server/data/config/yolo_tracking_clip_task.json"
        with open(task_json_file, 'r', encoding='utf-8') as file:
            task_json = json.load(file)
        task_info = SimplifiedAtomicTask.parse_obj(task_json)
        test_file = "/workspace/video_analysis_server/data/images/cat_224_224.jpg"
        video_frame = cv2.imread(test_file)
        bboxes = np.array([75.25, 9.45, 90.825, 211.75, 0.80078125, 16.0]).reshape(1, 6)
        track_objs = np.array([75.25, 9.45, 90.825, 211.75, 0.80078125, 16.0, 1]).reshape((1, 7))
        frame_info = self.prepare_frame_info(video_frame, bboxes, track_objs)
        exec_task_message = self.prepare_execute_task_message(frame_info=frame_info, task_info=task_info)
        self.push_message_to_module(exec_task_message, self.name)
        return exec_task_message