#!/bin/bash

# Docker镜像构建脚本
# 用于构建scheduler和inference-mock服务的Docker镜像

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装"
        exit 1
    fi
    
    if ! command -v mvn &> /dev/null; then
        log_error "Maven未安装，无法构建scheduler"
        exit 1
    fi
    
    log_success "依赖检查通过"
}

# 构建scheduler镜像
build_scheduler() {
    log_info "构建scheduler镜像..."

    cd services/scheduler
    
    # 构建Java项目
    log_info "编译scheduler项目..."
    mvn clean package -DskipTests
    
    if [ $? -ne 0 ]; then
        log_error "scheduler项目编译失败"
        cd ../..
        exit 1
    fi
    
    # 构建Docker镜像
    log_info "构建scheduler Docker镜像..."
    docker build -t cv-scheduler:latest .
    
    if [ $? -eq 0 ]; then
        log_success "scheduler镜像构建成功"
    else
        log_error "scheduler镜像构建失败"
        cd ../..
        exit 1
    fi

    cd ../..
}

# 构建inference-mock镜像
build_inference_mock() {
    log_info "构建inference-mock镜像..."

    cd services/inference-mock
    
    # 构建Docker镜像
    log_info "构建inference-mock Docker镜像..."
    docker build -t cv-inference-mock:latest .
    
    if [ $? -eq 0 ]; then
        log_success "inference-mock镜像构建成功"
    else
        log_error "inference-mock镜像构建失败"
        cd ../..
        exit 1
    fi

    cd ../..
}

# 显示构建结果
show_images() {
    log_info "构建完成的镜像:"
    docker images | grep -E "(cv-scheduler|cv-inference-mock)" || log_warning "未找到构建的镜像"
}

# 主函数
main() {
    echo "🚀 开始构建Docker镜像"
    
    check_dependencies
    
    # 根据参数决定构建哪些镜像
    if [ "$1" = "scheduler" ]; then
        build_scheduler
    elif [ "$1" = "inference-mock" ]; then
        build_inference_mock
    else
        # 默认构建所有镜像
        build_scheduler
        build_inference_mock
    fi
    
    show_images
    
    log_success "🎉 镜像构建完成！"
    echo ""
    log_info "💡 下一步:"
    echo "  1. 启动服务: ./start.sh"
    echo "  2. 查看镜像: docker images | grep cv-"
    echo ""
}

# 执行主函数
main "$@"
