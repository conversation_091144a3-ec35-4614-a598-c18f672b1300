# Kafka Docker 部署

这个目录包含了使用Docker部署Kafka集群的完整配置。

## 🚀 快速开始

### 1. 启动Kafka集群

```bash
# 给脚本执行权限
chmod +x *.sh

# 启动Kafka集群
./start-kafka.sh
```

### 2. 测试Kafka

```bash
# 测试Kafka连接和功能
./test-kafka.sh
```

### 3. 停止Kafka集群

```bash
# 停止Kafka集群
./stop-kafka.sh
```

## 📊 服务访问

启动成功后，可以通过以下地址访问服务：

- **Kafka Broker**: `localhost:9092`
- **Kafka UI**: http://localhost:8080 (推荐的Web管理界面)
- **Kafdrop**: http://localhost:9000 (备选的Web管理界面)
- **Zookeeper**: `localhost:2181`

## 🔧 服务组件

### Zookeeper
- **端口**: 2181
- **作用**: Kafka集群协调服务
- **数据持久化**: 是

### Kafka Broker
- **端口**: 9092 (外部访问), 29092 (内部通信)
- **JMX端口**: 9101
- **数据持久化**: 是
- **自动创建主题**: 启用

### Kafka UI
- **端口**: 8080
- **功能**: 现代化的Kafka Web管理界面
- **特性**: 主题管理、消息查看、消费者组监控

### Kafdrop
- **端口**: 9000
- **功能**: 轻量级的Kafka Web管理界面
- **特性**: 主题浏览、消息查看

## 📝 预创建的主题

启动脚本会自动创建以下主题：

- `cv-events`: 用于CV事件消息 (3分区)
- `cv-alerts`: 用于CV告警消息 (3分区)

## 🔧 常用命令

### Docker Compose 命令

```bash
# 启动服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f kafka

# 停止服务
docker-compose down

# 停止并删除数据卷
docker-compose down -v
```

### Kafka 命令

```bash
# 列出所有主题
docker exec kafka kafka-topics --list --bootstrap-server localhost:9092

# 创建主题
docker exec kafka kafka-topics --create \
    --bootstrap-server localhost:9092 \
    --topic my-topic \
    --partitions 3 \
    --replication-factor 1

# 删除主题
docker exec kafka kafka-topics --delete \
    --bootstrap-server localhost:9092 \
    --topic my-topic

# 查看主题详情
docker exec kafka kafka-topics --describe \
    --bootstrap-server localhost:9092 \
    --topic my-topic

# 发送消息
echo "Hello World" | docker exec -i kafka kafka-console-producer \
    --bootstrap-server localhost:9092 \
    --topic my-topic

# 消费消息
docker exec kafka kafka-console-consumer \
    --bootstrap-server localhost:9092 \
    --topic my-topic \
    --from-beginning

# 查看消费者组
docker exec kafka kafka-consumer-groups --list \
    --bootstrap-server localhost:9092

# 查看消费者组详情
docker exec kafka kafka-consumer-groups --describe \
    --bootstrap-server localhost:9092 \
    --group my-group
```

## 🔍 故障排除

### 1. 端口冲突

如果遇到端口冲突，可以修改 `docker-compose.yml` 中的端口映射：

```yaml
ports:
  - "19092:9092"  # 修改外部端口
```

### 2. 内存不足

如果系统内存不足，可以调整JVM参数：

```yaml
environment:
  KAFKA_HEAP_OPTS: "-Xmx512M -Xms512M"
```

### 3. 数据持久化

数据存储在Docker卷中，重启容器不会丢失数据。如需清理：

```bash
docker-compose down -v
docker volume prune -f
```

### 4. 网络连接问题

确保防火墙允许相关端口，或在本地开发环境中禁用防火墙。

## 📚 应用程序集成

### Python (kafka-python)

```python
from kafka import KafkaProducer, KafkaConsumer
import json

# 生产者
producer = KafkaProducer(
    bootstrap_servers=['localhost:9092'],
    value_serializer=lambda v: json.dumps(v).encode('utf-8')
)

producer.send('cv-events', {'message': 'Hello Kafka'})

# 消费者
consumer = KafkaConsumer(
    'cv-events',
    bootstrap_servers=['localhost:9092'],
    value_deserializer=lambda m: json.loads(m.decode('utf-8'))
)

for message in consumer:
    print(message.value)
```

### Java (Spring Kafka)

```java
@Service
public class KafkaService {
    
    @Autowired
    private KafkaTemplate<String, Object> kafkaTemplate;
    
    public void sendMessage(String topic, Object message) {
        kafkaTemplate.send(topic, message);
    }
    
    @KafkaListener(topics = "cv-events")
    public void handleMessage(String message) {
        // 处理消息
    }
}
```

## 🔒 生产环境注意事项

1. **安全性**: 启用SASL/SSL认证
2. **监控**: 集成Prometheus/Grafana监控
3. **备份**: 定期备份Kafka数据
4. **集群**: 部署多节点Kafka集群
5. **资源**: 根据负载调整内存和存储配置
