#!/bin/bash

echo "🧪 测试Kafka连接..."

# 检查Kafka是否运行
if ! docker exec kafka kafka-broker-api-versions --bootstrap-server localhost:9092 > /dev/null 2>&1; then
    echo "❌ Kafka未运行或不可访问"
    exit 1
fi

echo "✅ Kafka连接正常"

# 创建测试主题
echo "📝 创建测试主题 'test-topic'..."
docker exec kafka kafka-topics --create \
    --bootstrap-server localhost:9092 \
    --topic test-topic \
    --partitions 1 \
    --replication-factor 1 \
    --if-not-exists

# 发送测试消息
echo "📤 发送测试消息..."
echo "Hello Kafka $(date)" | docker exec -i kafka kafka-console-producer \
    --bootstrap-server localhost:9092 \
    --topic test-topic

echo "Test message from script $(date)" | docker exec -i kafka kafka-console-producer \
    --bootstrap-server localhost:9092 \
    --topic test-topic

# 读取消息
echo "📥 读取消息 (5秒后自动停止)..."
timeout 5 docker exec kafka kafka-console-consumer \
    --bootstrap-server localhost:9092 \
    --topic test-topic \
    --from-beginning || true

echo ""
echo "🎉 Kafka测试完成!"
echo ""
echo "💡 提示:"
echo "  - 可以通过 http://localhost:8080 访问Kafka UI"
echo "  - 可以通过 http://localhost:9000 访问Kafdrop"
echo "  - 主题 'cv-events' 和 'cv-alerts' 已创建，可用于应用程序"
