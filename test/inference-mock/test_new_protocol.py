#!/usr/bin/env python3
"""
测试新协议的脚本
"""
import json
import asyncio
from datetime import datetime
from src.models import (
    TaskRequest, SimplifiedAtomicTask, TaskMeta, AlgorithmOrchestration,
    Algorithm, Device, StreamConfig, DecoderConfig, AlertConfig, TrainingConfig,
    DataCollectionConfig, AtomicEventInstance, EntityInstance, BoundingBox,
    EntityAttribute, TaskInfo
)


def test_simplified_task_creation():
    """测试简化任务创建"""
    print("=== 测试简化任务创建 ===")
    
    # 创建数据收集配置
    data_collection = DataCollectionConfig(
        enabled=True,
        thresholds={"minConfidence": 0.1, "maxConfidence": 0.9},
        sampling_rate=0.1,
        max_samples_per_day=1000
    )
    
    # 创建训练配置
    training_config = TrainingConfig(
        labels=["helmet", "no_helmet"],
        data_collection=data_collection,
        model_version="v1.0"
    )
    
    # 创建告警配置
    alert_config = AlertConfig(
        labels=["no_helmet"],
        confidence=0.8
    )
    
    # 创建算法
    algorithms = [
        Algorithm(
            algorithm_id="person_detection",
            algorithm_name="人员检测",
            algorithm_type="DETECTION",
            order=1,
            required=True,
            config={"confidence": 0.7, "nms_threshold": 0.5}
        ),
        Algorithm(
            algorithm_id="person_tracking",
            algorithm_name="人员跟踪",
            algorithm_type="TRACKING",
            order=2,
            required=True,
            depends_on=["person_detection"],
            config={}
        ),
        Algorithm(
            algorithm_id="helmet_detection",
            algorithm_name="安全帽检测",
            algorithm_type="CLASSIFICATION",
            order=3,
            required=True,
            depends_on=["person_tracking"],
            alert_config=alert_config,
            training_config=training_config
        )
    ]
    
    # 创建算法编排
    orchestration = AlgorithmOrchestration(
        orchestration_id="orch_001",
        orchestration_type="YOLO_TRACKING_CLIP",
        algorithm_chain=algorithms
    )
    
    # 创建解码器配置
    decoder_conf = DecoderConfig(
        key_frame_only=False,
        decode_step=4
    )
    
    # 创建流配置
    stream_config = StreamConfig(
        resolution="1920x1080",
        frame_rate=25,
        protocol="RTSP",
        url="rtsp://*************:554/stream",
        decoder_conf=decoder_conf
    )
    
    # 创建设备
    device = Device(
        device_id="camera_001",
        device_name="工地入口摄像头",
        stream_config=stream_config
    )
    
    # 创建任务元信息
    task_meta = TaskMeta(
        enabled=True,
        task_level="HIGH",
        protocol="VIDEO",
        event_type_id="event_type_uuid_001",
        event_action=["ALERT"],
        job_id="job_test_001"  # 添加jobId字段
    )
    
    # 创建简化原子任务
    simplified_task = SimplifiedAtomicTask(
        task_id="task_001",
        task_name="人员检测任务",
        task_description="检测未戴安全帽的人员",
        task_meta=task_meta,
        algorithm_orchestration=orchestration,
        device=device
    )
    
    # 创建任务请求
    task_request = TaskRequest(
        task_request=simplified_task,
        config={},
        region="default",
        priority=1
    )
    
    # 转换为JSON
    json_data = task_request.dict(by_alias=True)
    print("任务请求JSON:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    
    return task_request


def test_event_creation():
    """测试事件创建"""
    print("\n=== 测试事件创建 ===")
    
    # 创建实体属性
    helmet_attr = EntityAttribute(
        attributeName="helmet",
        attributeValue=False,
        confidence=0.85,
        algorithmId="helmet_detection"
    )
    
    # 创建边界框
    bbox = BoundingBox(x=100, y=100, width=150, height=200)
    
    # 创建实体
    entity = EntityInstance(
        entityInstanceId="track_001",
        entityType="Person",
        algorithmId="person_detection",
        boundingBox=bbox,
        entityAttributes={"helmet": helmet_attr},
        externalInfo={
            "trackId": "track_001",
            "quality": 0.92
        }
    )
    
    # 创建任务信息
    task_info = TaskInfo(
        taskId="task_001",
        eventTypeId="event_type_uuid_001",
        orchestrationId="orch_001",
        orchestrationType="YOLO_TRACKING_CLIP",
        taskLevel="HIGH",
        deviceName="工地入口摄像头",
        frameId="frame_20250719_143000_001",
        externalInfo={
            "taskName": "人员检测任务",
            "algorithmChain": ["person_detection", "helmet_detection"],
            "mockGenerated": True
        }
    )
    
    # 创建原子事件实例
    event = AtomicEventInstance(
        atomicEventInstanceId="aei_20250719_143000_001",
        eventTypeId="event_type_uuid_001",
        taskId="task_001",
        deviceId="camera_001",
        timestamp=int(datetime.now().timestamp() * 1000),
        imageUri="http://localhost:8081/screenshots/task_001_20250719_143000.jpg",
        entities=[entity],
        relationEntities=[],
        taskInfo=task_info
    )
    
    # 转换为JSON
    json_data = event.dict(by_alias=True)
    print("事件JSON:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    
    return event


def main():
    """主函数"""
    print("开始测试新协议...")
    
    try:
        # 测试任务创建
        task_request = test_simplified_task_creation()
        print("✅ 任务创建测试通过")
        
        # 测试事件创建
        event = test_event_creation()
        print("✅ 事件创建测试通过")
        
        print("\n🎉 所有测试通过！新协议工作正常。")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
