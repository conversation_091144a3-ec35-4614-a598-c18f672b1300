#!/usr/bin/env python3
"""
Kafka消费者测试
验证inference-mock发送的消息内容
"""

from kafka import KafkaConsumer
import json
from datetime import datetime
import time

def test_kafka_consumer():
    """测试Kafka消费者"""
    print("🚀 开始Kafka消费者测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 创建Kafka消费者
        consumer = KafkaConsumer(
            'vision-events',  # topic名称
            bootstrap_servers=['localhost:9092'],
            auto_offset_reset='earliest',  # 从最早的消息开始读取
            enable_auto_commit=True,
            group_id='test-consumer-group',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')) if x else None,
            key_deserializer=lambda x: x.decode('utf-8') if x else None,
            consumer_timeout_ms=10000  # 10秒超时
        )
        
        print("✅ Kafka消费者创建成功")
        print("📊 开始消费消息...")
        
        message_count = 0
        for message in consumer:
            message_count += 1
            print(f"\n📨 收到消息 #{message_count}:")
            print(f"   Topic: {message.topic}")
            print(f"   Partition: {message.partition}")
            print(f"   Offset: {message.offset}")
            print(f"   Key: {message.key}")
            print(f"   Timestamp: {datetime.fromtimestamp(message.timestamp/1000) if message.timestamp else 'N/A'}")
            
            # 解析消息内容
            if message.value:
                event = message.value
                print(f"   Event ID: {event.get('atomicEventInstanceId', 'N/A')}")
                print(f"   Device ID: {event.get('deviceId', 'N/A')}")
                print(f"   Event Type: {event.get('eventType', 'N/A')}")
                print(f"   Orchestration Type: {event.get('orchestrationType', 'N/A')}")
                print(f"   Timestamp: {event.get('timestamp', 'N/A')}")
                
                # 显示检测结果
                if 'detectionResults' in event:
                    results = event['detectionResults']
                    print(f"   Detection Results: {len(results)} objects")
                    for i, result in enumerate(results[:3]):  # 只显示前3个
                        print(f"     Object {i+1}: {result.get('className', 'N/A')} "
                              f"(confidence: {result.get('confidence', 'N/A')})")
                
                # 显示图片信息
                if 'imageUri' in event:
                    print(f"   Image URI: {event['imageUri']}")
            else:
                print("   ⚠️ 消息内容为空")
            
            # 限制显示消息数量
            if message_count >= 10:
                print(f"\n📊 已显示{message_count}条消息，停止消费")
                break
        
        if message_count == 0:
            print("❌ 没有收到任何消息")
        else:
            print(f"\n✅ 总共消费了 {message_count} 条消息")
            
    except Exception as e:
        print(f"❌ Kafka消费者异常: {e}")
    
    finally:
        try:
            consumer.close()
            print("🔒 Kafka消费者已关闭")
        except:
            pass
    
    print(f"\n🏁 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_kafka_consumer()
