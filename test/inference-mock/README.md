# 推理模拟器测试

这个目录包含专门针对推理模拟器(inference-mock)模块的测试脚本。

## 测试内容

1. **test_mock_service.py** - 推理模拟器服务功能测试
2. **test_new_protocol.py** - 新协议数据结构测试
3. **test_kafka_producer.py** - Kafka生产者测试
4. **test_kafka_consumer.py** - Kafka消费者测试

## 运行测试

确保推理模拟器服务已启动：

```bash
# 启动推理模拟器服务
cd inference-mock
docker-compose up -d

# 运行服务功能测试
python test_mock_service.py

# 运行协议测试
python test_new_protocol.py

# 运行Kafka生产者测试
python test_kafka_producer.py

# 运行Kafka消费者测试
python test_kafka_consumer.py
```