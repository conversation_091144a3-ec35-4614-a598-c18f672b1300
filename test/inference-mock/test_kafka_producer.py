#!/usr/bin/env python3
"""
测试Kafka生产者 - 发送模拟的vision-events数据
"""

import json
import time
import uuid
from datetime import datetime
from kafka import KafkaProducer

def create_producer():
    """创建Kafka生产者"""
    return KafkaProducer(
        bootstrap_servers=['localhost:9092'],
        value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
        key_serializer=lambda k: k.encode('utf-8') if k else None
    )

def create_sample_event():
    """创建符合AtomicEventInstance协议的示例事件数据"""
    atomic_event_id = str(uuid.uuid4())
    timestamp_ms = int(time.time() * 1000)  # 毫秒时间戳
    frame_id = f"frame_{int(time.time())}"

    # 符合协议的AtomicEventInstance数据结构
    event_data = {
        "atomicEventInstanceId": atomic_event_id,
        "eventTypeId": "HELMET_MISSING",
        "taskId": "test-task-001",
        "deviceId": "camera-001",
        "timestamp": timestamp_ms,
        "imageUri": f"https://storage.example.com/images/{frame_id}.jpg",
        "entities": [
            {
                "entityInstanceId": f"track_{int(time.time())}",
                "entityType": "Person",
                "algorithmId": "person_detection",
                "boundingBox": {
                    "x": 100,
                    "y": 150,
                    "width": 200,
                    "height": 400
                },
                "partOf": {
                    "head": {
                        "algorithmId": "person_detection",
                        "confidence": 0.95,
                        "entities": [f"track_{int(time.time())}_head"]
                    }
                },
                "entityAttributes": {
                    "helmet": {
                        "attributeName": "helmet",
                        "attributeValue": False,
                        "confidence": 0.93,
                        "algorithmId": "helmet_detection"
                    },
                    "age": {
                        "attributeName": "age",
                        "attributeValue": 35,
                        "confidence": 0.78,
                        "algorithmId": "age_estimation"
                    },
                    "gender": {
                        "attributeName": "gender",
                        "attributeValue": "MALE",
                        "confidence": 0.89,
                        "algorithmId": "gender_classification"
                    },
                    "isWorker": {
                        "attributeName": "isWorker",
                        "attributeValue": True,
                        "confidence": 0.96,
                        "algorithmId": "worker_classification"
                    }
                },
                "entityRelationship": {
                    "inArea": {
                        "confidence": 0.88,
                        "entities": ["area_entity_001"],
                        "algorithmId": "area_intrusion_detection"
                    }
                },
                "externalInfo": {
                    "trackId": f"track_{int(time.time())}",
                    "quality": 0.92,
                    "workerId": "worker_001",
                    "department": "construction"
                }
            }
        ],
        "relationEntities": [
            {
                "entityInstanceId": "area_entity_001",
                "entityType": "Area",
                "algorithmId": "area_intrusion_detection",
                "polygon": {
                    "points": [
                        {"x": 100, "y": 100},
                        {"x": 300, "y": 100},
                        {"x": 300, "y": 400},
                        {"x": 100, "y": 400}
                    ]
                },
                "entityAttributes": {
                    "areaType": {
                        "attributeName": "areaType",
                        "attributeValue": "restricted",
                        "confidence": 1.0,
                        "algorithmId": "area_intrusion_detection"
                    }
                },
                "externalInfo": {}
            }
        ],
        "taskInfo": {
            "taskId": "test-task-001",
            "eventTypeId": "HELMET_MISSING",
            "orchestrationId": "orch_001",
            "orchestrationType": "YOLO_TRACKING_CLIP",
            "taskLevel": "HIGH",
            "deviceName": "工地入口摄像头",
            "frameId": frame_id,
            "externalInfo": {
                "taskName": "人员安全帽检测任务",
                "algorithmChain": ["person_detection", "person_tracking", "helmet_detection"],
                "customFields": {
                    "workShift": "morning",
                    "weatherCondition": "sunny",
                    "actionRequired": True,
                    "recommendations": ["立即提醒作业人员佩戴安全帽", "暂停相关作业"]
                }
            }
        }
    }

    return event_data

def main():
    """主函数"""
    print("🚀 启动Kafka测试生产者")
    print("📡 连接到: localhost:9092")
    print("📋 Topic: vision-events")
    print("=" * 50)
    
    # 创建生产者
    producer = create_producer()
    
    try:
        # 发送5条测试消息
        for i in range(5):
            event_data = create_sample_event()
            key = f"event_{i+1}"
            
            print(f"📤 发送第 {i+1} 条消息...")
            print(f"🔑 Key: {key}")
            print(f"📄 Event ID: {event_data['atomicEventInstanceId']}")
            
            # 发送消息
            future = producer.send('vision-events', key=key, value=event_data)
            result = future.get(timeout=10)
            
            print(f"✅ 消息发送成功: partition={result.partition}, offset={result.offset}")
            print("-" * 50)
            
            time.sleep(2)  # 间隔2秒
            
    except Exception as e:
        print(f"❌ 发送消息失败: {e}")
    finally:
        producer.close()
        print("🏁 生产者已关闭")

if __name__ == "__main__":
    main()
