#!/usr/bin/env python3
"""
测试脚本 - 验证Inference Mock Service功能
"""
import time
import requests
import json
from typing import Dict, Any


class MockServiceTester:
    """Mock服务测试器"""
    
    def __init__(self, base_url: str = "http://localhost:8081"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health_check(self) -> bool:
        """测试健康检查"""
        try:
            print("🔍 测试健康检查...")
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 健康检查成功: {data['status']}")
                print(f"   详细信息: {json.dumps(data['details'], indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 健康检查失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 健康检查异常: {e}")
            return False
    
    def test_create_task(self, task_data: Dict[str, Any]) -> bool:
        """测试创建任务"""
        try:
            print(f"🔍 测试创建任务: {task_data['task_id']}")
            response = self.session.post(
                f"{self.base_url}/api/v1/tasks",
                json=task_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 任务创建成功: {data}")
                return True
            else:
                print(f"❌ 任务创建失败: HTTP {response.status_code}, {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ 任务创建异常: {e}")
            return False
    
    def test_get_task(self, task_id: str) -> bool:
        """测试获取任务"""
        try:
            print(f"🔍 测试获取任务: {task_id}")
            response = self.session.get(f"{self.base_url}/api/v1/tasks/{task_id}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 任务查询成功:")
                print(f"   状态: {data['status']}")
                print(f"   设备ID: {data['device_id']}")
                print(f"   事件数量: {data['event_count']}")
                return True
            else:
                print(f"❌ 任务查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 任务查询异常: {e}")
            return False
    
    def test_list_tasks(self) -> bool:
        """测试获取任务列表"""
        try:
            print("🔍 测试获取任务列表...")
            response = self.session.get(f"{self.base_url}/api/v1/tasks")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 任务列表查询成功: 共 {data['total']} 个任务")
                for task in data['tasks']:
                    print(f"   - {task['task_id']}: {task['status']}")
                return True
            else:
                print(f"❌ 任务列表查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 任务列表查询异常: {e}")
            return False
    
    def test_get_stats(self) -> bool:
        """测试获取统计信息"""
        try:
            print("🔍 测试获取统计信息...")
            response = self.session.get(f"{self.base_url}/api/v1/stats")
            
            if response.status_code == 200:
                data = response.json()
                print("✅ 统计信息查询成功:")
                print(f"   任务管理器: {json.dumps(data['task_manager'], indent=2, ensure_ascii=False)}")
                print(f"   Kafka生产者: {json.dumps(data['kafka_producer'], indent=2, ensure_ascii=False)}")
                return True
            else:
                print(f"❌ 统计信息查询失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 统计信息查询异常: {e}")
            return False
    
    def test_delete_task(self, task_id: str) -> bool:
        """测试删除任务"""
        try:
            print(f"🔍 测试删除任务: {task_id}")
            response = self.session.delete(f"{self.base_url}/api/v1/tasks/{task_id}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 任务删除成功: {data}")
                return True
            else:
                print(f"❌ 任务删除失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 任务删除异常: {e}")
            return False
    
    def run_full_test(self):
        """运行完整测试"""
        print("🚀 开始测试 Inference Mock Service")
        print("=" * 50)
        
        # 测试健康检查
        if not self.test_health_check():
            print("❌ 健康检查失败，停止测试")
            return
        
        print()
        
        # 测试创建任务
        task_data = {
            "task_id": "test-task-001",
            "device_id": "camera-test-001",
            "rtsp_url": "rtsp://example.com/test-stream",
            "algorithm_orchestration": {
                "algorithm_type": "OBJECT_DETECTION",
                "model_name": "yolo-v8",
                "model_version": "1.0.0",
                "input_format": "IMAGE",
                "output_format": "JSON"
            },
            "config": {
                "screenshot_interval": 10
            }
        }
        
        if not self.test_create_task(task_data):
            print("❌ 任务创建失败，停止测试")
            return
        
        print()
        
        # 等待任务启动
        print("⏳ 等待任务启动...")
        time.sleep(3)
        
        # 测试获取任务
        self.test_get_task(task_data["task_id"])
        print()
        
        # 测试任务列表
        self.test_list_tasks()
        print()
        
        # 测试统计信息
        self.test_get_stats()
        print()
        
        # 等待一段时间观察事件生成
        print("⏳ 等待30秒观察事件生成...")
        time.sleep(30)
        
        # 再次查看统计信息
        self.test_get_stats()
        print()
        
        # 测试删除任务
        self.test_delete_task(task_data["task_id"])
        print()
        
        print("✅ 测试完成!")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description="测试Inference Mock Service")
    parser.add_argument("--url", default="http://localhost:8081", help="服务URL")
    parser.add_argument("--health-only", action="store_true", help="仅测试健康检查")
    
    args = parser.parse_args()
    
    tester = MockServiceTester(args.url)
    
    if args.health_only:
        tester.test_health_check()
    else:
        tester.run_full_test()


if __name__ == "__main__":
    main()
