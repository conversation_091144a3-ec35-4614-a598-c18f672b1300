#!/usr/bin/env python3
"""
快速验证脚本 - 自动化测试inference-mock的核心功能
"""
import requests
import time
import json
from datetime import datetime
from typing import Dict, Any, List
import subprocess
import os
import sys


class QuickVerifier:
    """快速验证器"""
    
    def __init__(self):
        self.scheduler_url = "http://localhost:8080"
        self.mock_url = "http://localhost:8081"
        self.results = []
        
    def log(self, level: str, message: str):
        """日志输出"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        symbols = {
            "INFO": "ℹ️",
            "SUCCESS": "✅", 
            "WARNING": "⚠️",
            "ERROR": "❌",
            "TEST": "🧪"
        }
        symbol = symbols.get(level, "📝")
        print(f"{symbol} [{timestamp}] {message}")
        
    def add_result(self, test_name: str, success: bool, message: str):
        """添加测试结果"""
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now()
        })
        
    def test_health_checks(self) -> bool:
        """测试健康检查"""
        self.log("TEST", "开始健康检查测试...")
        
        # 检查scheduler
        try:
            response = requests.get(f"{self.scheduler_url}/health", timeout=5)
            if response.status_code == 200:
                self.log("SUCCESS", "Scheduler健康检查通过")
                scheduler_ok = True
            else:
                self.log("ERROR", f"Scheduler健康检查失败: {response.status_code}")
                scheduler_ok = False
        except Exception as e:
            self.log("ERROR", f"Scheduler连接失败: {e}")
            scheduler_ok = False
            
        # 检查inference-mock
        try:
            response = requests.get(f"{self.mock_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get("status") == "UP":
                    kafka_connected = data.get("details", {}).get("kafka_connected", False)
                    if kafka_connected:
                        self.log("SUCCESS", "Inference-Mock健康检查通过，Kafka已连接")
                        mock_ok = True
                    else:
                        self.log("WARNING", "Inference-Mock健康但Kafka未连接")
                        mock_ok = True  # 基础功能仍可用
                else:
                    self.log("ERROR", f"Inference-Mock状态异常: {data.get('status')}")
                    mock_ok = False
            else:
                self.log("ERROR", f"Inference-Mock健康检查失败: {response.status_code}")
                mock_ok = False
        except Exception as e:
            self.log("ERROR", f"Inference-Mock连接失败: {e}")
            mock_ok = False
            
        success = scheduler_ok and mock_ok
        self.add_result("健康检查", success, 
                       f"Scheduler: {'✅' if scheduler_ok else '❌'}, "
                       f"Inference-Mock: {'✅' if mock_ok else '❌'}")
        return success
        
    def test_service_registration(self) -> bool:
        """测试服务注册"""
        self.log("TEST", "检查服务注册状态...")
        
        try:
            response = requests.get(f"{self.scheduler_url}/api/v1/scheduler/services", timeout=5)
            if response.status_code == 200:
                services = response.json().get("services", [])
                mock_registered = any(
                    svc.get("serviceName", "").startswith("inference-mock") 
                    for svc in services
                )
                
                if mock_registered:
                    self.log("SUCCESS", "Inference-Mock已成功注册到Scheduler")
                    success = True
                else:
                    self.log("WARNING", "Inference-Mock未在Scheduler中找到，可能正在注册中...")
                    success = False
            else:
                self.log("ERROR", f"获取服务列表失败: {response.status_code}")
                success = False
                
        except Exception as e:
            self.log("ERROR", f"检查服务注册失败: {e}")
            success = False
            
        self.add_result("服务注册", success, 
                       "已注册" if success else "未注册或注册中")
        return success
        
    def create_yolo_task(self) -> str:
        """创建YOLO测试任务"""
        self.log("TEST", "创建YOLO_TRACKING_CLIP测试任务...")
        
        task_request = {
            "taskRequest": {
                "taskId": "quick-verify-yolo",
                "taskName": "快速验证YOLO任务",
                "taskDescription": "验证30秒告警间隔",
                "taskMeta": {
                    "eventTypeId": "HELMET_MISSING",
                    "taskLevel": "HIGH"
                },
                "device": {
                    "deviceId": "camera-quick-verify",
                    "streamConfig": {
                        "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                    }
                },
                "algorithmOrchestration": {
                    "orchestrationId": "orch-quick-verify",
                    "orchestrationType": "YOLO_TRACKING_CLIP",
                    "algorithmChain": [
                        {
                            "algorithmId": "yolo-v8",
                            "algorithmName": "YOLO目标检测",
                            "algorithmType": "DETECTION",
                            "order": 1,
                            "required": True,
                            "dependsOn": [],
                            "config": {
                                "confidence_threshold": 0.5
                            }
                        }
                    ]
                }
            },
            "config": {}
        }
        
        try:
            response = requests.post(
                f"{self.mock_url}/api/v1/tasks",
                headers={"Content-Type": "application/json"},
                json=task_request,
                timeout=10
            )
            
            if response.status_code == 200:
                self.log("SUCCESS", "YOLO任务创建成功")
                self.add_result("YOLO任务创建", True, "任务创建成功")
                return "quick-verify-yolo"
            else:
                self.log("ERROR", f"YOLO任务创建失败: {response.status_code} - {response.text}")
                self.add_result("YOLO任务创建", False, f"HTTP {response.status_code}")
                return None
                
        except Exception as e:
            self.log("ERROR", f"YOLO任务创建异常: {e}")
            self.add_result("YOLO任务创建", False, str(e))
            return None
            
    def monitor_yolo_alerts(self, task_id: str, monitor_minutes: int = 3) -> bool:
        """监控YOLO告警生成"""
        if not task_id:
            return False
            
        self.log("TEST", f"监控YOLO告警生成 {monitor_minutes}分钟...")
        self.log("INFO", "预期: 每30秒生成一次告警")
        
        start_time = time.time()
        end_time = start_time + (monitor_minutes * 60)
        last_count = 0
        alert_times = []
        
        while time.time() < end_time:
            try:
                response = requests.get(f"{self.mock_url}/api/v1/tasks/{task_id}", timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    current_count = data.get("event_count", 0)
                    
                    if current_count > last_count:
                        elapsed = int(time.time() - start_time)
                        alert_times.append(elapsed)
                        self.log("SUCCESS", f"[{elapsed}s] 检测到新告警! 总计: {current_count}")
                        last_count = current_count
                        
                        # 检查告警间隔
                        if len(alert_times) >= 2:
                            interval = alert_times[-1] - alert_times[-2]
                            if 25 <= interval <= 35:  # 允许5秒误差
                                self.log("SUCCESS", f"告警间隔正常: {interval}秒")
                            else:
                                self.log("WARNING", f"告警间隔异常: {interval}秒 (期望30秒)")
                
                time.sleep(10)  # 每10秒检查一次
                
            except Exception as e:
                self.log("ERROR", f"监控告警失败: {e}")
                
        # 评估结果
        expected_alerts = monitor_minutes * 2  # 每30秒一个，3分钟应该有6个
        success = last_count >= (expected_alerts * 0.7)  # 允许30%的误差
        
        self.add_result("YOLO告警生成", success, 
                       f"生成{last_count}个告警，期望{expected_alerts}个")
        
        if success:
            self.log("SUCCESS", f"YOLO告警生成测试通过: {last_count}/{expected_alerts}")
        else:
            self.log("WARNING", f"YOLO告警生成不足: {last_count}/{expected_alerts}")
            
        return success
        
    def test_screenshot_access(self, task_id: str) -> bool:
        """测试截图访问"""
        if not task_id:
            return False
            
        self.log("TEST", "测试截图生成和HTTP访问...")
        
        # 等待一些截图生成
        time.sleep(40)
        
        try:
            # 获取任务信息
            response = requests.get(f"{self.mock_url}/api/v1/tasks/{task_id}", timeout=5)
            if response.status_code != 200:
                self.log("ERROR", "无法获取任务信息")
                return False
                
            # 检查截图目录 (通过文件系统)
            screenshot_dir = "inference-mock/screenshots"
            if os.path.exists(screenshot_dir):
                screenshots = [f for f in os.listdir(screenshot_dir) 
                             if f.endswith('.jpg') and task_id in f]
                
                if screenshots:
                    # 测试HTTP访问
                    latest_screenshot = screenshots[-1]
                    screenshot_url = f"{self.mock_url}/screenshots/{latest_screenshot}"
                    
                    response = requests.head(screenshot_url, timeout=5)
                    if response.status_code == 200:
                        content_type = response.headers.get('content-type', '')
                        if 'image' in content_type:
                            self.log("SUCCESS", f"截图HTTP访问正常: {latest_screenshot}")
                            success = True
                        else:
                            self.log("ERROR", f"截图文件类型错误: {content_type}")
                            success = False
                    else:
                        self.log("ERROR", f"截图HTTP访问失败: {response.status_code}")
                        success = False
                else:
                    self.log("WARNING", "未找到任务对应的截图文件")
                    success = False
            else:
                self.log("ERROR", "截图目录不存在")
                success = False
                
        except Exception as e:
            self.log("ERROR", f"测试截图访问异常: {e}")
            success = False
            
        self.add_result("截图生成和访问", success, 
                       "正常" if success else "失败或无截图")
        return success
        
    def test_kafka_events(self) -> bool:
        """测试Kafka事件（简单检查统计）"""
        self.log("TEST", "检查Kafka事件推送统计...")
        
        try:
            response = requests.get(f"{self.mock_url}/api/v1/stats", timeout=5)
            if response.status_code == 200:
                stats = response.json()
                kafka_stats = stats.get("kafka_producer", {})
                
                messages_sent = kafka_stats.get("messages_sent", 0)
                messages_failed = kafka_stats.get("messages_failed", 0)
                connected = kafka_stats.get("connected", False)
                
                if connected and messages_sent > 0:
                    self.log("SUCCESS", f"Kafka事件推送正常: 已发送{messages_sent}条，失败{messages_failed}条")
                    success = True
                elif connected:
                    self.log("WARNING", "Kafka已连接但未发送事件")
                    success = False
                else:
                    self.log("ERROR", "Kafka未连接")
                    success = False
            else:
                self.log("ERROR", f"获取统计信息失败: {response.status_code}")
                success = False
                
        except Exception as e:
            self.log("ERROR", f"检查Kafka事件失败: {e}")
            success = False
            
        self.add_result("Kafka事件推送", success, 
                       "正常" if success else "失败或无事件")
        return success
        
    def cleanup_task(self, task_id: str):
        """清理测试任务"""
        if not task_id:
            return
            
        self.log("INFO", f"清理测试任务: {task_id}")
        try:
            response = requests.delete(f"{self.mock_url}/api/v1/tasks/{task_id}", timeout=5)
            if response.status_code == 200:
                self.log("SUCCESS", "测试任务已清理")
            else:
                self.log("WARNING", f"清理任务失败: {response.status_code}")
        except Exception as e:
            self.log("WARNING", f"清理任务异常: {e}")
            
    def print_summary(self):
        """打印测试总结"""
        self.log("INFO", "=" * 60)
        self.log("INFO", "📊 快速验证测试总结")
        self.log("INFO", "=" * 60)
        
        total_tests = len(self.results)
        passed_tests = sum(1 for r in self.results if r["success"])
        
        for result in self.results:
            status = "✅ PASS" if result["success"] else "❌ FAIL"
            self.log("INFO", f"{status} - {result['test']}: {result['message']}")
            
        self.log("INFO", "-" * 60)
        score = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        self.log("INFO", f"📈 总体评分: {passed_tests}/{total_tests} ({score:.1f}%)")
        
        if score >= 80:
            self.log("SUCCESS", "🎉 验证通过! 系统运行良好")
            return True
        elif score >= 60:
            self.log("WARNING", "⚠️ 部分功能异常，需要检查")
            return False
        else:
            self.log("ERROR", "🚨 验证失败! 系统存在严重问题")
            return False
            
    def run_quick_verify(self):
        """运行快速验证"""
        self.log("INFO", "🚀 开始快速验证inference-mock服务")
        self.log("INFO", f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # 1. 健康检查
            if not self.test_health_checks():
                self.log("ERROR", "健康检查失败，停止后续测试")
                return False
                
            # 2. 服务注册检查
            self.test_service_registration()
            
            # 3. 创建YOLO任务
            task_id = self.create_yolo_task()
            
            if task_id:
                # 4. 监控告警生成 (3分钟)
                self.monitor_yolo_alerts(task_id, monitor_minutes=3)
                
                # 5. 测试截图访问
                self.test_screenshot_access(task_id)
                
                # 6. 测试Kafka事件
                self.test_kafka_events()
                
                # 7. 清理任务
                self.cleanup_task(task_id)
            
            # 8. 打印总结
            return self.print_summary()
            
        except KeyboardInterrupt:
            self.log("WARNING", "测试被用户中断")
            return False
        except Exception as e:
            self.log("ERROR", f"测试过程异常: {e}")
            return False


def main():
    """主函数"""
    verifier = QuickVerifier()
    
    print("🧪 Inference Mock Service - 快速验证工具")
    print("=" * 60)
    print("📋 此工具将验证以下功能:")
    print("  1. ✅ 服务健康状态和Kafka连接")
    print("  2. 🔗 服务注册状态")  
    print("  3. 📝 YOLO任务创建")
    print("  4. ⏰ 30秒告警间隔验证")
    print("  5. 📸 截图生成和HTTP访问")
    print("  6. 📨 Kafka事件推送统计")
    print()
    print("⏱️ 预计测试时间: 4-5分钟")
    print("💡 请确保服务已启动: scheduler + inference-mock + kafka")
    print()
    
    input("按回车键开始测试...")
    print()
    
    success = verifier.run_quick_verify()
    
    print()
    if success:
        print("🎉 恭喜! 快速验证全部通过")
        sys.exit(0)
    else:
        print("⚠️ 快速验证发现问题，请检查日志和配置")
        sys.exit(1)


if __name__ == "__main__":
    main()