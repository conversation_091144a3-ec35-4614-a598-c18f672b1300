#!/usr/bin/env python3
"""
测试任务分配的幂等性
"""

import requests
import json
import time
from datetime import datetime

def test_idempotent_task_allocation():
    """测试任务分配的幂等性"""
    print("🔄 测试任务分配幂等性...")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试数据
    task_request = {
        "taskRequest": {
            "taskId": "idempotent-test-001",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-idempotent-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "protocol": "FILE"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-idempotent-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        }
    }
    
    scheduler_url = "http://localhost:8080/api/v1/scheduler/schedule"
    
    try:
        print("\n📤 第一次发送任务分配请求...")
        
        # 第一次请求
        response1 = requests.post(
            scheduler_url,
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response1.status_code == 200:
            result1 = response1.json()
            print("✅ 第一次请求成功")
            print(f"   任务ID: {result1.get('taskId')}")
            print(f"   分配ID: {result1.get('allocationId')}")
            print(f"   服务ID: {result1.get('serviceId')}")
            
            # 等待一秒
            time.sleep(1)
            
            print("\n📤 第二次发送相同任务分配请求...")
            
            # 第二次请求（相同的taskId）
            response2 = requests.post(
                scheduler_url,
                headers={"Content-Type": "application/json"},
                json=task_request,
                timeout=10
            )
            
            if response2.status_code == 200:
                result2 = response2.json()
                print("✅ 第二次请求成功")
                print(f"   任务ID: {result2.get('taskId')}")
                print(f"   分配ID: {result2.get('allocationId')}")
                print(f"   服务ID: {result2.get('serviceId')}")
                
                # 验证幂等性
                print("\n🔍 验证幂等性...")
                
                if result1.get('taskId') == result2.get('taskId'):
                    print("✅ 任务ID一致")
                else:
                    print("❌ 任务ID不一致")
                    return False
                
                if result1.get('allocationId') == result2.get('allocationId'):
                    print("✅ 分配ID一致（幂等性验证通过）")
                else:
                    print("❌ 分配ID不一致（幂等性验证失败）")
                    return False
                
                if result1.get('serviceId') == result2.get('serviceId'):
                    print("✅ 服务ID一致")
                else:
                    print("❌ 服务ID不一致")
                    return False
                
                print("\n🎉 幂等性测试通过！")
                return True
                
            else:
                print(f"❌ 第二次请求失败: {response2.status_code}")
                print(f"   错误信息: {response2.text}")
                return False
                
        else:
            print(f"❌ 第一次请求失败: {response1.status_code}")
            print(f"   错误信息: {response1.text}")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_different_tasks():
    """测试不同任务ID的分配"""
    print("\n🔄 测试不同任务ID的分配...")
    
    # 第一个任务
    task1 = {
        "taskRequest": {
            "taskId": "different-test-001",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-different-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "protocol": "FILE"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-different-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": []
            }
        }
    }
    
    # 第二个任务
    task2 = {
        "taskRequest": {
            "taskId": "different-test-002",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-different-002",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "protocol": "FILE"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-different-002",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": []
            }
        }
    }
    
    scheduler_url = "http://localhost:8080/api/v1/scheduler/schedule"
    
    try:
        # 发送第一个任务
        response1 = requests.post(scheduler_url, json=task1, timeout=10)
        if response1.status_code != 200:
            print(f"❌ 第一个任务分配失败: {response1.status_code}")
            return False
        
        result1 = response1.json()
        
        # 发送第二个任务
        response2 = requests.post(scheduler_url, json=task2, timeout=10)
        if response2.status_code != 200:
            print(f"❌ 第二个任务分配失败: {response2.status_code}")
            return False
        
        result2 = response2.json()
        
        # 验证不同任务有不同的分配ID
        if result1.get('allocationId') != result2.get('allocationId'):
            print("✅ 不同任务有不同的分配ID")
            return True
        else:
            print("❌ 不同任务有相同的分配ID")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始任务分配幂等性测试")
    
    # 测试幂等性
    idempotent_result = test_idempotent_task_allocation()
    
    # 测试不同任务
    different_result = test_different_tasks()
    
    print("\n📊 测试结果总结:")
    print(f"  幂等性测试: {'✅ 通过' if idempotent_result else '❌ 失败'}")
    print(f"  不同任务测试: {'✅ 通过' if different_result else '❌ 失败'}")
    
    if idempotent_result and different_result:
        print("\n🎉 所有测试通过！任务分配幂等性功能正常")
        return True
    else:
        print("\n❌ 部分测试失败，请检查实现")
        return False

if __name__ == "__main__":
    main()
