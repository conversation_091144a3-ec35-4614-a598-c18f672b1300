#!/usr/bin/env python3
"""
测试调度器是否正常工作
"""
from apscheduler.schedulers.background import BackgroundScheduler
import time
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_job():
    """测试任务"""
    logger.info("🔥 定时任务执行了！")

def main():
    """主函数"""
    print("🚀 测试调度器...")
    
    # 创建调度器
    scheduler = BackgroundScheduler()
    scheduler.start()
    
    # 添加测试任务
    scheduler.add_job(
        func=test_job,
        trigger="interval",
        seconds=5,
        id="test_job",
        replace_existing=True
    )
    
    print("⏰ 调度器已启动，每5秒执行一次任务...")
    print("⏳ 等待30秒观察任务执行...")
    
    # 等待30秒
    for i in range(30):
        time.sleep(1)
        if i % 5 == 0:
            print(f"⏰ 已等待 {i}/30 秒...")
    
    scheduler.shutdown()
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
