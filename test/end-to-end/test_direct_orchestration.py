#!/usr/bin/env python3
"""
直接测试推理模拟器不同编排类型的脚本
"""
import requests
import json
import time
from datetime import datetime

def test_yolo_tracking_clip_direct():
    """直接测试YOLO_TRACKING_CLIP编排"""
    print("🔍 直接测试YOLO_TRACKING_CLIP编排...")
    
    task_request = {
        "taskRequest": {
            "taskId": "direct-yolo-task-001",
            "taskName": "YOLO跟踪CLIP测试任务",
            "taskDescription": "测试YOLO_TRACKING_CLIP编排的事件生成",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-yolo-direct-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-yolo-direct-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ YOLO_TRACKING_CLIP任务创建成功")
            print(f"📄 响应: {response.json()}")
            return True
        else:
            print(f"❌ YOLO_TRACKING_CLIP任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_ovit_clip_direct():
    """直接测试OVIT_CLIP编排"""
    print("\n🔍 直接测试OVIT_CLIP编排...")
    
    task_request = {
        "taskRequest": {
            "taskId": "direct-ovit-task-001",
            "taskName": "OVIT CLIP测试任务",
            "taskDescription": "测试OVIT_CLIP编排的事件生成",
            "taskMeta": {
                "eventTypeId": "OBJECT_ABANDONED",
                "taskLevel": "MEDIUM"
            },
            "device": {
                "deviceId": "camera-ovit-direct-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-ovit-direct-001",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "ovit-detection",
                        "algorithmName": "O-VIT万物检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.6
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ OVIT_CLIP任务创建成功")
            print(f"📄 响应: {response.json()}")
            return True
        else:
            print(f"❌ OVIT_CLIP任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def wait_for_events(seconds=60):
    """等待事件生成"""
    print(f"\n⏳ 等待{seconds}秒让事件生成...")
    time.sleep(seconds)

def main():
    """主函数"""
    print("🚀 开始直接测试推理模拟器的不同编排类型")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试YOLO编排
    yolo_success = test_yolo_tracking_clip_direct()
    
    if yolo_success:
        wait_for_events(30)
    
    # 测试OVIT编排
    ovit_success = test_ovit_clip_direct()
    
    if ovit_success:
        wait_for_events(30)
    
    print("\n📊 测试总结:")
    print(f"  YOLO_TRACKING_CLIP: {'✅ 成功' if yolo_success else '❌ 失败'}")
    print(f"  OVIT_CLIP: {'✅ 成功' if ovit_success else '❌ 失败'}")
    
    if yolo_success or ovit_success:
        print("\n💡 提示:")
        print("  - 查看Kafka消息: python kafka_consumer.py")
        print("  - 查看推理模拟器日志观察不同编排的事件生成")
        print("  - 访问 http://localhost:8082 查看Kafka UI")

if __name__ == "__main__":
    main()
