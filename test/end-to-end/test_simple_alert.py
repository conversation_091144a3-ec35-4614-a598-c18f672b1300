#!/usr/bin/env python3
"""
简单测试告警生成
"""
import requests
import json
import time
from datetime import datetime

def create_simple_task():
    """创建简单测试任务"""
    print("🔍 创建简单测试任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "simple-alert-test",
            "taskName": "简单告警测试",
            "taskDescription": "测试30秒告警间隔",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-simple-test",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-simple-test",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功: {result.get('taskId')}")
            return result.get('taskId')
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def main():
    """主函数"""
    print("🚀 开始简单告警测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建任务
    task_id = create_simple_task()
    
    if task_id:
        print(f"\n✅ 任务创建成功: {task_id}")
        print("⏳ 等待90秒观察告警生成...")
        print("💡 请查看推理模拟器日志观察定时告警触发")
        
        # 等待90秒
        for i in range(90):
            if i % 10 == 0:
                print(f"⏰ 已等待 {i}/90 秒...")
            time.sleep(1)
        
        print("\n📊 测试完成!")
        print("💡 如果看到 '🔔 定时告警任务触发' 日志，说明定时任务正常工作")
    else:
        print("\n❌ 任务创建失败")

if __name__ == "__main__":
    main()
