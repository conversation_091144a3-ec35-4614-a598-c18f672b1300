#!/usr/bin/env python3
"""
测试不同编排类型的告警间隔
"""
import requests
import json
import time
from datetime import datetime

def create_yolo_task():
    """创建YOLO编排任务"""
    print("🔍 创建YOLO_TRACKING_CLIP任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "yolo-alert-test-001",
            "taskName": "YOLO告警间隔测试",
            "taskDescription": "测试YOLO编排30秒告警间隔",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-yolo-alert-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-yolo-alert-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    return create_task(task_request)

def create_ovit_task():
    """创建OVIT编排任务"""
    print("🔍 创建OVIT_CLIP任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "ovit-alert-test-001",
            "taskName": "OVIT告警间隔测试",
            "taskDescription": "测试OVIT编排5分钟告警间隔",
            "taskMeta": {
                "eventTypeId": "OBJECT_ABANDONED",
                "taskLevel": "MEDIUM"
            },
            "device": {
                "deviceId": "camera-ovit-alert-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-ovit-alert-001",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "ovit-detection",
                        "algorithmName": "O-VIT万物检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.6
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    return create_task(task_request)

def create_task(task_request):
    """创建任务"""
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功: {result.get('taskId')}")
            return result.get('taskId')
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    try:
        response = requests.get(
            f"http://localhost:8081/api/v1/tasks/{task_id}",
            timeout=5
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ 获取任务状态失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 获取任务状态异常: {e}")
        return None

def monitor_tasks(task_ids, duration_minutes=10):
    """监控任务告警生成"""
    print(f"\n📊 开始监控任务告警生成，持续{duration_minutes}分钟...")
    
    start_time = time.time()
    end_time = start_time + (duration_minutes * 60)
    
    last_event_counts = {}
    
    while time.time() < end_time:
        current_time = datetime.now().strftime("%H:%M:%S")
        print(f"\n⏰ {current_time} - 检查任务状态:")
        
        for task_id in task_ids:
            if task_id:
                status = get_task_status(task_id)
                if status:
                    task_info = status.get('task', status)  # 兼容不同的响应格式
                    event_count = task_info.get('event_count', task_info.get('eventCount', 0))
                    orchestration_type = task_info.get('orchestrationType', 'unknown')
                    last_event_time = task_info.get('last_event_time', task_info.get('lastEventTime'))
                    
                    # 检查事件计数变化
                    last_count = last_event_counts.get(task_id, 0)
                    if event_count > last_count:
                        print(f"  🔔 {task_id} ({orchestration_type}): 新告警! 总计: {event_count}")
                        last_event_counts[task_id] = event_count
                    else:
                        print(f"  📋 {task_id} ({orchestration_type}): 无新告警, 总计: {event_count}")
                    
                    if last_event_time:
                        print(f"     最后告警时间: {last_event_time}")
        
        # 等待30秒再检查
        time.sleep(30)
    
    print(f"\n📊 监控完成! 最终告警统计:")
    for task_id in task_ids:
        if task_id:
            final_count = last_event_counts.get(task_id, 0)
            print(f"  {task_id}: {final_count} 条告警")

def main():
    """主函数"""
    print("🚀 开始测试不同编排类型的告警间隔")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 预期告警间隔:")
    print("  - YOLO_TRACKING_CLIP: 30秒")
    print("  - OVIT_CLIP: 5分钟（300秒）")
    
    # 创建任务
    yolo_task_id = create_yolo_task()
    time.sleep(2)  # 稍等一下再创建下一个任务
    ovit_task_id = create_ovit_task()
    
    if yolo_task_id or ovit_task_id:
        # 监控任务10分钟
        task_ids = [yolo_task_id, ovit_task_id]
        monitor_tasks(task_ids, duration_minutes=10)
        
        print("\n💡 分析提示:")
        print("  - YOLO任务应该每30秒产生一条告警")
        print("  - OVIT任务应该每5分钟产生一条告警")
        print("  - 可以通过 python kafka_consumer.py 查看Kafka中的告警消息")
    else:
        print("\n❌ 没有成功创建任务，无法进行监控")

if __name__ == "__main__":
    main()
