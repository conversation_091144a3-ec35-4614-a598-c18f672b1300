# 端到端测试

这个目录包含完整的端到端测试脚本，用于验证整个系统的功能集成。

## 测试内容

1. **test_simple_alert.py** - 简单告警生成测试
2. **test_alert_intervals.py** - 不同编排类型的告警间隔测试
3. **test_orchestration.py** - 通过调度器测试不同编排类型
4. **test_direct_orchestration.py** - 直接测试推理模拟器的不同编排类型
5. **test_kafka_only.py** - Kafka消息发送测试
6. **test_rtsp_final.py** - 真实RTSP流截图和事件生成测试
7. **test_complete_5min.py** - 完整的5分钟端到端测试
8. **test_complete_correct.py** - 正确的完整端到端测试

## 运行测试

```bash
# 运行简单告警测试
python test_simple_alert.py

# 运行告警间隔测试
python test_alert_intervals.py

# 运行完整5分钟测试
python test_complete_5min.py
```