#!/usr/bin/env python3
"""
简单的Kafka消息发送测试
直接测试inference-mock的事件生成和Kafka发送功能
"""

import requests
import json
import time
from datetime import datetime

def test_kafka_message_generation():
    """测试Kafka消息生成"""
    print("🚀 开始Kafka消息发送测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查inference-mock服务状态
    try:
        response = requests.get("http://localhost:8081/health", timeout=5)
        if response.status_code == 200:
            print("✅ Inference-Mock服务正常")
        else:
            print("❌ Inference-Mock服务异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到Inference-Mock服务: {e}")
        return
    
    # 创建测试任务
    task_data = {
        "taskRequest": {
            "taskId": "kafka-test-001",
            "taskName": "Kafka消息测试",
            "taskDescription": "测试Kafka消息发送功能",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "HIGH",
                "protocol": "VIDEO",
                "eventTypeId": "helmet_detection_event",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-kafka-test-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "person_detection",
                        "algorithmName": "人员检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence": 0.7,
                            "nms_threshold": 0.5
                        }
                    }
                ]
            },
            "device": {
                "deviceId": "camera-kafka-test-001",
                "deviceName": "测试摄像头-Kafka",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP"
                }
            },
            "taskConfig": {
                "alertInterval": 10,  # 10秒间隔，快速生成告警
                "maxDuration": 60,    # 最大运行60秒
                "enableAlert": True
            }
        }
    }
    
    print("\n📤 创建测试任务...")
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            json=task_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功: {result.get('taskId')}")
        else:
            print(f"❌ 任务创建失败: {response.status_code} - {response.text}")
            return
            
    except Exception as e:
        print(f"❌ 任务创建异常: {e}")
        return
    
    # 等待事件生成
    print("\n⏳ 等待事件生成...")
    print("📝 观察inference-mock日志中的事件生成情况")
    print("📊 检查Kafka UI (http://localhost:8082) 中的消息")
    
    # 等待30秒让系统生成一些事件
    for i in range(30):
        print(f"⏰ 等待中... {i+1}/30秒", end="\r")
        time.sleep(1)
    
    print("\n\n🔍 检查Kafka消息统计...")
    
    # 检查Kafka生产者统计
    try:
        response = requests.get("http://localhost:8081/api/v1/kafka/stats", timeout=5)
        if response.status_code == 200:
            stats = response.json()
            print(f"📊 Kafka统计信息:")
            print(f"   - 已发送消息: {stats.get('messages_sent', 0)}")
            print(f"   - 发送失败: {stats.get('messages_failed', 0)}")
            print(f"   - 连接状态: {stats.get('connected', False)}")
            print(f"   - 最后错误: {stats.get('last_error', 'None')}")
            
            if stats.get('messages_sent', 0) > 0:
                print("✅ Kafka消息发送成功！")
            else:
                print("❌ 没有发送任何Kafka消息")
                
        else:
            print(f"❌ 无法获取Kafka统计: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 获取Kafka统计异常: {e}")
    
    # 停止任务
    print("\n🛑 停止测试任务...")
    try:
        response = requests.delete(f"http://localhost:8081/api/v1/tasks/kafka-test-001", timeout=5)
        if response.status_code == 200:
            print("✅ 任务停止成功")
        else:
            print(f"⚠️ 任务停止失败: {response.status_code}")
    except Exception as e:
        print(f"⚠️ 停止任务异常: {e}")
    
    print(f"\n🏁 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    test_kafka_message_generation()
