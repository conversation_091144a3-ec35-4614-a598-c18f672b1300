#!/usr/bin/env python3
"""
最终测试真实RTSP流截图和事件生成
"""
import requests
import json
import time
from datetime import datetime

def create_test_task():
    """创建测试任务"""
    print("🔍 创建真实RTSP流测试任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "rtsp-final-test-001",
            "taskName": "真实RTSP流测试任务",
            "taskDescription": "测试真实视频文件的RTSP流截图和事件生成",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-rtsp-final-001",
                "streamConfig": {
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-rtsp-final-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ 任务创建成功")
            print(f"📄 响应: {response.json()}")
            return True
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def wait_for_events(seconds=90):
    """等待事件生成"""
    print(f"\n⏳ 等待{seconds}秒让定时截图和事件生成...")
    for i in range(seconds):
        if i % 10 == 0:
            print(f"⏰ 已等待 {i}/{seconds} 秒...")
        time.sleep(1)

def main():
    """主函数"""
    print("🚀 开始最终测试真实RTSP流截图和事件生成")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 创建任务
    success = create_test_task()
    
    if success:
        # 等待事件生成（30秒截图间隔，等待90秒应该能看到3次截图）
        wait_for_events(90)
        
        print("\n📊 测试完成!")
        print("💡 提示:")
        print("  - 查看推理模拟器日志观察定时截图和事件生成")
        print("  - 查看Kafka消息: python kafka_consumer.py")
        print("  - 查看生成的截图: ls -la inference-mock/screenshots/")
        print("  - 访问 http://localhost:8082 查看Kafka UI")
    else:
        print("\n❌ 任务创建失败，无法进行测试")

if __name__ == "__main__":
    main()
