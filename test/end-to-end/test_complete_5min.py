#!/usr/bin/env python3
"""
完整的5分钟端到端测试
测试YOLO和OVIT编排的告警间隔、截图保存和Kafka消息
"""
import requests
import json
import time
import os
import glob
from datetime import datetime
from kafka import KafkaConsumer
import threading
import signal
import sys

# 全局变量
kafka_messages = []
test_start_time = None
test_running = True

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    global test_running
    print("\n🛑 收到停止信号，正在清理...")
    test_running = False

def kafka_consumer_thread():
    """Kafka消费者线程"""
    global kafka_messages, test_running
    
    try:
        consumer = KafkaConsumer(
            'vision-events',
            bootstrap_servers=['175.168.10.52:9092'],
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=1000,
            auto_offset_reset='latest'
        )
        
        print("📡 Kafka消费者已启动")
        
        while test_running:
            try:
                for message in consumer:
                    if not test_running:
                        break
                    kafka_messages.append({
                        'timestamp': datetime.now(),
                        'message': message.value
                    })
                    print(f"📨 收到Kafka消息: {message.value.get('atomicEventInstanceId', 'unknown')}")
            except Exception as e:
                if test_running:
                    print(f"⚠️ Kafka消费异常: {e}")
                time.sleep(1)
                
    except Exception as e:
        print(f"❌ Kafka消费者启动失败: {e}")

def create_yolo_task():
    """创建YOLO编排任务"""
    print("🔍 创建YOLO_TRACKING_CLIP任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "yolo-5min-test",
            "taskName": "YOLO 5分钟测试",
            "taskDescription": "测试YOLO编排30秒告警间隔",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-yolo-5min",
                "streamConfig": {
                    "url": "rtsp://localhost:9554/knight"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-yolo-5min",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    return create_task(task_request)

def create_ovit_task():
    """创建OVIT编排任务"""
    print("🔍 创建OVIT_CLIP任务...")
    
    task_request = {
        "taskRequest": {
            "taskId": "ovit-5min-test",
            "taskName": "OVIT 5分钟测试",
            "taskDescription": "测试OVIT编排5分钟告警间隔",
            "taskMeta": {
                "eventTypeId": "OBJECT_ABANDONED",
                "taskLevel": "MEDIUM"
            },
            "device": {
                "deviceId": "camera-ovit-5min",
                "streamConfig": {
                    "url": "rtsp://localhost:9554/knight"
                }
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-ovit-5min",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "ovit-detection",
                        "algorithmName": "O-VIT万物检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.6
                        }
                    }
                ]
            }
        },
        "config": {}
    }
    
    return create_task(task_request)

def create_task(task_request):
    """创建任务"""
    try:
        response = requests.post(
            "http://localhost:8081/api/v1/tasks",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功: {result.get('taskId')}")
            return result.get('taskId')
        else:
            print(f"❌ 任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return None

def get_task_status(task_id):
    """获取任务状态"""
    try:
        response = requests.get(
            f"http://localhost:8081/api/v1/tasks/{task_id}",
            timeout=5
        )
        
        if response.status_code == 200:
            return response.json()
        else:
            return None
            
    except Exception as e:
        return None

def count_screenshots():
    """统计截图数量"""
    screenshot_dir = "inference-mock/screenshots"
    if not os.path.exists(screenshot_dir):
        return 0
    
    jpg_files = glob.glob(os.path.join(screenshot_dir, "*.jpg"))
    return len(jpg_files)

def analyze_results(task_ids, test_duration_minutes=5):
    """分析测试结果"""
    print(f"\n📊 测试结果分析 (测试时长: {test_duration_minutes}分钟)")
    print("=" * 60)
    
    # 分析截图
    screenshot_count = count_screenshots()
    print(f"📸 总截图数量: {screenshot_count}")
    
    # 分析Kafka消息
    print(f"📨 Kafka消息数量: {len(kafka_messages)}")
    
    # 分析任务状态
    for task_id in task_ids:
        if task_id:
            status = get_task_status(task_id)
            if status:
                task_info = status.get('task', status)
                orchestration_type = task_info.get('orchestrationType', 'unknown')
                event_count = task_info.get('event_count', task_info.get('eventCount', 0))
                
                print(f"\n🎯 任务: {task_id}")
                print(f"   编排类型: {orchestration_type}")
                print(f"   事件数量: {event_count}")
                
                # 计算预期告警数量
                if orchestration_type == "YOLO_TRACKING_CLIP":
                    expected_alerts = test_duration_minutes * 2  # 每30秒一次，5分钟=10次
                    print(f"   预期告警: {expected_alerts} (每30秒一次)")
                elif orchestration_type == "OVIT_CLIP":
                    expected_alerts = 1  # 5分钟内只有1次
                    print(f"   预期告警: {expected_alerts} (每5分钟一次)")
                
                # 分析符合度
                if event_count >= expected_alerts * 0.8:  # 允许20%误差
                    print(f"   ✅ 告警频率符合预期")
                else:
                    print(f"   ❌ 告警频率不符合预期")
    
    print("\n" + "=" * 60)

def main():
    """主函数"""
    global test_start_time, test_running
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    print("🚀 开始5分钟完整端到端测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("\n📋 测试内容:")
    print("  - YOLO_TRACKING_CLIP: 30秒告警间隔")
    print("  - OVIT_CLIP: 5分钟告警间隔")
    print("  - 截图保存验证")
    print("  - Kafka消息验证")
    
    test_start_time = datetime.now()
    
    # 启动Kafka消费者线程
    kafka_thread = threading.Thread(target=kafka_consumer_thread, daemon=True)
    kafka_thread.start()
    time.sleep(2)  # 等待Kafka消费者启动
    
    # 创建任务
    print("\n🎬 创建测试任务...")
    yolo_task_id = create_yolo_task()
    time.sleep(2)
    ovit_task_id = create_ovit_task()
    
    if not yolo_task_id and not ovit_task_id:
        print("❌ 没有成功创建任何任务，测试终止")
        return
    
    task_ids = [yolo_task_id, ovit_task_id]
    
    # 运行5分钟测试
    print(f"\n⏳ 开始5分钟监控...")
    test_duration = 5 * 60  # 5分钟
    
    for elapsed in range(0, test_duration + 1, 30):  # 每30秒检查一次
        if not test_running:
            break
            
        minutes = elapsed // 60
        seconds = elapsed % 60
        print(f"⏰ 已运行 {minutes:02d}:{seconds:02d} / 05:00")
        
        # 显示当前状态
        screenshot_count = count_screenshots()
        kafka_count = len(kafka_messages)
        print(f"   📸 截图: {screenshot_count}, 📨 Kafka: {kafka_count}")
        
        if elapsed < test_duration:
            time.sleep(30)
    
    test_running = False
    
    # 分析结果
    analyze_results(task_ids)
    
    print(f"\n🎉 测试完成! 总用时: {(datetime.now() - test_start_time).total_seconds():.1f}秒")

if __name__ == "__main__":
    main()
