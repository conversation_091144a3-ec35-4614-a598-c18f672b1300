#!/usr/bin/env python3
"""
测试不同编排类型的脚本
"""
import requests
import json
import time
from datetime import datetime

def test_yolo_tracking_clip():
    """测试YOLO_TRACKING_CLIP编排"""
    print("🔍 测试YOLO_TRACKING_CLIP编排...")
    
    task_request = {
        "taskRequest": {
            "taskId": "test-yolo-task-001",
            "taskMeta": {
                "eventTypeId": "HELMET_MISSING",
                "taskLevel": "HIGH"
            },
            "device": {
                "deviceId": "camera-yolo-001",
                "rtspUrl": "rtsp://mock-camera/stream1"
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-yolo-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "yolo-v8",
                        "algorithmName": "YOLO目标检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.5,
                            "nms_threshold": 0.4
                        }
                    }
                ]
            }
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8080/api/v1/scheduler/schedule",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ YOLO_TRACKING_CLIP任务创建成功")
            print(f"📄 响应: {response.json()}")
            return True
        else:
            print(f"❌ YOLO_TRACKING_CLIP任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_ovit_clip():
    """测试OVIT_CLIP编排"""
    print("\n🔍 测试OVIT_CLIP编排...")
    
    task_request = {
        "taskRequest": {
            "taskId": "test-ovit-task-001",
            "taskMeta": {
                "eventTypeId": "OBJECT_ABANDONED",
                "taskLevel": "MEDIUM"
            },
            "device": {
                "deviceId": "camera-ovit-001",
                "rtspUrl": "rtsp://mock-camera/stream2"
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-ovit-001",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "ovit-detection",
                        "algorithmName": "O-VIT万物检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence_threshold": 0.6
                        }
                    }
                ]
            }
        }
    }
    
    try:
        response = requests.post(
            "http://localhost:8080/api/v1/scheduler/schedule",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ OVIT_CLIP任务创建成功")
            print(f"📄 响应: {response.json()}")
            return True
        else:
            print(f"❌ OVIT_CLIP任务创建失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def wait_for_events():
    """等待事件生成"""
    print("\n⏳ 等待60秒让事件生成...")
    time.sleep(60)

def main():
    """主函数"""
    print("🚀 开始测试不同编排类型的事件生成")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试YOLO编排
    yolo_success = test_yolo_tracking_clip()
    
    if yolo_success:
        wait_for_events()
    
    # 测试OVIT编排
    ovit_success = test_ovit_clip()
    
    if ovit_success:
        wait_for_events()
    
    print("\n📊 测试总结:")
    print(f"  YOLO_TRACKING_CLIP: {'✅ 成功' if yolo_success else '❌ 失败'}")
    print(f"  OVIT_CLIP: {'✅ 成功' if ovit_success else '❌ 失败'}")
    
    if yolo_success or ovit_success:
        print("\n💡 提示:")
        print("  - 查看Kafka消息: python kafka_consumer.py")
        print("  - 查看推理模拟器日志观察不同编排的事件生成")
        print("  - 访问 http://localhost:8082 查看Kafka UI")

if __name__ == "__main__":
    main()
