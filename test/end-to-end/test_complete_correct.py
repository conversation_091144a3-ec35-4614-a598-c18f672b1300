#!/usr/bin/env python3
"""
正确的完整端到端测试
1. 启动scheduler、inference-mock服务
2. 通过scheduler接口确认inference-mock已注册
3. 通过scheduler下发任务给inference-mock服务
4. inference-mock服务收到任务后开始拉流按照定时要求输出告警
5. 监听kafka和文件存储目录，确认对应时间是否有告警
"""
import requests
import json
import time
import os
import glob
from datetime import datetime, timedelta
from kafka import KafkaConsumer
import threading
import signal
import sys

# 全局变量
kafka_messages = []
test_start_time = None
test_running = True
scheduler_url = "http://localhost:8080"
inference_url = "http://localhost:8081"

def signal_handler(sig, frame):
    """处理Ctrl+C信号"""
    global test_running
    print("\n🛑 收到停止信号，正在清理...")
    test_running = False

def kafka_consumer_thread():
    """Kafka消费者线程"""
    global kafka_messages, test_running
    
    try:
        consumer = KafkaConsumer(
            'vision-events',  # 修正topic名称，与inference-mock配置一致
            bootstrap_servers=['*************:9092'],  # 使用远端Kafka服务器
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=1000,
            auto_offset_reset='latest'
        )
        
        print("📡 Kafka消费者已启动")
        
        while test_running:
            try:
                for message in consumer:
                    if not test_running:
                        break
                    kafka_messages.append({
                        'timestamp': datetime.now(),
                        'message': message.value
                    })
                    event_id = message.value.get('atomicEventInstanceId', 'unknown')
                    orchestration = message.value.get('orchestrationType', 'unknown')
                    print(f"📨 收到Kafka消息: {event_id} ({orchestration})")
            except Exception as e:
                if test_running:
                    print(f"⚠️ Kafka消费异常: {e}")
                time.sleep(1)
                
    except Exception as e:
        print(f"❌ Kafka消费者启动失败: {e}")

def check_scheduler_health():
    """检查Scheduler健康状态"""
    try:
        response = requests.get(f"{scheduler_url}/actuator/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_inference_health():
    """检查Inference服务健康状态"""
    try:
        response = requests.get(f"{inference_url}/health", timeout=5)
        return response.status_code == 200
    except:
        return False

def check_service_registration():
    """检查inference-mock是否已注册到scheduler"""
    try:
        response = requests.get(f"{scheduler_url}/api/v1/scheduler/services", timeout=10)
        if response.status_code == 200:
            services = response.json()
            for service in services:
                if "inference-mock" in service.get('serviceName', '').lower():
                    print(f"✅ 发现已注册的inference服务: {service.get('serviceName')} ({service.get('serviceId')})")
                    return True
            print("❌ 未找到已注册的inference-mock服务")
            return False
        else:
            print(f"❌ 查询服务列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 查询服务注册状态异常: {e}")
        return False

def create_yolo_tracking_task():
    """创建YOLO+TRACKING+CLASSIFICATION完整任务链"""
    task_request = {
        "taskRequest": {
            "taskId": "yolo-tracking-test-001",
            "taskName": "YOLO跟踪分类测试",
            "taskDescription": "完整的YOLO检测+跟踪+分类任务链",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "HIGH",
                "protocol": "VIDEO",
                "eventTypeId": "helmet_detection_event",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-yolo-tracking-001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "person_detection",
                        "algorithmName": "人员检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence": 0.7,
                            "nms_threshold": 0.5
                        }
                    },
                    {
                        "algorithmId": "person_tracking",
                        "algorithmName": "人员跟踪",
                        "algorithmType": "TRACKING",
                        "order": 2,
                        "required": True,
                        "dependsOn": ["person_detection"],
                        "config": {}
                    },
                    {
                        "algorithmId": "helmet_detection",
                        "algorithmName": "安全帽检测",
                        "algorithmType": "CLASSIFICATION",
                        "order": 3,
                        "required": True,
                        "dependsOn": ["person_tracking"],
                        "alertConfig": {
                            "labels": ["no_helmet"],
                            "confidence": 0.8
                        },
                        "trainingConfig": {
                            "labels": ["helmet", "no_helmet"],
                            "dataCollection": {
                                "enabled": True,
                                "thresholds": {
                                    "minConfidence": 0.1,
                                    "maxConfidence": 0.9
                                },
                                "samplingRate": 0.1,
                                "maxSamplesPerDay": 1000
                            },
                            "modelVersion": "v1.0"
                        }
                    }
                ]
            },
            "device": {
                "deviceId": "camera-yolo-test-001",
                "deviceName": "YOLO测试摄像头",
                "streamConfig": {
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP",
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "decoderConf": {
                        "keyFrameOnly": False,
                        "decodeStep": 4
                    }
                }
            }
        },
        "config": {},
        "region": "default",
        "priority": 1
    }
    
    return schedule_task(task_request)

def create_ovit_classification_task():
    """创建OVIT+CLASSIFICATION完整任务链"""
    task_request = {
        "taskRequest": {
            "taskId": "ovit-classification-test-001",
            "taskName": "OVIT万物检测分类测试",
            "taskDescription": "完整的OVIT检测+分类任务链",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "MEDIUM",
                "protocol": "VIDEO",
                "eventTypeId": "object_detection_event",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch-ovit-classification-001",
                "orchestrationType": "OVIT_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "ovit_detection",
                        "algorithmName": "OVIT万物检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "dependsOn": [],
                        "config": {
                            "confidence": 0.6,
                            "nms_threshold": 0.4
                        }
                    },
                    {
                        "algorithmId": "object_classification",
                        "algorithmName": "物体分类",
                        "algorithmType": "CLASSIFICATION",
                        "order": 2,
                        "required": True,
                        "dependsOn": ["ovit_detection"],
                        "alertConfig": {
                            "labels": ["abandoned_object", "suspicious_item"],
                            "confidence": 0.7
                        },
                        "trainingConfig": {
                            "labels": ["person", "vehicle", "object", "animal", "tool"],
                            "dataCollection": {
                                "enabled": True,
                                "thresholds": {
                                    "minConfidence": 0.1,
                                    "maxConfidence": 0.9
                                },
                                "samplingRate": 0.05,
                                "maxSamplesPerDay": 500
                            },
                            "modelVersion": "v2.0"
                        }
                    }
                ]
            },
            "device": {
                "deviceId": "camera-ovit-test-001",
                "deviceName": "OVIT测试摄像头",
                "streamConfig": {
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP",
                    "url": "/Users/<USER>/Workspace/bohua/bxt-analysis-1/video/knight.mov",
                    "decoderConf": {
                        "keyFrameOnly": False,
                        "decodeStep": 4
                    }
                }
            }
        },
        "config": {},
        "region": "default",
        "priority": 1
    }
    
    return schedule_task(task_request)

def schedule_task(task_request):
    """通过Scheduler调度任务"""
    try:
        response = requests.post(
            f"{scheduler_url}/api/v1/scheduler/schedule",
            headers={"Content-Type": "application/json"},
            json=task_request,
            timeout=15
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 任务调度成功: {task_request['taskRequest']['taskId']}")
                print(f"   分配到服务: {result.get('serviceId')}")
                print(f"   分配ID: {result.get('allocationId')}")
                return {
                    'task_id': task_request['taskRequest']['taskId'],
                    'allocation_id': result.get('allocationId'),
                    'service_id': result.get('serviceId'),
                    'orchestration_type': task_request['taskRequest']['algorithmOrchestration']['orchestrationType']
                }
            else:
                print(f"❌ 任务调度失败: {result.get('errorMessage')}")
                return None
        else:
            print(f"❌ 任务调度请求失败: {response.status_code}")
            print(f"📄 错误: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任务调度异常: {e}")
        return None

def count_screenshots():
    """统计截图数量"""
    screenshot_dir = "inference-mock/screenshots"
    if not os.path.exists(screenshot_dir):
        return 0
    
    jpg_files = glob.glob(os.path.join(screenshot_dir, "*.jpg"))
    return len(jpg_files)

def monitor_alerts(tasks, test_duration_minutes=5):
    """监控告警生成"""
    print(f"\n📊 开始监控告警生成，持续{test_duration_minutes}分钟...")
    
    start_time = datetime.now()
    end_time = start_time + timedelta(minutes=test_duration_minutes)
    
    last_screenshot_count = 0
    last_kafka_count = 0
    
    # 记录每个任务的告警时间
    task_alerts = {task['task_id']: [] for task in tasks if task}
    
    while datetime.now() < end_time and test_running:
        current_time = datetime.now()
        elapsed = (current_time - start_time).total_seconds()
        remaining = (end_time - current_time).total_seconds()
        
        # 统计当前状态
        screenshot_count = count_screenshots()
        kafka_count = len(kafka_messages)
        
        # 检查新增告警
        new_screenshots = screenshot_count - last_screenshot_count
        new_kafka = kafka_count - last_kafka_count
        
        if new_screenshots > 0 or new_kafka > 0:
            print(f"⏰ {current_time.strftime('%H:%M:%S')} - 新增告警!")
            print(f"   📸 新增截图: {new_screenshots}, 📨 新增Kafka: {new_kafka}")
            print(f"   📊 总计: 截图 {screenshot_count}, Kafka {kafka_count}")
            
            # 记录告警时间
            for task in tasks:
                if task:
                    task_alerts[task['task_id']].append(current_time)
        
        # 检查告警间隔是否符合预期
        for task in tasks:
            if task:
                task_id = task['task_id']
                orchestration_type = task['orchestration_type']
                alerts = task_alerts[task_id]
                
                if len(alerts) >= 2:
                    last_alert = alerts[-1]
                    second_last_alert = alerts[-2]
                    interval = (last_alert - second_last_alert).total_seconds()
                    
                    expected_interval = 30 if orchestration_type == "YOLO_TRACKING_CLIP" else 300
                    
                    if abs(interval - expected_interval) > 5:  # 允许5秒误差
                        print(f"⚠️ 告警间隔异常: {task_id}, 实际: {interval:.1f}s, 预期: {expected_interval}s")
                    
                    # 如果超过一个周期还未产生告警，停止测试
                    time_since_last = (current_time - last_alert).total_seconds()
                    if time_since_last > expected_interval + 10:  # 允许10秒延迟
                        print(f"❌ 告警超时: {task_id}, 上次告警: {time_since_last:.1f}s前, 预期间隔: {expected_interval}s")
                        print("🛑 停止测试，查看日志排查问题")
                        return False
        
        last_screenshot_count = screenshot_count
        last_kafka_count = kafka_count
        
        # 每10秒检查一次
        time.sleep(10)
    
    return True

def main():
    """主函数"""
    global test_start_time, test_running
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    
    print("🚀 开始正确的完整端到端测试")
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查服务健康状态
    print("\n🔍 步骤1: 检查服务健康状态")
    if not check_scheduler_health():
        print("❌ Scheduler服务不可用，请先启动Scheduler")
        return
    print("✅ Scheduler服务正常")
    
    if not check_inference_health():
        print("❌ Inference-Mock服务不可用，请先启动Inference-Mock")
        return
    print("✅ Inference-Mock服务正常")
    
    # 2. 检查服务注册
    print("\n🔍 步骤2: 检查服务注册状态")
    if not check_service_registration():
        print("❌ Inference-Mock未注册到Scheduler，等待30秒后重试...")
        time.sleep(30)
        if not check_service_registration():
            print("❌ 服务注册检查失败，请检查服务配置")
            return
    
    # 3. 启动Kafka消费者
    print("\n🔍 步骤3: 启动Kafka消费者")
    kafka_thread = threading.Thread(target=kafka_consumer_thread, daemon=True)
    kafka_thread.start()
    time.sleep(2)
    
    # 4. 通过Scheduler下发任务
    print("\n🔍 步骤4: 通过Scheduler下发任务")
    yolo_task = create_yolo_tracking_task()
    time.sleep(3)
    ovit_task = create_ovit_classification_task()
    
    tasks = [task for task in [yolo_task, ovit_task] if task]
    
    if not tasks:
        print("❌ 没有成功创建任何任务，测试终止")
        return
    
    print(f"\n✅ 成功创建 {len(tasks)} 个任务")
    for task in tasks:
        print(f"   - {task['task_id']} ({task['orchestration_type']})")
    
    # 5. 监控告警生成
    print("\n🔍 步骤5: 监控告警生成")
    test_start_time = datetime.now()
    
    success = monitor_alerts(tasks, test_duration_minutes=5)
    
    test_running = False
    
    # 6. 分析结果
    print(f"\n📊 测试结果分析")
    print("=" * 60)
    
    screenshot_count = count_screenshots()
    kafka_count = len(kafka_messages)
    
    print(f"📸 总截图数量: {screenshot_count}")
    print(f"📨 总Kafka消息: {kafka_count}")
    
    # 分析每个任务的告警情况
    for task in tasks:
        orchestration_type = task['orchestration_type']
        expected_alerts = 10 if orchestration_type == "YOLO_TRACKING_CLIP" else 1
        
        print(f"\n🎯 任务: {task['task_id']}")
        print(f"   编排类型: {orchestration_type}")
        print(f"   预期告警: {expected_alerts}")
        
        # 统计该任务的Kafka消息
        task_kafka_count = sum(1 for msg in kafka_messages 
                              if task['task_id'] in str(msg['message']))
        print(f"   实际Kafka消息: {task_kafka_count}")
        
        if task_kafka_count >= expected_alerts * 0.8:  # 允许20%误差
            print(f"   ✅ 告警频率符合预期")
        else:
            print(f"   ❌ 告警频率不符合预期")
    
    if success:
        print(f"\n🎉 测试完成! 总用时: {(datetime.now() - test_start_time).total_seconds():.1f}秒")
    else:
        print(f"\n⚠️ 测试提前结束，请检查日志")

if __name__ == "__main__":
    main()
