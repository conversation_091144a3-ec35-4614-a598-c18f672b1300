# 测试目录

这个目录包含了按照不同维度分类的测试脚本。

## 目录结构

- **end-to-end/** - 端到端测试，验证整个系统的功能集成
- **scheduler/** - 调度器模块专用测试
- **inference-mock/** - 推理模拟器模块专用测试

## 运行测试

### 环境准备

在运行测试之前，请确保相关服务已启动：

```bash
# 启动Kafka和Zookeeper
cd inference-mock
docker-compose up -d

# 启动调度器(可选)
cd scheduler
docker-compose up -d
```

### 运行不同类型的测试

```bash
# 运行端到端测试
cd test/end-to-end
python test_simple_alert.py

# 运行调度器测试
cd test/scheduler
python test_scheduler.py

# 运行推理模拟器测试
cd test/inference-mock
python test_mock_service.py
```

## 测试类型说明

### 端到端测试
验证从任务创建到事件生成和传输的完整流程。

### 调度器测试
专门测试调度器的功能，包括任务分配、服务注册等。

### 推理模拟器测试
测试推理模拟器的各项功能，包括RTSP处理、事件生成、Kafka消息发送等。

## 快速启动

```bash
# 进入scheduler目录
cd scheduler

# 编译项目
mvn clean compile

# 设置环境变量（基于你的.env配置）
export MONGODB_URI="****************************************************************************************"
export MONGODB_DATABASE="cv_scheduler"

# 启动服务（方式1：使用Maven）
mvn spring-boot:run

# 或者启动服务（方式2：打包后运行）
mvn clean package -DskipTests
java -jar target/cv-scheduler-*.jar
```