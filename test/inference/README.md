# 推理模块测试

这个目录包含专门针对推理模块的测试脚本。

## 测试内容

1. **test_yolov8_detector_module.py** - YOLOv8检测器模块测试
2. **test_dsl_pipeline_module.py** - DSL流水线模块测试
3. **test_video_cap_module.py** - 视频捕获模块测试

## 运行测试

这些测试需要相应的依赖环境：

```bash
# 安装依赖
pip install -r inference/requirements.txt

# 运行YOLOv8检测器测试
python test_yolov8_detector_module.py

# 运行DSL流水线测试
python test_dsl_pipeline_module.py

# 运行视频捕获模块测试
python test_video_cap_module.py
```