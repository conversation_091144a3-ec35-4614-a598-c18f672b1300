#!/bin/bash

# CV分析系统Docker部署停止脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

# 解析服务名称
parse_services() {
    local services=()
    shift # 跳过第一个参数(命令)

    if [ $# -eq 0 ]; then
        services=("all")
    else
        services=("$@")
    fi

    echo "${services[@]}"
}

# 将服务名称转换为docker-compose服务名
get_compose_services() {
    local input_services=("$@")
    local compose_services=()

    for service in "${input_services[@]}"; do
        case "$service" in
            "scheduler")
                compose_services+=("cv-scheduler")
                ;;
            "mock1")
                compose_services+=("cv-inference-mock-1")
                ;;
            "mock2")
                compose_services+=("cv-inference-mock-2")
                ;;
            "all")
                # 不指定具体服务，停止所有
                ;;
            *)
                log_warning "未知服务名: $service，忽略"
                ;;
        esac
    done

    echo "${compose_services[@]}"
}

# 停止服务
stop_services() {
    local target_services=("$@")

    if [ ${#target_services[@]} -eq 0 ] || [[ " ${target_services[*]} " =~ " all " ]]; then
        log_info "停止所有CV分析系统服务..."

        # 停止docker-compose服务
        if [ -f "docker-compose.yml" ]; then
            docker-compose down

            if [ $? -eq 0 ]; then
                log_success "Docker Compose服务停止成功"
            else
                log_warning "Docker Compose服务停止时出现问题"
            fi
        else
            log_warning "docker-compose.yml文件不存在"
        fi

        # 强制停止相关容器
        log_info "检查并停止相关容器..."

        containers=$(docker ps -q --filter "name=cv-scheduler" --filter "name=cv-inference-mock")
        if [ ! -z "$containers" ]; then
            log_info "强制停止容器..."
            docker stop $containers
            docker rm $containers
            log_success "容器停止并删除完成"
        else
            log_info "没有找到运行中的相关容器"
        fi
    else
        log_info "停止指定服务: ${target_services[*]}"

        if [ -f "docker-compose.yml" ]; then
            docker-compose stop "${target_services[@]}"
            docker-compose rm -f "${target_services[@]}"

            if [ $? -eq 0 ]; then
                log_success "指定服务停止成功"
            else
                log_warning "指定服务停止时出现问题"
            fi
        else
            log_warning "docker-compose.yml文件不存在"
        fi
    fi
}

# 清理资源
cleanup_resources() {
    if [ "$1" = "--cleanup" ] || [ "$1" = "-c" ]; then
        log_info "清理Docker资源..."
        
        # 删除未使用的镜像
        log_info "删除未使用的镜像..."
        docker image prune -f
        
        # 删除未使用的卷
        log_info "删除未使用的卷..."
        docker volume prune -f
        
        # 删除未使用的网络
        log_info "删除未使用的网络..."
        docker network prune -f
        
        log_success "资源清理完成"
    fi
}

# 显示状态
show_status() {
    echo ""
    log_info "📊 当前状态:"
    
    # 检查容器状态
    containers=$(docker ps -a --filter "name=cv-scheduler" --filter "name=cv-inference-mock" --format "table {{.Names}}\t{{.Status}}")
    if [ ! -z "$containers" ]; then
        echo "$containers"
    else
        echo "  没有找到相关容器"
    fi
    
    echo ""
}

# 显示使用说明
show_usage() {
    echo "使用方法: $0 [选项] [服务名...]"
    echo ""
    echo "选项:"
    echo "  (无参数)        停止所有服务"
    echo "  --cleanup, -c   停止服务并清理Docker资源"
    echo "  --help, -h      显示此帮助信息"
    echo ""
    echo "服务名 (可指定多个):"
    echo "  scheduler       CV调度器服务"
    echo "  mock1          推理模拟服务1"
    echo "  mock2          推理模拟服务2"
    echo "  all            所有服务 (默认)"
    echo ""
    echo "示例:"
    echo "  $0 scheduler            # 只停止调度器"
    echo "  $0 mock1                # 只停止推理服务1"
    echo "  $0 scheduler mock1      # 停止调度器和推理服务1"
    echo "  $0 --cleanup            # 停止所有服务并清理资源"
    echo ""
}

# 主函数
main() {
    local first_arg="${1:-}"
    local services_input=()
    local compose_services=()
    local cleanup_flag=false

    # 检查是否有cleanup参数
    if [ "$first_arg" = "--cleanup" ] || [ "$first_arg" = "-c" ]; then
        cleanup_flag=true
        shift
        services_input=($(parse_services "stop" "$@"))
        compose_services=($(get_compose_services "${services_input[@]}"))
    elif [ "$first_arg" = "--help" ] || [ "$first_arg" = "-h" ]; then
        show_usage
        return
    elif [ "$first_arg" = "" ]; then
        # 无参数，停止所有服务
        services_input=("all")
    else
        # 解析服务参数
        services_input=($(parse_services "stop" "$@"))
        compose_services=($(get_compose_services "${services_input[@]}"))
    fi

    # 停止服务
    if [ ${#compose_services[@]} -gt 0 ] && [[ ! " ${services_input[*]} " =~ " all " ]]; then
        stop_services "${compose_services[@]}"
    else
        stop_services
    fi

    # 清理资源
    if [ "$cleanup_flag" = true ]; then
        cleanup_resources "--cleanup"
    fi

    show_status
    log_success "🎉 停止操作完成！"
}

# 执行主函数
main "$@"
