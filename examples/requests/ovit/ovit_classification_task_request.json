{"taskRequest": {"taskId": "ovit-classification-test-001", "taskName": "OVIT万物检测+分类测试", "taskDescription": "基于OVIT的万物检测和CLIP分类任务链，适用于长周期监控场景", "taskMeta": {"enabled": true, "taskLevel": "MEDIUM", "protocol": "VIDEO", "eventTypeId": "object_abandoned_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-ovit-001", "orchestrationType": "OVIT_CLIP", "algorithmChain": [{"algorithmId": "ovit_detection", "algorithmName": "OVIT万物检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"query_texts": ["person"], "confidence": 0.15, "nms_threshold": 0.3}}, {"algorithmId": "object_classification", "algorithmName": "物体分类识别", "algorithmType": "CLASSIFICATION", "order": 2, "required": true, "dependsOn": ["ovit_detection"], "config": {"confidence": 0.6}, "alertConfig": {"labels": ["abandoned_object", "suspicious_item"], "positiveLabels": ["遗留物品", "可疑物体", "无人看管的包"], "negativeLabels": ["正常携带", "有人看管"], "confidence": 0.6}, "trainingConfig": {"labels": ["遗留物品", "可疑物体", "正常携带", "有人看管"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8}, "samplingRate": 0.05, "maxSamplesPerDay": 500}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-ovit-test-001", "deviceName": "OVIT长周期监控摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://175.168.10.52:9554/knight", "decoderConf": {"keyFrameOnly": true, "decodeStep": 750}}}}, "config": {"alert_interval": 300, "max_objects_per_frame": 5, "tracking_enabled": false}, "region": "default", "priority": 2}