{"taskRequest": {"taskId": "yolo-bicycle-zone-intrusion-classification-001", "taskName": "YOLO非机动车跟踪+区域入侵+分类", "taskDescription": "基于YOLO非机动车检测、跟踪、区域入侵检测和分类的完整算法链", "taskMeta": {"enabled": true, "taskLevel": "MEDIUM", "protocol": "VIDEO", "eventTypeId": "bicycle_zone_intrusion_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-yolo-bicycle-zone-001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "detection", "algorithmName": "目标检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"confidence": 0.6, "nms_threshold": 0.5, "detection_type": "NON_MOTOR_VEHICLE"}}, {"algorithmId": "tracking", "algorithmName": "目标跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["detection"], "config": {"track_thresh": 0.4, "track_buffer": 20, "match_thresh": 0.7, "frame_rate": 30}}, {"algorithmId": "zone_intrusion_detection", "algorithmName": "区域入侵检测", "algorithmType": "RULE", "order": 3, "required": true, "dependsOn": ["tracking"], "config": {"alarm_thresh": 5, "alarm_frames": 100, "fps": 25, "re_alarm_thresh": 15, "re_alarm_frames": 300}, "ruleConfig": {"ruleType": "zone_intrusion", "polygons": [{"polygonId": "bicycle_forbidden_zone_001", "polygonName": "非机动车禁行区域", "points": [{"x": 200, "y": 150}, {"x": 600, "y": 150}, {"x": 600, "y": 400}, {"x": 200, "y": 400}], "alarmThresh": 5.0, "reAlarmThresh": 15.0, "enabled": true}]}}, {"algorithmId": "bicycle_classification", "algorithmName": "非机动车分类", "algorithmType": "CLASSIFICATION", "order": 4, "required": false, "dependsOn": ["zone_intrusion_detection"], "config": {"confidence": 0.5}, "alertConfig": {"labels": ["illegal_bicycle", "electric_scooter", "unauthorized_entry"], "positiveLabels": ["违规非机动车", "电动车违规", "未授权进入"], "negativeLabels": ["正常通行", "授权车辆"], "confidence": 0.5}, "trainingConfig": {"labels": ["违规非机动车", "电动车违规", "正常通行", "授权车辆"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8}, "samplingRate": 0.08, "maxSamplesPerDay": 150}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-bicycle-zone-001", "deviceName": "非机动车区域监控摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://175.168.10.52:9554/knight", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "region": "default", "priority": 2}