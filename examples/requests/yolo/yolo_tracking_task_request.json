{"taskRequest": {"taskId": "yolo-tracking-test-006", "taskName": "YOLO跟踪分类测试", "taskDescription": "完整的YOLO检测+跟踪+分类任务链", "taskMeta": {"enabled": true, "taskLevel": "HIGH", "protocol": "VIDEO", "eventTypeId": "helmet_detection_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-yolo-tracking-001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "detection", "algorithmName": "目标检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"confidence": 0.7, "nms_threshold": 0.5, "detection_type": "PERSON"}}, {"algorithmId": "tracking", "algorithmName": "目标跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["detection"], "config": {}}, {"algorithmId": "green_clothing_detection", "algorithmName": "绿色衣服检测", "algorithmType": "CLASSIFICATION", "order": 3, "required": true, "dependsOn": ["tracking"], "alertConfig": {"labels": [], "positiveLabels": ["绿色上衣"], "negativeLabels": ["蓝色上衣"], "confidence": 0.15}, "trainingConfig": {"labels": ["绿色上衣", "蓝色上衣"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.1, "maxConfidence": 0.9}, "samplingRate": 0.1, "maxSamplesPerDay": 1000}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-yolo-test-001", "deviceName": "YOLO测试摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://175.168.10.52:9554/knight", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "config": {}, "region": "default", "priority": 1}