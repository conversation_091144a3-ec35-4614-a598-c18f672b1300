{"taskRequest": {"taskId": "yolo-tripwire-classification-001", "taskName": "YOLO跟踪+绊线检测+分类", "taskDescription": "基于YOLO检测、跟踪、绊线检测和分类的完整算法链", "taskMeta": {"enabled": true, "taskLevel": "HIGH", "protocol": "VIDEO", "eventTypeId": "tripwire_crossing_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-yolo-tripwire-001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "detection", "algorithmName": "目标检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"confidence": 0.7, "nms_threshold": 0.5, "detection_type": "PERSON"}}, {"algorithmId": "tracking", "algorithmName": "目标跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["detection"], "config": {"track_thresh": 0.45, "track_buffer": 25, "match_thresh": 0.8, "frame_rate": 30}}, {"algorithmId": "tripwire_detection", "algorithmName": "绊线检测", "algorithmType": "RULE", "order": 3, "required": true, "dependsOn": ["tracking"], "config": {"direction": "both", "max_age": 3}, "ruleConfig": {"ruleType": "tripwire", "lines": [{"lineId": "line_001", "lineName": "主入口绊线", "points": [{"x": 100, "y": 200}, {"x": 500, "y": 200}], "direction": "both", "enabled": true}]}}, {"algorithmId": "person_classification", "algorithmName": "人员分类", "algorithmType": "CLASSIFICATION", "order": 4, "required": false, "dependsOn": ["tripwire_detection"], "config": {"confidence": 0.6}, "alertConfig": {"labels": ["unauthorized_person", "suspicious_behavior"], "positiveLabels": ["可疑人员", "未授权人员", "异常行为"], "negativeLabels": ["正常人员", "授权人员"], "confidence": 0.6}, "trainingConfig": {"labels": ["可疑人员", "未授权人员", "正常人员", "授权人员"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8}, "samplingRate": 0.1, "maxSamplesPerDay": 200}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-tripwire-001", "deviceName": "绊线检测摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://*************:9554/knight", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "region": "default", "priority": 1}