{"taskRequest": {"taskId": "yolo-vehicle-tripwire-classification-001", "taskName": "YOLO车辆跟踪+绊线检测+分类", "taskDescription": "基于YOLO车辆检测、跟踪、绊线检测和分类的完整算法链", "taskMeta": {"enabled": true, "taskLevel": "HIGH", "protocol": "VIDEO", "eventTypeId": "vehicle_tripwire_crossing_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-yolo-vehicle-tripwire-001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "detection", "algorithmName": "目标检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"confidence": 0.7, "nms_threshold": 0.5, "detection_type": "VEHICLE"}}, {"algorithmId": "tracking", "algorithmName": "目标跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["detection"], "config": {"track_thresh": 0.45, "track_buffer": 25, "match_thresh": 0.8, "frame_rate": 30}}, {"algorithmId": "tripwire_detection", "algorithmName": "绊线检测", "algorithmType": "RULE", "order": 3, "required": true, "dependsOn": ["tracking"], "config": {"direction": "both", "max_age": 3}, "ruleConfig": {"ruleType": "tripwire", "lines": [{"lineId": "vehicle_line_001", "lineName": "车辆通行绊线", "points": [{"x": 100, "y": 300}, {"x": 800, "y": 300}], "direction": "both", "enabled": true}]}}, {"algorithmId": "vehicle_classification", "algorithmName": "车辆分类", "algorithmType": "CLASSIFICATION", "order": 4, "required": false, "dependsOn": ["tripwire_detection"], "config": {"confidence": 0.6}, "alertConfig": {"labels": ["illegal_vehicle", "oversized_vehicle", "unauthorized_vehicle"], "positiveLabels": ["违规车辆", "超大车辆", "未授权车辆", "危险品车辆"], "negativeLabels": ["正常车辆", "授权车辆", "小型车辆"], "confidence": 0.6}, "trainingConfig": {"labels": ["违规车辆", "超大车辆", "正常车辆", "授权车辆"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8}, "samplingRate": 0.05, "maxSamplesPerDay": 300}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-vehicle-tripwire-001", "deviceName": "车辆绊线检测摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://*************:9554/knight", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "region": "default", "priority": 1}