{"taskRequest": {"taskId": "yolo-zone-intrusion-classification-001", "taskName": "YOLO跟踪+区域入侵+分类", "taskDescription": "基于YOLO检测、跟踪、区域入侵检测和分类的完整算法链", "taskMeta": {"enabled": true, "taskLevel": "HIGH", "protocol": "VIDEO", "eventTypeId": "zone_intrusion_event", "eventAction": ["ALERT"]}, "algorithmOrchestration": {"orchestrationId": "orch-yolo-zone-001", "orchestrationType": "YOLO_TRACKING_CLIP", "algorithmChain": [{"algorithmId": "detection", "algorithmName": "目标检测", "algorithmType": "DETECTION", "order": 1, "required": true, "dependsOn": [], "config": {"confidence": 0.7, "nms_threshold": 0.5, "detection_type": "PERSON"}}, {"algorithmId": "tracking", "algorithmName": "目标跟踪", "algorithmType": "TRACKING", "order": 2, "required": true, "dependsOn": ["detection"], "config": {"track_thresh": 0.45, "track_buffer": 25, "match_thresh": 0.8, "frame_rate": 30}}, {"algorithmId": "zone_intrusion_detection", "algorithmName": "区域入侵检测", "algorithmType": "RULE", "order": 3, "required": true, "dependsOn": ["tracking"], "config": {"alarm_thresh": 10, "alarm_frames": 200, "fps": 25, "re_alarm_thresh": 10, "re_alarm_frames": 200}, "ruleConfig": {"ruleType": "zone_intrusion", "polygons": [{"polygonId": "zone_001", "polygonName": "禁入区域", "points": [{"x": 100, "y": 100}, {"x": 400, "y": 100}, {"x": 400, "y": 300}, {"x": 100, "y": 300}], "alarmThresh": 10.0, "reAlarmThresh": 30.0, "enabled": true}]}}, {"algorithmId": "person_classification", "algorithmName": "人员分类", "algorithmType": "CLASSIFICATION", "order": 4, "required": false, "dependsOn": ["zone_intrusion_detection"], "config": {"confidence": 0.6}, "alertConfig": {"labels": ["unauthorized_intrusion", "security_breach"], "positiveLabels": ["非法入侵", "安全违规", "未授权进入"], "negativeLabels": ["正常通行", "授权人员"], "confidence": 0.6}, "trainingConfig": {"labels": ["非法入侵", "安全违规", "正常通行", "授权人员"], "dataCollection": {"enabled": true, "thresholds": {"minConfidence": 0.2, "maxConfidence": 0.8}, "samplingRate": 0.1, "maxSamplesPerDay": 200}, "modelVersion": "v1.0"}}]}, "device": {"deviceId": "camera-zone-intrusion-001", "deviceName": "区域入侵检测摄像头", "streamConfig": {"resolution": "1920x1080", "frameRate": 25, "protocol": "RTSP", "url": "rtsp://175.168.10.52:9554/knight", "decoderConf": {"keyFrameOnly": false, "decodeStep": 4}}}}, "region": "default", "priority": 1}