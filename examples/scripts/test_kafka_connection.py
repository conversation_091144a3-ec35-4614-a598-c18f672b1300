#!/usr/bin/env python3
"""
Kafka 连接测试脚本
用于验证 Kafka 配置是否正确
"""

import json
import time
from kafka import KafkaProducer, KafkaConsumer
from kafka.errors import KafkaError
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Kafka 配置
KAFKA_CONFIG = {
    'bootstrap_servers': ['175.168.10.52:9092'],
    'topic': 'vision-events',
    'client_id': 'kafka-test-client'
}

def test_kafka_producer():
    """测试 Kafka 生产者"""
    logger.info("🔍 测试 Kafka 生产者连接...")
    
    try:
        producer = KafkaProducer(
            bootstrap_servers=KAFKA_CONFIG['bootstrap_servers'],
            client_id=KAFKA_CONFIG['client_id'] + '-producer',
            acks=1,
            retries=3,
            value_serializer=lambda v: json.dumps(v, ensure_ascii=False).encode('utf-8'),
            key_serializer=lambda k: k.encode('utf-8') if k else None
        )
        
        # 测试连接
        logger.info("正在测试连接...")
        metadata = producer.bootstrap_connected()
        if metadata:
            logger.info("✅ Kafka 生产者连接成功")
            
            # 发送测试消息
            test_message = {
                'test': True,
                'timestamp': time.time(),
                'message': 'Kafka 连接测试消息'
            }
            
            logger.info(f"发送测试消息到 topic: {KAFKA_CONFIG['topic']}")
            future = producer.send(KAFKA_CONFIG['topic'], value=test_message, key='test-key')
            
            # 等待发送完成
            record_metadata = future.get(timeout=10)
            logger.info(f"✅ 消息发送成功: topic={record_metadata.topic}, "
                       f"partition={record_metadata.partition}, offset={record_metadata.offset}")
            
            producer.flush()
            producer.close()
            return True
        else:
            logger.error("❌ Kafka 生产者连接失败")
            return False
            
    except KafkaError as e:
        logger.error(f"❌ Kafka 生产者错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 生产者测试异常: {e}")
        return False

def test_kafka_consumer():
    """测试 Kafka 消费者"""
    logger.info("🔍 测试 Kafka 消费者连接...")
    
    try:
        consumer = KafkaConsumer(
            KAFKA_CONFIG['topic'],
            bootstrap_servers=KAFKA_CONFIG['bootstrap_servers'],
            client_id=KAFKA_CONFIG['client_id'] + '-consumer',
            value_deserializer=lambda x: json.loads(x.decode('utf-8')),
            consumer_timeout_ms=5000,  # 5秒超时
            auto_offset_reset='latest'
        )
        
        logger.info("✅ Kafka 消费者连接成功")
        logger.info(f"监听 topic: {KAFKA_CONFIG['topic']}")
        logger.info("等待消息（5秒超时）...")
        
        message_count = 0
        for message in consumer:
            message_count += 1
            logger.info(f"📨 收到消息 #{message_count}: {message.value}")
            
            # 只读取几条消息作为测试
            if message_count >= 3:
                break
        
        if message_count == 0:
            logger.info("⚠️ 未收到任何消息（可能是正常的，如果没有新消息产生）")
        
        consumer.close()
        return True
        
    except KafkaError as e:
        logger.error(f"❌ Kafka 消费者错误: {e}")
        return False
    except Exception as e:
        logger.error(f"❌ 消费者测试异常: {e}")
        return False

def main():
    """主测试函数"""
    logger.info("🚀 开始 Kafka 连接测试")
    logger.info(f"配置信息:")
    logger.info(f"  服务器: {KAFKA_CONFIG['bootstrap_servers']}")
    logger.info(f"  主题: {KAFKA_CONFIG['topic']}")
    logger.info(f"  客户端ID: {KAFKA_CONFIG['client_id']}")
    logger.info("=" * 50)
    
    # 测试生产者
    producer_ok = test_kafka_producer()
    time.sleep(2)
    
    # 测试消费者
    consumer_ok = test_kafka_consumer()
    
    # 总结
    logger.info("=" * 50)
    logger.info("📊 测试结果:")
    logger.info(f"  生产者: {'✅ 成功' if producer_ok else '❌ 失败'}")
    logger.info(f"  消费者: {'✅ 成功' if consumer_ok else '❌ 失败'}")
    
    if producer_ok and consumer_ok:
        logger.info("🎉 Kafka 配置测试通过！")
        return True
    else:
        logger.error("💥 Kafka 配置测试失败，请检查配置和网络连接")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
