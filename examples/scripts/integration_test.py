#!/usr/bin/env python3
"""
调度器和推理模拟器联调测试脚本
"""
import json
import time
import requests
from datetime import datetime

# 服务地址
SCHEDULER_URL = "http://localhost:8080"
INFERENCE_MOCK_URL = "http://localhost:8081"

def test_service_health():
    """测试服务健康状态"""
    print("=== 测试服务健康状态 ===")
    
    # 测试调度器健康状态
    try:
        response = requests.get(f"{SCHEDULER_URL}/actuator/health", timeout=5)
        print(f"调度器健康状态: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"调度器健康检查失败: {e}")
    
    # 测试推理模拟器健康状态
    try:
        response = requests.get(f"{INFERENCE_MOCK_URL}/health", timeout=5)
        print(f"推理模拟器健康状态: {response.status_code} - {response.json()}")
    except Exception as e:
        print(f"推理模拟器健康检查失败: {e}")


def create_simplified_task_request():
    """创建简化任务请求"""
    return {
        "taskRequest": {
            "taskId": "integration_test_001",
            "taskName": "联调测试任务",
            "taskDescription": "测试调度器和推理模拟器的联调",
            "taskMeta": {
                "enabled": True,
                "taskLevel": "HIGH",
                "protocol": "VIDEO",
                "eventTypeId": "event_type_integration_001",
                "eventAction": ["ALERT"]
            },
            "algorithmOrchestration": {
                "orchestrationId": "orch_integration_001",
                "orchestrationType": "YOLO_TRACKING_CLIP",
                "algorithmChain": [
                    {
                        "algorithmId": "person_detection",
                        "algorithmName": "人员检测",
                        "algorithmType": "DETECTION",
                        "order": 1,
                        "required": True,
                        "config": {
                            "confidence": 0.7,
                            "nms_threshold": 0.5
                        }
                    },
                    {
                        "algorithmId": "helmet_detection",
                        "algorithmName": "安全帽检测",
                        "algorithmType": "CLASSIFICATION",
                        "order": 2,
                        "required": True,
                        "dependsOn": ["person_detection"],
                        "alertConfig": {
                            "labels": ["no_helmet"],
                            "confidence": 0.8
                        },
                        "trainingConfig": {
                            "labels": ["helmet", "no_helmet"],
                            "dataCollection": {
                                "enabled": True,
                                "thresholds": {
                                    "minConfidence": 0.1,
                                    "maxConfidence": 0.9
                                },
                                "samplingRate": 0.1,
                                "maxSamplesPerDay": 1000
                            },
                            "modelVersion": "v1.0"
                        }
                    }
                ]
            },
            "device": {
                "deviceId": "integration_camera_001",
                "deviceName": "联调测试摄像头",
                "streamConfig": {
                    "resolution": "1920x1080",
                    "frameRate": 25,
                    "protocol": "RTSP",
                    "url": "rtsp://demo.stream/test",
                    "decoderConf": {
                        "keyFrameOnly": False,
                        "decodeStep": 4
                    }
                }
            }
        },
        "config": {},
        "region": "test",
        "priority": 1
    }


def test_task_scheduling():
    """测试任务调度"""
    print("\n=== 测试任务调度 ===")
    
    task_request = create_simplified_task_request()
    
    try:
        # 发送调度请求
        print("发送任务调度请求...")
        response = requests.post(
            f"{SCHEDULER_URL}/api/v1/scheduler/schedule",
            json=task_request,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"调度响应状态: {response.status_code}")
        print(f"调度响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务调度成功: {result}")
            return result
        else:
            print(f"❌ 任务调度失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任务调度异常: {e}")
        return None


def test_inference_mock_task_creation():
    """测试推理模拟器任务创建"""
    print("\n=== 测试推理模拟器任务创建 ===")
    
    task_request = create_simplified_task_request()
    
    try:
        # 直接向推理模拟器发送任务创建请求
        print("向推理模拟器发送任务创建请求...")
        response = requests.post(
            f"{INFERENCE_MOCK_URL}/api/v1/tasks",
            json=task_request,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"任务创建响应状态: {response.status_code}")
        print(f"任务创建响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务创建成功: {result}")
            return result
        else:
            print(f"❌ 任务创建失败: {response.status_code} - {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任务创建异常: {e}")
        return None


def test_task_status():
    """测试任务状态查询"""
    print("\n=== 测试任务状态查询 ===")
    
    task_id = "integration_test_001"
    
    try:
        # 查询推理模拟器中的任务状态
        response = requests.get(
            f"{INFERENCE_MOCK_URL}/api/v1/tasks/{task_id}",
            timeout=5
        )
        
        print(f"任务状态查询响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 任务状态: {result}")
            return result
        else:
            print(f"❌ 任务状态查询失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 任务状态查询异常: {e}")
        return None


def test_event_generation():
    """测试事件生成"""
    print("\n=== 测试事件生成 ===")
    
    task_id = "integration_test_001"
    
    try:
        # 等待一段时间让任务运行
        print("等待5秒让任务运行...")
        time.sleep(5)
        
        # 查询生成的事件
        response = requests.get(
            f"{INFERENCE_MOCK_URL}/api/v1/tasks/{task_id}/events",
            timeout=5
        )
        
        print(f"事件查询响应: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"✅ 生成的事件数量: {len(result.get('events', []))}")
            if result.get('events'):
                print(f"最新事件示例: {json.dumps(result['events'][0], indent=2, ensure_ascii=False)}")
            return result
        else:
            print(f"❌ 事件查询失败: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ 事件查询异常: {e}")
        return None


def main():
    """主测试函数"""
    print("🚀 开始调度器和推理模拟器联调测试")
    print(f"测试时间: {datetime.now()}")
    print(f"调度器地址: {SCHEDULER_URL}")
    print(f"推理模拟器地址: {INFERENCE_MOCK_URL}")
    
    # 1. 测试服务健康状态
    test_service_health()
    
    # 2. 测试推理模拟器任务创建
    task_result = test_inference_mock_task_creation()
    
    # 3. 测试任务状态查询
    if task_result:
        test_task_status()
        
        # 4. 测试事件生成
        test_event_generation()
    
    # 5. 测试调度器任务调度（可选，因为需要服务注册）
    print("\n=== 测试调度器任务调度（可选） ===")
    print("注意：调度器需要推理服务注册后才能正常调度")
    schedule_result = test_task_scheduling()
    
    print("\n🎉 联调测试完成！")


if __name__ == "__main__":
    main()
