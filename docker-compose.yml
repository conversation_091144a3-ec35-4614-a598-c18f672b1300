version: '3.8'

services:
  # CV调度器服务
  cv-scheduler:
    image: cv-scheduler:latest
    container_name: cv-scheduler
    ports:
      - "${SCHEDULER_PORT:-8080}:8080"
    environment:
      # Spring配置
      - SPRING_PROFILES_ACTIVE=docker
      
      # MongoDB配置 - 使用外部MongoDB
      - MONGODB_URI=${MONGODB_URI:-mongodb://admin:<EMAIL>:27017/cv_scheduler?authSource=admin}
      - MONGODB_DATABASE=${MONGODB_DATABASE:-cv_scheduler}
      
      # Redis配置 - 可选，用于分布式锁
      - REDIS_HOST=${REDIS_HOST:-host.docker.internal}
      - REDIS_PORT=${REDIS_PORT:-6379}
      - REDIS_PASSWORD=${REDIS_PASSWORD:-}
      
      # 调度器特定配置
      - SCHEDULER_LOCK_TYPE=${SCHEDULER_LOCK_TYPE:-local}
      - SCHEDULER_HEALTH_CHECK_INTERVAL=${SCHEDULER_HEALTH_CHECK_INTERVAL:-30000}
      - SCHEDULER_HEALTH_CHECK_TIMEOUT=${SCHEDULER_HEALTH_CHECK_TIMEOUT:-5000}
      
    volumes:
      - ./infrastructure/logs/scheduler:/app/logs
    networks:
      - cv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # 推理模拟服务1
  cv-inference-mock-1:
    image: cv-inference-mock:latest
    container_name: cv-inference-mock-1
    ports:
      - "${INFERENCE_MOCK_1_PORT:-8081}:8081"
    environment:
      # 服务配置
      - SERVICE_HOST=cv-inference-mock-1
      - SERVICE_NAME=${INFERENCE_MOCK_1_NAME:-inference-mock-1}
      - SERVICE_PORT=8081
      - SERVICE_REGION=${INFERENCE_MOCK_1_REGION:-default}
      - SERVICE_GPU_TYPE=${INFERENCE_MOCK_1_GPU_TYPE:-A10}
      - SERVICE_MAX_QUOTA=${INFERENCE_MOCK_1_MAX_QUOTA:-10}
      
      # 调度器配置
      - SCHEDULER_URL=${SCHEDULER_URL:-http://cv-scheduler:8080}
      
      # Kafka配置 - 使用外部Kafka
      - KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-host.docker.internal:9092}
      - KAFKA_TOPIC=${KAFKA_TOPIC:-vision-events}
      - KAFKA_CLIENT_ID=${INFERENCE_MOCK_1_NAME:-inference-mock-1}
      
      # RTSP配置
      - RTSP_DEFAULT_SERVER=${RTSP_DEFAULT_SERVER:-host.docker.internal:9554}
      - RTSP_SIMULATION_MODE=${RTSP_SIMULATION_MODE:-true}
      
      # 编排配置
      - CURRENT_ORCHESTRATION=${INFERENCE_MOCK_1_ORCHESTRATION:-YOLO_TRACKING_CLIP}
      
    volumes:
      - ./inference-mock/screenshots:/app/screenshots
      - ./inference-mock/logs:/app/logs
      - inference_mock_1_data:/app/data
    networks:
      - cv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://cv-inference-mock-1:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # 推理模拟服务2 (可选)
  cv-inference-mock-2:
    image: cv-inference-mock:latest
    container_name: cv-inference-mock-2
    ports:
      - "${INFERENCE_MOCK_2_PORT:-8082}:8081"
    environment:
      # 服务配置
      - SERVICE_HOST=cv-inference-mock-2
      - SERVICE_NAME=${INFERENCE_MOCK_2_NAME:-inference-mock-2}
      - SERVICE_PORT=8081
      - SERVICE_REGION=${INFERENCE_MOCK_2_REGION:-default}
      - SERVICE_GPU_TYPE=${INFERENCE_MOCK_2_GPU_TYPE:-A10}
      - SERVICE_MAX_QUOTA=${INFERENCE_MOCK_2_MAX_QUOTA:-10}
      
      # 调度器配置
      - SCHEDULER_URL=${SCHEDULER_URL:-http://cv-scheduler:8080}
      
      # Kafka配置 - 使用外部Kafka
      - KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-host.docker.internal:9092}
      - KAFKA_TOPIC=${KAFKA_TOPIC:-vision-events}
      - KAFKA_CLIENT_ID=${INFERENCE_MOCK_2_NAME:-inference-mock-2}
      
      # RTSP配置
      - RTSP_DEFAULT_SERVER=${RTSP_DEFAULT_SERVER:-host.docker.internal:9554}
      - RTSP_SIMULATION_MODE=${RTSP_SIMULATION_MODE:-true}
      
      # 编排配置
      - CURRENT_ORCHESTRATION=${INFERENCE_MOCK_2_ORCHESTRATION:-OVIT_CLIP}
      
    volumes:
      - ./inference-mock/screenshots-2:/app/screenshots
      - ./inference-mock/logs-2:/app/logs
      - inference_mock_2_data:/app/data
    networks:
      - cv-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://cv-inference-mock-2:8081/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    profiles:
      - multi-instance  # 使用profile控制是否启动第二个实例

volumes:
  scheduler_logs:
  inference_mock_1_data:
  inference_mock_2_data:

networks:
  cv-network:
    driver: bridge
    name: cv-network
